import { StyleSheet } from 'react-native'
import { ifIphoneX } from 'react-native-iphone-x-helper'
import { color, typography } from '@app/theme'
// import { typography } from '../../../../theme'

const styles = StyleSheet.create({
  boxAddress: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    paddingBottom: 12,
    paddingLeft: 11,
  },
  boxCheckInTime: {
    flex: 1,
    flexDirection: 'column'
  },
  boxContact: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    height: 125,
    marginBottom: 20,
    marginLeft: 15,
    marginRight: 10,
    marginTop: 24,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10,
    width: 288
  },
  boxService: {
    backgroundColor: '#fff',
    borderBottomColor: '#f6f6f7',
    flexDirection: 'row',
    marginHorizontal: 15,
    marginVertical: 16
  },
  boxViewAddress: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    marginBottom: 26,
    marginHorizontal: 15,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10
  },
  boxViewPhone: {
    flexDirection: 'row',
  },
  btnDelete: {
    backgroundColor: color.primaryBackground,
    height: 44,
    minWidth: 80
  },
  btnNote: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    marginLeft: 8,
    textAlign: 'left',
    textAlignVertical: 'top',
    width: '95%'
  },
  btnSubmit: {
    alignContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ff8ba1',
    borderRadius: 8,
    flex: 1,
    // fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold',
    height: 44,
    justifyContent: 'center',
    letterSpacing: 0,
    marginTop: 26,
    textAlign: 'center'
  },
  btnSubmitCT1: {
    // backgroundColor: '#4fc3f7',
    height: 44,
    minWidth: 80
  },
  container: {
    backgroundColor: color.primaryBackground,
    ...ifIphoneX({
      paddingBottom: 156
    }, {
      paddingBottom: 166
    }),
  },
  containerAddressUS: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
  },
  containerCoupon: {
    flexDirection: 'row',
  },
  containerList: {
    marginBottom: 89,
    marginLeft: 15,
    marginTop: 15
  },
  containerServiceChoose: {
    flexDirection: 'row',
    marginLeft: 15,
    marginTop: 15
  },
  contentCash: {
    color: '#333',
    // fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
  },
  contentDateTime: {
    color: '#333',
    // fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginLeft: 15
  },
  contentSelectPayment: {
    color: color.primary,
    // fontFamily: typography.normal,
    fontSize: 14,
  },
  contentText: {
    alignItems: 'flex-start',
    flex: 1,
    position: 'relative',
  },
  couponText: {
    color: 'rgba(0, 0, 0, 0.85)',
    // fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 15,
    textAlign: 'center',
    paddingVertical: 10
  },
  couponTextNone: {
    color: '#f3373a',
    fontFamily: typography.normal,
    fontSize: 14,
    fontStyle: 'italic',
    marginLeft: 15,
    marginTop: -2,
    textDecorationLine: 'underline'
  },
  dateTime: {
    // flex: 1,
    marginLeft: 10,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  discountValue: {
    color: '#fff',
    paddingHorizontal: 10,
    paddingVertical: 3
  },
  iView: {
    borderBottomColor: '#ccc',
    borderBottomWidth: 1,
    flex: 1,
    padding: 10
  },
  iView1: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center'
  },
  iView1Text: {
    color: '#333333',
    fontSize: 13,
    width: 100
  },
  iView1Text1: {
    color: '#333333',
    flex: 1,
    fontSize: 13,
    marginLeft: 10
  },
  iView2: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 4
  },
  icArrowBack: {
    marginLeft: 10,
  },
  icLocation: {
    marginTop: 2
  },
  imageChoose: {
    borderRadius: 8,
    height: 50,
    width: 50
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
    ...ifIphoneX({
      marginTop: 0
    }, {
      marginTop: 5
    }),
  },
  noteContent: {
    backgroundColor: '#f7f9fc',
    borderColor: '#EFEFEF',
    borderRadius: 4,
    borderWidth: 1,
    color: '#333333',
    // fontFamily: typography.normal,
    fontSize: 14,
    marginBottom: 15,
    marginLeft: 15,
    marginRight: 15,
    marginTop: 15,
    minHeight: 80,
    padding: 12,
    textAlign: 'left',
    textAlignVertical: 'top',
  },
  orderNo: {
    color: '#333',
    // fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 15,
    marginLeft: 15
  },
  rMd: {
    justifyContent: 'flex-end',
    margin: 0
  },
  rMdView: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 10,
    maxHeight: 500,
    padding: 20
  },
  rMdView1: {
    alignItems: 'flex-end',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: 20
  },
  rMdView1Text: {
    color: '#999',
    fontSize: 14,
    textAlign: 'center',
    textDecorationLine: 'underline'
  },
  rMdViewText: {
    color: '#CB1016',
    fontSize: 14,
    marginBottom: 10
  },
  rMdViewText1: {
    color: '#333',
    fontSize: 13,
    marginBottom: 4,
    marginLeft: 10
  },
  rMdViewText2: {
    alignSelf: 'center',
    color: '#999',
    fontSize: 13,
    marginBottom: 4,
    marginTop: 30,
    paddingBottom: 30,
    textAlign: 'center'
  },
  rSp: {
    alignItems: 'flex-start',
    borderBottomColor: '#E9E9E9',
    // borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    paddingBottom: 20,
    paddingTop: 20
  },
  rSpView: {
    borderColor: '#E2E2E2',
    // borderRadius: 9,
    borderWidth: 1,
    height: 100,
    position: 'relative',
    width: 100
  },
  rSpView1: {
    flex: 1,
    paddingLeft: 20
  },
  rSpView1Input: {
    color: '#333',
    marginTop: 10
  },
  rSpView1Text: {
    color: '#333',
    fontSize: 14,
    lineHeight: 18
  },
  rSpView1Text1: {
    color: '#999999',
    fontSize: 12,
    marginTop: 10
  },
  rSpView1Text2: {
    color: '#999999',
    fontSize: 12,
    marginTop: 10,
  },
  rSpView2: {
    alignItems: 'center',
    justifyContent: 'center'
  },
  rSpView2Cb: {
    height: 15,
    width: 15
  },
  rSpView2Top: {
    marginTop: 20,
    padding: 4
  },
  rSpView2TopImage: {
    height: 24,
    width: 24
  },
  rSpViewImage: {
    height: '100%',
    resizeMode: 'contain',
    width: '100%'
  },
  rdkTopImage: {
    borderRadius: 8,
    height: 50,
    resizeMode: 'cover',
    width: 50
  },
  rdkTopText: {
    color: '#333',
    // fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0,
    paddingBottom: 10
  },
  rdkTopText1: {
    color: '#acb1c0',
    // fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0
  },
  renderItemListAddress: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 12,
    paddingLeft: 13,
    paddingRight: 13,
  },
  renderItemListPhone: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 18,
    paddingLeft: 13,
    paddingRight: 13,
  },
  renderItemListPrice: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rsCb: {
    height: 20,
    width: 20
  },
  rsView: {
    backgroundColor: '#fff',
    borderBottomColor: '#F6F6F7',
    marginBottom: 5,
    paddingHorizontal: 15,
    paddingVertical: 20
  },
  rsView1: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center'
  },
  rsView1Text: {
    color: '#333',
    fontSize: 16,
    fontWeight: '500',
    paddingBottom: 5
  },
  rsView1Top: {
    borderColor: 'green',
    borderRadius: 20,
    borderStyle: 'dotted',
    borderWidth: 1,
    marginLeft: 4,
    paddingBottom: 3,
    paddingLeft: 10,
    paddingRight: 10,
    paddingTop: 3
  },
  rsView1TopText:
    {
      color: '#000',
      fontSize: 11
    },
  rsView2: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginLeft: 2,
    marginTop: 4
  },
  rsView2Image: {
    height: 16,
    resizeMode: 'contain',
    width: 12
  },
  rsView2Text: {
    color: '#333',
    flex: 1,
    fontSize: 14,
    marginLeft: 10
  },
  sAv: {
    backgroundColor: '#CB1016',
    flex: 1,
    marginTop: -4
  },
  sAvView: {
    backgroundColor: '#fff',
    flex: 1,
    paddingBottom: 60
  },
  sAvView0: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  sAvView0Image: {
    height: 14,
    resizeMode: 'contain',
    width: 24
  },
  sAvView0Text: {
    flex: 1,
    fontSize: 25,
  },
  sAvView1: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center'
  },
  sAvView1Image: {
    resizeMode: 'contain',
    width: 100
  },
  sAvView1Text: {
    color: '#333',
    fontSize: 14,
  },
  sAvView2: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    paddingTop: 20
  },
  sAvView2Text: {
    borderColor: '#E9E9E9',
    borderRadius: 4,
    borderWidth: 1,
    color: '#ccc',
    flex: 1,
    fontSize: 13,
    height: 45,
    marginRight: 10,
    paddingLeft: 12,
    textAlignVertical: 'center',
  },
  sAvView2Top: {
    alignItems: 'center',
    backgroundColor: '#1274E7',
    borderRadius: 4,
    height: 42,
    justifyContent: 'center',
    paddingLeft: 15,
    paddingRight: 15
  },
  sAvView2TopText: {
    color: '#fff',
    fontSize: 13,
    padding: 5,
  },
  sAvView3: {
    backgroundColor: '#fff',
    borderTopColor: '#F6F6F7',
    borderTopWidth: 1,
    elevation: 1,
    paddingBottom: 30,
    paddingHorizontal: 15,
    paddingVertical: 10,
    shadowColor: '#000',
    shadowOffset: { width: -2, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 10
  },
  sAvView31: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center'
  },
  sAvView31Text: {
    color: '#333',
    flex: 1,
    fontSize: 13
  },
  sAvView31Text1: {
    color: '#333',
    fontSize: 13,
    marginTop: 5,
  },
  sAvView4: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center'
  },
  sAvView5: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center'
  },
  sAvView5Text: {
    color: '#333',
    flex: 1,
    fontSize: 13
  },
  sAvView5Text1: {
    color: '#CB1016',
    fontSize: 13,
    marginTop: 5,
  },
  sAvView5Top: {
    backgroundColor: '#CB1016',
    borderRadius: 4,
    marginTop: 10,
    padding: 15,
    width: '100%'
  },
  sAvView5TopText: {
    color: '#FFFFFF',
    fontSize: 13,
    lineHeight: 18,
    textAlign: 'center'
  },
  sPText: {
    color: '#CB1016',
    fontSize: 13,
    marginTop: 10,
  },
  selectBank: {
    // flex: 1,
    flexDirection: 'row',
    // paddingRight: 15,
    marginTop: 15,
    // alignItems: 'center'
  },
  selectPayment: {
    color: '#333',
    // fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
  },
  serviceContent: {
    flex: 1,
    paddingHorizontal: 10
  },
  textAddaddress: {
    color: color.primary,
    // fontFamily: typography.normal,
    fontSize: 14,
    textAlign: 'right',
  },
  textAddressBranch: {
    color: '#333',
    // fontFamily: typography.normal,
    fontSize: 14,
    height: 36,
    letterSpacing: 0,
    marginHorizontal: 7,
    width: '90%'
  },
  textAddressPhone: {
    color: '#333',
    // fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginLeft: 7
  },
  textBtn: {
    fontSize: 16,
    fontWeight: '500'
  },
  textContent: {
    color: color.primary,
    // fontFamily: typography.normal,
    fontSize: 14,
    marginTop: 10,
    fontWeight: '600'
  },
  textInputNote: {
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 14,
    marginTop: 6,
    minHeight: 200,
    padding: 12,
    textAlign: 'left',
    textAlignVertical: 'top',
  },
  textLabel: {
    color: '#333',
    // fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0,
    marginLeft: 15
  },
  textLabelPayment: {
    color: '#333',
    // fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginLeft: 15
  },
  textName: {
    color: '#333',
    // fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 12,
  },
  textPrice_Count: {
    color: color.primary,
    // fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    marginVertical: 10,
  },
  textSubLabel: {
    color: '#979797',
    // fontFamily: typography.normal,
    fontSize: 14,
    marginVertical: 10
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    // fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',

  },
  textTitleService: {
    color: '#333',
    // fontFamily: typography.normal,
    fontSize: 14,
  },
  title: {
    color: '#333',
    // fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
    letterSpacing: 0,
    paddingHorizontal: 15,
    paddingVertical: 20
  },
  titleLabel: {
    color: '#979797',
    fontSize: 14,
    fontWeight: '500',
    textTransform: 'uppercase'
  },
  totalMoney: {
    color: '#979797',
    fontWeight: '600',
    marginVertical: 10
  },
  totalMoneyContent: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  valueCoupon: {
    color: '#333',
    fontSize: 14,
    fontStyle: 'italic',
    fontWeight: '500',
    paddingVertical: 5
  },
  viewBtn: {
    marginHorizontal: 16,
    marginVertical: 12,
    // marginTop: 30,
    // paddingBottom: 8
  },
  viewBtnItem: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 15
  },
  viewDiscountValue: {
    backgroundColor: color.primary,
    borderRadius: 4
  },
  viewHeaderList: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 1,
    paddingHorizontal: 16,
    paddingVertical: 20
  },
  viewImageService: {
    alignItems: 'center',
    borderRadius: 16,
    justifyContent: 'center',
    marginLeft: 0,
  },
  viewInputCoupon: {
    backgroundColor: '#fff',
    paddingTop: 12
  },
  viewLabel: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 10,
  },
  viewPayment: {
    backgroundColor: '#fff',
    paddingHorizontal: 16
  },
  viewPaymentMethod: {
    backgroundColor: '#fff',
    marginVertical: 5,
    paddingBottom: 20,
    paddingHorizontal: 16
  },
  viewService: {
    flexDirection: 'column',
    flex: 1,
    marginLeft: 10
  },
  viewShippingAddress: {
    backgroundColor: '#fff',
    marginBottom: 5,
    paddingHorizontal: 16
  },
  viewTextLabel: {
    alignItems: 'center',
    flexDirection: 'row',
    paddingVertical: 15
  },
  viewTextLabelAddressUser: {
    alignItems: 'center',
    // backgroundColor: '#edf1f7',
    flexDirection: 'row',
    // height: 30,
    marginVertical: 15
  },
  viewTextLabelFooter: {
    alignItems: 'center',
    backgroundColor: '#edf1f7',
    flexDirection: 'row',
    height: 30,
    marginTop: 15
  },
  viewTextNote: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    paddingVertical: 15
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  viewTtinput: {
    flex: 1,
    marginRight: 10,
    width: '100%'
  }

})

export default styles
