import { SafeAreaView } from 'react-native-safe-area-context'
import React, { useContext, useEffect, useRef, useState } from 'react'
import { observer } from 'mobx-react-lite'
import {
  PaymentProductChoose,
  TButton,
  Text,
  ButtonBack,
  TTextInput,
  LazyImage,
  RenderBranch, ConfirmDialog
} from '@app/components'
import { FlatList, TouchableOpacity, View, TextInput } from 'react-native'
import { StackActions, useNavigation, useRoute } from '@react-navigation/native'
import styles from './styles'
import { useStores, OrderProductStoreModel } from '@app/models'
import { useTranslation } from 'react-i18next'
import { ModalContext } from '@app/context'
import { SCREENS } from '@app/navigation'
import { Modalize } from 'react-native-modalize'
import { responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import database from '@react-native-firebase/database'

import { Payment } from '../service-detail-screen/tabrender/payment/payment'
import {
  removeItemBuyedAllShop
} from '../../services/shoppingServices'
import { useAuth } from '@app/use-hooks/use-auth'
import Icon from 'react-native-vector-icons/Ionicons'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import { color } from '@app/theme'
import { ifIphoneX } from 'react-native-iphone-x-helper'
import LinearGradient from 'react-native-linear-gradient'
import { formatMoney } from '@app/utils'

const numeral = require('numeral')

const showPrice = (item) => {
  if (item.classifyActive && Object.keys(item.classifyActive).length) {
    const regex = new RegExp(',', 'g')
    const p = item.classifyActive.price.replace(regex, '')
    // giá con
    return <Text style={styles.textContent}>{formatMoney(parseInt(p))} <Text style={[styles.textPrice_Count, { textDecorationLine: 'underline' }]}>đ</Text></Text>
  } else {
    // fixed price
    return <Text style={styles.textContent}>{formatMoney(item.info.price)} <Text style={[styles.textPrice_Count, { textDecorationLine: 'underline' }]}>đ</Text></Text>
  }
}

const RenderItemProduct = (props) => {
  const { t } : any = useTranslation()
  const { item, index } = props
  const [note, setNote] = useState<any>(item.noteProduct)
  // const products = item.products.filter(product => product.select === true)
  return (
    <>
      <View style={styles.rSp}>
        <View style={styles.rSpView}>
          <LazyImage source={{ uri: item.info.thumbail }} style={styles.rSpViewImage}/>
        </View>
        <View style={styles.rSpView1}>
          <Text style={styles.rSpView1Text}>{item.info.name}</Text>
          {showPrice(item)}
          {/* {item.classifies.map(e => { */}
          {/*  if (e.index == 0) { */}
          {/*    return <Text key={e.index} style={styles.rSpView1Text1}>{e.name}: <Text style={{ fontSize: 13, color: '#333' }}>{e.value}</Text></Text> */}
          {/*  } */}
          {/*  return null */}
          {/* })} */}
          {item.classifyActive ? <Text key={index} style={styles.rSpView1Text1}>{item.classifyActive.name}: <Text style={{ fontSize: 13, color: '#333' }}>{item.classifyActive.value}</Text></Text> : null}
          <Text style={styles.rSpView1Text2}>{t('COUNT')}: <Text style={{ fontSize: 13, color: '#333' }}>{item.count}</Text></Text>

        </View>
      </View>
      <Text style={{ fontSize: 13, color: '#333' }}>Ghi chú: {item.noteProduct || 'Không có'}</Text>
    </>
  )
}

export const ReviewBookingProductScreen = observer(function ReviewBookingProductScreen(props) {
  const { t } : any = useTranslation()
  const route: any = useRoute()
  const { generalPrice } = route?.params
  const { navigate, goBack } = useNavigation()
  const { profileStore, bookingStore, productStore } = useStores()
  const navigation = useNavigation()
  const { showError, showSuccess, showCustomError, showCustomSuccess } = useContext(ModalContext)
  const modalizeBankList = useRef<Modalize>(null)
  const modalInputNote = useRef<Modalize>(null)
  const [bankCode, setBankCode] = useState('')
  const [isSubmitting, setSubmitting] = useState(false)
  const [selectPayment, setSelectPayment] = useState(0)
  const findDefaultIndex = profileStore.addressList.findIndex((x) => x.default === true)
  const [selectedAddressShip, setSelectedAddressShip] = useState(profileStore.addressList.length > 0 ? findDefaultIndex : -1)
  const [addressShipping, setAddressShipping] = useState(null)
  const [transportFee, setTransportFee] = useState(0)
  const [reloadData, setReloadData] = useState(true)
  const [tongTien, setTongTien] = useState(0)
  const { updateShoppingCount } = useAuth()
  const { shoppingCount } = useAuth()
  // const [refreshAddress, setRefreshAddress] = useState(false)
  const [isShowConfirmLogOut, setIsShowConfirmLogOut] = useState(false)
  const [note, setNote] = useState('')
  const [noteSave, setNoteSave] = useState('')
  const listShopFilter = productStore.shopProducts.filter(x => x.haveItem === true)

  useEffect(() => {
    if (profileStore.addressList.length <= 0) {
      setTimeout(() => { showAlert() }, 1000)
    }
    productStore.calculateTotalPrice()
  }, [])

  const showAlert = () => {
    setIsShowConfirmLogOut(true)
    setSubmitting(false)
  }

  useEffect(() => {
    if (profileStore.reloadData) {
      // setRefreshAddress(true)
      profileStore.setReloadData(false)
      profileStore.setRefreshAddress(true)
      setSelectedAddressShip(findDefaultIndex !== -1 ? findDefaultIndex : 0)
    }
  }, [profileStore.reloadData])

  const formatMoney = (value) => {
    return numeral(value).format('0,0')
  }

  useEffect(() => {
    setTongTien(productStore.totalPrice + transportFee)
  }, [productStore.totalPrice, transportFee])

  const onGoBack = () => {
    goBack()
    setTimeout(() => { onRemoveCouponShop() }, 100)
  }

  const onOpen = () => {
    modalizeBankList.current?.open()
  }
  const onClose = () => {
    modalizeBankList.current?.close()
  }

  const renderHeaderModal = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onClose} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>Chọn hình thức thanh toán</Text>
    </View>
  )

  const onSelectBank = (item) => {
    setBankCode(item)
    onClose()
  }

  function onSetNoteShop(value, indexShop) {
    const idShop = listShopFilter[indexShop].info._id
    const shop = listShopFilter.find(x => x.info._id === idShop)
    shop.note = value
    // __DEV__ && console.log('productStore.shopProducts[indexShop].coupon', shop)
  }

  // 1. set cho tung shop
  function onSetCouponShop(coupon, indexShop) {
    const idShop = listShopFilter[indexShop].info._id
    const shop = listShopFilter.find(x => x.info._id === idShop)
    // productStore.calculateTotalPriceDiscount().then(r => {
    //   setReloadData(!reloadData)
    // })
    if (coupon === '') {
      shop.coupon = ''
      shop.discount = 0
      shop.valueCoupon = ''
      shop.totalPriceShop = shop.totalPriceShopNoDiscount
    }
    if (coupon && coupon.length >= 4) {
      shop.coupon = coupon
      // setReloadData(!reloadData)
      productStore.calculateTotalPriceDiscount().then(r => {
        setReloadData(!reloadData)
      })
    }
    // __DEV__ && console.log('productStore.shopProducts[indexShop].coupon', shop)
  }
  // remove all coupon shop
  function onRemoveCouponShop() {
    for (let i = 0; i < listShopFilter.length; i++) {
      const shop = listShopFilter[i]
      if (shop.coupon !== '') {
        shop.coupon = ''
        shop.discount = 0
        shop.valueCoupon = ''
        shop.totalPriceShop = shop.totalPriceShopNoDiscount
        // setIsDisableBtn(true)
      }
    }
    productStore.calculateTotalPriceDiscount().then(r => {
      setReloadData(!reloadData)
    })
    productStore.setTotalPrice(productStore.generalPrice)
  }

  /*
  * 2. tinh tien khi nhap coupon cua tung shop
  * */
  const onCalculateShop = async (indexShop) => {
    // setSubmitting(true)
    // const shop = productStore.shopProducts[indexShop]
    const shop = listShopFilter[indexShop]
    if (shop && !shop?.discount) {
      const rs = await productStore.checkCoupon(shop.coupon || '', shop.totalPriceShop, 0, shop.storeId)
      if (rs && rs.data.error) {
        showCustomError(t('FAIL'), `${rs.data.message}`)
        shop.discount = 0
        await productStore.calculateTotalPriceDiscount()
      } else {
        const data = rs?.data?.data
        // __DEV__ && console.log('onCalculateShop data => ', data)
        if (data.rsCoupon.error) {
          showCustomError(t('FAIL'), `${data.rsCoupon.msg}`)
          shop.discount = 0
          await productStore.calculateTotalPriceDiscount()
        } else if (data.totalPrice) {
          shop.discount = data.discount
          shop.totalPriceShop = data.totalPrice
          shop.valueCoupon = data.coupon.value
          shop.rsCoupon = data.coupon.code
          await productStore.calculateTotalPriceDiscount()
        }
      }
      setReloadData(!reloadData)
    }
    setSubmitting(false)
  }

  useEffect(() => {
    productStore.setOrderId(generateOrderNo())
  }, [])

  useEffect(() => {
    setSubmitting(true)
    const addressList = profileStore.addressList
    if (addressShipping === null && selectedAddressShip === 0) {
      const find = addressList.find((x) => x.default === true)
      // const findDefaultIndex = addressList.findDefaultIndex((x) => x.default === true)
      // default first item of array (BUG: error default address)
      if (!find) {
        setAddressShipping(addressList[0])
      } else {
        setAddressShipping(find)
        setSelectedAddressShip(findDefaultIndex !== -1 ? findDefaultIndex : 0)
      }
    } else {
      setAddressShipping(addressList[selectedAddressShip])
    }
  }, [selectedAddressShip])

  useEffect(() => {
    if (addressShipping) {
      calculateFeeShip()
    }
  }, [addressShipping])

  const calculateFeeShip = async () => {
    let totalTransportFee = 0
    const mapShopProducts = listShopFilter.map(async shop => {
      // TODO: goi api
      // shop[shop.info._id].products
      if (shop[shop.info._id].products.filter(product => product.select === true).length > 0) {
        const body = {
          storeId: shop.info._id,
          province: addressShipping?.province || '',
          district: addressShipping?.district || '',
          ward: addressShipping?.ward || '',
          street: addressShipping?.street || '',
          address: addressShipping?.address,
          location: addressShipping?.address,
          shippingService: 'ghtk',
          products: shop[shop.info._id].products.filter(product => product.select === true)
        }
        const rs = await productStore.calculateFeeShip(body)
        __DEV__ && console.log('ReviewBookingProductScreen -> rs', rs)
        if (rs?.data?.data && !rs.data.error) {
          totalTransportFee += Number(rs.data.data.totalFee)
          shop.transportFee = Number(rs.data.data.totalFee)
          shop.totalWeight = Number(rs.data.data.totalWeight)
          // __DEV__ && console.log('totalTransportFee', totalTransportFee)
          // __DEV__ && console.log('shop.transportFee', shop.transportFee)
          shop.shippingService = 'ghtk'
        } else {
          // showError(t('FAIL'), 'Không thể tính phí giao hàng, vui lòng thử lại sau.')
          showError(t('FAIL'), `Không thể tính phí giao hàng do ${rs?.data.message}`)
          setSubmitting(false)
        }
      }
    })
    // __DEV__ && console.log('totalTransportFee', _.sumBy(productStore.shopProducts, (o: any) => (o.transportFee)))
    await Promise.all(mapShopProducts)
    // productStore.shopProducts.reduce(function (acc, obj) { return acc + obj[obj.info._id].transportFee; }, 0);
    setTransportFee(totalTransportFee)
    // console.tron.log('calculateFeeShip -> productStore.shopProducts', productStore.shopProducts)
    setSubmitting(false)
  }

  // ham xoa nhung san pham da mua trong gio hang
  const reloadShoppingCart = async (shops: any) => {
    await removeItemBuyedAllShop(shops, updateShoppingCount) // xoa
    await productStore.fetchCartData(updateShoppingCount) // goi api reload
  }

  const onSubmit = async () => {
    if (profileStore.addressList.length <= 0) {
      setTimeout(() => { showAlert() }, 1000)
      return
    }
    setSubmitting(true)
    // build array , get only selected shop and selected item in shop
    const shops = []
    listShopFilter.map((item, index) => {
      shops.push({ ...item, products: item[item.info._id].products.filter((p, index) => p.select === true) })
    })
    const dataBookingModel = {
      ...productStore,
      ...addressShipping,
      userId: profileStore._id,
      paymentId: productStore.orderId,
      bookingType: 0,
      location: addressShipping.address,
      // name: addressShipping.name,
      paymentMethod: selectPayment === 0 ? 1 : selectPayment === 1 ? 0 : '',
      items: shops,
      totalAmount: productStore.totalPrice,
      shippingService: 'ghtk',
      transportFee: transportFee,
      note: note || 'Không có ghi chú',
      coupon: productStore.rsCoupon || '' // TODO: lay tu ket qua da check
    }
    const dataBooking = OrderProductStoreModel.create(dataBookingModel)
    // const dataOrder = bookingData
    // _.assign(dataBooking, { orderId: productStore.orderId }) // orderNo
    if (selectPayment === 0 && !bankCode) {
      // TODO: translate
      // showCustomError(t('FAIL'), 'Bạn chưa chọn ngân hàng thanh toán')
      onOpen()
      setSubmitting(false)
    } else if (bankCode && selectPayment === 0) {
      dataBooking.setBankCode(bankCode)
      productStore.createOrder(dataBooking).then(rs => {
        if (rs && rs.data?.error) {
          setSubmitting(false)
          showError(t('FAIL'), t(`${rs.data.message}`))
        } else if (rs.data.data.vnpUrl) {
          setSubmitting(true)
          reloadShoppingCart(shops) // thực hiện xóa những sản phẩm đã mua trong giỏ hàng
          goBack()
          navigate(SCREENS.payment, { vnpUrl: rs.data.data.vnpUrl, orderId: productStore.orderId, paymentId: rs.data.data.paymentId, typeBooking: 0 })
          clearData()
        }
      }).catch(err => {
        showError(t('FAIL'), 'unknown')
        setSubmitting(false)
        __DEV__ && console.error(err)
      })
    } else if (selectPayment === 1) {
      // THANH TOAN TIEN MAT
      dataBooking.setBankCode('TM')
      productStore.createOrder(dataBooking).then(rs => {
        if (rs && rs.data?.error) {
          showError(t('FAIL'), t(`${rs.data.message}`))
          setSubmitting(false)
        } else {
          // removeAllInCart(updateShoppingCount)
          showSuccess(t('THANHCONG'), t('BOOKING_PRODUCT_SUCCESS'))
          // setTimeout(() => navigate(SCREENS.shoppingScreen), 2000)
          reloadShoppingCart(shops) // thực hiện xóa những sản phẩm đã mua trong giỏ hàng
          setTimeout(() => goBookingDetail(), 500)
          clearData()
        }
      }).catch(err => {
        showError(t('FAIL'), 'unknown')
        console.error(err)
      })
    } else {
      showError(t('FAIL'), 'unknown')
    }
  }

  const clearData = () => {
    productStore.setGeneralCoupon('')
    productStore.setRsCoupon('')
    productStore.clearFieldsCoupon()
    productStore.setTotalDisCount(0)
    productStore.setReloadData(true)
  }

  const renderHeaderModalInput = () => (
    <View style={styles.modal__header}>
      <Text onPress={onCloseModalInputNote} style={{ color: '#333' }}>{t('CANCEL')}</Text>
      {/* <ButtonBack onPress={onClose} style={styles.viewTouchButtonTop} /> */}
      <Text style={styles.textTitleHeader}>{t('Add_note')}</Text>
      <TouchableOpacity
        onPress={() => {
          setNoteSave(note)
          onCloseModalInputNote()
        }}
      >
        <Text style={{ color: color.primary }}>{t('XONG')}</Text>
      </TouchableOpacity>

    </View>
  )

  const renderInputNote = () => {
    return (
      <View style={{ paddingBottom: 70, paddingHorizontal: 8 }}>
        <TextInput
          placeholder={t('NHAPNOIDUNG')}
          defaultValue={noteSave}
          underlineColorAndroid="transparent"
          autoCapitalize='none'
          style={styles.textInputNote}
          numberOfLines={4}
          maxLength={200}
          multiline={true}
          onChangeText={e => setNote(e.trim())}
        />
      </View>
    )
  }

  const renderShop = (item, index) => {
    return <View style={styles.rsView}>
      <View style={styles.rsView1}>
        <TouchableOpacity style={{ flex: 1 }}>
          <Text style={styles.rsView1Text}>{item.info.name}</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.rsView2}>
        {/* <Image style={styles.rsView2Image} source={IcMap}/> */}
        <Icon name={'location-outline'} size={20} color={'red'}/>
        <Text style={styles.rsView2Text}>{item.info.address}</Text>
      </View>
      <FlatList
        showsVerticalScrollIndicator={false}
        style={{ paddingLeft: 2 }}
        data={item[item.info._id].products.filter(x => x.select)}
        extraData={item[item.info._id].products.filter(x => x.select)}
        keyExtractor={(item, index) => item._id + index.toString()}
        renderItem={({ item, index }) => <RenderItemProduct item={item} index={index}/>}
      />
      <View >
        {/* <View style={{ flexDirection: 'column', marginTop: 10 }}>
          <TTextInput
            typeRadius={'rounded'}
            autoCapitalize={'none'}
            placeholder={t('Ghi chú sản phẩm, màu sắc, kích cỡ...')}
            placeholderTextColor={'#a0a0a0'}
            underlineColorAndroid="transparent"
            onChangeText={(e) => onSetNoteShop(e, index)}
            defaultValue={productStore.shopProducts[index]?.note || ''}
          />
        </View> */}
        <View style={styles.sAvView2}>
          <View style={styles.viewTtinput}>
            <TTextInput
              typeInput={'code'}
              typeRadius={'rounded'}
              // keyboardType="phone-pad"
              maxLength={6}
              autoCapitalize={'none'}
              defaultValue={listShopFilter[index]?.coupon || ''}
              placeholder={t('Mã giảm giá của shop')}
              placeholderStyle={{ textAlign: 'center' }}
              onChangeText={(e) => onSetCouponShop(e, index)}
              iconRightClick={() => onSetCouponShop('', index)}
              iconRight={<Icon name='close-circle-outline' size={24} color='#a0a0a0' />}
            />
          </View>
          { listShopFilter[index]?.discount > 0 ? (<TButton
            onPress={() => onSetCouponShop('', index)}
            buttonStyle={styles.btnSubmitCT1}
            titleStyle={styles.textBtn}
            title={t('DELETE')}
            typeRadius={'rounded'}
          />) : (
            <TButton
              // disabled={isDisableBtn.flag}
              typeRadius={'rounded'}
              buttonStyle={styles.btnSubmitCT1}
              title={t('APDUNG')}
              titleStyle={styles.textBtn}
              onPress={() => onCalculateShop(index)} />
          )
          }
        </View>
      </View>
      <View style={styles.viewInputCoupon}>
        <View style={styles.containerCoupon}>
          <Text style={styles.textSubLabel}>{t('DISCOUNT')} của shop: </Text>
          {listShopFilter[index]?.valueCoupon ? <Text style={styles.couponText}>{listShopFilter[index]?.rsCoupon}</Text>
            : <Text style={[styles.textSubLabel, { color: '#333' }]}>{t('INPUT_COUPON')}</Text>}
          {listShopFilter[index]?.valueCoupon > 0 ? <TouchableOpacity onPress={() => onSetCouponShop('', index)}><Icon style={{ marginLeft: 10, paddingVertical: 8 }} name={'close-outline'} size={20} color={'#979797'} /></TouchableOpacity>
            : null}
        </View>
        { listShopFilter[index]?.valueCoupon > 0 ? <Text style={styles.valueCoupon}>(Giảm giá {listShopFilter[index]?.valueCoupon} %)</Text> : null}
      </View>
      <Text style={[styles.textSubLabel, { marginTop: 20 }]}>Phí vận chuyển:  <Text style={styles.textContent}>{formatMoney(item.transportFee)} <Text style={[styles.textPrice_Count, { textDecorationLine: 'underline' }]}>đ</Text></Text></Text>
      <Text style={[styles.textSubLabel, { marginBottom: 0 }]}>Tổng số tiền ({(item.shopCount)} sản phẩm):  <Text style={styles.textContent}>{formatMoney(item.totalPriceShop)} <Text style={[styles.textPrice_Count, { textDecorationLine: 'underline' }]}>đ</Text></Text></Text>
    </View>
  }

  const generateOrderNo = () => {
    const date = new Date()
    const components = [
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      date.getHours(),
      date.getMinutes(),
      date.getSeconds()
    ]
    return components.join('')
  }

  // LISTEN CALL BACK IPN PAYMENT VIA QR CODE
  useEffect(() => {
    if (!bookingStore.orderId) {
      // @ts-ignore
      return
    }
    const path = `payment/vnpay/${bookingStore.orderId}`
    const onValueChange = database()
      .ref(path)
      .on('value', snapshot => {
        const data = snapshot.val()
        const responsCode = data?.vnp_ResponseCode
        // console.log('orderId data: ', snapshot.val())
        if (responsCode == '00') {
          goBookingDetail()
        }
      })

    // Stop listening for updates when no longer required
    return () =>
      database()
        .ref(path)
        .off('value', onValueChange)
  }, [bookingStore.orderId])

  const goBookingDetail = () => {
    navigation.dispatch(
      StackActions.replace(SCREENS.bookingHistoryScreen, { bookingType: 0, title: t('BOOKING_HISTORY') })
    )
  }

  const onOpenModalInputNote = () => {
    modalInputNote.current?.open()
  }
  const onCloseModalInputNote = () => {
    modalInputNote.current?.close()
  }

  const renderHeaderList = () => {
    return (
      <View style={styles.viewHeaderList}>
        <Text style={styles.titleLabel}>Sản phẩm trong giỏ</Text>
        <TouchableOpacity
          onPress={() => onGoBack()}
        >
          <Text style={{ color: color.primary, fontSize: 14, fontWeight: '500' }}>Thay đổi</Text>
        </TouchableOpacity>
      </View>
    )
  }

  const renderBottomBtn = () => {
    return (
      <View style={{
        position: 'absolute',
        ...ifIphoneX({
          bottom: 55
        }, {
          bottom: 55
        }),
        backgroundColor: '#fff'
      }}>
        <View style={{ backgroundColor: '#fff', borderTopWidth: 1, borderTopColor: '#eee', width: responsiveWidth(100) }}>
          <View style={styles.viewBtn} >
            <TButton typeRadius={'rounded'} disabled={ isSubmitting} loading={isSubmitting} title={t('DATHANG') + ' ' + `- ${formatMoney(tongTien)} đ`} onPress={onSubmit} titleStyle={styles.textBtn} />
          </View>
        </View>
      </View>
    )
  }

  const renderBtnInputNote = () => {
    return (
      <View style={{ backgroundColor: '#fff', marginBottom: 5, paddingHorizontal: 15, flex: 1 }}>
        <View style={styles.viewTextLabel}>
          <Text style={styles.titleLabel}>{t('NOTE')}</Text>
        </View>
        <TouchableOpacity
          onPress={() => onOpenModalInputNote()}
          style={styles.viewTextNote}>
          <Icon name={'chatbox-ellipses-outline'} size={20} color={'#333'} />
          {noteSave ? <Text numberOfLines={1} style={styles.btnNote}>{noteSave}</Text> : <Text numberOfLines={1} style={styles.btnNote}>{t('Add_note')}</Text>}
        </TouchableOpacity>
      </View>
    )
  }

  return (
    <>
      <SafeAreaView style={{ backgroundColor: color.primaryBackground, marginTop: -4 }} edges={['right', 'top', 'left']}>
        <View style={styles.container}>
          <Header
            // statusBarProps={{ barStyle: 'light-content' }}
            // barStyle="light-content" // or directly
            leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={onGoBack}/>}
            centerComponent={{ text: t('Thông tin mua hàng'), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
            // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
            //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
            // </TouchableOpacity> : null}
            containerStyle={common.headerContainer}
            statusBarProps={{ barStyle: 'light-content' }}
            ViewComponent={LinearGradient}
            linearGradientProps={linearGradientProps}
          />
          <FlatList
            showsVerticalScrollIndicator={false}
            data={listShopFilter}
            extraData={listShopFilter}
            keyExtractor={(item, index) => item._id + index.toString()}
            renderItem={({ item, index }) => renderShop(item, index)}
            ListHeaderComponent={renderHeaderList}
            ListFooterComponent={<View>
              <View style={styles.viewShippingAddress}>
                <View style={styles.viewTextLabelAddressUser}>
                  <View style={styles.containerAddressUS}>
                    <Text style={styles.titleLabel}>{t('Địa chỉ nhận hàng')}</Text>
                    <Text onPress={() => { navigate(SCREENS.renderAddAddress) }} style={styles.textAddaddress}>{t('ADDADDRESS')}</Text>
                  </View>
                </View>
                <View>
                  <RenderBranch dataBranch={profileStore.addressList} selectedAddressIndex={(selectedAddressUserIndex) => {
                    setSelectedAddressShip(selectedAddressUserIndex)
                  }} refreshAddress={profileStore.refreshAddress}/>
                </View>
              </View>
              {/* {renderBtnInputNote()} */}
              {/* <View style={styles.viewTextLabelFooter}> */}
              {/*  <Text style={styles.textLabel}>{t('NOTE')}</Text> */}
              {/* </View> */}
              {/* <Text style={styles.noteContent}>{bookingData.note}</Text> */}
              <View style={styles.viewPayment}>
                <View style={styles.viewTextLabel}>
                  <Text style={styles.titleLabel}>{t('TOTAL_MONEY')}</Text>
                </View>
                <View style={styles.totalMoneyContent}>
                  <Text style={styles.textSubLabel}>{t('Tổng tiền hàng')}</Text>
                  <Text style={styles.textPrice_Count}>{formatMoney(productStore.totalPrice)} <Text style={[styles.textPrice_Count, { textDecorationLine: 'underline' }]}>đ</Text></Text>
                </View>
                {/* <View style={styles.renderItemListPrice}> */}
                {/*  <Text style={styles.textSubLabel}>{t('Shop giảm giá')}</Text> */}
                {/*  <Text style={styles.textPrice_Count}> - {formatMoney(productStore.totalShopDiscount)} VNĐ</Text> */}
                {/* </View> */}
                <View style={styles.totalMoneyContent}>
                  <Text style={styles.textSubLabel}>{t('Tổng tiền phí vận chuyển')}</Text>
                  <Text style={styles.textPrice_Count}>{formatMoney(transportFee)} <Text style={[styles.textPrice_Count, { textDecorationLine: 'underline' }]}>đ</Text></Text>
                </View>
                <View style={styles.renderItemListPrice}>
                  <Text style={styles.textSubLabel}>{t('Giảm giá')}</Text>
                  <Text style={styles.textPrice_Count}>{formatMoney(productStore.totalDiscount)} <Text style={[styles.textPrice_Count, { textDecorationLine: 'underline' }]}>đ</Text></Text>
                </View>
                {/* <View style={styles.renderItemListPrice}> */}
                {/*  <View style={styles.containerCoupon}> */}
                {/*    <Text style={styles.textSubLabel}>{t('DISCOUNT')} Mypet</Text> */}
                {/*    {productStore.coupon */}
                {/*      ? <Text style={[styles.textPrice_Count, { marginLeft: 10 }]}>{productStore.coupon} */}
                {/*      </Text> */}
                {/*      : null} */}
                {/*  </View> */}
                {/*  <Text style={styles.textPrice_Count}>{productStore.valueCoupon ? productStore.valueCoupon : 0} %</Text> */}
                {/* </View> */}
                {/* <View style={styles.renderItemListPrice}> */}
                {/*  <Text style={styles.textSubLabel}>{t('Tổng tiền đã giảm giá discount chung')}</Text> */}
                {/*  <Text style={styles.textPrice_Count}>{formatMoney(productStore.generalPrice)}</Text> */}
                {/* </View> */}
                <View style={styles.renderItemListPrice}>
                  <Text style={styles.textSubLabel}>{t('HAVETOPAY')}</Text>
                  <Text style={styles.textPrice_Count}>{formatMoney(tongTien)} <Text style={[styles.textPrice_Count, { textDecorationLine: 'underline' }]}>đ</Text></Text>
                </View>
                {/* <View style={styles.totalMoneyContent}> */}
                {/*  <Text style={styles.textSubLabel}>{t('TOTAL_MONEY')}  // cuối cùng</Text> */}
                {/*  <Text style={styles.textPrice_Count}>{formatMoney(tongTien)} VNĐ</Text> */}
                {/* </View> */}
              </View>
              <View style={styles.viewPaymentMethod}>
                <View style={styles.viewTextLabel}>
                  <Text style={styles.titleLabel}>{t('PHUONGTHUC_THANHTOAN')}</Text>
                </View>
                <PaymentProductChoose onPress={(item) => { setSelectPayment(item) }} />
                <View>
                  {selectPayment === 0
                    ? <TouchableOpacity style={styles.selectBank}
                      onPress={() => {
                        onOpen()
                      }}>
                      <Text style={styles.contentSelectPayment}>{t('CHON_NGAN_HANG')}</Text>
                      <View style={styles.dateTime}>
                        {bankCode ? <Text style={styles.selectPayment}>{bankCode}</Text> : null}
                      </View>
                    </TouchableOpacity> : <View><View style={styles.selectBank}>
                      <Text style={styles.contentCash}>{t('Payment_on_delivery')}</Text>
                    </View></View>}
                </View>
              </View>
            </View>}
          />
        </View>
        {renderBottomBtn()}
        <Modalize
          HeaderComponent={renderHeaderModal}
          ref={modalizeBankList}
          modalHeight={responsiveHeight(85)}
          keyboardAvoidingBehavior={'padding'}
        >
          <View>
            <Payment onSelect={onSelectBank} oncloseModal={onClose}/>
          </View>
        </Modalize>
        <ConfirmDialog
          confirmText={t('DONGY')}
          cancelText={t('EXIT')}
          onClosed={() => {
            setIsShowConfirmLogOut(false)
            onGoBack()
          }}
          title={'Nhắc nhở'}
          isVisible={isShowConfirmLogOut} message={'Không có địa chỉ nhận hàng. Vui lòng thêm địa chỉ'}
          onConfirm={() => {
            setIsShowConfirmLogOut(false)
            setTimeout(() => { navigate(SCREENS.renderAddAddress) }, 100)
          }
          }/>
        <Modalize
          HeaderComponent={renderHeaderModalInput}
          ref={modalInputNote}
          adjustToContentHeight
          // snapPoint={405}
          // modalHeight={405}
          // onClosed={() => {}}
          keyboardAvoidingBehavior={'padding'}
        >
          {renderInputNote()}
        </Modalize>
      </SafeAreaView>
    </>
  )
})
