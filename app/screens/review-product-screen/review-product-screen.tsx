import React, { useEffect, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { View, Text, TouchableOpacity, FlatList } from 'react-native'
import { useStores } from '@app/models'
import styles from './styles'
import StarRating from 'react-native-star-rating'
import { useTranslation } from 'react-i18next'
import { ButtonBack, EmptyData, LazyImage } from '@app/components'
import moment from 'moment'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import { useNavigation, useRoute } from '@react-navigation/native'
import RenderStar from '@app/screens/service-detail-screen/tabrender/RenderStar/renderStar'
import { numberFormat } from '@app/utils/number'
import LinearGradient from 'react-native-linear-gradient'

export const ReviewProductScreen = observer(function ReviewProductScreen() {
  const { productStore, } = useStores()
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()
  const route: any = useRoute()
  const { productId } = route?.params
  const [images, setImages] = useState([])
  const [rateValue, setRateValue] = useState(0)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    await productStore.getProductRate(productId)
    setImages([...productStore.product.pictures, productStore.product.thumbail])
    setRateValue(productStore.totalRateValue || 0)
  }

  const renderProductDetail = () => {
    return (
      <View style={styles.viewProductDetail}>
        <LazyImage style={styles.productImg} source={{ uri: images[0] }}></LazyImage>
        <View style={styles.content}>
          <Text style={styles.productName}>{productStore.product.name}</Text>
          <View style={{ flexDirection: 'row', paddingVertical: 5 }}>
            <Text style={styles.topPrice}>{numberFormat(productStore.product.price)} đ</Text>
            {/* <Text style={styles.topOldPrice}>{numberFormat(productStore.product.price)} đ</Text> */}
            {/* <View style={styles.viewDiscount}> */}
            {/*  <Text style={styles.discount}>-10%</Text> */}
            {/* </View> */}
          </View>
          <View style={{ flexDirection: 'row' }}>
            <View style={styles.viewStar}>
              <StarRating
                fullStarColor={'#FFC107'}
                disabled={true}
                maxStars={5}
                rating={Number(rateValue)}
                emptyStarColor={'#edf1f7'}
                emptyStar={'star'}
                fullStar={'star'}
                halfStar={'star-half-o'}
                iconSet={'FontAwesome'}
                starSize={13}
                starStyle={styles.customStar}
              />
              <Text style={{ color: '#3f3f3f', fontWeight: '400', fontSize: 14, marginLeft: 10 }}>{rateValue}</Text>
              <Text style={{ color: '#3f3f3f', fontWeight: '400', fontSize: 14, marginLeft: 10 }}>({productStore?.commentData?.length} {t('REVIEW')})</Text>
            </View>
          </View>
        </View>
      </View>
    )
  }

  const renderItemComment = ({ item }) => {
    return (
      <View style={styles.container}>
        <View style={styles.containerComment}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', flex: 1 }}>
            <TouchableOpacity>
              <Text numberOfLines={2} style={styles.textTitle}>{item.userId.fullName || item.userId.phone}</Text>
            </TouchableOpacity>
            <View style={styles.viewStarComment}>
              {item.rate !== 0
                ? <StarRating
                  fullStarColor={'#FFC107'}
                  disabled={true}
                  maxStars={5}
                  rating={Number(item.rate)}
                  emptyStarColor={'#edf1f7'}
                  emptyStar={'star'}
                  fullStar={'star'}
                  halfStar={'star-half-o'}
                  iconSet={'FontAwesome'}
                  starSize={12}
                  starStyle={styles.customStar}
                /> : null}
              <View style={{ flex: 1 }}>
                <Text style={styles.textTime}>{moment(item.modifyAt).locale('vi').fromNow()}</Text>
              </View>
            </View>
            {/* <View style={{ */}
            {/*  justifyContent: 'space-between', */}
            {/*  alignItems: 'flex-end', */}
            {/* }}> */}
            {/*  <Icon onPress={() => { onOpen(item) }} name={'ellipsis-horizontal-outline'} size={24} color={'#d8d8d8'} /> */}
            {/* </View> */}
          </View>
          {item.content ? <View style={styles.viewService}>
            <Text style={styles.textContent}>{item.content}</Text>
          </View> : null}
        </View>

      </View>
    )
  }

  const renderHeaderTabReview = () => {
    return (<View>
      {renderProductDetail()}
      <RenderStar type={'product'}/>
      <View style={styles.viewComment}>
        <Text style={styles.titleLabel}>{t('COMMENT')}</Text>
      </View>
    </View>)
  }

  return (
    <View style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <Header
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
        centerComponent={{ text: t(t('REVIEW')), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      {productStore?.commentData?.length > 0 ? <View style={{ flex: 1 }}>
        <FlatList
          showsVerticalScrollIndicator={false}
          data={productStore?.commentData}
          // onRefresh={() => onRefresh()}
          // refreshing={refreshing}
          keyExtractor={item => item.id}
          renderItem={renderItemComment}
          ListHeaderComponent={renderHeaderTabReview}
          // onEndReached={e => {
          //   // console.log('onEndReached', JSON.stringify(e))
          //   __DEV__ && console.log(e)
          //   if (e.distanceFromEnd > 0) {
          //     setLoadMore(true)
          //   }
          // }}
          onEndReachedThreshold={0.5}
          // ListFooterComponent={renderFooter}
        />
      </View> : <View style={{ backgroundColor: '#fff', flex: 1 }}><EmptyData title={t('Chưa có đánh giá')} message={t('Đánh giá của khách hàng sẽ hiện ở đây nếu có')}/></View>}
    </View>
  )
})
