import { Dimensions, StyleSheet } from 'react-native'
import { responsiveWidth } from 'react-native-responsive-dimensions'
import { color, typography } from '@app/theme'
const { height } = Dimensions.get('window')

const styles = StyleSheet.create({
  boxContainerStar: {
    flexDirection: 'column',
  },
  boxStar: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginLeft: 8,
    marginTop: -2
  },
  boxviewImage: {
    width: 50
  },
  container: {
    borderBottomColor: '#fff',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  containerComment: {
    backgroundColor: '#fff',
    flex: 1,
    marginBottom: 1,
    paddingHorizontal: 16,
    paddingVertical: 20
    // flexDirection: "column"
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: 15
  },
  customStar: {
    marginRight: 2,
    marginTop: 3
  },
  discount: {
    color: '#fff',
    fontSize: 14,
    paddingHorizontal: 5,
    paddingVertical: 3
  },
  imageBooking: {
    borderRadius: 5,
    height: 44,
    width: 44,
  },
  itemStar: {
    backgroundColor: '#e2e2e2',
    marginHorizontal: 2,
    padding: 4,
    width: '20%'
  },
  productImg: {
    height: 100,
    width: 100
  },
  productName: {
    color: '#333',
    fontSize: 16,
    fontWeight: '400'
  },
  renderItem: {
    borderBottomWidth: 0.5,
    borderColor: '#E2E2E2',
    paddingHorizontal: 8
  },
  renderStar: {
    alignItems: 'center',
    flexDirection: 'row'
    // marginBottom: 8
  },
  safeAreaView: {
    backgroundColor: color.primaryBackground,
    flex: 1,
    marginTop: -4
  },
  startContainer: {
    // marginLeft: 30,
  },
  textContent: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '400',
    letterSpacing: 0.5
  },
  textContentNoti: {
    color: '#333',
    fontSize: 13,
    marginTop: 10,
  },
  textPoinRate: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 5,
    marginTop: 4,
  },
  textTime: {
    color: '#979797',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '400',
    justifyContent: 'flex-end',
    textAlign: 'right'
  },
  textTitle: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    maxWidth: responsiveWidth(60)
  },
  titleLabel: {
    color: '#979797',
    fontSize: 14,
    fontWeight: '500',
    textTransform: 'uppercase'
  },
  topOldPrice: {
    alignSelf: 'center',
    color: '#9d9d9d',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 15,
    textDecorationLine: 'line-through'
  },
  topPrice: {
    color: color.primary,
    fontSize: 20,
    fontWeight: 'bold',
    // paddingHorizontal: 15
  },
  userName: {
    color: '#616161',
    fontFamily: typography.normal,
    fontSize: 13,
  },
  viewComment: {
    backgroundColor: '#fff',
    paddingBottom: 5,
    paddingHorizontal: 16,
    paddingTop: 20
  },
  viewContent: {
    flexDirection: 'row',
    marginBottom: 5
  },
  viewDiscount: {
    backgroundColor: color.primary,
    borderRadius: 4,
    marginLeft: 15
  },
  viewFlatList: {
    flex: 1,
    height: height,
  },
  viewImage: {
    alignItems: 'center',
    flex: 1,
    height: 44,
    width: 44,
  },
  viewProductDetail: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    marginBottom: 5,
    marginTop: 4,
    paddingHorizontal: 16,
    paddingVertical: 20
  },
  viewService: {
    flexDirection: 'column',
    marginTop: 17
  },
  viewStar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginRight: 10
  },
  viewStarComment: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
    marginHorizontal: 10
  },
})
export default styles
