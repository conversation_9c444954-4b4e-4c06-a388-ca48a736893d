import { StyleSheet, TouchableOpacity, View } from 'react-native'
import React, { useState } from 'react'
import { Text } from '@app/components'
import { Calendar } from 'react-native-calendars'
import moment from 'moment/moment'

export const WorkDayHistoryScreen = () => {
  const toDay = new Date()
  const toDayStr = moment(toDay).format('YYYY-MM-DD')
  const [currentDate, setCurrentDate] = useState(toDayStr)
  const markedDates = {
    '2024-06-01': { selected: true, marked: true, selectedColor: '#78af41' },
    '2024-11-02': { selected: true, marked: true, selectedColor: '#78af41' },
    '2024-11-03': { selected: true, marked: true, selectedColor: '#78af41' },
    '2024-11-04': { selected: true, marked: true, selectedColor: '#78af41' },
    '2024-11-05': { selected: true, marked: true, selectedColor: '#78af41' },
    '2024-11-07': { selected: true, marked: true, selectedColor: '#29343d' },
    '2024-11-08': { selected: true, marked: true, selectedColor: '#29343d' },
    '2024-11-09': { selected: true, marked: true, selectedColor: '#29343d' },
    '2024-11-10': { selected: true, marked: true, selectedColor: '#ff5722' }
  }
  const goToPreviousMonth = () => {
    const date = new Date(currentDate)
    date.setMonth(date.getMonth() - 1)
    setCurrentDate(date.toISOString().split('T')[0]) // Cập nhật `currentDate` dạng 'YYYY-MM-DD'
  }

  // Hàm chuyển sang tháng tiếp theo
  const goToNextMonth = () => {
    const date = new Date(currentDate)
    date.setMonth(date.getMonth() + 1)
    setCurrentDate(date.toISOString().split('T')[0]) // Cập nhật `currentDate` dạng 'YYYY-MM-DD'
  }

  const header = () => {
    return <View style={styles.header}>
      <Text style={styles.monthYearText}>
        {new Date(currentDate).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
      </Text>
      <View style={styles.buttonContainer}>
        <TouchableOpacity onPress={goToPreviousMonth} style={styles.navButton}>
          <Text style={styles.navButtonText}>{'<'}</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={goToNextMonth} style={styles.navButton}>
          <Text style={styles.navButtonText}>{'>'}</Text>
        </TouchableOpacity>
      </View>
    </View>
  }
  return (
    <View style={styles.container}>
      <Calendar
        style={styles.calendar}
        renderHeader={header}
        current={currentDate}
        monthFormat={'MMMM yyyy'}
        onDayPress={(day) => {
          console.log(day)
        }}
        hideArrows={true} // Ẩn nút điều hướng mặc định
        markingType={'multi-dot'}
        markedDates={markedDates}
        // markedDates={markedDates}
        theme={{
          selectedDayBackgroundColor: '#78af41',
          selectedDayTextColor: '#ffffff',
          todayTextColor: '#00adf5',
          arrowColor: 'black',
          monthTextColor: 'black',
          textMonthFontWeight: 'bold',
          textDayFontSize: 16,
          textMonthFontSize: 20,
        }}
      />
      <View style={styles.legend}>
        <View style={[styles.legendItem, { backgroundColor: '#78af41' }]}>
          <Text style={styles.legendText}>Ngày công</Text>
        </View>
        <View style={[styles.legendItem, { backgroundColor: '#29343d' }]}>
          <Text style={styles.legendText}>Ngày nghỉ</Text>
        </View>
        <View style={[styles.legendItem, { backgroundColor: '#ff5722' }]}>
          <Text style={styles.legendText}>Đi muộn</Text>
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  legendItem: {
    borderRadius: 10,
    padding: 10,
    paddingHorizontal: 15,
  },
  legendText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  header: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 10,
  },
  monthYearText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'black'
  },
  navButton: {
    padding: 10,
  },
  navButtonText: {
    fontSize: 18,
    color: 'black',
  },
  buttonContainer: {
    flexDirection: 'row',
  },
  calendar: {
    padding: 5,
    borderColor: 'grey',
    margin: 20,
    elevation: 1.5,
    borderRadius: 15
  }
})
