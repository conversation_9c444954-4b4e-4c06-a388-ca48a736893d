import {FlatList, StyleSheet, View} from "react-native";
import React from 'react';
import {arrWorkDat, TypeWorkDay} from "@app/screens/workday-history/slice";
import {Text} from "@app/components";

export const WorkDayTableScreen = () => {
    const colorItem = (type) => {
        if(type === TypeWorkDay.WORK) {
            return '#78af41'
        } else if(type === TypeWorkDay.LEAVE){
            return '#29343d'
        }
        return '#ff5722'
    }
    const ItemParentList = ({item}) => (
        <View style={styles.item}>
            <Text style={styles.title}>{'Tháng ' + item.month}</Text>
            <FlatList
                data={item?.data}
                horizontal={true}
                keyExtractor={(item, index) => item.type + index}
                renderItem={ItemChildList}
                ItemSeparatorComponent={()=> {return <View style={{width:15}}></View>}}
            />
        </View>
    )
    const ItemChildList = ({item}) => (
        <View style={[styles.item, {backgroundColor: colorItem(item.type)}]}>
            <Text style={styles.type}>{item.type}</Text>
            <Text style={styles.date}>{item.date}</Text>
        </View>
    )
    return (
        <View>
            <FlatList
                data={arrWorkDat}
                keyExtractor={(item, index) => item.month + index}
                renderItem={ItemParentList}
            />
        </View>
    );
};
const styles = StyleSheet.create({
    title: {
        fontSize: 16,
        color: '#01061f',
        marginBottom: 16
    },
    type:{
        fontSize: 14
    },
    date: {
        fontSize: 24,
        fontWeight:'600'
    },
    item: {
        borderRadius: 10,
        paddingLeft: 17,
        paddingBottom:17,
        paddingTop:10,
        paddingRight:20,
    },
    header: {

    },
    itemContainer: {

    },

});
