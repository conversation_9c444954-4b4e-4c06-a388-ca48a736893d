export const enum TypeWorkDay {
    WORK = '<PERSON><PERSON><PERSON> công',
    LEAVE = '<PERSON><PERSON><PERSON> nghỉ',
    LATE = 'Đ<PERSON> muộn'
}
export const arrWorkDat = [
    {
        month: '01/2024',
        data: [
            {
                type: TypeWorkDay.WORK,
                date: "22/04"
            },
            {
                type: TypeWorkDay.LEAVE,
                date: "22/04"
            },
            {
                type: TypeWorkDay.LATE,
                date: "22/04"
            },
        ]
    },
    {
        month: '02/2024',
        data: [
            {
                type: TypeWorkDay.WORK,
                date: "22/04"
            },
            {
                type: TypeWorkDay.LEAVE,
                date: "22/04"
            },
            {
                type: TypeWorkDay.LATE,
                date: "22/04"
            },
        ]
    },
    {
        month: '03/2024',
        data: [
            {
                type: TypeWorkDay.WORK,
                date: "22/04"
            },
            {
                type: TypeWorkDay.LEAVE,
                date: "22/04"
            },
            {
                type: TypeWorkDay.LATE,
                date: "22/04"
            },
        ]
    },
    {
        month: '04/2024',
        data: [
            {
                type: TypeWorkDay.WORK,
                date: "22/04"
            },
            {
                type: TypeWorkDay.LEAVE,
                date: "22/04"
            },
            {
                type: TypeWorkDay.LATE,
                date: "22/04"
            },
        ]
    },
]