import React from 'react'
import { observer } from 'mobx-react-lite'
import { Dimensions, StyleSheet, Text, View } from 'react-native'
import { TabBar, TabView } from 'react-native-tab-view'
import { useNavigation } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import { typography } from '@app/theme'
import { ButtonBack } from '@app/components'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import { SafeAreaView } from 'react-native-safe-area-context'
import { WorkDayTableScreen } from '@app/screens/workday-history/work-day-table.screen'
import { WorkDayHistoryScreen } from '@app/screens/workday-history/work-day-history.screen'
import LinearGradient from 'react-native-linear-gradient'

const initialLayout = { width: Dimensions.get('window').width }

export const WorkDayScreen = observer((props: any) => {
  const { t } : any = useTranslation()
  const { goBack } = useNavigation()
  const [index, setIndex] = React.useState(0)

  const [routes, setRoutes] = React.useState([
    { key: 'buy', title: t('Bảng ngày công') }, // TODO: chưa rõ chưa fix được text ko đổi
    { key: 'myinsurance', title: t('Lịch sử chấm công') },
  ])

  // const refreshData = async () => {
  //   setIsFetched(true)
  //   await profileStore.getBookingHistory(1, false)
  //   setLoadMore(false)
  //   setRefreshing(false)
  //   setIsFetched(false)
  // }

  const onGoBack = () => {
    goBack()
  }

  const renderScene = ({ route }) => {
    switch (route.key) {
      case 'buy':
        return <WorkDayHistoryScreen/>

      case 'myinsurance':
        return <WorkDayTableScreen />

      default:
        return null
    }
  }

  const renderTabBar = (props) => (
    <TabBar
      {...props}
      indicatorStyle={{ backgroundColor: '#78af41' }}
      style={styles.tabBar}
      renderLabel={({ route, focused, color }) => (
        <Text style={[styles.tabBarText, { fontSize: 16 }, { color: focused ? '#78af41' : 'black' }]}>
          {route.title}
        </Text>
      )}
    />
  )

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <Header
        // statusBarProps={{ barStyle: 'light-content' }}
        // barStyle="light-content" // or directly
        leftComponent={<ButtonBack style={{ color: 'black' }} onPress={onGoBack}/>}
        centerComponent={{ text: t(t('Lịch sử chấm công')), style: { color: 'black', fontWeight: 'bold', fontSize: 16 } }}
        // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
        //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
        // </TouchableOpacity> : null}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      <View style={styles.background}>
        {/* <View style={styles.renderTitle}> */}
        {/*  <ButtonBack onPress={goBack}/> */}
        {/*  <Text style={styles.TitleText}>{title}</Text> */}
        {/* </View> */}
        {/* <Header position={offset} title={t('BOOKING_HISTORY')} onPressButtonLeft={goBack}/> */}

        <TabView
          navigationState={{ index, routes }}
          renderScene={renderScene}
          onIndexChange={setIndex}
          initialLayout={initialLayout}
          renderTabBar={renderTabBar}
          // initialPage={defaultTabIndex || 0}
          style={{ backgroundColor: '#f7f9fc' }}
        />

      </View>
    </SafeAreaView>
  )
})

const styles = StyleSheet.create({
  background: {
    backgroundColor: '#fff',
    flex: 1
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },

  tabBar: {
    // Remove border top on both android & ios
    backgroundColor: '#fff',
    borderTopColor: 'transparent',
    borderTopWidth: 0,
    elevation: 0,
    shadowColor: '#5bc4ff',
    shadowOffset: {
      height: 0,
      width: 0,
    },
    shadowOpacity: 0,
    shadowRadius: 0,
  },
  tabBarText: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
  },
})
