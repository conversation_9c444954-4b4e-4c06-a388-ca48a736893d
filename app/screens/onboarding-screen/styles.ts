import { StyleSheet } from 'react-native'
import { color, typography } from '../../theme'

const styles = StyleSheet.create({
  activeDot: {
    backgroundColor: '#ff6f8a',
    borderRadius: 4,
    height: 8,
    marginBottom: 3,
    marginLeft: 3,
    marginRight: 3,
    marginTop: 3,
    width: 8
  },
  buttonContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 50,
    marginLeft: 30,
    marginRight: 30,
    marginTop: 50
  },
  buttonLogin: {
    alignItems: 'center',
    backgroundColor: color.primary,
    borderRadius: 22,
    flex: 1,
    flexDirection: 'row',
    height: 44,
    justifyContent: 'center',
    marginBottom: 74,
    width: 150
  },
  buttonLoginText: {
    color: '#ffffff',
    fontSize: 17,
    fontWeight: 'bold',
    textAlign: 'center'
  },
  buttonRegister: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 22,
    flexDirection: 'row',
    flex: 1,
    height: 44,
    justifyContent: 'center',
    marginLeft: 15,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 1,
    shadowRadius: 10,
    width: 150,
    elevation: 2
  },
  buttonRegisterText: {
    color: '#222b45',
    fontSize: 17,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  container: {
    flexDirection: 'column',
    flex: 1,
    alignItems: 'center',
    backgroundColor: '#fff'
  },
  containerSwiper: {
    backgroundColor: '#ffffff',
    borderBottomLeftRadius: 50,
    borderBottomRightRadius: 50,
    elevation: 2,
    height: '80%',
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 1,
    shadowRadius: 10
  },
  dot: {
    backgroundColor: 'rgba(255, 207, 216, 0.5)',
    borderRadius: 4,
    height: 5,
    marginBottom: 3,
    marginLeft: 3,
    marginRight: 3,
    marginTop: 3,
    width: 5,
  },

  image: {
    alignItems: 'center',
    height: 80,
  },

  slide: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
  },
  slideContent: {
    color: color.primary,
    fontFamily: typography.normal,
    fontSize: 15,
    letterSpacing: 0,
    marginTop: 20,
    textAlign: 'center'
  },
  slideTitle: {
    color: '#222b45',
    fontFamily: typography.normal,
    fontSize: 24,
    fontWeight: 'bold',
    letterSpacing: 0,
    textAlign: 'center',
  },
  text: {
    color: '#fff',
    fontSize: 30,
    fontWeight: 'bold'
  },
  viewImage: {
    alignItems: 'center',
    marginTop: '20%',
  },
  viewSlideContent: {
    // marginBottom: 91,
    marginTop: 13,
    width: '50%'
  },
  viewSlideTitle: {
    alignItems: 'center',
    marginTop: 43,
    width: '90%',
  },
  wrapper: {}
})
export default styles
