import React, { useEffect } from 'react'
import { Image, Text, View, TouchableOpacity, PermissionsAndroid, Platform } from 'react-native'
import Swiper from 'react-native-swiper'
import styles from './styles'
import { logoMypet } from '../../../app/assets/images'
import { observer } from 'mobx-react-lite'
import { useNavigation } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import * as Animatable from 'react-native-animatable'
import { useAuth } from '@app/use-hooks/use-auth'
import { save } from '@app/utils/storage'
import Geolocation from '@react-native-community/geolocation'

export const Onboarding = observer((props) => {
  const { t } : any = useTranslation()
  const navigation = useNavigation()
  const { setBoarded } = useAuth() // should be signUp

  useEffect(() => {
    // getCurrentLocation()
    return () => {

    }
  }, [])

  const goLogin = async () => {
    setBoarded()
    // await saveString('isOnBoarding', 'true')
    // navigation.navigate(SCREENS.primaryStack)
    // navigation.navigate(SCREENS.authStack, { screen: SCREENS.login })
  }
  const goRegister = async () => {
    setBoarded()
    // await saveString('isOnBoarding', 'true')
    // navigation.navigate(SCREENS.primaryStack)
    // navigation.navigate(SCREENS.authStack, { screen: SCREENS.register })
  }

  const getCurrentLocation = () => {
    // Checking for the permission just after component loaded
    if (Platform.OS === 'ios') {
      callLocation()
    } else {
      const requestLocationPermission = async () => {
        try {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION, {
              buttonPositive: '',
              title: 'Ứng dụng yêu cầu truy cập vị trí',
              message: 'This App needs to Access your location'
            },
          )
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            // To Check, If Permission is granted
            callLocation()
          }
        } catch (err) {
          console.warn(err)
        }
      }
      requestLocationPermission().then(r => {})
    }
  }

  const callLocation = () => {
    // alert("callLocation Called");
    Geolocation.getCurrentPosition(
      // Will give you the current location
      async (pos) => {
        __DEV__ && console.log(pos)
        // setPosition({
        //   latitude: pos.coords.latitude,
        //   longitude: pos.coords.longitude
        // })
        await save('location', {
          latitude: pos.coords.latitude,
          longitude: pos.coords.longitude
        })
      },
      (error) => {
        __DEV__ && console.log(error.message)
      },
      { enableHighAccuracy: true, timeout: 20000, maximumAge: 1000 }
    )
  }

  return (
    <View style={styles.container} testID="WelcomeScreen">
      <View style={styles.containerSwiper}>
        <View>
          <Swiper style={styles.wrapper} height={205} dot={ <View style={styles.dot} /> }
            activeDot={ <View
              style={styles.activeDot}
            />
            } loop >
            <View
              style={styles.slide}
            >
              <Animatable.View
                style={styles.viewImage}
                animation="zoomIn"
              >
                <Image
                  style={styles.image}
                  resizeMode="contain"
                  source={logoMypet}
                />
              </Animatable.View>
              <View style={styles.viewSlideTitle}>
                <Animatable.Text animation="fadeInUp"
                  iterationCount={1} style={styles.slideTitle}>Chào mừng bạn đến với
                  maxQ</Animatable.Text>
              </View>
              <View style={styles.viewSlideContent}>
                <Animatable.Text animation="fadeInUp"
                  iterationCount={1} style={styles.slideContent}>Một ứng dụng với nhiều tính năng tuyệt vời như đặt lịch sữa chữa bảo dưỡng với Gara, dịch vụ chăm sóc xe, tìm kiếm các địa điểm hữu ích và mua sắm phụ kiện, phụ tùng</Animatable.Text >
              </View>

            </View>
            <View
              style={styles.slide}
            >
              <View style={styles.viewImage}>
                <Image
                  style={styles.image}
                  resizeMode="contain"
                  source={logoMypet}
                />
              </View>

              <View style={styles.viewSlideTitle}>
                <Animatable.Text numberOfLines={2} style={styles.slideTitle}
                  animation="fadeInUp"
                  iterationCount={1}
                >maxQ - Ứng dụng chăm sóc xe toàn diện nhất hiện nay</Animatable.Text>
              </View>
              <View style={styles.viewSlideContent}>
                <Animatable.Text style={styles.slideContent}
                  animation="slideInUp"
                  iterationCount={1}
                >Hướng tới 5 triệu thành viên, maxQ là ứng dụng được yêu thích và sử dụng hàng ngày để chăm sóc xế cưng</Animatable.Text>
              </View>
            </View>
            <View
              style={styles.slide}
            >
              <View style={styles.viewImage}>
                <Image
                  style={styles.image}
                  resizeMode="contain"
                  source={logoMypet}
                />
              </View>
              <View style={styles.viewSlideTitle}>
                <Text numberOfLines={2} style={styles.slideTitle}>Đặt lịch sửa chữa, bảo dưỡng, chăm sóc xe...</Text>
              </View>
              <View style={styles.viewSlideContent}>
                <Text style={styles.slideContent}>Tìm gara, xưởng dịch vụ, và các địa điểm yêu thích gần bạn nhất với công nghệ GEO</Text>
              </View>
            </View>
          </Swiper>
        </View>
      </View>
      <View style={styles.buttonContainer}>
        <TouchableOpacity testID="btnstart" onPress={goLogin} style={styles.buttonLogin}>
          <Text style={styles.buttonLoginText}>{t('ONBOARDING_START')}</Text>
        </TouchableOpacity>
        {/* <TouchableOpacity onPress={goLogin} style={styles.buttonLogin}> */}
        {/*  <Text style={styles.buttonLoginText}>{t('DANGNHAP_login')}</Text> */}
        {/* </TouchableOpacity> */}
        {/* <TouchableOpacity onPress={goRegister} style={styles.buttonRegister}> */}
        {/*  <Text style={styles.buttonRegisterText}>{t('DANGKY_register')}</Text> */}
        {/* </TouchableOpacity> */}
      </View>
    </View>
  )
})
