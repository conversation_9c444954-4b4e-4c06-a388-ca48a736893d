import { StyleSheet, Dimensions } from 'react-native'
import { spacing, typography } from '@app/theme'
import { ifIphoneX } from 'react-native-iphone-x-helper'

const tab1ItemSize = (Dimensions.get('window').width - 30) / 5
const { height } = Dimensions.get('window')
const styles = StyleSheet.create({
  admin: {
    marginTop: 10
  },
  boxviewImage: {
    width: 50
  },
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    paddingVertical: 10
  },
  containerComment: {
    flex: 1,
    marginLeft: 8,
    marginRight: 16
    // flexDirection: "column"
  },
  customStar: {
    marginRight: 4,
    marginTop: 3
  },
  dot: {
    backgroundColor: '#6dd400',
    borderRadius: 8,
    bottom: 12,
    elevation: 2,
    height: 8,
    left: 6,
    position: 'absolute',
    shadowColor: 'rgba(0, 0, 0, 0.15)',
    shadowOffset: {
      width: 0,
      height: 0
    },
    shadowOpacity: 1,
    shadowRadius: 5,
    width: 8
  },
  icArrowBack: {
    marginLeft: 3,
    paddingBottom: 20,
    paddingRight: 20
  },
  imageBooking: {
    borderRadius: 5,
    height: 44,
    width: 44,
  },
  imageUser: {
    borderRadius: 22,
    height: 44,
    width: 44,
  },
  itemNotificationContainer: {
    height: 36, // Adjust this height to display exactly two lines
    overflow: 'hidden'
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    paddingHorizontal: 15,
    paddingVertical: 15,
    justifyContent: 'space-between'
  },
  orderId: {
    color: '#00ACC1',
    fontSize: 14
  },
  safeAreaView: {
    backgroundColor: '#F8F8F8',
    flex: 1,
    marginTop: -4
    // height: responsiveHeight(100),
  },
  textContent: {
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 12,
    letterSpacing: 0
  },
  textContentNoti: {
    color: '#616161',
    fontSize: 14,
  },
  textTime: {
    color: '#9D9D9D',
    fontFamily: typography.normal,
    fontSize: 12,
    letterSpacing: 0,
    marginBottom: spacing.tiny
  },
  textTitle: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
  },
  textTitleDetailNoti: {
    // textAlign: 'center',
    // alignItems: 'center',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 14,
    marginBottom: 10
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',

  },
  textTitleTotal: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: spacing.small,
  },
  viewContent: {
    flexDirection: 'row',
    paddingVertical: 5
  },
  viewContentSystem: {
    marginBottom: 5
  },
  viewDetailNoti: {
    marginBottom: 30,
    marginLeft: 32,
    marginRight: 32,
    marginTop: 10,
    ...ifIphoneX({
      paddingBottom: 120,
    }, {
      paddingBottom: 90,
    }),
  },
  viewFlatlist: {
    flex: 1,
    // height: responsiveHeight(100),
    // ...ifIphoneX({
    //   paddingBottom: 89,
    // }, {
    //   paddingBottom: 60,
    // }),
    // width: responsiveWidth(100)
  },
  viewIcon: {
    alignItems: 'flex-end',
    flex: 1,
    justifyContent: 'flex-end',
  },
  viewImage: {
    alignItems: 'center',
    flex: 1,
    height: 44,
    width: 44,
  },
  viewService: {
    flexDirection: 'column',
    marginTop: 17
  },
  viewStar: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between'
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  tabBar: {
    // Remove border top on both android & ios
    backgroundColor: '#fff',
    borderTopColor: 'transparent',
    borderTopWidth: 0,
    elevation: 0,
    shadowColor: '#5bc4ff',
    shadowOffset: {
      height: 0,
    },
    shadowOpacity: 0,
    shadowRadius: 0,
  },
  tabBarText: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
  },
})
export default styles
