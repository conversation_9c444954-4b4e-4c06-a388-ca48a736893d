import React, { useImperativeHandle } from 'react'
import { observer } from 'mobx-react-lite'
import {
  View, Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native'

import { useNavigation } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import moment from 'moment'
import { spacing, typography } from '@app/theme'
import { ifIphoneX } from 'react-native-iphone-x-helper'

/**
 * Describe your component here
 */

interface Props {
  data?: any,
  daXemThongBao?: (item, index)=> void
  checkAndOpenPage?: (value)=> void
  type?: string
}
export const RenderItemLichSuGiaoDichComponent = observer((props: Props, ref) => {
  const { data, daXemThongBao, checkAndOpenPage, type = 'LS' } = props
  const item = data.item
  const index = data.index

  const { navigate } = useNavigation()

  // const [distance, setDistance] = useState(20)
  // const [filter, setFilter] = useState(null)
  const { t } : any = useTranslation()

  useImperativeHandle(ref, () => {
    return {
    //   loadData: loadData
    }
  })

  const optionConvertHtml = {
    wordwrap: 130, // Giới hạn độ dài dòng
    preserveNewlines: true, // Bảo toàn ký tự xuống dòng
    selectors: [
      { selector: 'ol', format: 'inline' },
      { selector: 'li', format: 'inline', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },
      { selector: 'br', format: 'inline', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },
      { selector: 'strong', format: 'inline' }
    ]
  }

  return (
    <View>
      <TouchableOpacity style={{ backgroundColor: item.watched == 0 ? '#fff' : '#ffffff', borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 10, marginHorizontal: 24, marginTop: 16 }} onPress={() => {
        checkAndOpenPage(item)
        if (item.watched === 0) {
          daXemThongBao && daXemThongBao(item, index)
        }
      }}>
        <View style={styles.container}>
          <View style={styles.viewImage}/>

          {type === 'LS' ? <View style={styles.containerComment}>
            <Text style={styles.textTime}>{moment(item.createAt).format('DD/MM/YYYY')}</Text>
            <View>
              <View style={{ flexDirection: 'row', paddingBottom: 5 }}>
                <Text style={styles.orderId}>Giao dịch ứng lương: <Text style={{ color: '#78AF41' }}>+5.000.000đ</Text> Tài khoản nhận: 0101893888 Ngân hàng TMCP Đại chúng Việt Nam tài khoản chuyển 09889393939933 ngân hàng TMCP Đại chúng Việt Nam thực hiện lúc 01/05/2024 15:30:25</Text>
              </View>
            </View>
          </View> : <View style={styles.containerComment}>
            <Text style={styles.textTime}>{moment(item.createAt).format('DD/MM/YYYY')}</Text>
            <View>
              <View style={{ flexDirection: 'row', paddingBottom: 5 }}>
                <Text numberOfLines={1} style={styles.textTitle}>Đơn hàng: </Text>
                <Text style={styles.orderId}>#{item.orderId}</Text>
              </View>
              <View style={{ flexDirection: 'row', }}>
                <Text numberOfLines={1} style={styles.textTitle}>Trạng thái: </Text>
                <Text style={styles.orderId}>{item.status}</Text>
              </View>
            </View>
          </View>}
          {item.watched === 0 ? <View style={styles.dot}/> : null}
        </View>
      </TouchableOpacity>
    </View>
  )
}, { forwardRef: true })

const styles = StyleSheet.create({

  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    paddingVertical: 16
  },
  containerComment: {
    flex: 1,
    marginLeft: 8,
    marginRight: 16
    // flexDirection: "column"
  },

  dot: {
    backgroundColor: '#6dd400',
    borderRadius: 8,
    bottom: 12,
    elevation: 2,
    height: 8,
    left: 6,
    position: 'absolute',
    shadowColor: 'rgba(0, 0, 0, 0.15)',
    shadowOffset: {
      width: 0,
      height: 0
    },
    shadowOpacity: 1,
    shadowRadius: 5,
    width: 8
  },

  imageBooking: {
    borderRadius: 5,
    height: 44,
    width: 44,
  },
  imageUser: {
    borderRadius: 22,
    height: 44,
    width: 44,
  },

  orderId: {
    fontSize: 12,
    color: '#2D384C',
    lineHeight: 18
  },

  textContentNoti: {
    color: '#616161',
    fontSize: 14,
  },
  textTime: {
    color: '#9D9D9D',
    fontFamily: typography.normal,
    fontSize: 12,
    letterSpacing: 0,
    marginBottom: spacing.tiny
  },
  textTitle: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 12,
  },
  textTitleDetailNoti: {
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 14,
    marginBottom: 10
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',

  },
  textTitleTotal: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: spacing.small,
  },
  viewContent: {
    flexDirection: 'row',
    paddingVertical: 5
  },
  viewContentSystem: {
    marginBottom: 5
  },
  viewDetailNoti: {
    marginBottom: 30,
    marginLeft: 15,
    marginRight: 15,
    marginTop: 10,
    ...ifIphoneX({
      paddingBottom: 120,
    }, {
      paddingBottom: 90,
    }),
  },
  viewFlatlist: {
    flex: 1,
  },
  viewIcon: {
    alignItems: 'flex-end',
    flex: 1,
    justifyContent: 'flex-end',
  },
  viewImage: {
    height: 50,
    width: 50,
    marginRight: 4,
    borderRadius: 50,
    backgroundColor: '#F3F3F3'
  },
  viewService: {
    flexDirection: 'column',
    marginTop: 17
  },
  viewStar: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between'
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  tabBar: {
    backgroundColor: '#fff',
    borderTopColor: 'transparent',
    borderTopWidth: 0,
    elevation: 0,
    shadowColor: '#5bc4ff',
    shadowOffset: {
      height: 0,
    },
    shadowOpacity: 0,
    shadowRadius: 0,
  },
  tabBarText: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
  },
})
