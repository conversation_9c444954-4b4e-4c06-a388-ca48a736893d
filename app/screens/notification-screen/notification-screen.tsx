import { SafeAreaView } from 'react-native-safe-area-context'
import React, { useState, useEffect, useRef } from 'react'
import {

  View,
  Text,
  Image, TouchableOpacity, FlatList, Linking, useWindowDimensions, ScrollView,
  Dimensions,
} from 'react-native'
import styles from './styles'
import { useNavigation } from '@react-navigation/native'
import { useStores } from '@app/models'
import moment from 'moment'
import { ButtonBack, LazyImage, TButton } from '@app/components'
import { observer } from 'mobx-react-lite'
import { logoMypet } from '../../assets/images'

import { Modalize } from 'react-native-modalize'
import { color } from '@app/theme'
import { SCREENS } from '@app/navigation'
import { useTranslation } from 'react-i18next'
import { Header } from 'react-native-elements'
import common, { linearGradientProps } from '@app/theme/styles/common'
import LinearGradient from 'react-native-linear-gradient'
import { isAndroid } from '@app/utils/chat/deviceInfo'
// import { getBadgeCount, getNotificationBadgeSetting, setBadgeCount } from 'react-native-notification-badge'
import { APP_STORE_LINK, PLAY_MARKET_LINK } from '@app/constants/links'
import RenderHtml from 'react-native-render-html'
import { NotificationType } from '@app/models/constants/constants'
import validate from 'validate.js'
import { convert } from 'html-to-text'
import { TabBar, TabView } from 'react-native-tab-view'
import { palette } from '@app/theme/palette'
import { DATA_LS } from './components/constant'
import { RenderItemLichSuGiaoDichComponent } from './components/render-item-ls-giao-dich'
import { RenderItemThongBaoComponent } from './components/render-item-thong-bao'
import { RenderItemTatCaComponent } from './components/render-item-tat-ca'

const initialLayout = { width: Dimensions.get('window').width }
export const NotificationScreen: React.FC = observer((props) => {
  console.log('NotificationScreen')
  const { t } : any = useTranslation()
  const { navigate } = useNavigation()
  const modalizeRef = useRef<Modalize>(null)
  const navigation = useNavigation()
  const [data, setData] = useState([])
  const goBack = () => navigation.goBack()
  const [itemData, setItemData] = useState(null)
  const [refreshing, setRefreshing] = useState(false)
  const [loadMore, setLoadMore] = useState(false)
  const [page, setPage] = useState(1)
  const [isFetched, setIsFetched] = useState(true) // event view placeholder
  const [index, setIndex] = useState(0) // event view placeholder
  const { notificationStore } = useStores()
  const { width } = useWindowDimensions()
  const optionConvertHtml = {
    wordwrap: 130, // Giới hạn độ dài dòng
    preserveNewlines: true, // Bảo toàn ký tự xuống dòng
    selectors: [
      { selector: 'ol', format: 'inline' },
      { selector: 'li', format: 'inline', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },
      { selector: 'br', format: 'inline', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },
      { selector: 'strong', format: 'inline' }
    ]
  }

  const [routes] = React.useState([
    { key: 'ALL', title: 'Tất cả' },
    { key: 'HISTORY', title: 'Lịch sử giao dịch' },
    { key: 'NOTIFI', title: 'Thông báo' },
  ])

  useEffect(() => {
    loadData().then(r => {
    })
  }, [])

  useEffect(() => {
    __DEV__ && console.log('useEffect', page)
    loadData().then(r => {
    })
  }, [page])

  // load more
  // useEffect(() => {
  //   __DEV__ && console.log('useEffect handleLoadMore', page)
  //   handleLoadMore()
  // }, [page, loadMore])

  const onOpenModal = () => {
    modalizeRef.current?.open()
  }

  const onCloseModal = () => {
    // onRefresh()
    modalizeRef.current?.close()
  }
  /**
   * call Store
   */
  const loadData = async () => {
    const isLoadMore = page > 1
    if (!isLoadMore) {
      setIsFetched(true)
    }
    await notificationStore.getNotification(page, isLoadMore)
    setData(notificationStore.notifications)
    setLoadMore(false)
    setRefreshing(false)
    setIsFetched(false)
    await updateBadgeCount()
  }

  const refreshData = async () => {
    // __DEV__ && console.log('AAAAAA')
    await notificationStore.getNotification(1, false)
    setLoadMore(false)
    setRefreshing(false)
    setIsFetched(false)
  }

  /**
   * onRefresh
   */
  const onRefresh = () => {
    __DEV__ && console.log('onRefresh ', page)
    setRefreshing(true)
    if (page > 1) {
      setPage(1)
    } else {
      refreshData().then(r => {
      })
    }
  }
  /**
   * onLoadMore Data
   */
  const handleLoadMore = () => {
    // if (!loadMore) return
    // khi scroll dừng lại sẽ gọi vào hàm này
    const totalPage = notificationStore.totalPage
    if (page < totalPage) {
      setPage(page + 1)
    }
    if (page === totalPage) {
      __DEV__ && console.log('No more data...')
      setLoadMore(false)
    }
  }
  /**
   * render Footer UI
   */

  const renderFooter = () => {
    const Spinner = require('react-native-spinkit')
    return loadMore === true ? (
      <View
        style={{
          marginTop: 10,
          alignItems: 'center',
        }}
      >
        <Spinner isVisible={true} size={40} type='ThreeBounce' color={color.primary}/>
      </View>
    ) : <Text style={{ padding: 16, color: color.primary, textAlign: 'center' }}>{t('No_more_data')}</Text>
  }
  const daXemThongBao = async (item, index) => {
    notificationStore.setType(item)
    await notificationStore.watchedNotification(item._id)
    await updateBadgeCount()
  }

  const updateBadgeCount = async () => {
    // if (isIOS) {
    //   // const granted = await requestNotificationPermissions(['badge'])
    //   const badgeCount = await getBadgeCount()
    //   const permission = await getNotificationBadgeSetting()
    //   if (permission === 'enabled' && notificationStore?.notSeen > 0) {
    //     await setBadgeCount(notificationStore.notSeen || 0)
    //   } else {
    //     __DEV__ && console.warn("Badge permission has not yet been granted. I'll ask the user later")
    //   }
    //   __DEV__ && console.log('badgeCount', badgeCount)
    // }
  }

  const checkAndOpenPage = (item) => {
    setItemData(item)
    const typeOrderProduct = item.typeService ? item.typeService : 0
    switch (item.type) {
      case 0:
        navigate(SCREENS.bookingHistoryDetail, { orderId: item.orderId, bookingType: typeOrderProduct }) // product
        break
      case 2:
        navigate(SCREENS.bookingHistoryDetail, { orderId: item.orderId, bookingType: typeOrderProduct }) // product
        break
      case 3:
        navigate(SCREENS.bookingHistoryDetail, { orderId: item.orderId, bookingType: item.typeService })// booking
        break
      case 4:
        navigate(SCREENS.bookingHistoryDetail, { orderId: item.orderId, bookingType: item.typeService })// booking
        break
      case 5:
        navigate(SCREENS.bookingHistoryDetail, { orderId: item.orderId, bookingType: item.typeService }) // booking
        break
      case 6:
        navigate(SCREENS.bookingHistoryDetail, { orderId: item.orderId, bookingType: typeOrderProduct }) // product
        break
      case 11:
        // this.props.request(`/user/api/chi-tiet-lich-hen/${item.bookId}.html`, { index: item.typeService }, false, ({ dataBook }) => {
        //   this.props.open('ChiTietLichHen', { book: { ...dataBook, type: item.typeService }, reloadData: () => { } })
        // }, false, null, false, 'get')
        break
      case 12: // có bản cập nhật
        onOpenModal()
        break
      case -1:
        // custom type from database
        navigate(item?.screen, item?.screenParams) // product
        break
    }
  }

  const renderItem = ({ item, index }) => {
    return (
      <View>
        <TouchableOpacity style={{ backgroundColor: item.watched == 0 ? '#fff' : '#ffffff', borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 10, marginHorizontal: 24, marginTop: 16 }} onPress={() => {
          checkAndOpenPage(item)
          if (item.watched === 0) {
            daXemThongBao(item, index)
          }
        }}>
          <View style={styles.container}>
            <View style={styles.boxviewImage}>
              <View style={styles.viewImage}>
                {item.type === NotificationType.AdminSendNotification ? <Image style={styles.imageUser} resizeMode='cover' source={logoMypet}></Image> : <View>
                  <LazyImage style={styles.imageBooking} resizeMode='cover' source={{ uri: item.imageNotify }}></LazyImage>
                </View>}
              </View>
            </View>
            <View style={styles.containerComment}>
              <View>
                <View>
                  <Text style={styles.textTime}>{moment(item.createAt).format('DD/MM/YYYY')}</Text>
                </View>
                {item.type === NotificationType.AdminSendNotification ? <View>
                  <View style={styles.viewContent}>
                    <Text numberOfLines={1} style={[styles.textTitle, { fontWeight: item.watched == 0 ? 'bold' : 'normal' }]}>{item.title}</Text>
                  </View>
                  <Text numberOfLines={2} style={styles.textContentNoti}>{convert(item.message, optionConvertHtml)}</Text>
                  {/* <RenderHtml */}
                  {/*  contentWidth={width} */}
                  {/*  source={{ html: item.message }} */}
                  {/*  renderersProps={{ */}
                  {/*    img: { */}
                  {/*      enableExperimentalPercentWidth: true */}
                  {/*    } */}
                  {/*  }} */}
                  {/*  ignoredTags={['script']} */}
                  {/*  ignoredStyles={['font-family']} */}
                  {/* /> */}
                </View> : <View>
                  <View style={{ flexDirection: 'row', paddingBottom: 5 }}>
                    <Text numberOfLines={1} style={styles.textTitle}>Đơn hàng: </Text>
                    <Text style={styles.orderId}>#{item.orderId}</Text>
                  </View>
                  <Text numberOfLines={1} style={styles.textContentNoti}>{item.title}</Text>
                  {item.message ? <View style={styles.viewContent}>
                    <Text style={styles.textTitle}>Lý do: </Text>
                    {/* <Text style={styles.textContentNoti}>{item.message}</Text> */}
                    <RenderHtml
                      contentWidth={width}
                      source={{ html: item.message }}
                      renderersProps={{
                        img: {
                          enableExperimentalPercentWidth: true
                        }
                      }}
                      ignoredTags={['script']}
                      ignoredStyles={['font-family']}
                    />
                  </View> : null}
                </View>}
              </View>

            </View>
            {item.watched === 0 ? <View style={styles.dot}/> : null}
          </View>
        </TouchableOpacity>
      </View>
    )
  }
  const renderHeaderModalize = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onCloseModal} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>Chi tiết thông báo</Text>
      <View style={{ width: 20 }} />
    </View>
  )

  const updateNow = () => {
    const link = isAndroid ? PLAY_MARKET_LINK : APP_STORE_LINK
    Linking.canOpenURL(link).then(
      (supported) => {
        supported && Linking.openURL(link)
      },
      (err) => console.log(err)
    )
  }

  function tryParseJSON(jsonString: string) {
    try {
      return JSON.parse(jsonString)
    } catch (e) {
      __DEV__ && console.warn('Invalid JSON:', e)
      return null // hoặc bạn có thể trả về một giá trị mặc định khác nếu cần
    }
  }

  function viewNow(itemNotification: any) {
    const item = tryParseJSON(itemNotification?.extraData)
    if (!item) {
      __DEV__ && console.warn('Could not parse extraData:', itemNotification?.extraData)
      return // Thoát ra nếu item không hợp lệ
    }

    console.log('onPress =>>>>>>>>>>>>>>>>>', item)
    if (item?.screen_open) {
      const params = item?.screen_param || ''
      const trueParams = params !== '#'

      switch (item.screen_open) {
        case 'PROMOTION_DETAIL_SCREEN':
          if (trueParams) {
            navigate(item.screen_open, { id: params })
          }
          break
        case 'SHOPPING_STACK':
          navigate(item.screen_open)
          break
        case 'PRODUCT_DETAILS':
          if (trueParams) {
            navigate(item.screen_open, { id: params })
          }
          break
        case 'BLOG_DETAIL_SCREEN':
          if (trueParams) {
            navigate(item.screen_open, { id: params })
          }
          break
        case 'SERVICE_DETAIL':
          if (trueParams) {
            navigate(item.screen_open, { id: params })
          }
          break
        case 'POPUP_SERVICE_OF_BRAND':
          if (params && item?.screen_param_spaId) {
            navigate(SCREENS.serviceDetail, { id: params, spaId: item?.screen_param_spaId })
          }
          break
        case 'BLOG_SCREEN':
          if (trueParams) {
            navigate(item.screen_open)
          }
          break
        case 'BHTNDSCAR':
          if (trueParams) {
            navigate(SCREENS.baoHiemTNDSB1, { prodId: params })
          }
          break
        case 'BHVCCAR':
          if (trueParams) {
            navigate(SCREENS.baoHiemVCXB1, { prodId: params })
          }
          break
        case 'BHTNDSBIKE':
          if (trueParams) {
            navigate(SCREENS.baoHiemTNDSXeMayB1, { prodId: params })
          }
          break
        case 'SCREEN_NAME':
          navigate(item?.screen_param || '')
          break
        default:
          const isValidURL = validate({ website: params }, { website: { url: true } })
          if (trueParams && isValidURL === undefined) {
            Linking.openURL(params)
          }
          break
      }
    }
  }

  const renderDetail = () => {
    const itemJson = tryParseJSON(itemData?.extraData)
    __DEV__ && console.log('itemData?.extraData', itemJson)
    return (
      <ScrollView style={styles.viewDetailNoti}>
        <Text style={styles.textTitleDetailNoti}>{itemData.title}</Text>
        {/* <Text style={styles.admin}>{itemData.message}</Text> */}
        <RenderHtml
          contentWidth={width}
          source={{ html: itemData.message }}
          renderersProps={{
            img: {
              enableExperimentalPercentWidth: true
            }
          }}
          ignoredTags={['script']}
          ignoredStyles={['font-family']}
        />
        <Text style={styles.admin}></Text>
        { itemData?.type == NotificationType.AdminSendNotification && itemData?.typeNotify == 2 ? <TButton typeRadius={'rounded'} buttonStyle={{ backgroundColor: '#0A5936', marginTop: 20 }} title={t('Cập nhật ngay')} onPress={updateNow} /> : null }
        { itemData?.extraData ? <TButton typeRadius={'rounded'} buttonStyle={{ backgroundColor: '#0A5936', marginTop: 20 }} title={t(itemJson?.buttonText || 'Xem ngay')} onPress={() => viewNow(itemData)} /> : null }
        {/* <Text style={styles.textTime}>{moment(itemData.createAt).locale('vi').fromNow()}</Text> */}
      </ScrollView>

    )
  }

  const renderScene = ({ route }) => {
    switch (route.key) {
      case 'ALL':
        return <FlatList
          showsVerticalScrollIndicator={false}
          data={data}
          initialNumToRender={10}
          refreshing={refreshing}
          onRefresh={onRefresh}
          keyExtractor={(_, index) => index.toString() }
          renderItem={(data) => <RenderItemTatCaComponent data={data} daXemThongBao={() => daXemThongBao(data.item, data.index)} checkAndOpenPage={checkAndOpenPage} />}
          extraData={data}
          onScrollBeginDrag={e => {
            __DEV__ && console.log('onScrollBeginDrag')
            setLoadMore(true)
          }}
          onMomentumScrollEnd={handleLoadMore}
          ListFooterComponent={renderFooter}
        />
      case 'HISTORY':
        return <FlatList
          showsVerticalScrollIndicator={false}
          data={DATA_LS}
          initialNumToRender={10}
          refreshing={refreshing}
          onRefresh={onRefresh}
          keyExtractor={(_, index) => index.toString() }
          renderItem={(data) => <RenderItemLichSuGiaoDichComponent data={data} daXemThongBao={() => daXemThongBao(data.item, data.index)} checkAndOpenPage={checkAndOpenPage} />}
          extraData={data}
          onScrollBeginDrag={e => {
            __DEV__ && console.log('onScrollBeginDrag')
            setLoadMore(true)
          }}
          onMomentumScrollEnd={handleLoadMore}
          ListFooterComponent={renderFooter}
        />
      case 'NOTIFI':
        return <FlatList
          showsVerticalScrollIndicator={false}
          data={data}
          initialNumToRender={10}
          refreshing={refreshing}
          onRefresh={onRefresh}
          keyExtractor={(_, index) => index.toString() }
          renderItem={(data) => <RenderItemThongBaoComponent data={data} daXemThongBao={() => daXemThongBao(data.item, data.index)} checkAndOpenPage={checkAndOpenPage} />}
          extraData={data}
          onScrollBeginDrag={e => {
            __DEV__ && console.log('onScrollBeginDrag')
            setLoadMore(true)
          }}
          onMomentumScrollEnd={handleLoadMore}
          ListFooterComponent={renderFooter}
        />
      default:
        return null
    }
  }

  const renderTabBar = (props) => (
    <TabBar
      {...props}
      indicatorStyle={{ backgroundColor: color.palette.primaryGreen }}
      style={styles.tabBar}
      renderTabItemButton={renderTabItemButton}
      renderLabel={({ route, focused, color }) => (
        <Text style={[styles.tabBarText, { color: focused ? palette.primaryGreen : '#848484' }]}>
          {route.title}
        </Text>
      )}
    />
  )

  const renderTabItemButton = (tabBtnInfo: any, { route }) => {
    const { isActive } = tabBtnInfo
    // const tabImage = staticData.TabData[index]
    const activeTextStyle = {
      fontSize: 14,
      color: '#2e2e2e',
    }
    const noActiveTextStyle = {
      fontSize: 13,
      color: '#848484',
    }
    const textStyle = isActive ? activeTextStyle : noActiveTextStyle
    return (
      <View>
        <Text style={textStyle}>{route.title}</Text>
      </View>
    )
  }

  return (<SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
    {/* <ButtonBack onPress={goBack} style={styles.icArrowBack}/> */}
    {/* <Text style={styles.textTitleTotal}>Tất cả thông báo</Text> */}
    <Header
      // statusBarProps={{ barStyle: 'light-content' }}
      // barStyle="light-content" // or directly
      leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
      centerComponent={{ text: t(t('Thông báo')), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
      // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
      //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
      // </TouchableOpacity> : null}
      containerStyle={common.headerContainer}
      statusBarProps={{ barStyle: 'light-content' }}
      ViewComponent={LinearGradient}
      linearGradientProps={linearGradientProps}
    />
    <TabView
      navigationState={{ index, routes }}
      renderScene={renderScene}
      onIndexChange={setIndex}
      initialLayout={initialLayout}
      renderTabBar={renderTabBar}
      // style={{ backgroundColor: '#fff' }}
    />
    {/* {isFetched ? <PlaceHolder/> : <View style={styles.viewFlatlist}>{!data || !data.length
      ? <EmptyData title={'Không có thông báo'} message={'Thông báo sẽ hiển thị khi có tin nhắn từ hệ thống'}/>
      : <FlatList
        showsVerticalScrollIndicator={false}
        data={data}
        initialNumToRender={10}
        refreshing={refreshing}
        onRefresh={onRefresh}
        keyExtractor={(item, index) => item._id + index }
        renderItem={renderItem}
        extraData={data}
        onScrollBeginDrag={e => {
          __DEV__ && console.log('onScrollBeginDrag')
          setLoadMore(true)
        }}
        onMomentumScrollEnd={handleLoadMore}
        ListFooterComponent={renderFooter}
      />}</View>
    } */}
    <Modalize
      HeaderComponent={renderHeaderModalize}
      ref={modalizeRef}
      adjustToContentHeight
      keyboardAvoidingBehavior={'padding'}
    >
      {itemData ? renderDetail() : null}
    </Modalize>
  </SafeAreaView>)
})
