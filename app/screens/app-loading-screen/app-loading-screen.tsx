import React, { useEffect } from 'react'
import { observer } from 'mobx-react-lite'
import { StyleSheet, View, Text } from 'react-native'
import { color } from '../../theme'
import { SCREENS } from '@app/navigation'
import { getToken } from '../../services/tokenService'
import { useStores } from '../../models/root-store'
import { useNavigation } from '@react-navigation/native'
import { useAuth } from '@app/context/use-auth'
import messaging from '@react-native-firebase/messaging'
const Spinner = require('react-native-spinkit')

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flex: 1,
    justifyContent: 'center'
  },
  spinner: {
    marginBottom: 50
  },
})

export const AppLoadingScreen = observer(() => {
  // Pull in one of our MST stores
  // const { someStore, anotherStore } = useStores()
  // OR
  // const rootStore = useStores()
  // Pull in navigation via hook
  const navigation = useNavigation()
  const { profileStore } = useStores()
  const { isSignedIn } = useAuth()

  useEffect(() => {
    getUserData().then(r => {})
    return () => {

    }
  }, [])

  // save FCM to user
  const getFcmToken = async () => {
    messaging().requestPermission().then(r => {
      messaging().getToken().then(token => {
        __DEV__ && console.log('TOKEN FCM root-navigator.tsx', token)
        if (token) {
          profileStore.updateTokenNotification(token) // update token after login
        }
      })
    })
  }

  const getUserData = async () => {
    const userToken = await getToken()
    if (userToken) {
      profileStore.reset()
      await profileStore.getProfile()
      getFcmToken()
      navigation.navigate(SCREENS.primaryStack)
    } else {
      navigation.navigate(SCREENS.authStack)
    }
    __DEV__ && console.log('AppLoadingScreen userToken useEffect =>>>>', userToken)
    __DEV__ && console.log('AppLoadingScreen isSignedIn useEffect =>>>>', isSignedIn)
  }

  return (
    <View style={styles.container}>
      <Text style={{ color: '#333' }}>Vui lòng đợi trong giây lát</Text>
      <Spinner style={styles.spinner} isVisible={true} size={50} type='ThreeBounce' color={color.primary}/>
    </View>
  )
})
