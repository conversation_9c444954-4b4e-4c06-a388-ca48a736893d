/* eslint-disable */
import { Dimensions, StyleSheet } from 'react-native'
import { ifIphoneX } from "react-native-iphone-x-helper"
import { color, spacing, typography } from '../../theme'
import { responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import { palette } from '@app/theme/palette'
const { width } = Dimensions.get('window')
const styles = StyleSheet.create({
  petAvatar: {
    width: 145,
    backgroundColor: '#fff',
    height: 145,
    marginRight: 8,
    borderRadius: 10,
    marginBottom: 15,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 5,
    elevation: 4,
    alignItems: 'center',
    justifyContent: 'center'
  },
  btn_MID: {
    marginTop: 15,
    backgroundColor: color.primary,
    borderRadius: 3,
    width: 185
  },
  textM_ID: {
    color: '#fff',
    paddingVertical: 10,
    paddingHorizontal: 20,
    fontWeight: '600',
    fontSize: 14
  },
  petName: {
    fontSize: 24,
    fontWeight: '600',
    color: '#3f3f3f',
    flex: 1
  },
  chooseImageIc: {
    borderRadius: 50
  },
  add_circle: {
    position: 'absolute',
    bottom:10,
    right: 0
    // backgroundColor: 'red',
  },
  textClose: {
    color: color.primary,
    fontSize: 14,
    fontWeight: '500',
    fontFamily: typography.normal,
    width: 35
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
    marginRight: -10
  },
  viewBtnBack: {
    position: 'absolute',
    left: 0,
    top: 14,
  },
  modalHeader: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
    ...ifIphoneX({
      marginTop: 0
    }, {
      marginTop: 5
    }),
  },
  textAdd: {
    fontFamily: typography.normal,
    fontSize: 13,
    color: '#333',
    marginTop: 30
  },
  iconAdd: {
    width: 50,
    height: 50,
    marginVertical: 15
  },
  title: {
    color: color.primary,
    fontSize: 16,
    fontWeight: '600',
    fontFamily: typography.normal
  },
  sectionNote: {
      backgroundColor: '#fff',
      flex: 1
      // borderRadius: 10,
    // shadowColor: 'rgba(85, 85, 85, 0.1)',
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 1,
    // shadowRadius: 5,
    //   elevation: 2,
    //   marginHorizontal: 8,
  },
  sectionNoteHeader: {
    backgroundColor: '#f9f9f9',
    height: 55,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingLeft: 10
  },
  viewProvince: {
    alignItems: 'center',
    backgroundColor: '#f3f3f3',
    // borderColor: '#edf1f7',
    borderRadius: 4,
    // borderStyle: 'solid',
    // borderWidth: 1,
    flexDirection: 'row',
    height: 40,
    justifyContent: 'space-between',
    marginTop: 10,
    paddingLeft: 14
  },
  textProvince: {
    alignSelf: 'center',
    color: '#a0a0a0',
    fontFamily: typography.normal,
    fontSize: 14
  },
  imgIcon: {
    width: 40,
    height: 40
  },
  text: {
    color: '#333',
    fontSize: 12,
    width: 70,
    textAlign: 'center',
    // fontWeight: '500',
    fontFamily: typography.normal,
  },
  touchIcon: {
    alignItems: 'center',
    // marginHorizontal: 15
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  image: {
    width: 90,
    height: 90,
    borderRadius: 100,
  },
  content: {
    fontSize: 12,
    fontWeight: '500',
    color: '#3f3f3f',
    marginTop: 10,
    flex: 1,

  },
  petInfo: {
    backgroundColor: '#fff',
    marginLeft: 15,
    justifyContent: 'space-between',
    flex: 1,
    // height: 145,
    // width: responsiveWidth(55),
  },
  label: {
    fontSize: 13,
    fontWeight: 'bold',
    color: '#333',
  },
  listIcon: {
    // backgroundColor: '#fff',
    height:70,
    // borderRadius: 10,
    marginBottom: 15,
    // shadowColor: 'rgba(85, 85, 85, 0.1)',
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 1,
    // shadowRadius: 5,
    // elevation: 4,
    marginHorizontal: 8
  },
  textInput: {
    alignItems: 'center',
    backgroundColor: '#f3f3f3',
    borderRadius: 4,
    color: '#333',
    fontSize: 14,
    padding: 12
  }
})


export default styles
