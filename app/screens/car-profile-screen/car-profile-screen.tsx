import React, { useContext, useEffect, useRef, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { View, Text, TouchableOpacity, Image, Animated, ScrollView } from 'react-native'
import { useStores } from '@app/models'
import { useNavigation, useRoute } from '@react-navigation/native'
import { ButtonBack, LazyImage } from '@app/components'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import { useTranslation } from 'react-i18next'
import styles from './styles'
import { SCREENS } from '@app/navigation'
import { iconAddRight, icSetting2 } from '@app/assets/images'
import { responsiveHeight } from 'react-native-responsive-dimensions'
import { Modalize } from 'react-native-modalize'
import { AddDiaryScreen, InsuranceOfCarScreen, ChartOfCarScreen, ListPetDiaryScreen, PetAlbumsScreen, UpdatePetAlbumsScreen } from '@app/screens'
import Icon from 'react-native-vector-icons/Ionicons'
import { ModalContext } from '@app/components/modal-success'
import { color, typography } from '@app/theme'
import { DEFAULT_API_CONFIG } from '@app/services/api/api-config'
import { SafeAreaView } from 'react-native-safe-area-context'
import FastImage from 'react-native-fast-image'
import LinearGradient from 'react-native-linear-gradient'

export const CarProfileScreen = observer(function CarProfileScreen() {
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()
  const { carStore, searchStore, homeStore } = useStores()
  const route: any = useRoute()
  const { item } = route?.params
  const [data, setData] = useState(null)
  const [catInfo, setCatInfo] = useState({})
  const modalAddNote = useRef<Modalize>(null)
  const modalAddImage = useRef<Modalize>(null)
  const modalEditProfile = useRef<Modalize>(null)
  const refDiary = useRef(null)
  const refAlbums = useRef(null)
  const refReload = useRef(null)
  const [tabIndex, setTabIndex] = useState('diary')
  const { showError, showSuccess } = useContext(ModalContext)
  const scrollY = new Animated.Value(0)

  // format age
  // const parseYear = parseInt(moment(data?.dob).format('YYYY'))
  // const parseMonth = parseInt(moment(data?.dob).format('M'))
  // const parseDay = parseInt(moment(data?.dob).format('DD'))
  // const parseYearNow = parseInt(moment(new Date().getTime()).format('YYYY'))
  // const parseMonthNow = parseInt(moment(new Date().getTime()).format('M'))
  // const parseDayNow = parseInt(moment(new Date().getTime()).format('DD'))
  // const a = moment([parseYearNow, parseMonthNow, parseDayNow])
  // const b = moment([parseYear, parseMonth, parseDay])
  // const years = a.diff(b, 'year')
  // b.add(years, 'years')
  // const months = a.diff(b, 'months')
  // b.add(months, 'months')
  // const days = a.diff(b, 'days')
  // console.log(years + ' years ' + months + ' months ' + days + ' days')

  const listIcon = [
    {
      type: 'diary',
      image: require('../../assets/images/icons/nhat-ky.png'),
      name: t('Diary')
    },
    // {
    //   type: 'clock',
    //   image: require('../../assets/images/icons/IC-Calendar.png'),
    //   name: 'Nhắc nhở'
    // },
    {
      type: 'chart',
      image: require('../../assets/images/icons/theodoichiphi.png'),
      name: 'Theo dõi chi phí'
    },
    {
      type: 'insurance',
      image: require('../../assets/images/icons/baohiem.png'),
      name: 'Bảo hiểm'
    },
    // {
    //   type: 'albums',
    //   image: require('../../assets/images/icons/gallery.png'),
    //   name: 'Albums'
    // },
    {
      type: 3,
      image: require('../../assets/images/icons/new-spa.png'),
      name: t('SPA')
    },
    {
      type: 1,
      image: require('../../assets/images/icons/gara.png'),
      name: t('Garage')
    },
    {
      type: 2,
      image: require('../../assets/images/icons/store.png'),
      name: 'Parking'
    },
  ]

  const onGoBack = () => {
    goBack()
    setTimeout(() => {
      homeStore.setReloadData(true)
      // carStore.clearFields()
      // carStore.clearListMonth()
    }, 100)
  }

  const onDone = async () => {
    await refDiary?.current.onCreateDiary()
  }

  const onUpdateAlbums = async () => {
    await refAlbums?.current.albums()
  }

  const onCreated = async () => {
    setTimeout(() => {
      carStore.clearFields()
      CloseModalAddNote()
    }
    , 1500,
    )
    setTimeout(() => {
      loadData()
    }
    , 2000,
    )
  }

  const onUpdated = async () => {
    setTimeout(() => {
      carStore.clearFields()
      CloseModalAddImage()
    }
    , 1500,
    )
    setTimeout(() => {
      loadData()
    }
    , 2000,
    )
    await refReload?.current.reloadAlbums()
  }

  useEffect(() => {
    carStore.clearFields()
    carStore.clearListMonth()
    loadData()
    // setData(item) lấy từ api
  }, [])

  useEffect(() => {
    if (carStore.reloadData === true) {
      loadData()
      carStore.setReloadData(false)
    }
  }, [carStore.reloadData])

  const loadData = async () => {
    const rs = await carStore.getCarById(item.id)
    if (rs?.data?.data?.attributes) {
      setData({ id: rs.data.data.id, ...rs.data.data.attributes })
      // setCatInfo(rs.data.categoryInfo[0])
      // console.log('---.>>>>>>.', rs.data.dob)
    }
    carStore.getDiary(item.id) // lấy nhật ký
  }

  /**
   * TODO: không dùng đến
   */
  const onUpdateProfile = async () => {
    const path = carStore.petAvatar
    const picture = !path ? '/user/default-avatar.jpg' : path
    if (picture !== '') {
      carStore.setPicture(picture)
    }
    const body = {
      name: data?.name,
      photo: carStore.petAvatar,
      categoryId: catInfo?._id,
      dob: data.dob,
      description: ''
    }
    const rs = await carStore.updatePetProfile(data?._id, body)
    __DEV__ && console.log('UPDATEPROFILE', rs)
    if (rs && !rs.data.error) {
      showSuccess(t('THANHCONG'), t('ALERT_UPDATE_PROFILE_SUCCESS'))
      carStore.setReloadData(true)
      setTimeout(() => {
        loadData()
      }
      , 3000,
      )
    } else {
      showError(t('FAIL'), t(`${rs.data.message}`))
    }
  }

  const onClickIcon = (item) => {
    if (item.type === 1 || item.type === 2 || item.type === 3) {
      searchStore.setTypeSearch(item.type)
      navigate(SCREENS.search, { filterType: item.type })
    } else {
      setTabIndex(item.type)
    }
  }

  const onUpdate = () => {
    onUpdateProfile()
    setTimeout(() => {
      CloseModalEditProfile()
    }, 3000)
  }

  const onCloseModalEditProfile = () => {
    carStore.clearFields()
    CloseModalEditProfile()
  }
  const CloseModalEditProfile = () => {
    modalEditProfile.current?.close()
  }

  const onOpenModalAddNote = () => {
    modalAddNote.current?.open()
  }

  const CloseModalAddNote = () => {
    modalAddNote.current?.close()
  }

  const onOpenModalAddImage = () => {
    modalAddImage.current?.open()
  }

  const onCloseModalAddImage = () => {
    carStore.clearFields()
    CloseModalAddImage()
  }

  const CloseModalAddImage = () => {
    modalAddImage.current?.close()
  }

  const onCloseModalAddNote = () => {
    carStore.clearFields()
    CloseModalAddNote()
  }

  const icAddRight = <View style={{ backgroundColor: color.primary, padding: 4, borderRadius: 100 }}>
    <Image style={{ width: 20, height: 20 }} source={iconAddRight}></Image>
  </View>

  const renderModalEditAvatar = () => {
    return (
      <View>
        <TouchableOpacity
          // onPress={getPicture}
          style={{ padding: 15, flexDirection: 'row' }}>
          <View style={{ backgroundColor: '#e2e2e2', width: 30, height: 30, borderRadius: 25, justifyContent: 'center', alignItems: 'center', marginRight: 8 }}>
            <Icon style={styles.chooseImageIc} name={'image-outline'} size={20} color={'#333'}/>
          </View>
          <Text style={{ color: '#333', fontFamily: typography.normal, fontSize: 15, fontWeight: 'bold', alignSelf: 'center' }}>{t('Choose_image')}</Text>
        </TouchableOpacity>
        {carStore.petAvatar ? <View style={{ justifyContent: 'center', flexDirection: 'row' }}>
          <LazyImage style={styles.image} resizeMode={'cover'} source={{ uri: `${DEFAULT_API_CONFIG.url2}${item?.attributes?.image?.data?.attributes.url}` }} />
        </View> : null }

      </View>
    )
  }

  const renderHeaderModalAddDiary: any = () => (
    <View style={styles.modalHeader}>
      <ButtonBack onPress={onCloseModalAddNote} style={styles.viewBtnBack} />
      <Text style={styles.textTitleHeader}>{t('Create_diary')}</Text>
      {/* <TouchableOpacity */}
      {/*  onPress={onDone} */}
      {/* > */}
      {/*  <Text style={styles.textClose}>{t('Tạo')}</Text> */}
      {/* </TouchableOpacity> */}
    </View>
  )

  const renderHeaderModalAddImage: any = () => (
    <View style={styles.modalHeader}>
      <ButtonBack onPress={onCloseModalAddImage} style={styles.viewBtnBack} />
      <Text style={styles.textTitleHeader}>{t('Add_picture_to_albums')}</Text>
      <TouchableOpacity
        onPress={onUpdateAlbums}
      >
        <Text style={styles.textClose}>{t('Add')}</Text>
      </TouchableOpacity>
    </View>
  )

  const renderHeaderModalEditProfile: any = () => (
    <View style={styles.modalHeader}>
      <ButtonBack onPress={onCloseModalEditProfile} style={styles.viewBtnBack} />
      <Text style={styles.textTitleHeader}>{t('Change_avatar')}</Text>
      <TouchableOpacity
        onPress={onUpdate}
      >
        {carStore.petAvatar ? <Text style={styles.textClose}>{t('XONG')}</Text> : <View style={{ width: 35 }}></View>}
      </TouchableOpacity>
    </View>
  )

  const renderItemIcon = (item, index) => {
    return (
      <TouchableOpacity key={index} onPress={() => onClickIcon(item)}
        style={styles.touchIcon}>
        <Image style={styles.imgIcon} source={item.image}></Image>
        <Text numberOfLines={2} style={[styles.text, { color: item.type == tabIndex ? color.primary : '#333' }]}>{item.name}</Text>
      </TouchableOpacity>
    )
  }

  const sectionListIc = () => {
    return (<ScrollView horizontal={true} showsHorizontalScrollIndicator={false}>
      {listIcon ? <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
        {listIcon?.map((item, index) => renderItemIcon(item, index))}
      </View> : null}
    </ScrollView>)
  }

  const renderTabGallery = () => {
    return (
      <View style={styles.sectionNoteHeader}>
        <Image style={{ width: 24, height: 24, marginRight: 10 }} source={require('../../assets/images/icons/gallery.png')}></Image>
        <Text style={styles.title}>{t('Gallery')}</Text>
        <TouchableOpacity
          style={{ position: 'absolute', right: 12 }}
          onPress={() => onOpenModalAddImage()}
        >
          {icAddRight}
        </TouchableOpacity>
      </View>
    )
  }

  const renderTabDiary = () => {
    return (
      <>
        <View style={styles.sectionNoteHeader}>
          <Image style={{ width: 24, height: 24, marginRight: 10 }} source={require('../../assets/images/icons/nhat-ky.png')}></Image>
          <Text style={styles.title}>{t('Nhật ký')}</Text>
          <TouchableOpacity
            style={{ position: 'absolute', right: 12 }}
            onPress={() => onOpenModalAddNote()}
          >
            {icAddRight}
          </TouchableOpacity>
        </View>
        {carStore?.listMonth.length > 0 ? <ListPetDiaryScreen listMonth={carStore?.listMonth} />
          : <TouchableOpacity style={{ alignItems: 'center' }}>
            <Text style={styles.textAdd}>{t('Press_plus_in_the_right_corner_to_create_a_diary')}</Text>
          </TouchableOpacity>}
      </>
    )
  }

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <Header
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={onGoBack}/>}
        centerComponent={{ text: t('Thông tin xe'), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        containerStyle={[common.headerContainer]}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      <View style={{ flexDirection: 'row', marginTop: 15, marginVertical: 20, marginHorizontal: 16 }}>
        <TouchableOpacity
          // onPress={OpenModalEditProfile}
          // style={styles.petAvatar}
        >
          {data?.image?.data && <FastImage style={styles.image} resizeMode={'cover'} source={{ uri: `${data?.image?.data?.attributes.url}` }} />}
          {/* <Icon style={styles.add_circle} name={'create'} size={24} color={'#ddd'}/> */}
        </TouchableOpacity>
        <View style={styles.petInfo}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 5 }}>
            <Text numberOfLines={1} style={styles.petName}>{data?.name}</Text>
            <TouchableOpacity onPress={() => navigate(SCREENS.editCar, { item: data })}>
              <Image source={icSetting2} style={{ width: 24, height: 24, marginLeft: 10 }}/>
            </TouchableOpacity>
          </View>
          <View style={{ flexDirection: 'row' }}>
            <Text style={styles.content}>{data?.bsx}</Text>
            <Text style={styles.content}>{data?.year}</Text>
            {/* <Text style={styles.content}>{data?.weight}kg</Text> */}
          </View>
          {/* <Text numberOfLines={1} style={styles.content}>{t('Species')}: {catInfo?.name || t('chua_ro')}</Text> */}
          {/* <Text numberOfLines={1} style={styles.label}>{t('Microchip Id')} :   <Text style={styles.content}></Text></Text> */}
          {/* <TouchableOpacity style={styles.btn_MID}> */}
          {/*  <Text style={styles.textM_ID}>Đăng ký Microchip ID</Text> */}
          {/* </TouchableOpacity> */}
        </View>
        {/* <View style={styles.petInfo}> */}
        {/*  <Text numberOfLines={1} style={styles.label}>{t('PETNAME')} :   <Text style={styles.content}>{data?.name || ''}</Text></Text> */}
        {/*  <Text numberOfLines={1} style={styles.label}>{t('HOSOCANHAN_sex')} :   {data?.gender === 1 ? <Text style={styles.content}>{t('HOSOCANHAN_nam')}</Text> : data?.gender === 2 ? <Text style={styles.content}>{t('HOSOCANHAN_nu')}</Text> : null}</Text> */}
        {/*  <Text numberOfLines={1} style={styles.label}>{t('HOSOCANHAN_birthday')} :  {data?.dob === 0 ? <Text style={styles.content}>{t('chua_ro')}</Text> : <Text style={styles.content}>{years > 0 ? years + ` ${t('Year_old')} ` : null}{months > 0 ? months + ` ${t('Months')} ` : null}{days + ` ${t('Days')}`}</Text>}</Text> */}
        {/*  <Text numberOfLines={1} style={styles.label}>{t('Species')} :   <Text style={styles.content}>{catInfo?.name || t('chua_ro')}</Text></Text> */}
        {/*  <Text numberOfLines={1} style={styles.label}>{t('WEIGHT')} :   <Text style={styles.content}>{data?.weight}</Text></Text> */}
        {/*  <Text numberOfLines={1} style={styles.label}>{t('Microchip Id')} :   <Text style={styles.content}></Text></Text> */}
        {/* </View> */}
      </View>
      <View style={styles.listIcon}>
        {sectionListIc()}
      </View>
      {tabIndex === 'albums' ? <View style={styles.sectionNote}>
        {renderTabGallery()}
        <PetAlbumsScreen ref={refReload} id={data?._id} />
      </View> : null}

      { tabIndex === 'diary' ? <View style={styles.sectionNote}>
        {renderTabDiary()}

      </View> : null }

      {tabIndex === 'chart' && <ChartOfCarScreen/> }
      {tabIndex === 'insurance' && <InsuranceOfCarScreen bks={data?.bsx}/> }

      <Modalize
        HeaderComponent={renderHeaderModalAddDiary}
        ref={modalAddNote}
        modalHeight={responsiveHeight(75)}
        keyboardAvoidingBehavior={'padding'}
      >
        <AddDiaryScreen id={item?.id} ref={refDiary} handleClose={(value) => {
          if (value === true) {
            onCreated()
          }
        }}
        />
      </Modalize>
      <Modalize
        HeaderComponent={renderHeaderModalAddImage}
        ref={modalAddImage}
        // modalHeight={responsiveHeight(75)}
        adjustToContentHeight
        keyboardAvoidingBehavior={'padding'}
      >
        <UpdatePetAlbumsScreen id={data?._id} ref={refAlbums} handleClose={(value) => {
          if (value === true) {
            onUpdated()
          }
        }}
        />
      </Modalize>

      <Modalize
        HeaderComponent={renderHeaderModalEditProfile}
        ref={modalEditProfile}
        modalHeight={responsiveHeight(30)}
        keyboardAvoidingBehavior={'padding'}
      >
        {renderModalEditAvatar()}
      </Modalize>
    </SafeAreaView>
  )
})
