import React, { useContext, useEffect, useRef, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { FlatList, Image, Linking, ScrollView, TextInput, TouchableOpacity, View } from 'react-native'
import { ButtonB<PERSON>, TButton, Text } from '@app/components/index'
import styles from '@app/screens/car-profile-screen/styles'
import { color } from '@app/theme'
import { iconAddRight } from '@app/assets/images'
import { useTranslation } from 'react-i18next'
import { useStores } from '@app/models'
import BottomSheet, { BottomSheetBackdrop, BottomSheetView } from '@gorhom/bottom-sheet'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { styled } from 'nativewind'
import { RNCamera } from 'react-native-camera'
import QRCodeScanner from 'react-native-qrcode-scanner'
import { ModalContext } from '@app/components/modal-success'
import { useNavigation } from '@react-navigation/native'
import { SCREENS } from '@app/navigation'
import FastImage from 'react-native-fast-image'
import Icon from 'react-native-vector-icons/Ionicons'
const Div = styled(View)
const SView = styled(View)
const SText = styled(Text)
// const StyledText = styled(Text)

const lastYears = (back) => {
  const year = new Date().getFullYear()
  return Array.from({ length: back }, (v, i) => year - back + i + 1).map(i => {
    return { label: i, value: i }
  })
}

export const InsuranceOfCarScreen = observer(function InsuranceOfCarScreen(props: any) {
  const { t } : any = useTranslation()
  // const { navigate, goBack } = useNavigation()
  const bottomSheetRef = useRef<BottomSheet>(null)
  const [isShowScanQr, setIsShowScanQr] = useState(false)
  const { insuranceStore, profileStore, carStore } = useStores()
  const [refreshing, setRefreshing] = useState(false)
  const [loadMore, setLoadMore] = useState(false)
  const [page, setPage] = useState(1)
  const [isFetched, setIsFetched] = useState(true)
  const { showError, showSuccess } = useContext(ModalContext)
  const { navigate, goBack } = useNavigation()

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    const isLoadMore = page > 1
    if (!isLoadMore) {
      setIsFetched(true)
    }
    await carStore.getLichSuMuaBHByBKS(profileStore._id, props?.bks)
    // if (index == BookingType.CLINIC) {
    //   __DEV__ && console.log('LoadData getBookingHistory', index)
    //   await profileStore.getBookingHistory(page, isLoadMore)
    //   serviceStore.setTypeBooking(BookingType.CLINIC)
    // }
    // if (index == BookingType.PARKING) {
    //   __DEV__ && console.log('LoadData getBookingClinic', index)
    //   await profileStore.getBookingClinic(page, isLoadMore)
    //   serviceStore.setTypeBooking(BookingType.PARKING)
    // }
    // if (index == BookingType.SHOP) {
    //   __DEV__ && console.log('LoadData get--Booking--ProDuct', index)
    //   await profileStore.getBookingProduct(page, isLoadMore)
    //   serviceStore.setTypeBooking(BookingType.SHOP)
    // }
    // if (index == BookingType.SHOWROOM) {
    //   __DEV__ && console.log('LoadData SHOWROOM', index)
    //   await profileStore.getBookingShowRoom(page, isLoadMore)
    //   serviceStore.setTypeBooking(BookingType.SHOWROOM)
    // }
    // setLoadMore(false)
    setRefreshing(false)
    setIsFetched(false)
  }

  const onRefresh = () => {
    __DEV__ && console.log('onRefresh ', page)
    setRefreshing(true)
    loadData()
  }

  const colorBackground = (value) => {
    if (value == 'PVI') {
      return '#186DB5'
    }
    if (value == 'BIC') {
      return '#08655A'
    }
    if (value == 'BIC') {
      return '#08655A'
    }
    if (value == 'PIJICO') {
      return '#F68824'
    }
    return '#F68824'
  }

  const taiTucBH = async (item) => {
    __DEV__ && console.log('item selected', item)
    if (item?.attributes?.type == 'BẢO HIỂM VẬT CHẤT') {
      navigate(SCREENS.baoHiemVCXB1, { prodId: item?.attributes?.ProductId, isRenew: true, dataRenew: item?.attributes?.dataRenew })
    }
    if (item?.attributes?.type == 'BẢO HIỂM TNDS') {
      navigate(SCREENS.baoHiemTNDSB1, { prodId: item?.attributes?.ProductId, isRenew: true, dataRenew: item?.attributes?.dataRenew })
    }
    if (item?.attributes?.type == 'BẢO HIỂM TNDS XE MÁY') {
      navigate(SCREENS.baoHiemTNDSXeMayB1, { prodId: item?.attributes?.ProductId, isRenew: true, dataRenew: item?.attributes?.dataRenew })
    }
  }

  const viewDetail = async (item) => {
    __DEV__ && console.log('item selected', item)
    if (item?.attributes?.type == 'BẢO HIỂM VẬT CHẤT') {
      navigate(SCREENS.baoHiemVCXB1, { prodId: item?.attributes?.ProductId, isRenew: true, isViewDetail: true, dataRenew: item?.attributes?.dataRenew })
    }
    if (item?.attributes?.type == 'BẢO HIỂM TNDS') {
      navigate(SCREENS.baoHiemTNDSB1, { prodId: item?.attributes?.ProductId, isRenew: true, isViewDetail: true, dataRenew: item?.attributes?.dataRenew })
    }
    if (item?.attributes?.type == 'BẢO HIỂM TNDS XE MÁY') {
      navigate(SCREENS.baoHiemTNDSXeMayB1, { prodId: item?.attributes?.ProductId, isRenew: true, isViewDetail: true, dataRenew: item?.attributes?.dataRenew })
    }
  }

  const deleteProduct = (id) => {
    insuranceStore.deleteInsuranceLink(id)
  }

  // Pull in navigation via hook
  function renderProduct({ item, index }) {
    const brand = item?.attributes?.loai_bao_hiem?.data?.attributes.name
    return (<TouchableOpacity onPress={() => viewDetail(item)} style={{ marginBottom: 15 }}>
      <SView className="rounded-lg" style={{ height: 220, marginHorizontal: 10, backgroundColor: colorBackground(brand) }}>
        <SView className="absolute" style={{ left: 19, top: 80 }}>
          <SText className="text-white mt-1 uppercase">{ item?.attributes?.TenKH }</SText>
          <SText className="text-white mt-1">{item?.attributes?.BienKiemSoat || ''}</SText>
          <SText className="text-white mt-1">{`Bắt đầu ${item?.attributes?.GioDau || ''} ${item?.attributes?.NgayDau}`}</SText>
          {/* <SText className="absolute text-xs text-white" style={{ left: 82, top: 53, }}>Hotline: 1900.000</SText> */}
          <SText className="text-white mt-1">{`Hết hạn ${item?.attributes?.GioCuoi || ''} ${item?.attributes?.NgayCuoi}`}</SText>
          <TouchableOpacity onPress={() => { taiTucBH(item) }}><SText className="font-bold text-white  mt-5">Tái tục bảo hiểm</SText></TouchableOpacity>
        </SView>
        <SView style={{ left: 19, top: 17, }}>
          <FastImage resizeMode={'cover'} style={{ width: 50, height: 50, borderRadius: 3 }} source={{ uri: item?.attributes?.loai_bao_hiem?.data?.attributes?.image?.data?.attributes?.url }}/>
        </SView>
        <SText className="absolute text-xs text-white" style={{ left: 82, top: 15, }}>BẢO HIỂM {brand}</SText>
        <SText className="absolute text-xs text-white" style={{ textTransform: 'uppercase', left: 82, top: 35, fontSize: 14 }}>{item?.attributes?.type}</SText>
        <SText className="absolute text-xs text-white" style={{ left: 82, top: 55, fontSize: 14 }}>Hotline: {item?.attributes?.loai_bao_hiem?.data?.attributes?.hotline}</SText>
        <SView className="absolute items-center justify-center" style={{ right: 15, bottom: 15, width: 30, height: 30, backgroundColor: '#E10714', borderRadius: 50 }}>
          <TouchableOpacity onPress={() => { deleteProduct(item?.id) }}>
            <Icon name={'close'} size={24} color={'#fff'}/>
          </TouchableOpacity>
        </SView>
      </SView>
    </TouchableOpacity>)
  }

  useEffect(() => {
    carStore.getCostType().then((costType) => {
    })
    return () => {

    }
  }, [])

  const icAddRight = <View style={{ backgroundColor: color.primary, padding: 4, borderRadius: 100 }}>
    <Image style={{ width: 20, height: 20 }} source={iconAddRight}></Image>
  </View>

  function openModal() {
      bottomSheetRef?.current.expand()
  }

  const onSuccess = e => {
    Linking.openURL(e.data).catch(err =>
      console.error('An error occured', err)
    )
  }

  return (
    <>
      <View style={styles.sectionNoteHeader}>
        <Image style={{ width: 24, height: 24, marginRight: 10 }} source={require('../../assets/images/icons/baohiem.png')}></Image>
        <Text style={styles.title}>{t('Bảo hiểm xe ')}</Text>
        <TouchableOpacity
          style={{ position: 'absolute', right: 12 }}
          onPress={() => openModal()}
        >
          {icAddRight}
        </TouchableOpacity>
      </View>
      {/* <ScrollView contentContainerStyle={{ */}
      {/*  justifyContent: 'center' */}
      {/* }}> */}

      {/* </ScrollView> */}
      <FlatList
        contentContainerStyle={{ backgroundColor: '#fff', marginTop: 15 }}
        data={carStore.dataInsurancePosts}
        // numColumns={2}
        // initialNumToRender={10}
        refreshing={refreshing}
        onRefresh={onRefresh}
        renderItem={renderProduct}
        keyExtractor={item => item.id + 1}
        extraData={carStore.dataInsurancePosts}
        showsVerticalScrollIndicator={false}
        // onScrollBeginDrag={e => {
        //   __DEV__ && console.log('onScrollBeginDrag')
        //   // show icon loading bottom
        //   onScroll(e)
        //   setLoadMore(true)
        //   // if (page === profileStore.totalPage) {
        //   //   __DEV__ && console.log('No more data...')
        //   //   setLoadMore(false)
        //   // }
        // }}
        // onScrollEndDrag={onScroll}
        // onMomentumScrollEnd={handleLoadMore}
        // ListFooterComponent={renderFooter}
        // ListHeaderComponent={renderHeader}
      />
      <BottomSheet
        index={-1}
        ref={bottomSheetRef}
        snapPoints={[isShowScanQr ? '90%' : '50%']}
        enablePanDownToClose={true}
        // backdropComponent={() => <View style={{ flex: 1, backgroundColor: '#333', width: 200, height: 200 }}/>}
        backdropComponent={(props) => (
          <BottomSheetBackdrop
            {...props}
            appearsOnIndex={0}
            disappearsOnIndex={-1}
          />
        )}
      >
        <BottomSheetView style={{ flex: 1 }}>
          <View style={{ paddingBottom: 10, borderBottomColor: '#f2f2f2', borderBottomWidth: 1 }}>
            <ButtonBack onPress={() => { bottomSheetRef?.current.close() }} style={{ position: 'absolute', left: 16, top: 0, zIndex: 99999 }} />
            <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: 10 }}>
              <Text style={{ color: '#333', fontSize: 14 }}>Thêm thông tin bảo hiểm </Text>
            </View>
          </View>
          <ScrollView
            showsVerticalScrollIndicator={false}
            style={{ paddingTop: 20, marginBottom: useSafeAreaInsets().bottom + 30, marginHorizontal: 16 }}>
            <Text style={{ color: '#333', fontSize: 14 }}>Nhập số chứng nhận BH, biển số xe hoặc quét mã QR Code trên giấy chứng nhận bản in</Text>
            <Div className="flex flex-row justify-between mt-4">
              <Div className="w-2/3">
                <TextInput
                  style={styles.textInput}
                  selectTextOnFocus={false}
                  placeholderTextColor="#9E9E9E"
                  placeholder={t('Nhập số...')}
                  defaultValue={''}
                  onChangeText={(e) => {
                    // onChangeText('title', e)
                  }}
                />
              </Div>
              <Div className="w-1/3 ml-3">
                <TouchableOpacity
                  style={styles.textInput}
                  onPress={() => {
                    setIsShowScanQr(!isShowScanQr)
                  }}
                >
                  <Text style={{ color: '#333', fontSize: 14, fontWeight: 'bold' }}>{ isShowScanQr ? 'Thôi quét' : 'Quét mã QR' }</Text>
                </TouchableOpacity>
              </Div>
            </Div>
            { isShowScanQr && <View>
              <Text style={{ fontSize: 14, textAlign: 'center', color: 'gray', marginVertical: 10 }}>Bạn cần đồng ý quyền truy cập camera để quét mã</Text>
              <QRCodeScanner
                onRead={onSuccess}
                flashMode={RNCamera.Constants.FlashMode.off}
              /></View> }
            <Div className="mt-4">
              <TButton typeRadius={'rounded'} buttonStyle={{ width: '100%' }} title={'Tra cứu'} onPress={() => {}} />
            </Div>
          </ScrollView>
        </BottomSheetView>
      </BottomSheet>
    </>
  )
})
