import React, { useEffect, useRef, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { Dimensions, Image, ScrollView, TouchableOpacity, View } from 'react-native'
import { Text, Loading, BottomSheetPicker } from '@app/components/index'
import styles from '@app/screens/car-profile-screen/styles'
import { useTranslation } from 'react-i18next'
import { useStores } from '@app/models'
import { styled } from 'nativewind'
import { Line<PERSON>hart, PieChart } from 'react-native-chart-kit'
import { color } from '@app/theme'
import { formatMoney } from '@app/utils'
import Icon from 'react-native-vector-icons/Ionicons'
const Div = styled(View)
const StyledText = styled(Text)

const lastYears = (back) => {
  const year = new Date().getFullYear()
  return Array.from({ length: back }, (v, i) => year - back + i + 1).map(i => {
    return { label: i, value: i }
  })
}

const getMonths = () => {
  return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map(i => {
    return { label: i, value: i }
  })
}

export const ChartOfCarScreen = observer(function ChartOfCarScreen() {
  const { t } : any = useTranslation()
  // const { navigate, goBack } = useNavigation()
  const { carStore } = useStores()
  const [yearSelected, setYearSelected] = useState(new Date().getFullYear())
  const [monthSelected, setMonthSelected] = useState(new Date().getMonth() + 1)
  const [years] : any = useState(lastYears(7))
  const [months] : any = useState(getMonths())
  const [isFetched, setIsFetched] = useState(true)
  const selectYearRef = useRef(null)
  const selectMonthRef = useRef(null)

  useEffect(() => {
    loadData()
  }, [monthSelected, yearSelected])

  const loadData = async () => {
    setIsFetched(true)
    await carStore.getCostType()
    await carStore.getLineChartData(carStore.currentCarId, monthSelected, yearSelected)
    setIsFetched(false)
  }

  const renderPieCharItem = (item, index) => {
    return (
      <Div key={index} className="flex-row mb-5 justify-between">
        <Div className="flex-row">
          <Div className="mr-2" style={{ width: 16, height: 16, borderRadius: 8, backgroundColor: item.color }}></Div>
          <StyledText className="text-black" style={{ fontSize: 14 }}>{item.name} - {parseFloat(item.population).toFixed(2)}%</StyledText>
        </Div>
        <StyledText className="text-black text-right" style={{ fontSize: 14 }}>{formatMoney(item.value)} đ</StyledText>
      </Div>)
  }

  const iconRight = <Icon name={'chevron-down-outline'} size={20} color={'#333'} style={{ marginRight: 8 }} />

  return (
    <>
      <View style={styles.sectionNoteHeader}>
        <Image style={{ width: 24, height: 24, marginRight: 10 }} source={require('../../assets/images/icons/nhat-ky.png')}></Image>
        <Text style={styles.title}>{t('Car_diary')}</Text>
      </View>
      <Div className="flex-row justify-between p-2">
        <Div className="w-1/2 pr-1">
          {/* <MyDropDownPicker placeholder={'Chọn năm'} */}
          {/*  items={years} */}
          {/*  value={yearSelected} */}
          {/*  onSelectItem={() => {}} */}
          {/*  setValue={setYearSelected} */}
          {/*  open={open} */}
          {/*  listMode={'MODAL'} */}
          {/*  modalTitle="Chọn năm" */}
          {/*  setOpen={setOpen} /> */}
          <TouchableOpacity onPress={() => selectYearRef?.current.open()} style={styles.viewProvince}>
            <View style={{ flexDirection: 'row' }}>
              {!yearSelected ? <Text style={styles.textProvince}>Chọn năm</Text> : <Text style={[styles.textProvince, { color: '#333' }]}>{yearSelected}</Text>}
            </View>
            {iconRight}
          </TouchableOpacity>
        </Div>
        <Div className="w-1/2 pl-1">
          {/* <MyDropDownPicker placeholder={'Chọn tháng'} */}
          {/*  items={months} */}
          {/*  value={monthSelected} */}
          {/*  onSelectItem={() => {}} */}
          {/*  setValue={setMonthSelected} */}
          {/*  open={openMonth} */}
          {/*  listMode={'MODAL'} */}
          {/*  modalTitle="Chọn tháng" */}
          {/*  setOpen={setOpenMonth} /> */}
          <TouchableOpacity onPress={() => selectMonthRef?.current.open()} style={styles.viewProvince}>
            <View style={{ flexDirection: 'row' }}>
              {!monthSelected ? <Text style={styles.textProvince}>Chọn tháng</Text> : <Text style={[styles.textProvince, { color: '#333' }]}>{monthSelected}</Text>}
            </View>
            {iconRight}
          </TouchableOpacity>
        </Div>
      </Div>
      <ScrollView contentContainerStyle={{
        justifyContent: 'center'
      }}>
        { !isFetched ? <Div><LineChart
          data={carStore.lineChartData}
          width={Dimensions.get('window').width} // from react-native
          height={220}
          yAxisLabel=""
          yAxisSuffix="K"
          yAxisInterval={1} // optional, defaults to 1
          withShadow={false}
          withInnerLines={true}
          withHorizontalLines={true}
          withVerticalLines={false}
          withDots={true}
          segments={4}
          chartConfig={{
            backgroundColor: '#fff',
            backgroundGradientFrom: '#fff',
            backgroundGradientTo: '#fff',
            decimalPlaces: 0, // optional, defaults to 2dp
            color: (opacity = 1) => color.primary,
            labelColor: (opacity = 1) => '#333',
            style: {
              borderRadius: 0
            },
            propsForDots: {
              r: '6',
              strokeWidth: '2',
              stroke: '#fff'
            }
          }}
          bezier
          style={{
            marginVertical: 15,
            // marginHorizontal: 10,
          }}
        /></Div> : <Loading/>}

        { !isFetched ? <>
          <Div className="flex-row justify-center">
            <PieChart
              data={carStore.pieChartData}
              width={250} // from react-native
              height={250}
              chartConfig={{
                backgroundGradientFrom: '#fff',
                backgroundGradientTo: '#fff',
                decimalPlaces: 2, // optional, defaults to 2dp
                color: (opacity = 1) => `rgba(229, 115, 115, ${opacity})`,
                labelColor: (opacity = 1) => '#333'
              }}
              hasLegend={false}
              accessor={'population'}
              backgroundColor={'transparent'}
              paddingLeft={'15'}
              center={[50, 0]}
              absolute
              style={{
              // paddingBottom: 100,
              // marginHorizontal: 10,
              }}
            />
          </Div>
          <Div className="flex-col justify-center">
            <Div className="px-5">
              {carStore.pieChartData.map((data, index) => renderPieCharItem(data, index))}
            </Div>
            <View style={{ paddingHorizontal: 15, borderBottomColor: '#f2f2f2', borderBottomWidth: 1 }}></View>
            <Div className="flex-row mb-5 mt-3 justify-between px-5">
              <StyledText className="text-black ml-6 font-bold text-red-600" style={{ fontSize: 14 }}>Tổng cộng</StyledText>
              <StyledText className="text-black text-right font-bold text-red-600" style={{ fontSize: 14 }}>{formatMoney(carStore.pieChartSumData)} đ</StyledText>
            </Div>
          </Div>
        </> : <Loading/>}

      </ScrollView>
      <BottomSheetPicker
        headerText={'Theo năm'}
        items={years}
        onSelectItem={(i) => {
          setYearSelected(i?.value || '')
        }}
        ref={selectYearRef}
      />
      <BottomSheetPicker
        headerText={'Theo tháng'}
        items={months}
        onSelectItem={(i) => {
          setMonthSelected(i?.value || '')
        }}
        ref={selectMonthRef}
      />
    </>
  )
})
