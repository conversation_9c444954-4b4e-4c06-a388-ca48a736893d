import React, { useEffect } from 'react'
import {
  View, TouchableOpacity, Text, ScrollView,
} from 'react-native'
import { useTranslation } from 'react-i18next'
import styles from './styles'
import Icon from 'react-native-vector-icons/Ionicons'
import { observer } from 'mobx-react-lite'
import { saveString } from '../../utils/storage'
import { color } from '@app/theme'

export const Language = observer((props) => {
  const { t, i18n } = useTranslation()

  useEffect(() => {

  }, [])

  const storeData = async (value) => {
    try {
      await saveString('i18nextLng', value)
    } catch (error) {
      // Error saving data
    }
  }

  return (<View style={styles.background}>
    <ScrollView
      bounces={false}
      style={styles.scrollView}
      showsVerticalScrollIndicator={false}>
      <View>
        <TouchableOpacity style={styles.btnLogin}
          onPress={() => {
            storeData('en')
            i18n.changeLanguage('en')
          }}
        >
          {i18n.language === 'en' ? <Icon name="checkmark-circle" size={25} color={color.primary} />
            : <Icon name="ellipse-outline" size={25} color="#ECEFF1" />}
          <Text style={{
            ...styles.textButton,
            color: '#000',
          }}>
            {t('TIENGANH')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.btnLogin}
          onPress={() => {
            storeData('vi')
            i18n.changeLanguage('vi')
          }}
        >
          {i18n.language === 'vi' ? <Icon name="checkmark-circle" size={25} color={color.primary} />
            : <Icon name="ellipse-outline" size={25} color="#ECEFF1" />}
          <Text style={{
            ...styles.textButton,
            color: '#000',
          }}>
            {t('TIENGVIET')}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  </View>
  )
})
export default Language
