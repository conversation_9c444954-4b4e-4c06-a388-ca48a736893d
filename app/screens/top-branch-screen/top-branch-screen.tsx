import React from 'react'
import { observer } from 'mobx-react-lite'
import { View } from 'react-native-animatable'
import { FlatList, Linking, Platform, Text, TouchableOpacity } from 'react-native'
import { useNavigation, useRoute } from '@react-navigation/native'
import { ButtonBack, LazyImage, NearDistance, StoreListItem } from '@app/components'
import { useTranslation } from 'react-i18next'
import styles from './styles'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import { showLocation } from 'react-native-map-link'
import SimpleToast from 'react-native-simple-toast'
import Icon from 'react-native-vector-icons/Ionicons'
import LinearGradient from 'react-native-linear-gradient'
import { SafeAreaView } from 'react-native-safe-area-context'
import { SCREENS } from '@app/navigation'

export const TopBranchScreen = observer(function TopBranchScreen() {
  const { navigate, goBack } = useNavigation()
  const { t } : any = useTranslation()
  const route: any = useRoute()
  const { data, title } = route?.params

  const onPressImage = (item, navigate) => {
    if (item?.screen) {
      const params = item?.params || ''
      switch (item.screen) {
        case 'PROMOTION_DETAIL_SCREEN':
          navigate(item.screen, { id: params })
          break
        case 'SHOPPING_STACK':
          navigate(item.screen)
          break
        case 'PRODUCT_DETAILS':
          navigate(item.screen, { id: params })
          break
        case 'BLOG_DETAIL_SCREEN':
          navigate(item.screen, { id: params })
          break
        case 'SERVICE_DETAIL':
          navigate(item.screen, { id: params })
          break
        case 'POPUP_SERVICE_OF_BRAND':
          if (params && item?.screen_param_spaId) {
            navigate(SCREENS.serviceDetail, { id: params, spaId: item?.screen_param_spaId })
          }
          break
        case 'BLOG_SCREEN':
          navigate(item.screen)
          break
        default:
          Linking.openURL(params)
          break
      }
    }
  }

  const renderList = ({ item }) => (
    <StoreListItem type={'top-branch'} item={item} onPress={() => onPressImage(item, navigate)}/>
  )

  const RenderTopBranch = ({ item, index }) => {
    const distance = Number.parseFloat(String(item?.calculated / 1000)).toFixed(1)
    return (
      <View key={index} style={styles.item}>
        <TouchableOpacity
          // onPress={() => props.onPress(item.id)}
        >
          <LazyImage
            style={styles.image}
            source={{ uri: item.image || item.thumbail || item.picture }} />
        </TouchableOpacity>
        <View style={{ flex: 1 }}>
          <TouchableOpacity
            // onPress={() => props.onPress(item.id)}
          >
            <Text numberOfLines={1} style={{ color: '#333' }}>{item?.serviceName }</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={{ flexDirection: 'row', flex: 1 }}
            onPress={() => {
              if (item?.storeInfo?.branches?.length > 0 && item.storeInfo.branches[0].lat && item.storeInfo.branches[0].lng) {
                const options: any = {
                  latitude: item.storeInfo.branches[0].lat,
                  longitude: item.storeInfo.branches[0].lng,
                }
                if (Platform.OS === 'ios') {
                  options.title = item.name
                }
                showLocation(options).then((r) => {})
              } else { SimpleToast.show('Không có chỉ dẫn') }
            }}
          >
            {item?.screen === 'SERVICE_DETAIL' ? <View style={{ flexDirection: 'row', marginVertical: 5, flex: 1 }}>
              <Icon style={{}} name={'location-outline'} size={16} color={'red'}/>
              <Text numberOfLines={2} style={styles.textAddress}>{item?.storeInfo?.address}</Text>
            </View> : null}
          </TouchableOpacity>
          <View style={{ flex: 1 }}>
            { distance !== 'NaN' ? <Text>{distance} km</Text> : item?.branches && <NearDistance item={item.branches}/>}
            {item.totalRate ? <View style={{ flexDirection: 'row' }}>
              <View style={{ flex: 1, flexDirection: 'row' }}>
                <Icon style={styles.star} name="star"/>
                <Text style={styles.pointStar}>{item?.totalRate}</Text>
              </View>
            </View> : null}
          </View>
        </View>
        {item?.screen === 'SERVICE_DETAIL' && item?.storeInfo ? <TouchableOpacity style={styles.btnRight} onPress={() => onPressImage(item, navigate)}>
          <Text style={styles.textViewDetail}> {t('BOOKNOW')}</Text>
        </TouchableOpacity> : item?.screen === 'BLOG_SCREEN' ? <TouchableOpacity style={styles.btnRight} onPress={() => onPressImage(item, navigate)}>
          <Text style={styles.textViewDetail}> {t('Xem chi tiết')}</Text>
        </TouchableOpacity> : item?.screen === 'PRODUCT_DETAILS' ? <TouchableOpacity style={styles.btnRight} onPress={() => onPressImage(item, navigate)}>
          <Text style={styles.textViewDetail}> {t('Mua ngay')}</Text>
        </TouchableOpacity> : null}
      </View>
    )
  }

  return (
    <SafeAreaView style={{ backgroundColor: '#fff', flex: 1, marginTop: -4 }}>
      <View style={styles.containerItem}>
        <Header
        // statusBarProps={{ barStyle: 'light-content' }}
        // barStyle="light-content" // or directly
          leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
          centerComponent={{ text: t(t(`${title}`)), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
          // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
          //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
          // </TouchableOpacity> : null}
          containerStyle={common.headerContainer}
          statusBarProps={{ barStyle: 'light-content' }}
          ViewComponent={LinearGradient}
          linearGradientProps={linearGradientProps}
        />

        {/* <View style={styles.viewBTTop}> */}
        {/*  <ButtonBack onPress={goBack} style={styles.icArrowBack}/> */}
        {/*  <Text style={styles.title}>{t(`${title}`)}</Text> */}
        {/*  <View></View> */}
        {/* </View> */}
        <View style={{ paddingTop: 1, flex: 1 }}>
          <FlatList
            data={data}
            showsHorizontalScrollIndicator={false}
            renderItem={RenderTopBranch}
            extraData={data}
            keyExtractor={(item, index) => item + index.toString()}/>
        </View>
      </View>
    </SafeAreaView>
  )
})
