import { StyleSheet } from 'react-native'
import { color, typography } from '@app/theme'
import { palette } from '@app/theme/palette'
const styles = StyleSheet.create({
  btnRight: {
    alignItems: 'center',
    backgroundColor: color.primary,
    borderRadius: 2,
    bottom: 8,
    height: 25,
    justifyContent: 'center',
    position: 'absolute',
    right: 16,
    width: 90
  },
  containerItem: {
    flex: 1,
    // minHeight: 100
  },
  icArrowBack: {
    paddingRight: 10
  },
  image: {
    height: 120,
    marginRight: 10,
    width: 120
  },
  item: {
    flexDirection: 'row',
    flex: 1,
    backgroundColor: '#fff',
    marginBottom: 1,
    paddingVertical: 15,
    paddingHorizontal: 16
  },
  pointStar: {
    color: '#333',
    fontSize: 12,
    marginLeft: 5
  },
  star: {
    color: '#ff8900',
    fontSize: 14
  },
  textAddress: {
    color: '#979797',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 14
  },
  textDes: {
    color: '#979797',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 15,
    marginVertical: 5,
    // paddingRight: 40
  },
  textViewDetail: {
    color: palette.white,
    fontFamily: typography.normal,
    fontSize: 12,
    textAlign: 'center',
  },
  title: {
    fontSize: 16
  },
  viewBTTop: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
    paddingVertical: 10
  }
})
export default styles
