import React, { useEffect, useRef, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { View, Text, Dimensions, TouchableOpacity, FlatList, Image } from 'react-native'
import { TabView, TabBar } from 'react-native-tab-view'
import styles from './styles-booking-history'
import { useNavigation, useRoute } from '@react-navigation/native'
import { SCREENS } from '@app/navigation'
import { useStores } from '@app/models'
import moment from 'moment'
import { useTranslation } from 'react-i18next'
import { color, spacing } from '@app/theme'
import Icon from 'react-native-vector-icons/Ionicons'
import { ButtonBack, EmptyData, PlaceHolder } from '@app/components'
import { numberFormat } from '@app/utils/number'
import _ from 'lodash'
import { useHeaderFixed } from '@app/use-hooks'
import { iconInvoice, iconTruckOutline } from '@app/assets/images'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import LinearGradient from 'react-native-linear-gradient'
import { SafeAreaView } from 'react-native-safe-area-context'
import { BookingType } from '@app/constants/bookingType'
import FastImage from 'react-native-fast-image'

const initialLayout = { width: Dimensions.get('window').width }

const TabNames = {
  SHOP: 'SANPHAM',
  CLINIC: 'GARAGE',
  HOTEL: 'HOTEL',
  SPA: 'SERVICE',
  SHOWROOM: 'SHOWROOM',
}

export const BookingHistoryScreen = observer((props: any) => {
  const { t } : any = useTranslation()
  const { profileStore, serviceStore, bookingStore, productStore } = useStores()
  const { navigate, goBack } = useNavigation()
  const route: any = useRoute()
  const { bookingType } = route?.params || { bookingType: 0 }
  const [index, setIndex] = React.useState(0)
  const [refreshing, setRefreshing] = useState(false)
  const [loadMore, setLoadMore] = useState(false)
  const [page, setPage] = useState(1)
  const [isFetched, setIsFetched] = useState(true)
  const refContainer = useRef(null)
  const { offset, onScroll } = useHeaderFixed()
  const [defaultTabIndex, setDefaultTabIndex] = useState(0)
  const [tabs, setTabs] = useState([])

  const [routes, setRoutes] = React.useState([
    { key: 'product', title: t('SANPHAM') }, // TODO: chưa rõ chưa fix được text ko đổi
    { key: 'spa', title: t('SPA') },
    { key: 'khambenh', title: t('GARAGE') },
    { key: 'hotel', title: t('PARKING') },
    { key: 'show-room', title: t('SHOWROOM') },
  ])

  const tabNames = () => {
    const tabs = []
    if (bookingStore.dataServiceOfSpa.length) {
      tabs.push(t(TabNames.SPA))
    }

    if (bookingStore.dataRoomOfHotel.length) {
      tabs.push(t(TabNames.HOTEL))
    }

    if (bookingStore.dataServiceOfClinic.length) {
      tabs.push(t('Xưởng DV'))
    }
    tabs.push(t(TabNames.SHOP))
    defaultTabView(tabs)
    setTabs(tabs)
  }

  const goLogin = () => {
    navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  const defaultTabView = (tabArray) => {
    // console.warn('type', bookingType)
    switch (bookingType) {
      case BookingType.SHOP:
        __DEV__ && console.log(tabArray.findIndex((x) => x === t(TabNames.SHOP)))
        setDefaultTabIndex(tabArray.findIndex((x) => x === t(TabNames.SHOP)))
        break
      case BookingType.CLINIC:
        setDefaultTabIndex(tabArray.findIndex((x) => x === t('Xưởng DV')))
        break
      case BookingType.PARKING:
        setDefaultTabIndex(tabArray.findIndex((x) => x === t(TabNames.HOTEL)))
        break
      case BookingType.SPA:
        setDefaultTabIndex(tabArray.findIndex((x) => x === t(TabNames.SPA)))
        break
      case BookingType.SHOWROOM:
        setDefaultTabIndex(tabArray.findIndex((x) => x === t(TabNames.SHOWROOM)))
        break
      default:
        setDefaultTabIndex(0)
        break
    }
  }

  useEffect(() => {
    loadData(index).then(r => {})
  }, [index])

  useEffect(() => {
    if (profileStore.reloadStatus) {
      loadData(index).then(r => {})
    }
  }, [profileStore.reloadStatus])

  const loadData = async (index) => {
    __DEV__ && console.log('LoadData')
    const isLoadMore = page > 1
    if (!isLoadMore) {
      setIsFetched(true)
    }
    if (index == BookingType.SPA) {
      __DEV__ && console.log('LoadData getBookingHote', index)
      await profileStore.getBookingHotel(page, isLoadMore)
      serviceStore.setTypeBooking(BookingType.SPA)
    }
    if (index == BookingType.CLINIC) {
      __DEV__ && console.log('LoadData getBookingHistory', index)
      await profileStore.getBookingHistory(page, isLoadMore)
      serviceStore.setTypeBooking(BookingType.CLINIC)
    }
    if (index == BookingType.PARKING) {
      __DEV__ && console.log('LoadData getBookingClinic', index)
      await profileStore.getBookingClinic(page, isLoadMore)
      serviceStore.setTypeBooking(BookingType.PARKING)
    }
    if (index == BookingType.SHOP) {
      __DEV__ && console.log('LoadData get--Booking--ProDuct', index)
      await profileStore.getBookingProduct(page, isLoadMore)
      serviceStore.setTypeBooking(BookingType.SHOP)
    }
    if (index == BookingType.SHOWROOM) {
      __DEV__ && console.log('LoadData SHOWROOM', index)
      await profileStore.getBookingShowRoom(page, isLoadMore)
      serviceStore.setTypeBooking(BookingType.SHOWROOM)
    }
    setLoadMore(false)
    setRefreshing(false)
    setIsFetched(false)
    profileStore.setReLoadStatus(false)
    tabNames() // calculate tab name
  }
  const handleLoadMore = () => {
    // if (!loadMore) return
    // khi scroll dừng lại sẽ gọi vào hàm này
    const totalPage = profileStore.totalPage
    if (page < totalPage) {
      setPage(page + 1)
    }
    if (page === totalPage) {
      __DEV__ && console.log('No more data...')
      // setLoadMore(false)
    }
  }

  // const refreshData = async () => {
  //   setIsFetched(true)
  //   await profileStore.getBookingHistory(1, false)
  //   setLoadMore(false)
  //   setRefreshing(false)
  //   setIsFetched(false)
  // }

  const onRefresh = () => {
    __DEV__ && console.log('onRefresh ', page)
    setRefreshing(true)
    loadData(index)
  }

  const onGoBack = () => {
    goBack()
    setTimeout(() => { productStore.setReloadData(true) }, 200)
  }

  const renderFooter = () => {
    const Spinner = require('react-native-spinkit')
    return loadMore === true && page !== profileStore.totalPage ? (
      <View
        style={{
          marginTop: 10,
          alignItems: 'center'
        }}
      >
        <Spinner isVisible={true} size={40} type='ThreeBounce' color={color.primary}/>
      </View>
    ) : null
  }

  const Tab1 = () => (
    // <View style={styles.containerItem}>
    //   <EmptyData title={t('NO_HISTORY')} message={t('DONT_HAVE_MEDICAL_HISTORY')}/>
    // </View>
    <View style={styles.flatListContainer}>
      {isFetched ? <PlaceHolder/> : <View style={styles.containerItem}>
        {!profileStore.bookingHotel || !profileStore.bookingHotel.length
          ? <EmptyData title={t('NO_HISTORY')} message={t('DONT_HAVE_BOOKING_SPA_HISTORY')}/>
          : <FlatList
            ref={refContainer}
            data={profileStore.bookingHotel}
            initialNumToRender={10}
            refreshing={refreshing}
            onRefresh={onRefresh}
            renderItem={renderBookingHotel}
            keyExtractor={item => item._id}
            extraData={profileStore.bookingHotel}
            onScrollBeginDrag={e => {
              __DEV__ && console.log('onScrollBeginDrag')
              // show icon loading bottom
              onScroll(e)
              setLoadMore(true)
              // if (page === profileStore.totalPage) {
              //   __DEV__ && console.log('No more data...')
              //   setLoadMore(false)
              // }
            }}
            onScrollEndDrag={onScroll}
            onMomentumScrollEnd={handleLoadMore}
            ListFooterComponent={renderFooter}
          />
        }
      </View>
      }
    </View>
  )
  const Tab2 = () => (
    <View style={styles.flatListContainer}>
      {isFetched ? <PlaceHolder/> : <View style={styles.containerItem}>
        {!profileStore?.bookingHistory?.length
          ? <EmptyData title={t('NO_HISTORY')} message={t('DONT_HAVE_BOOKING_SPA_HISTORY')}/>
          : <FlatList
            ref={refContainer}
            data={profileStore.bookingHistory}
            initialNumToRender={10}
            refreshing={refreshing}
            onRefresh={onRefresh}
            renderItem={renderSpa}
            keyExtractor={item => item._id}
            extraData={profileStore.bookingHistory}
            onScrollBeginDrag={e => {
              __DEV__ && console.log('onScrollBeginDrag')
              // show icon loading bottom
              onScroll(e)
              setLoadMore(true)
              // if (page === profileStore.totalPage) {
              //   __DEV__ && console.log('No more data...')
              //   setLoadMore(false)
              // }
            }}
            onScrollEndDrag={onScroll}
            onMomentumScrollEnd={handleLoadMore}
            ListFooterComponent={renderFooter}
          />
        }
      </View>
      }
    </View>
  )
  const Tab3 = () => (
    <View style={styles.flatListContainer}>
      {isFetched ? <PlaceHolder/> : <View style={styles.containerItem}>
        {!profileStore?.bookingClinic?.length
          ? <EmptyData title={t('NO_HISTORY')} message={t('DONT_HAVE_BOOKING_SPA_HISTORY')}/>
          : <FlatList
            ref={refContainer}
            data={profileStore.bookingClinic}
            initialNumToRender={10}
            refreshing={refreshing}
            onRefresh={onRefresh}
            renderItem={renderBookingClinic}
            keyExtractor={item => item._id}
            extraData={profileStore.bookingClinic}
            onScrollBeginDrag={e => {
              __DEV__ && console.log('onScrollBeginDrag')
              // show icon loading bottom
              onScroll(e)
              setLoadMore(true)
              // if (page === profileStore.totalPage) {
              //   __DEV__ && console.log('No more data...')
              //   setLoadMore(false)
              // }
            }}
            onScrollEndDrag={onScroll}
            onMomentumScrollEnd={handleLoadMore}
            ListFooterComponent={renderFooter}
          />
        }
      </View>
      }
    </View>
  )

  const Tab4 = () => (
    <View style={styles.flatListContainer}>
      {isFetched ? <PlaceHolder/> : <View style={styles.containerItem}>
        {!profileStore?.bookingProduct?.length
          ? <EmptyData title={t('NO_HISTORY')} message={t('DONT_HAVE_BOOKING_SPA_HISTORY')}/>
          : <FlatList
            ref={refContainer}
            data={profileStore.bookingProduct}
            initialNumToRender={10}
            refreshing={refreshing}
            onRefresh={onRefresh}
            renderItem={renderBookingProduct}
            keyExtractor={item => item._id}
            extraData={profileStore.bookingProduct}
            onScrollBeginDrag={e => {
              __DEV__ && console.log('onScrollBeginDrag')
              // show icon loading bottom
              onScroll(e)
              setLoadMore(true)
              // if (page === profileStore.totalPage) {
              //   __DEV__ && console.log('No more data...')
              //   setLoadMore(false)
              // }
            }}
            onScrollEndDrag={onScroll}
            onMomentumScrollEnd={handleLoadMore}
            ListFooterComponent={renderFooter}
          />
        }
      </View>
      }
    </View>
  )

  const TabBookingShowRoom = () => (
    <View style={styles.flatListContainer}>
      {isFetched ? <PlaceHolder/> : <View style={styles.containerItem}>
        {!profileStore?.bookingShowroomData?.length
          ? <EmptyData title={t('NO_HISTORY')} message={t('Không có dữ liệu')}/>
          : <FlatList
            ref={refContainer}
            data={profileStore.bookingShowroomData}
            initialNumToRender={10}
            refreshing={refreshing}
            onRefresh={onRefresh}
            renderItem={renderItemBookingShowRoom}
            keyExtractor={item => item._id}
            extraData={profileStore.bookingShowroomData}
            onScrollBeginDrag={e => {
              __DEV__ && console.log('onScrollBeginDrag')
              // show icon loading bottom
              onScroll(e)
              setLoadMore(true)
              // if (page === profileStore.totalPage) {
              //   __DEV__ && console.log('No more data...')
              //   setLoadMore(false)
              // }
            }}
            onScrollEndDrag={onScroll}
            onMomentumScrollEnd={handleLoadMore}
            ListFooterComponent={renderFooter}
          />
        }
      </View>
      }
    </View>
  )

  // const renderScene = SceneMap({
  //   hotel: Tab1,
  //   spa: Tab2,
  //   khambenh: Tab3,
  //   product: Tab4,
  // })

  const renderScene = ({ route }) => {
    switch (route.key) {
      case 'hotel':
        return Tab1()
      case 'spa':
        return Tab2()
      case 'khambenh':
        return Tab3()
      case 'product':
        return Tab4()
      case 'show-room':
        return TabBookingShowRoom()
      default:
        return null
    }
  }

  const renderTabBar = (props) => (
    <TabBar
      {...props}
      indicatorStyle={{ backgroundColor: '#f3373a' }}
      style={styles.tabBar}
      renderLabel={({ route, focused, color }) => (
        <Text style={[styles.tabBarText, { fontSize: 12 }, { color: focused ? '#2e2e2e' : '#848484' }]}>
          {route.title}
        </Text>
      )}
    />
  )

  const renderPaymentMethod = (item) => {
    return (
      <View>
        <View style={styles.starRate}>
          <Text style={styles.paymentMethod}>{t('PHUONGTHUC_THANHTOAN')}: </Text>
          {item && item.paymentMethod === 1 ? <Text style={styles.detail}>{t('PHUONGTHUCTT_online')} </Text> : item.paymentMethod === 0 ? <Text style={styles.detail}>{t('PHUONGTHUCTT_COD')}</Text> : null}
        </View>
        <View style={styles.starRate}>
          <Text style={styles.paymentMethod}>{t('STATUS')}: </Text>
          {item && item.paymentMethod === 1 && item.isPayOnline === 0 ? <Text style={[styles.detail, { color: color.primary }]}>{t('Unsuccessful')} </Text> : item.paymentMethod === 1 && item.isPayOnline === 1 ? <Text style={[styles.detail, { color: 'green' }]}>{t('THANHCONG')}</Text> : item.paymentMethod === 0 ? <Text style={styles.detail}>{t('PHUONGTHUCTT_taiquay')}</Text> : null}
        </View>
        <View style={styles.starRate}>
          <Text style={styles.paymentMethod}>{t('TIME_ODER')}: </Text>
          <Text style={styles.detail}>{moment(item.createAt).format('HH:mm   DD/MM/YYYY')}</Text>
        </View>
      </View>
    )
  }

  const renderPaymentMethodProduct = (item) => {
    return (
      <View>
        <View style={styles.starRate}>
          <Text style={styles.paymentMethod}>{t('PHUONGTHUC_THANHTOAN')}: </Text>
          {item && item.paymentMethod === 1 ? <Text style={styles.detail}>{t('PHUONGTHUCTT_online')} </Text> : item.paymentMethod === 0 ? <Text style={styles.detail}>{t('PHUONGTHUCTT_COD')}</Text> : null}
        </View>
        <View style={styles.starRate}>
          <Text style={styles.paymentMethod}>{t('STATUS')}: </Text>
          {item && item.paymentMethod === 1 && item.isPayOnline === 0 ? <Text style={[styles.detail, { color: color.primary }]}>{t('Unsuccessful')} </Text> : item.paymentMethod === 1 && item.isPayOnline === 1 ? <Text style={[styles.detail, { color: 'green' }]}>{t('THANHCONG')}</Text> : item.paymentMethod === 0 ? <Text style={styles.detail}>{t('Payment_on_delivery')}</Text> : null}
        </View>
      </View>
    )
  }

  const renderOrderIdLine = (item) => {
    return (
      <View style={styles.viewStatus}>
        <Text numberOfLines={1} style={styles.rdkTopText}>{t('ORDER_CODE')}: #{item.orderId}</Text>
        {item && item.status === 0 ? <View
          style={[styles.statusBorder, { borderColor: 'orange', }]}>
          <Text style={{ color: 'orange', fontSize: 12 }}>{t('CHOXACNHAN')}</Text>
        </View> : item.status === 1 ? <View
          style={[styles.statusBorder, { borderColor: '#7579e7', }]}>
          <Text style={{ color: '#7579e7', fontSize: 12 }}>{t('CHOLAMDV')}</Text>
        </View> : item.status === 2 ? <View style={[styles.statusBorder, { borderColor: '#f3373a' }]}>
          <Text style={{ color: '#f3373a', fontSize: 12 }}>{t('CANCELED')}</Text>
        </View> : item.status === 3 ? <View
          style={[styles.statusBorder, { borderColor: 'green' }]}>
          <Text style={{ color: 'green', fontSize: 12 }}>{t('DONE')}</Text>
        </View> : null }
      </View>
    )
  }

  const renderSpa = ({ item }) => (
    <View>
      <TouchableOpacity
        onPress={() => {
          navigate(SCREENS.bookingHistoryDetail, { orderId: item.orderId, bookingType: BookingType.SPA })
        }}
        style={styles.rdkTop}>
        <Image resizeMode={'contain'} style={styles.imageInvoice} source={iconInvoice} />
        <View style={styles.contentText}>
          {renderOrderIdLine(item)}
          {renderPaymentMethod(item)}
        </View>
      </TouchableOpacity>
    </View>
  )

  const renderItemBookingShowRoom = ({ item }) => {
    return (
      <View>
        <TouchableOpacity
          onPress={() => {
            navigate(SCREENS.bookingHistoryDetail, { orderId: item.orderId, bookingType: BookingType.SHOWROOM })
          }}
          style={styles.rdkTop}>
          <Image resizeMode={'contain'} style={styles.imageInvoice} source={iconInvoice} />
          <View style={styles.contentText}>
            {renderOrderIdLine(item)}
            <View style={styles.starRate}>
              <Text style={styles.paymentMethod}>Số điện thoại: </Text>
              <Text style={styles.detail}>{item.phone}</Text>
            </View>
            <View style={styles.starRate}>
              <Text style={styles.paymentMethod}>Ghi chú: </Text>
              <Text style={styles.detail}>{item.note}</Text>
            </View>
            <View style={styles.starRate}>
              <Text style={styles.paymentMethod}>{t('Ngày tạo đơn')}: </Text>
              <Text style={styles.detail}>{moment(item.createAt).format('HH:mm   DD/MM/YYYY')}</Text>
            </View>
          </View>
        </TouchableOpacity>
      </View>
    )
  }

  const renderBookingHotel = ({ item }) => (
    <View>
      <TouchableOpacity
        onPress={() => {
          // __DEV__ && console.log(item)
          navigate(SCREENS.bookingHistoryDetail, { orderId: item.orderId, bookingType: BookingType.PARKING })
        }}
        style={styles.rdkTop}>
        <Image resizeMode={'contain'} style={styles.imageInvoice} source={iconInvoice} />
        <View style={styles.contentText}>
          {renderOrderIdLine(item)}
          {renderPaymentMethod(item)}
        </View>
      </TouchableOpacity>
    </View>
  )
  const renderBookingClinic = ({ item }) => (
    <View>
      <TouchableOpacity
        onPress={() => {
          // __DEV__ && console.log(item)
          navigate(SCREENS.bookingHistoryDetail, { orderId: item.orderId, bookingType: BookingType.CLINIC })
        }}
        style={styles.rdkTop}>
        <Image resizeMode={'contain'} style={styles.imageInvoice} source={iconInvoice} />
        <View style={styles.contentText}>
          {renderOrderIdLine(item)}
          {renderPaymentMethod(item)}
        </View>
      </TouchableOpacity>
    </View>
  )

  const renderBookingProduct = ({ item }) => (
    <View>
      <TouchableOpacity
        onPress={() => {
          // __DEV__ && console.log(item)
          navigate(SCREENS.bookingHistoryDetail, { orderId: item.orderId, bookingType: BookingType.SHOP })
        }}
        style={styles.rdkTop}>
        <FastImage resizeMode={'contain'} style={styles.imageInvoice} source={iconInvoice} />
        <View style={styles.contentText}>
          <View style={styles.viewStatus}>
            <Text numberOfLines={1} style={styles.rdkTopText}>{t('ORDER_CODE')}: #{item.orderId}</Text>
            {item && item.statusText === 'Chờ xác nhận' ? <View style={[styles.statusBorder, { borderColor: 'orange' }]}>
              <Text style={{ color: 'orange', fontSize: 12 }}>{t('CHOXACNHAN')}</Text>
            </View> : item.statusText === 'Chờ giao hàng' ? <View style={[styles.statusBorder, { borderColor: '#7579e7' }]}>
              <Text style={{ color: '#7579e7', fontSize: 12 }}>{t('wait_for_shipping')}</Text>
            </View> : item.statusText === 'Đang giao' ? <View style={[styles.statusBorder, { borderColor: '#7579e7' }]}>
              <Text style={{ color: '#7579e7', fontSize: 12 }}>{t('delivering')}</Text>
            </View> : item.statusText === 'Hoàn thành' ? <View style={[styles.statusBorder, { borderColor: 'green' }]}>
              <Text style={{ color: 'green', fontSize: 12 }}>{t('DONE')}</Text>
            </View> : item.statusText === 'Trả hàng' ? <View style={[styles.statusBorder, { borderColor: 'orange' }]}>
              <Text style={{ color: 'orange', fontSize: 12 }}>{t('returns')}</Text>
            </View> : item.statusText === 'Đã hủy' ? <View style={[styles.statusBorder, { borderColor: '#f3373a' }]}>
              <Text style={{ color: '#f3373a', fontSize: 12 }}>{t('CANCELED')}</Text>
            </View> : null }
          </View>
          <View style={styles.starRate}>
            <Text style={styles.paymentMethod}>{t('COUNT')}:</Text>
            <Text style={styles.detail}>{_.sumBy(item.products, (o: any) => (o.count))}</Text>
          </View>
          <View style={styles.starRate}>
            <Text style={styles.paymentMethod}>Thành tiền: </Text>
            <Text style={styles.detailPrice}>{numberFormat(parseInt(item.totalPriceShop) + parseInt(item.transportFee))} đ</Text>
          </View>
          {renderPaymentMethodProduct(item)}
          {item?.shippingInfo && item?.shippingInfo.length > 0 ? <TouchableOpacity
            style={styles.btnShippingInfo}
            onPress={(e) => {
              navigate(SCREENS.shippingInformationScreen, { productDetail: item })
            }}
          >
            <View style={{ flexDirection: 'row', marginBottom: -5, flex: 1, justifyContent: 'space-between' }}>
              <View style={{ flexDirection: 'row' }}>
                <Image style={{ width: 30, height: 30 }} source={iconTruckOutline}/>
                <Text style={{ color: '#4DB6AC', alignSelf: 'center', marginLeft: spacing.small }}>{item?.shippingInfo[0].statusText}</Text>
              </View>
              <Icon color={'#4DB6AC'} name='chevron-forward-outline' size={18} style={{ alignSelf: 'center' }}/>
            </View>
          </TouchableOpacity> : null}
        </View>
      </TouchableOpacity>
    </View>
  )

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <Header
        // statusBarProps={{ barStyle: 'light-content' }}
        // barStyle="light-content" // or directly
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={onGoBack}/>}
        centerComponent={{ text: t(t('BOOKING_HISTORY')), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
        //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
        // </TouchableOpacity> : null}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      <View style={styles.background}>
        {/* <View style={styles.renderTitle}> */}
        {/*  <ButtonBack onPress={goBack}/> */}
        {/*  <Text style={styles.TitleText}>{title}</Text> */}
        {/* </View> */}
        {/* <Header position={offset} title={t('BOOKING_HISTORY')} onPressButtonLeft={goBack}/> */}

        { profileStore.isSignedIn() ? <TabView
          navigationState={{ index, routes }}
          renderScene={renderScene}
          onIndexChange={setIndex}
          initialLayout={initialLayout}
          renderTabBar={renderTabBar}
          // initialPage={defaultTabIndex || 0}
          style={{ backgroundColor: '#f7f9fc' }}
        /> : <View style={{
          // alignItems: 'center'
        }}>
          <View style={{
            flexDirection: 'row',
            justifyContent: 'center',
            padding: 10,
            marginTop: 40
          }}>
            <Text style={{
              fontSize: 14,
              color: '#333',
            }}>Yêu cầu</Text>
            <TouchableOpacity
              onPress={goLogin}
              // style={[{
              //   alignItems: 'center',
              //   // backgroundColor: '#d2d2d2',
              //   // elevation: 5,
              //   justifyContent: 'center',
              //   margin: 10,
              //   // shadowColor: '#000',
              //   // shadowOffset: {
              //   //   width: 0,
              //   //   height: 1,
              //   // },
              //   // shadowOpacity: 0.22,
              //   // shadowRadius: 2.22,
              // }]}
            >
              <Text style={{
                fontSize: 14,
                color: color.primary,
                fontWeight: 'bold',
              }}> {t('LOGIN')}</Text>
            </TouchableOpacity>
            <Text style={{
              fontSize: 14,
              color: '#333',
            }}> để xem nội dung</Text>
          </View>
        </View> }

      </View>
    </SafeAreaView>
  )
})
