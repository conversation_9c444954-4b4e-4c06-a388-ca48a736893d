import { StyleSheet, Dimensions } from 'react-native'
import { responsiveHeight } from 'react-native-responsive-dimensions'
import { color, typography } from '../../theme'
import { ifIphoneX } from 'react-native-iphone-x-helper'

const { width, height } = Dimensions.get('window')
const styles = StyleSheet.create({
  TitleText: {
    color: '#333',
    fontSize: 26,
    fontWeight: 'bold',
    padding: 15,
    paddingBottom: 5,
    fontFamily: typography.normal,
  },
  background: {
    backgroundColor: '#fff',
    flex: 1,
    ...ifIphoneX({
      paddingBottom: 60,
    }, {
      paddingBottom: 60,
    }),
  },
  btnShippingInfo: {
    borderBottomWidth: 0.3,
    borderColor: '#f2f2f2',
    borderTopWidth: 0.3,
    flexDirection: 'row',
    marginTop: 10,
    paddingBottom: 7,
  },
  containerItem: {
    paddingTop: 1
  },
  contentText: {
    alignItems: 'flex-start',
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    marginLeft: 10,
    paddingRight: 16
  },
  detail: {
    alignSelf: 'center',
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 12,
    paddingLeft: 10,
    paddingTop: 1
  },
  detailPrice: {
    alignSelf: 'center',
    color: color.primary,
    fontFamily: typography.normal,
    fontSize: 12,
    paddingLeft: 10,
    paddingTop: 1
  },
  flatListContainer: {
    flex: 1,
    height: responsiveHeight(100),
    // ...ifIphoneX({
    //   paddingBottom: 89,
    // }, {
    //   paddingBottom: 60,
    // }),
    // width: responsiveWidth(100),
    // marginLeft: 15,
    // marginRight: 15
  },
  icArrowBack: {
    marginLeft: 10,
  },
  icStatus: {
    paddingHorizontal: 3,
  },
  imageInvoice: {
    height: 24,
    marginLeft: 14,
    marginTop: 10,
    width: 24

  },
  label: {
    color: '#333',
    fontSize: 13
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
  },
  orderId: {
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 12,
    fontWeight: 'bold',
  },
  payment: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    width: '100%'
  },
  paymentMethod: {
    color: '#9d9d9d',
    fontFamily: typography.normal,
    fontSize: 14
  },
  rdkTop: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#fff',
    // borderRadius: 8,
    // borderBottomWidth: 1,
    borderBottomColor: '#f6f6f7',
    // marginHorizontal: spacing.small,
    marginBottom: 1,
    justifyContent: 'space-between',
    paddingVertical: 10
  },
  rdkTopImage: {
    borderBottomLeftRadius: 8,
    borderTopLeftRadius: 8,
    height: 83,
    resizeMode: 'cover',
    width: 87
  },
  rdkTopText: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
  },
  rdkTopText1: {
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 12,
    height: 40,
    paddingLeft: 8,
    paddingRight: 10,
    paddingTop: 3
  },
  renderTitle: {
    flexDirection: 'column',
    paddingTop: 15
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  scene: {
    flex: 1
  },
  starRate: {
    alignItems: 'center',
    flexDirection: 'row',
    // justifyContent: 'space-between',
    width: '100%',
    marginVertical: 5
  },
  statusBorder: {
    alignItems: 'center',
    borderRadius: 3,
    borderWidth: 1,
    marginTop: 8,
    minWidth: 100,
    padding: 5
  },
  tabBar: {
    // Remove border top on both android & ios
    backgroundColor: '#fff',
    borderTopColor: 'transparent',
    borderTopWidth: 0,
    elevation: 0,
    shadowColor: '#5bc4ff',
    shadowOffset: {
      height: 0,
    },
    shadowOpacity: 0,
    shadowRadius: 0,
  },
  tabBarText: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  viewCount: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 8,
    width: '100%'
  },
  viewStatus: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  viewStatusPayment: {
    flexDirection: 'row'
  },
  viewTouchButtonTop: {
    marginTop: 3
  },
})
export default styles
