import { SafeAreaView } from 'react-native-safe-area-context'
import {

  Text,
  View,
  TouchableOpacity,
  ScrollView,
  RefreshControl, Image, Platform
} from 'react-native'
import React, { useState, Fragment, useEffect, useRef, useContext } from 'react'
import styles from './styles'
import { useTranslation } from 'react-i18next'
import Icon from 'react-native-vector-icons/Ionicons'
import { useStores } from '../../models/root-store'
import { useNavigation } from '@react-navigation/native'
import { Modalize } from 'react-native-modalize'
import { EditPassword } from './edit-password-screen'
import { observer } from 'mobx-react-lite'
import ImagePicker from 'react-native-image-crop-picker'
import DateTimePickerModal from 'react-native-modal-datetime-picker'
import moment from 'moment'
import { LazyImage, ButtonBack, TTextInput, TButton, ConfirmDialog } from '@app/components'
import { ModalContext } from '@app/context'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import { UpdateProfileModel } from '@app/models'
import { getSnapshot } from 'mobx-state-tree'
import { color } from '@app/theme'
import { icCamera } from '@app/assets/images'
import SimpleToast from 'react-native-simple-toast'
import LinearGradient from 'react-native-linear-gradient'
import { Api } from '@app/services/api'
import DialogInput from 'react-native-dialog-input'
import { useAuth } from '@app/use-hooks/use-auth'
const api = new Api()

export const EditProfileScreen = observer((props) => {
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()
  const { accountStore, profileStore } = useStores()
  const modalizeRef = useRef<Modalize>(null)
  const [refreshing, setRefreshing] = useState(false)
  const [avatar, setAvatar] = useState('')
  const [pickerDate, setPickerDate] = useState(null)
  const [pickerDateVisible, setPickerDateVisible] = useState(false)
  const [strPiker, setStrPicker] = useState('')
  const [pickerTime, setPickerTime] = useState(null)
  const [strDate, setStrDate] = useState('')
  const currentName = profileStore.fullName
  const currentEmail = profileStore.email
  const [fullName, setFullName] = useState(profileStore.fullName || '')
  const [email, setEmail] = useState(profileStore.email || '')
  const [date, setDate] = useState('')
  const modalChangeName = useRef<Modalize>(null)
  const modalChangeEmail = useRef<Modalize>(null)
  const { showError, showSuccess } = useContext(ModalContext)
  const [isShowDeleteAccount, setIsShowDeleteAccount] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const { signOut, userToken } = useAuth() // should be signUp

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    await profileStore.getProfile()
    // setPhotoUrl(profileStore.photoUrl)
  }

  function onBack() {
    goBack()
  }

  const onOpenModalEditLanguage = () => {
    modalizeRef.current?.open()
  }

  const onCloseModel = () => {
    modalizeRef.current?.close()
  }
  const onOpenModalChangeName = () => {
    modalChangeName.current?.open()
  }
  const onCloseModalChangeName = () => {
    modalChangeName.current?.close()
  }

  const onOpenModalChangeEmail = () => {
    modalChangeEmail.current?.open()
  }
  const onCloseModalChangeEmail = () => {
    modalChangeEmail.current?.close()
  }

  const onRefresh = () => {
    if (refreshing) return
    loadData().then((r) => {
    })
  }

  const getPicture = () => {
    ImagePicker.openPicker({
      writeTempFile: true,
      width: 200,
      mediaType: 'photo',
      height: 200,
      compressImageMaxHeight: 200,
      compressImageMaxWidth: 200,
      cropping: true,
      multiple: false,
    }).then((response: any) => {
      __DEV__ && console.log(response)
      // setAvatar(response.path)
      const files = []
      files.push({
        uri: response.path,
        name: 'picture',
        fileName: response.filename + '.png',
        type: response.mime,
        size: response.size,
      })
      profileStore.uploadImage('/user/api/upload-image', files)
      // profileStore.uploadImageDropBox(files).then(rs => {
      // })
    })
  }

  const showDatePicker = () => {
    setPickerDate('date')
    setStrPicker(t('CHOOSE_A_DATE'))
  }

  const hidePicker = () => {
    setPickerDateVisible(false)
    setPickerDate(null)
    setPickerTime(null)
  }

  const handleConfirm = (date) => {
    const partDate = moment(date).format('DD/MM/YYYY')
    if (Date.parse(date) <= (new Date().getTime())) {
      setStrDate(partDate)
      setDate(partDate)
      profileStore.setBirthDay(partDate)
      hidePicker()
    } else {
      // showError(t('FAIL'), t('Bạn không thể chọn ngày chưa tới'))
      SimpleToast.show('Bạn không thể chọn ngày chưa tới')
      return Platform.OS === 'android' ? setPickerDateVisible(false) : null
    }
  }

  const onUpdateProfile = async () => {
    const path = profileStore.avatarUser
    const picture = !path ? '/user/default-avatar.jpg' : path
    if (picture !== '') {
      profileStore.setAvatarUser(picture)
      profileStore.setPicture(picture)
    }
    if (date !== '') {
      profileStore.setBirthDay(date)
    }
    if (fullName !== '') {
      profileStore.setFullName(fullName)
    }
    if (email !== '') {
      profileStore.setEmail(email)
    }
    const snapShotProfile = getSnapshot(profileStore)
    // __DEV__ && console.log('snapShotProfile', snapShotProfile)
    const body = UpdateProfileModel.create(snapShotProfile)
    const rs = await profileStore.updateProfile(body)
    // console.log('UPDATEPROFILE', rs)
    if (rs && !rs.data.error) {
      showSuccess(t('THANHCONG'), t('ALERT_UPDATE_PROFILE_SUCCESS'))
    } else {
      showError(t('FAIL'), t(`${rs.data.message}`))
    }
  }

  const requestLockAccount = async (password) => {
    const rs = await api.requestLockAccount({ phone: profileStore.phone, password })
    if (rs && rs.data.error) {
      showError(t('FAIL'), t(`${rs.data.message}`))
    } else {
      showSuccess(t('THANHCONG'), t('Yêu cầu xoá tài khoản thành công'))
      onSignOut()
    }
  }

  const onSignOut = async () => {
    try {
      signOut() // gọi hàm signout global
      setTimeout(() => { resetHistory() }, 500)
    } catch (e) {
      __DEV__ && console.log(e)
    }
  }

  const resetHistory = () => {
    profileStore.clearFields()
    profileStore.setReloadData(true)
    profileStore.setBookingSpa([])
    profileStore.setBookingHotel([])
    profileStore.setBookingClinic([])
    profileStore.setBookingProduct([])
    goBack()
  }

  const renderHeaderModalize = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onCloseModel} style={styles.viewTouchButtonTop}/>
      <Text style={styles.textTitleHeader}>{t('CHANGEPASSWORD')}</Text>
    </View>
  )

  const renderHeaderChangeName = () => {
    const str = t('Cập nhật tên người dùng')
    return (<View style={styles.modal__header}>
      <ButtonBack onPress={onCloseModalChangeName} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>{str}</Text>
      <TouchableOpacity onPress={onCloseModalChangeName}>
        <Text style={styles.textSave}>{t('XONG')}</Text>
      </TouchableOpacity>
    </View>)
  }

  const renderHeaderChangeEmail = () => {
    const str = t('Cập nhật email')
    return (<View style={styles.modal__header}>
      <ButtonBack onPress={ () => {
        setEmail('')
        onCloseModalChangeEmail()
      }
      }
      style={styles.viewTouchButtonTop}
      />
      <Text style={styles.textTitleHeader}>{str}</Text>
      <TouchableOpacity onPress={onCloseModalChangeEmail}>
        <Text style={styles.textSave}>{t('XONG')}</Text>
      </TouchableOpacity>
    </View>)
  }

  const renderInfo = () => {
    return (
      <View>
        <View style={styles.contentInfo}>
          <Text style={styles.label}>{t('FULLNAME')}</Text>
          <TouchableOpacity
            onPress={onOpenModalChangeName}
            style={{ flexDirection: 'row' }}
          >
            <Text numberOfLines={1} style={styles.textDetail}>{fullName !== '' ? fullName : currentName}</Text>
            {iconRight}
          </TouchableOpacity>
        </View>
        <View style={styles.contentInfo}>
          <Text style={styles.label}>{t('DANGNHAP_password')}</Text>
          <TouchableOpacity onPress={onOpenModalEditLanguage} style={{ flexDirection: 'row' }}>
            <Text style={[styles.textDetail, { marginTop: 3, alignSelf: 'flex-end' }]}>******</Text>
            {iconRight}
          </TouchableOpacity>
        </View>
        <View style={styles.contentInfo}>
          <Text style={styles.label}>{t('MOBILE')}</Text>
          <View style={{ flexDirection: 'row' }}>
            <Text style={styles.textDetail}>{profileStore.phone}</Text>
            {/* <TextInput */}
            {/*  keyboardType="default" */}
            {/*  maxLength={12} */}
            {/*  autoCapitalize={'none'} */}
            {/*  style={styles.content} */}
            {/*  placeholder={t('NHAP_SO_DT')} */}
            {/*  value={profileStore.phone} */}
            {/* /> */}
            <View style={[styles.iconRight, { width: 20 }]}></View>
          </View>
        </View>
        <View style={styles.contentInfo}>
          <Text style={styles.label}>{t('Email')}</Text>
          <TouchableOpacity
            onPress={onOpenModalChangeEmail}
            style={styles.boxButton}>
            <Text numberOfLines={1} style={styles.textDetail}>{email !== '' ? email : currentEmail}</Text>
            {/* <TextInput */}
            {/*  keyboardType="email-address" */}
            {/*  autoCapitalize={'none'} */}
            {/*  style={styles.content} */}
            {/*  placeholder={t('ENTER_EMAIL')} */}
            {/*  defaultValue={profileStore.email} */}
            {/*  onChangeText={(e) => { */}
            {/*    setEmail(e) */}
            {/*  }} */}
            {/* /> */}
            {iconRight}
          </TouchableOpacity>
        </View>
        <View style={styles.contentInfo}>
          <Text style={styles.label}>{t('HOSOCANHAN_birthday')}</Text>
          <TouchableOpacity
            onPress={() => setPickerDateVisible(true)}
            style={styles.boxButton}>
            <Text style={styles.textDetail}>{profileStore.birthday}</Text>
            {/* <Icon size={18} color={"#c5cee0"} style={styles.iconRight} name={"chevron-forward-outline"}/> */}
            {iconRight}
          </TouchableOpacity>
        </View>
        <View style={styles.contentInfo}>
          <Text style={styles.label}>{t('CCCD')}</Text>
          <TouchableOpacity
            onPress={() => setPickerDateVisible(true)}
            style={styles.boxButton}>
            <Text style={styles.textDetail}>0123456434445</Text>
            {/* <Icon size={18} color={"#c5cee0"} style={styles.iconRight} name={"chevron-forward-outline"}/> */}
            {iconRight}
          </TouchableOpacity>
        </View>
        <View style={styles.contentInfo}>
          <Text style={styles.label}>{t('Công ty')}</Text>
          <TouchableOpacity
            onPress={() => setPickerDateVisible(true)}
            style={styles.boxButton}>
            <Text style={styles.textDetail}>Chưa chọn</Text>
            {/* <Icon size={18} color={"#c5cee0"} style={styles.iconRight} name={"chevron-forward-outline"}/> */}
            {iconRight}
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  const renderAvatar = () => {
    return (
      <View style={styles.viewAvatar}>
        <TouchableOpacity onPress={getPicture} style={styles.touchUser}>
          <LazyImage
            resizeMode="cover"
            style={styles.avatar}
            source={{ uri: profileStore.avatarUser }}
          />
          {/* <Icon style={styles.add_circle} size={24} color={'#00e096'} name={'add-circle'}/> */}
          <View style={{ backgroundColor: color.primary, borderRadius: 100, position: 'absolute', bottom: 0, right: 0 }}>
            <Image source={icCamera} style={{ width: 17, height: 16, margin: 5 }}/>
          </View>
        </TouchableOpacity>
      </View>
    )
  }

  const renderChangeNameInput = () => {
    return (
      <View style={{ padding: 16, marginBottom: 30 }}>
        <Text style={{ fontWeight: '500', color: '#333', marginVertical: 10 }}>Tên người dùng</Text>
        <TTextInput
          typeInput={'code'}
          typeRadius={'rounded'}
          // keyboardType="phone-pad"
          maxLength={30}
          autoCapitalize={'sentences'}
          defaultValue={fullName || ''}
          placeholder={t('Nhập họ tên')}
          placeholderStyle={{ textAlign: 'center' }}
          onChangeText={(e) => setFullName(e.trim())}
          iconRightClick={() => setFullName('')}
          iconRight={<Icon name='close-circle-outline' size={24} color='#a0a0a0' />}
        />
      </View>
    )
  }

  const renderChangeEmailInput = () => {
    return (
      <View style={{ padding: 16, marginBottom: 30 }}>
        <Text style={{ fontWeight: '500', color: '#333', marginVertical: 10 }}>Địa chỉ email</Text>
        <TTextInput
          typeInput={'code'}
          typeRadius={'rounded'}
          keyboardType="email-address"
          maxLength={320}
          autoCapitalize={'sentences'}
          defaultValue={email || ''}
          placeholder={t('Nhập email')}
          placeholderStyle={{ textAlign: 'center' }}
          onChangeText={(e) => setEmail(e.trim())}
          iconRightClick={() => setEmail('')}
          iconRight={<Icon name='close-circle-outline' size={24} color='#a0a0a0' />}
        />
      </View>
    )
  }

  const iconRight = <Icon name={'chevron-forward-outline'} size={18} color={color.primary} style={styles.iconRight}/>

  return (
    <Fragment>
      <SafeAreaView style={{ flex: 1, backgroundColor: '#fff', marginTop: -4 }}>
        <Header
          leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
          centerComponent={{ text: t(t('Cập nhật thông tin')), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
          containerStyle={common.headerContainer}
          // rightComponent={<TouchableOpacity onPress={onUpdateProfile}>
          //   <Text style={styles.textSave}>{t('SAVE')}</Text>
          // </TouchableOpacity>}
          statusBarProps={{ barStyle: 'light-content' }}
          ViewComponent={LinearGradient}
          linearGradientProps={linearGradientProps}
        />
        <ScrollView refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh}/>} style={{ flex: 1 }}>
          <View style={styles.mainContainer}>
            <View style={styles.mainUser}>
              {renderAvatar()}
            </View>
            {/*<View style={styles.viewTitle}>*/}
            {/*  <Text style={styles.textTitle}>Thông tin người dùng</Text>*/}
            {/*</View>*/}
          </View>
          {renderInfo()}
          <View style={ { paddingHorizontal: 16, marginTop: 30 }}>
            <TouchableOpacity onPress={() => setIsShowDeleteAccount(true)}>
              <Text style={{ color: color.primary }}>Xoá tài khoản</Text>
            </TouchableOpacity>
          </View>
          <View style={ { paddingHorizontal: 16, marginTop: 30 }}>
            <TButton typeRadius={'rounded'} title={t('UPDATE')} onPress={() => {}} />
          </View>
        </ScrollView>
        <Modalize
          HeaderComponent={renderHeaderModalize}
          ref={modalizeRef}
          adjustToContentHeight
          keyboardAvoidingBehavior={'padding'}
        >
          <EditPassword closeModal={onCloseModel}/>
        </Modalize>
        <Modalize
          HeaderComponent={renderHeaderChangeName}
          ref={modalChangeName}
          // modalHeight={responsiveHeight(50)}
          adjustToContentHeight
          keyboardAvoidingBehavior={'padding'}
        >
          {renderChangeNameInput()}
        </Modalize>
        <Modalize
          HeaderComponent={renderHeaderChangeEmail}
          ref={modalChangeEmail}
          // modalHeight={responsiveHeight(50)}
          adjustToContentHeight
          keyboardAvoidingBehavior={'padding'}
        >
          {renderChangeEmailInput()}
        </Modalize>
        <DateTimePickerModal
          isVisible={pickerDateVisible}
          // mode={true}
          locale="vi_VN"
          cancelTextIOS={t('CANCEL')}
          confirmTextIOS={t('XACNHAN')}
          headerTextIOS={strPiker}
          onConfirm={handleConfirm}
          onCancel={hidePicker}
          isDarkModeEnabled={false}
        />
        <ConfirmDialog confirmText={t('DONGY')} cancelText={t('CANCEL')} isVisible={isShowDeleteAccount} message={`Nếu đồng ý xóa tài khoản bạn sẽ không thể đăng nhập hoặc khôi phục lại các thông tin của tài khoản ${profileStore.phone}\n\nBạn chắc chắn đồng ý xóa tài khoản khỏi hệ thống maxQ.`} title={'Xoá tài khoản'}
          onConfirm={() => {
            setIsShowDeleteAccount(false)
            // setTimeout(() => {
            //   setShowConfirmPassword(true)
            // }, 200)
          }}
          onClosed={
            () => setIsShowDeleteAccount(false)
          }
        />
        <DialogInput
          isDialogVisible={showConfirmPassword}
          submitInput={(inputText) => {
            setShowConfirmPassword(false)
            requestLockAccount(inputText)
          }}
          hintInput={t('Nhập mật khẩu xác nhận')}
          hintTextColor={'#acb1c0'}
          closeDialog={() => {
            setShowConfirmPassword(false)
          }}
          title={t('Xác nhận mật khẩu')}
          message={t('Mật khẩu')}
          cancelText={'Hủy'}
          submitText={'Xác nhận'}
        />
      </SafeAreaView>
    </Fragment>
  )
})
