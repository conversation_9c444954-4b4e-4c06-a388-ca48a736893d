import React, { useContext, useEffect, useState } from 'react'
import {
  View, ScrollView,
} from 'react-native'
import { useTranslation } from 'react-i18next'
import { useStores } from '../../models/root-store'
import styles from './styles'
import { Password } from '../register-screen/PasswordTextBox'
import { observer } from 'mobx-react-lite'
import { ModalContext } from '@app/context'
import { TButton } from '@app/components'

export const rounded = {
  borderRadius: 4,
  width: '100%',
  padding: 15,
  marginTop: 11,
  flexDirection: 'row',
}
export const EditPassword = observer((props: any) => {
  const { t } : any = useTranslation()
  const { accountStore } = useStores()
  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [rePassword, setRePassword] = useState('')

  const { showError, showSuccess } = useContext(ModalContext)

  useEffect(() => {
  }, [])

  const onPress = async () => {
    if (!currentPassword) {
      showError(t('FAIL'), t('VUI_LONG_NHAP_MAT_KHAU'))
    } else if (!newPassword) {
      showError(t('FAIL'), t('VUI_LONG_NHAP_MAT_KHAU_MOI'))
    } else if (!rePassword) {
      showError(t('FAIL'), t('VUI_LONG_NHAP_LAI_MAT_KHAU'))
    } else if (currentPassword.length < 5 || newPassword.length < 5 || rePassword.length < 5) {
      showError(t('FAIL'), t('ERROR_MK'))
    } else if (newPassword === currentPassword) {
      showError(t('FAIL'), t('ERROR_LOI_TRUNG_MK'))
    } else if (newPassword !== rePassword) {
      showError(t('FAIL'), t('MAT_KHAU_KHONG_KHOP'))
    } else if (newPassword === rePassword) {
      const rs = await accountStore.changePassword({
        currentPass: currentPassword, newPass: newPassword, newPassConf: rePassword,
      })
      if (rs.kind === 'ok' && rs.data.error === false) {
        showSuccess(t('THANHCONG'), t('Change_password_successful'))

        setTimeout(() => {
          props.closeModal()
        }
        , 3000,
        )
      } else if (rs.data.error === true && rs.data.message === 'Mật khẩu hiện tại không đúng') {
        showError(t('FAIL'), t('ERROR_OLD_PASSWORD'))
      } else {
        showError(t('FAIL'), 'null')
      }
    }
  }
  return (
    <>
      <View style={styles.background}>
        <ScrollView
          bounces={false}
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}>
          <View style={{}}>
            <Password
              label={t('CURRENTPASSWORD')}
              onChange={(e) => {
                setCurrentPassword(e)
              }}
            />
            <Password
              label={t('NEWPASSWORD')}
              onChange={(e) => {
                setNewPassword(e)
              }}
            />
            <Password
              label={t('CONFIRMPASSWORD')}
              onChange={(e) => {
                setRePassword(e)
              }}
            />
            {/* <TButton onPress={onPress} style={styles.btnUpdate}><Text style={styles.textUpdate}>{t('UPDATE')}</Text></TButton> */}
            <View style={styles.btnUpdatePw} >
              <TButton typeRadius={'rounded'} title={t('UPDATE')} onPress={() => onPress()} />
            </View>
          </View>
        </ScrollView>
      </View>

    </>
  )
},
)
export default EditPassword
