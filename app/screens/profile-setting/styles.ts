import { StyleSheet, Dimensions } from 'react-native'
import { color, typography } from '../../theme'

const { width, height } = Dimensions.get('window')
const styles = StyleSheet.create({
  add_circle: {
    bottom: 16,
    position: 'absolute',
    right: 0
  },
  avatar: {
    borderRadius: 100,
    height: 100,
    width: 100
  },
  background: {
    backgroundColor: '#fff',
    flex: 1,
    // ...ifIphoneX({
    //   paddingBottom: 89,
    // }, {
    //   paddingBottom: 60,
    // }),
    paddingLeft: 15,
    paddingRight: 15
  },
  boxButton: {
    // flex: 1,
    flexDirection: 'row',
    // marginLeft: 15,
    // marginRight: 15,
    // justifyContent: 'flex-start',
    // alignItems: 'center'
  },
  btnUpdate: {
    alignItems: 'center',
    backgroundColor: '#ff8ba1',
    borderRadius: 22,
    flexDirection: 'row',
    height: 44,
    justifyContent: 'center',
    marginBottom: 10,
    marginTop: 37
  },
  btnUpdatePw: {
    marginBottom: 10,
    marginTop: 20,
  },
  content: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 15,
    fontWeight: '500',
    width: 215
  },
  contentInfo: {
    borderBottomColor: '#f3f3f3',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 15
  },
  icon: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginRight: 15,
    marginTop: 50,

  },
  iconLeft: {
    marginLeft: 0,
    marginRight: 15,
    marginTop: 25,
  },
  iconRight: {
    justifyContent: 'flex-end',
    marginLeft: 15,
  },
  image: {
    borderRadius: 8,
    height: 92,
    marginBottom: 15,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10,
    width: 92
  },
  label: {
    color: '#979797',
    fontFamily: typography.normal,
    fontSize: 14,
    width: 100
  },
  mainContainer: {
    backgroundColor: '#fff',
    flex: 1,
    justifyContent: 'flex-start',
  },
  mainTitle: {
    backgroundColor: '#ffffff',
    paddingLeft: 15,
    paddingTop: 16,
  },
  mainTitleText: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
  },
  mainUser: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flexDirection: 'column',
    justifyContent: 'center',
    marginVertical: 15
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
  },
  scrollView: {
    flex: 1
  },
  textDetail: {
    color: '#333',
    // flex: 1,
    fontSize: 14,
    fontWeight: '400',
    maxWidth: 200
  },
  textInput: {
    backgroundColor: '#ffffff',
    borderColor: '#edf1f7',
    borderRadius: 22,
    borderStyle: 'solid',
    borderWidth: 1,
    height: 44,
    marginTop: '5%',
    paddingLeft: 15,
    width: '100%',
  },
  textName: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 20,
    fontWeight: '600',
    marginLeft: 15,

  },
  textPhone: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
    marginLeft: 15,
    marginTop: 6,
  },
  textSave: {
    color: color.primary,
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0,
    marginTop: 4,
    textAlign: 'right'
  },
  textTitle: {
    color: '#979797',
    fontSize: 14,
    fontWeight: '500',
    paddingVertical: 15,
    textTransform: 'uppercase'
  },

  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  textUpdate: {
    color: '#fff',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0,
  },
  touchUser: {
    alignItems: 'center',
    flexDirection: 'row',
    marginLeft: 15,
    marginRight: 15
  },
  viewAvatar: {
    flexDirection: 'row',
    justifyContent: 'center'
  },
  viewName: {
    flexDirection: 'column',
    flex: 1,
    justifyContent: 'flex-start',
    marginTop: 29,
  },
  viewSecond: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    flexDirection: 'column',
    marginLeft: 15,
    marginRight: 15,
    marginTop: 20,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10
  },
  viewThird: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    flexDirection: 'column',
    height: 58,
    marginBottom: 15,
    marginHorizontal: 15,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10
  },
  viewTitle: {
    backgroundColor: '#f3f3f3',
    flexDirection: 'row',
    justifyContent: 'center'
  },
  viewTopNavbar: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15
  },
  viewTouchButtonTop: {
    marginTop: 3
  }
})
export default styles
