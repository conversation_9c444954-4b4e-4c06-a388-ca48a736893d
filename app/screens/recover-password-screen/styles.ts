import { StyleSheet } from 'react-native'
import { typography } from '../../theme'

const styles = StyleSheet.create({
  boxContainer: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'space-between',
    marginLeft: 30,
    marginRight: 30,
  },
  buttonContainer: {
    marginTop: 50,
  },
  buttonDangky: {
    color: '#000811',
    fontFamily: typography.normal,
    fontSize: 14,
    marginLeft: 5,
    marginTop: 12,
    paddingTop: 5,
    textAlign: 'left',
  },
  buttonLogin: {
    alignItems: 'center',
    backgroundColor: '#ff8ba1',
    borderRadius: 22,
    flexDirection: 'row',
    height: 44,
    justifyContent: 'center',
  },
  buttonLoginText: {
    color: '#ffffff',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  container: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flex: 1,
    justifyContent: 'space-between',
    marginHorizontal: 30
  },
  content: {
    flex: 1,
    width: '100%'
  },
  icArrowBack: {
    margin: 11
  },
  mainTextInput: {
    alignItems: 'center',
    marginTop: 40,
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  subTitle: {
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 14,
    marginTop: 12,
    paddingTop: 5,
    textAlign: 'center',
  },
  text: {
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 14,
    marginTop: 12,
    textAlign: 'left',

  },
  textInput: {
    backgroundColor: '#ffffff',
    borderColor: '#edf1f7',
    borderRadius: 22,
    borderStyle: 'solid',
    borderWidth: 1,
    height: 44,
    marginTop: 20,
    paddingLeft: 14,
    width: '100%',
  },
  textTitle: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
    marginTop: 15,
  },

})
export default styles
