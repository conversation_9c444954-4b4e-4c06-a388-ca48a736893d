import { SafeAreaView } from 'react-native-safe-area-context'
import {

  Text,
  View,
} from 'react-native'
import React, { useContext, useState } from 'react'
import styles from './styles'
import { observer } from 'mobx-react-lite'
import { useStores } from '../../models/root-store'
import { useNavigation } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import { Password } from '../register-screen/PasswordTextBox'
import { SCREENS } from '@app/navigation'
import { ModalContext } from '@app/context'
import { TButton, ButtonBack } from '../../components'
import validate from 'validate.js'

export const RecoverPasswordScreen = observer(() => {
  const { t } : any = useTranslation()
  const { accountStore } = useStores()
  const { navigate, goBack } = useNavigation()
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const { showError, showSuccess } = useContext(ModalContext)
  const [isSubmitting, setSubmitting] = useState(false)

  function onBack() {
    goBack()
  }

  const validateFields = () => validate.isEmpty(confirmPassword) || validate.isEmpty(password)

  const onSubmit = async () => {
    if (!password) {
      showError(t('FAIL'), t('VUI_LONG_NHAP_MAT_KHAU'))
    } else if (!confirmPassword) {
      showError(t('FAIL'), t('VUI_LONG_NHAP_LAI_MAT_KHAU'))
    } else if (password === confirmPassword) {
      const rs = await accountStore.recoverPassword(accountStore.phoneNumber, password, confirmPassword)
      if (rs.kind === 'ok') {
        showSuccess(t('THANHCONG'), 'Lấy lại mật khẩu thành công')
        setTimeout(() => {
          navigate(SCREENS.login)
        }, 3000)
      } else {
        showError(t('FAIL'), 'Không thể tạo mật khẩu vui lòng thử lại sau!')
      }
    } else {
      showError(t('FAIL'), t('MAT_KHAU_KHONG_KHOP'))
    }
  }
  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <ButtonBack onPress={onBack}/>
      <View style={styles.container}>
        <View style={styles.content}>
          <View>
            <View>
              <Text numberOfLines={2} style={styles.textTitle}>{t('RECOVER_PASSWORD')}</Text>
            </View>
            <View style={{ flexDirection: 'row', justifyContent: 'flex-start' }}>
              <Text style={styles.text}>{t('Enter_New_Password_And_Confirm')}</Text>
            </View>
            <View style={styles.mainTextInput}>
              <Password
                label={t('NEWPASSWORD')}
                onChange={(e) => {
                  setPassword(e)
                }}
              />
              <Password
                label={t('NHAP_LAI_MK_MOI')}
                onChange={(e) => {
                  setConfirmPassword(e)
                }}
              />
            </View>
            <View style={styles.buttonContainer}>
              <TButton typeRadius={'rounded'} disabled={validateFields() || isSubmitting} loading={isSubmitting} title={t('TIEP_THEO')} onPress={() => onSubmit()} />
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  )
})
