import React, { useState, useEffect } from 'react'
import {

  View,
  Text, TouchableOpacity, FlatList, Alert,
} from 'react-native'

import styles from './styles'

import Icon from 'react-native-vector-icons/Ionicons'
import { useNavigation } from '@react-navigation/native'

import { observer } from 'mobx-react-lite'
import { useStores } from '../../models/root-store'
import { ButtonBack, LazyImage } from '../../components'
import { SCREENS } from '../../navigation'
import { useTranslation } from 'react-i18next'
import { spacing } from '@app/theme'
import { SafeAreaView } from 'react-native-safe-area-context'

export const CarManagementScreen: React.FC = observer((props) => {
  const { t } : any = useTranslation()
  const navigation = useNavigation()
  const { navigate } = useNavigation()
  const goBack = () => navigation.goBack()
  const { carStore, profileStore } = useStores()
  const [refreshing, setRefreshing] = useState(false)
  // const [selectedAddressIndex, setSelectedAddressIndex] = useState(-1)
  const [isFetched, setIsFetched] = useState(true) // event view placeholder

  useEffect(() => {
    loadData().then(r => {})
  }, [])

  useEffect(() => {
    if (carStore.reloadData) {
      loadData().then(r => {})
    }
  }, [carStore.reloadData])

  const loadData = async () => {
    await carStore.getListPet(profileStore._id)
    carStore.setReloadData(false)
  }
  /**
   * call Store
   */
  const refreshData = async () => {
    setIsFetched(true)
    setRefreshing(true)
    await carStore.getListPet(profileStore._id)
    setRefreshing(false)
    setIsFetched(false)
  }

  /**
   * onRefresh
   */
  const onRefresh = () => {
    setRefreshing(true)
    refreshData().then(r => {
    })
    setRefreshing(false)
  }
  const onDelete = async (id) => {
    setRefreshing(true)
    carStore.deletePetOfList(id)
    await carStore.deletePet(id)
    // await onRefresh()
    setRefreshing(false)
  }
  const renderItem = ({ item, index }) => {
    return (
      <View style={styles.boxViewAddress}>
        <View style={styles.viewImage}>
          <LazyImage source={{ uri: item.photo }}
            style={styles.petAvatar}/>
        </View>
        <View style={{ marginTop: spacing.tiny }}>
          <TouchableOpacity onPress={() => {
            // onSelectedAddress(index)
          }}>
            <View style={styles.renderItemListAddress}>
              <Text>{t('DANGKY_fullname')}: <Text style={styles.textName}>{item.name}</Text></Text>
            </View>
          </TouchableOpacity>
          <View style={styles.boxAddress}>
            <Text>Mô tả: </Text>
            <Text numberOfLines={2} style={styles.textAddressBranch}>{item.description}</Text>
          </View>

          <View style={styles.renderItemListPhone}>
            <Text>Danh mục: {item.categoryInfo.map((e, index) => {
              return <Text key={index}
                style={{ color: '#333' }}>{e.name}</Text>
            })}</Text>
            <View style={styles.boxViewBTN}>
              <TouchableOpacity style={{ marginRight: 12 }} onPress={() => {
                navigate(SCREENS.editCar, { item: item })
              }}>
                <Icon name={'create'} size={20} color={'#acb1c0'}/>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => {
                Alert.alert(
                  t('THANHTOAN_alert_thongbao'),
                  t('ALERT_DELETE'),
                  [{ text: t('CANCEL') }, {
                    text: t('DONGY'),
                    onPress: () => {
                      onDelete(item._id)
                    },
                  }], { cancelable: true },
                )
              }}>
                <Icon name={'trash-outline'} size={20} color={'#acb1c0'}/>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    )
  }

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <ButtonBack onPress={goBack} style={styles.icArrowBack}/>
      <Text style={styles.textTitleTotal}>Danh sách xe</Text>
      <View style={styles.viewFlatlist}>
        <FlatList
          data={carStore.dataListPet}
          onRefresh={() => onRefresh()}
          refreshing={refreshing}
          keyExtractor={item => item._id}
          renderItem={renderItem}
          onEndReachedThreshold={0.5}
          ListFooterComponent={
            <TouchableOpacity style={styles.buttonAdd}
              onPress={() => {
                navigate(SCREENS.addPet)
              }}
            >
              <Text style={styles.textButton}>{t('add_pet')}</Text>
            </TouchableOpacity>
          }
        />
      </View>
    </SafeAreaView>
  )
})
