import { StyleSheet } from 'react-native'
import { color, spacing, typography } from '@app/theme'
import { responsiveHeight } from 'react-native-responsive-dimensions'
import { palette } from '@app/theme/palette';
const styles = StyleSheet.create({
  wrapper: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  add_circle: {
    bottom: 10,
    position: 'absolute',
    right: 0
  },
  carLineTypeView: {
    alignItems: 'center',
  },
  carLineName: {
    color: '#333',
    paddingVertical: 12,
    textAlign: 'center'
  },
  vehicleName: {
    color: '#333',
    marginTop: 8,
    minWidth: 50,
    textAlign: 'center'
  },
  editForm: {
    marginHorizontal: 16
  },
  vehicleIc: {
    height: 37,
    width: 37
  },
  vehicleBtn: {
    alignItems: 'center',
    borderColor: palette.angry,
    borderRadius: 4,
    justifyContent: 'center',
    margin: 5,
    padding: 10
  },
  avatar: {
    borderRadius: 100,
    height: 100,
    width: 100
  },
  itemGender: {
    backgroundColor: '#f3f3f3',
    borderRadius: 4,
    color: '#333',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 10,
    marginHorizontal: 16,
  },
  background: {
    backgroundColor: '#fff',
    flex: 1,
    // ...ifIphoneX({
    //   paddingBottom: 89,
    // }, {
    //   paddingBottom: 60,
    // }),
    paddingLeft: 15,
    paddingRight: 15
  },
  boxAddress: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    paddingBottom: 12,
    paddingLeft: 15,
  },
  boxButton: {
    alignItems: 'center',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginHorizontal: spacing.small,
  },
  boxChoose: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 12,
  },
  boxContact: {
    backgroundColor: '#ffffff',
    borderRadius: 10,
    elevation: 2,
    height: 125,
    marginBottom: 20,
    marginLeft: 15,
    marginRight: 10,
    marginTop: 24,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10,
    width: 288
  },
  boxViewAddress: {
    backgroundColor: '#ffffff',
    borderRadius: 10,
    elevation: 2,
    flexDirection: 'row',
    marginBottom: spacing.medium,
    marginHorizontal: spacing.small,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10
  },
  boxViewBTN: {
    flexDirection: 'row',
    justifyContent: 'flex-end'
  },
  boxViewPhone: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  btnUpdate: {
    alignItems: 'center',
    backgroundColor: '#ff8ba1',
    borderRadius: 22,
    flexDirection: 'row',
    height: 44,
    justifyContent: 'center',
    marginBottom: 10,
    marginTop: 37
  },
  buttonAdd: {
    alignItems: 'center',
    // backgroundColor: '#edf1f7',
    borderColor: '#c5cee0',
    borderRadius: 10,
    // borderStyle: 'dashed',
    borderWidth: 1,
    height: 48,
    justifyContent: 'center',
    marginBottom: 60,
    marginHorizontal: 15,
    marginTop: 50

  },
  chooseAddress: {
    color: '#ffa8b4',
    fontSize: 12,
    marginLeft: 5,
  },
  container: {
    flex: 1,
    // height: responsiveHeight(100),
    // marginBottom: 100
  },
  containerDs: {
    // alignItems: 'center',
    borderBottomColor: '#F4F4F4',
    // borderBottomLeftRadius: 8,
    // borderBottomRightRadius: 10,
    borderBottomWidth: 1,
    borderStyle: 'solid',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  content: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'left',
  },
  contentInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    // paddingHorizontal: 16,
    paddingVertical: 15
  },
  dsViewText: {
    color: 'rgba(0, 0, 0, 0.5)',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginTop: 9
  },
  icArrowBack: {
    marginVertical: 15,
    paddingRight: 20
  },
  icon: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginRight: 15,
    marginTop: 50,

  },
  iconPetSelect: {
    height: 50,
    width: 50
  },
  iconRight: {
    marginLeft: 15
  },
  // iconRight: {
  //   justifyContent: 'flex-end',
  //   marginLeft: 15
  // },
  image: {
    borderRadius: 100,
    height: 50,
    width: 50
  },
  input: {
    backgroundColor: '#f3f3f3',
    borderRadius: 22,
    flex: 1
  },
  label: {
    color: '#979797',
    fontFamily: typography.normal,
    fontSize: 14,
    width: 120
  },
  mainContainer: {
    // backgroundColor: 'red',
    flex: 1,
    justifyContent: 'flex-start',
    // paddingBottom: 89,
    paddingTop: 15,
    // height: '100%'
  },
  mainTitle: {
    // backgroundColor: '#ffffff',
    paddingLeft: 15,
    paddingTop: 16,
  },
  mainTitleText: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
  },
  mainUser: {
    flexDirection: 'row',
    flex: 1,
    // justifyContent: 'center',
    borderWidth: 1,
    borderRadius: 4,
    borderStyle: 'dashed',
    borderColor: '#ff9c9c',
    marginTop: 20
  },
  modalHeader: {
    // paddingVertical: 10,
  },
  modal__header: {
    alignItems: 'center',
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    paddingVertical: 15
  },
  petAvatar: {
    borderRadius: 9,
    height: 100,
    // padding: spacing.small,
    resizeMode: 'contain',
    width: 100
  },
  placeholder: {
    borderRadius: 4,
    marginHorizontal: 15,
    marginVertical: 6,
  },
  renderItemListAddress: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 12,
    paddingLeft: 14,
    paddingRight: 14,
  },
  renderItemListPhone: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: spacing.small,
    paddingLeft: 14,
    paddingRight: spacing.small
  },
  sAvView2: {
    backgroundColor: '#f3f3f3',
    borderColor: 'transparent',
    borderRadius: 22,
    borderStyle: 'solid',
    borderWidth: 1,
    flexDirection: 'row',
    height: 44,
    marginTop: 8
  },
  safeAreaView: {
    backgroundColor: '#f7f9fc',
    flex: 1,
    marginTop: -4
  },
  scrollView: {
    flex: 1
  },
  selectPet: {
    color: '#333',
    fontSize: 13,
    // marginLeft: 15,
    // marginTop: 26,
    textAlign: 'center',
    fontWeight: 'bold'
  },
  selectPiker: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 13,
    fontWeight: '500'
  },
  selectProvince: {
    color: '#333',
    fontSize: 15,
    marginLeft: 15,
    marginTop: 26,
  },
  textAddressBranch: {
    color: '#333',
    fontSize: 15,
    marginLeft: 9,
    width: 245,
  },
  textAddressPhone: {
    color: '#333',
    fontSize: 15,
    marginLeft: 7,
  },
  textButton: {
    color: '#acb1c0',
    fontSize: 15,
    fontWeight: '600',
    textAlign: 'center',

  },
  textChooeseImg: {
    color: color.primary,
    marginLeft: 10
  },
  textDelete: {
    color: color.primary,
    fontFamily: typography.normal,
    fontSize: 14,
    width: 120
  },
  textDetail: {
    color: '#333',
    fontSize: 14,
    fontWeight: '400',
    maxWidth: 200
  },
  textIP: {
    alignItems: 'center',
    justifyContent: 'center',
    margin: 10,
  },
  textInput: {
    alignItems: 'center',
    backgroundColor: '#f3f3f3',
    // borderColor: '#edf1f7',
    borderRadius: 4,
    // borderStyle: 'solid',
    // borderWidth: 1,
    color: '#333',
    flexDirection: 'row',
    fontSize: 15,
    height: 44,
    justifyContent: 'space-between',
    marginTop: 15,
    paddingLeft: 14
  },
  textName: {
    color: '#333',
    fontSize: 15,
    fontWeight: 'bold',
    marginTop: 12
  },

  textProvince: {
    alignSelf: 'center',
    color: '#a0a0a0',
    fontFamily: typography.normal,
    fontSize: 14
  },
  textSave: {
    color: color.primary,
    fontFamily: typography.normal,
    fontSize: 15,
    fontWeight: '600',
    marginTop: 2,
    textAlign: 'right'
  },
  textTitle: {
    color: '#979797',
    fontSize: 14,
    fontWeight: '500',
    paddingVertical: 15,
    textTransform: 'uppercase'
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  textTitleTotal: {
    color: '#333',
    fontSize: 26,
    fontWeight: 'bold',
    marginHorizontal: spacing.small
  },
  textUpdate: {
    color: '#fff',
    fontFamily: typography.normal,
    fontSize: 15,
    fontWeight: '600',
    letterSpacing: 0,
  },
  titleLabel: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    paddingTop: 20
  },
  touchUser: {
    alignItems: 'center',
    flexDirection: 'row',
    margin: 10
  },
  unChooseAddress: {
    color: '#b0aeae',
    fontSize: 12,
    marginLeft: 5,
    textDecorationLine: 'underline'
  },
  viewAvatar: {
    flexDirection: 'row',
    justifyContent: 'center'
  },
  viewCapture: {
    alignItems: 'center',
    backgroundColor: '#d6d6d6',
    borderRadius: 50,
    height: 50,
    justifyContent: 'center',
    padding: 20,
    width: 50
  },
  viewFlatlist: {
    height: responsiveHeight(100) - 220,
    // justifyContent: 'space-between',
    // marginBottom: 90
    marginTop: 27
  },

  viewIconAdd: {
    alignItems: 'center',
    flexDirection: 'row'
  },
  viewImage: {
    borderRadius: 9,
    marginLeft: spacing.small,
    marginTop: spacing.small,
  },
  viewLabel: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  viewName: {
    flexDirection: 'column',
    flex: 1,
    justifyContent: 'flex-start',
    marginTop: 29,
  },
  viewProvince: {
    alignItems: 'center',
    backgroundColor: '#f3f3f3',
    // borderColor: '#edf1f7',
    borderRadius: 4,
    // borderStyle: 'solid',
    // borderWidth: 1,
    flexDirection: 'row',
    height: 44,
    justifyContent: 'space-between',
    marginTop: 15,
    paddingLeft: 14
  },
  viewSecond: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    flexDirection: 'column',
    marginLeft: 15,
    marginRight: 15,
    marginTop: 20,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10
  },

  viewTouchButtonTop: {
    // marginTop: 2
    marginLeft: 16
  },
  emptyView: {
    marginRight: 16,
    width: 22
  },
  viewTitle: {
    backgroundColor: '#f3f3f3',
    flexDirection: 'row',
    justifyContent: 'center'
  },

})
export default styles
