import React, { Fragment, useContext, useEffect, useImperativeHandle, useRef, useState } from 'react'
import {
  Image,
  ScrollView,
  Text, TextInput, TouchableOpacity, View
} from 'react-native'
import styles from './styles'
import { observer } from 'mobx-react-lite'
import { ButtonBack, TButton } from '@app/components'
import ImagePicker from 'react-native-image-crop-picker'
import Icon from 'react-native-vector-icons/Ionicons'
import { useTranslation } from 'react-i18next'
import { ModalContext } from '@app/context'
import { useStores } from '@app/models'
import { Modalize } from 'react-native-modalize'
import { responsiveHeight } from 'react-native-responsive-dimensions'
import { Api } from '@app/services/api'
import { useNavigation } from '@react-navigation/native'
import FastImage from 'react-native-fast-image'
import {
  bikeIcon,
  busIcon,
  carIcon,
  icAddRed,
  icCapture
} from '@app/assets/images'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import LinearGradient from 'react-native-linear-gradient'
import { SafeAreaView } from 'react-native-safe-area-context'
import { isEmpty } from '@app/utils/validate'

const api = new Api()
export const carLineType = [
  {
    type: 0,
    name: 'Ô tô',
    key: 'car',
    icon: carIcon
  },
  {
    type: 1,
    name: 'Xe máy',
    key: 'bike',
    icon: bikeIcon
  },
  {
    type: 2,
    name: 'Khác',
    key: 'other',
    icon: busIcon
  }
]

export interface ModalAddPet {
  handleClosed?: any
}

export const AddCarScreen = observer((props: ModalAddPet, ref) => {
  const { navigate, goBack } = useNavigation()
  const { t }: any = useTranslation()
  const { showError, showSuccess } = useContext(ModalContext)
  const { homeStore, carStore, profileStore } = useStores()
  const modalize = useRef<Modalize>(null)
  const modalThuonghieu = useRef<Modalize>(null)
  const modalDongxe = useRef<Modalize>(null)
  const modalLoaixe = useRef<Modalize>(null)
  const [search, setSearch] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [dataCarBrand, setDataCarBrand] = useState(carStore.carBrand)
  const [dataBikeBrand, setDataBikeBrand] = useState(carStore.bikeBrand)
  const [dataOtherBrand, setDataOtherBrand] = useState(carStore.otherBrand)

  const [dataCarLine, setDataCarLine] = useState(carStore.carLine)
  const [dataCarType, setDataCarType] = useState(carStore.carType)

  const [isSelectedType, setIsSelectedType] = useState(0)
  const [isSelectedIndex, setIsSelectedIndex] = useState(0)

  const [carTypeSelected, setCarTypeSelected] = useState('car')

  const [brandLabel, setBrandLabel] = useState('')
  const [carLineLabel, setCarLineLabel] = useState('')
  const [carTypeLabel, setCarTypeLabel] = useState('')

  // search state
  const [searchTextCar, setSearchTextCar] = useState('')
  const [searchTextBike, setSearchTextBike] = useState('')
  const [searchTextOther, setSearchTextOther] = useState('')
  const [searchTexTCarLine, setSearchTextCarLine] = useState('')
  const [searchTexTCarType, setSearchTextCarType] = useState('')

  // image array choose
  const [filesUpload, setFilesUpload] = useState([])
  const [base64Upload, setBase64Upload] = useState('')

  const [modalTitle] = useState({
    loaixe: 'Chọn loại xe',
    thuonghieu: 'Chọn thương hiệu',
    dongxe: 'Chọn dòng xe'
  })
  const [objValue, setObjValue] = useState({
    bsx: '',
    name: '',
    gpsId: '',
    somay: '',
    sokhung: '',
    car_line: null,
    car_brands: null,
    car_categories: null,
    year: null,
    user_id: profileStore._id,
    image: null
  })

  useEffect(() => {
    getApiData()
  }, [])

  // search car brand
  useEffect(() => {
    if (searchTextCar) {
      const lowerCaseSearchText = searchTextCar?.toLowerCase()
      const filter: any = carStore.carBrand.filter(
        (asset) =>
          asset?.attributes.name?.toLowerCase()?.includes(lowerCaseSearchText)
      )
      setDataCarBrand(filter)
    }
    if (searchTextCar === '') {
      setDataCarBrand(carStore.carBrand)
    }
  }, [searchTextCar])

  // search bike brand
  useEffect(() => {
    if (searchTextBike) {
      const lowerCaseSearchText = searchTextBike?.toLowerCase()
      const filter: any = carStore.bikeBrand.filter(
        (asset) =>
          asset?.attributes.name?.toLowerCase()?.includes(lowerCaseSearchText)
      )
      setDataBikeBrand(filter)
    }
    if (searchTextBike === '') {
      setDataBikeBrand(carStore.bikeBrand)
    }
  }, [searchTextBike])

  // search other brand
  useEffect(() => {
    if (searchTextOther) {
      const lowerCaseSearchText = searchTextOther?.toLowerCase()
      const filter: any = carStore.otherBrand.filter(
        (asset) =>
          asset?.attributes.name?.toLowerCase()?.includes(lowerCaseSearchText)
      )
      setDataOtherBrand(filter)
    }
    if (searchTextOther === '') {
      setDataOtherBrand(carStore.otherBrand)
    }
  }, [searchTextOther])

  // search carline
  useEffect(() => {
    if (searchTexTCarLine) {
      const lowerCaseSearchText = searchTexTCarLine?.toLowerCase()
      const filter: any = carStore.carLine.filter(
        (asset) =>
          asset?.attributes.name?.toLowerCase()?.includes(lowerCaseSearchText)
      )
      setDataCarLine(filter)
    }
    if (searchTexTCarLine === '') {
      setDataCarLine(carStore.carLine)
    }
  }, [searchTexTCarLine])

  // search car type
  useEffect(() => {
    if (searchTexTCarType) {
      const lowerCaseSearchText = searchTexTCarType?.toLowerCase()
      const filter: any = carStore.carType.filter(
        (asset) =>
          asset?.attributes.name?.toLowerCase()?.includes(lowerCaseSearchText)
      )
      setDataCarType(filter)
    }
    if (searchTexTCarType === '') {
      setDataCarType(carStore.carType)
    }
  }, [searchTexTCarType])

  const getApiData = async () => {
    return Promise.all([
      carStore.getCarType(),
      carStore.getCarBrand('car'),
      carStore.getBikeBrand('bike'),
      carStore.getOtherBrand('other')
    ])
  }

  useImperativeHandle(ref, () => {
    return {
      onSubmit: onSubmit
    }
  })

  const onGoBack = () => {
    goBack()
    homeStore.setReloadData(true)
  }

  const getPicture = () => {
    ImagePicker.openPicker({
      writeTempFile: true,
      width: 200,
      mediaType: 'photo',
      height: 200,
      // compressImageMaxHeight: 200,
      // compressImageMaxWidth: 200,
      includeBase64: true,
      cropping: true,
      multiple: false
    }).then(async (response: any) => {
      __DEV__ && console.log('image selected=>>>>', response)

      setBase64Upload(`data:${response.mime};base64,${response.data}`)
      const uriParts = response.path.split('.')
      const fileType = uriParts[uriParts.length - 1]

      const files = []
      files.push({
        uri: response.path,
        name: 'files',
        fileName: response.filename + '.' + fileType,
        type: response.mime,
        size: response.size
      })
      console.log('files uploaded=>>>>>>', files)
      setFilesUpload(files)
      // const rsUpload = await api.uploadImagesV2('/api/upload', files) // upload
      // __DEV__ && console.log('upload rs', rsUpload)
      // profileStore.uploadImageDropBox(files).then(rs => {
      // })
    })
  }

  const validateFields = (
    isEmpty(objValue.name) ||
    isEmpty(objValue.bsx) ||
    isEmpty(objValue.car_categories) ||
    isEmpty(objValue.car_brands) ||
    isEmpty(objValue.car_line) ||
    // objValue.sokhung === '' ||
    // objValue.somay === '' ||
    isEmpty(objValue.user_id) ||
    // objValue.gpsId === '' ||
    isEmpty(objValue.year)
  )

  const onSubmit = async () => {
    setIsSubmitting(true)
    if (validateFields) {
      showError(t('FAIL'), t('FILL_OUT_THE_FORM'))
      setIsSubmitting(false)
    } else {
      const path = carStore.picture
      const picture = !path ? '/user/default-avatar.jpg' : path
      if (picture !== '') {
        carStore.setPicture(picture)
      }
      // if (fullName !== '') {
      //   carStore.setFullName(fullName)
      // }
      console.log('filesUpload', filesUpload)
      const rsUpload = await api.uploadImagesV2('/api/upload', filesUpload) // upload
      console.log('result uploaded', rsUpload)
      if (!rsUpload?.error) {
        onChangeText('image', rsUpload[0].id)
        const dataCreate = { ...objValue, image: rsUpload[0].id }
        const body: any = { data: dataCreate }
        const rsCreate = await carStore.addCar(body)
        console.log('result rsCreate', rsCreate)
        if (rsCreate && !rsCreate.data.error) {
          showSuccess(t('THANHCONG'), t('Xe của bạn đã được thêm thành công'))
          setTimeout(() => onGoBack(), 500)
        } else {
          showError(t('FAIL'), t(`${rsCreate.data.message}`))
          setIsSubmitting(false)
        }
      } else {
        showError(t('FAIL'), t('Xảy ra lỗi không thể tải ảnh'))
        setIsSubmitting(false)
      }
    }
    // setIsSubmitting(false)
  }

  const onChangeText = (field, value) => {
    setObjValue((prev) => ({ ...prev, [field]: value }))
  }

  const onPressBtnBack = (title) => {
    if (title === modalTitle.dongxe) {
      modalDongxe.current.close()
    }
    if (title === modalTitle.loaixe) {
      modalLoaixe.current.close()
    }
    if (title === modalTitle.thuonghieu) {
      modalThuonghieu.current.close()
    }
  }

  const onChangeCarLineType = (item, index) => {
    setIsSelectedIndex(index)
    setIsSelectedType(item.type)
    onChangeType(carLineType[index], index)
  }

  const onChangeType = (item, index) => {
    setCarTypeSelected(item.key)
    const arrFilter: any = carStore.carType.filter(x => x.attributes.type == item.key)
    if (arrFilter) {
      setDataCarType(arrFilter)
    }
  }

  const renderHeaderModal: any = (title) => {
    return (<View style={styles.modalHeader}>
      <View style={styles.modal__header}>
        <ButtonBack onPress={() => onPressBtnBack(title)} style={styles.viewTouchButtonTop} />
        <Text style={styles.textTitleHeader}>{title}</Text>
        <View style={styles.emptyView} />
      </View>
      <View style={{ marginHorizontal: 16 }}>
        <View style={styles.carLineTypeView}>
          {title === modalTitle.thuonghieu && renderVehicleType()}
          {title === modalTitle.loaixe &&
            <ScrollView showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false} horizontal>
              {carLineType.map((item, index) => {
                return <View key={index} style={[styles.vehicleBtn, { borderWidth: carTypeSelected == item.key ? 1 : 0 }]}
                  // onPress={() => onChangeType(item, index)}
                >
                  <Image source={item.icon} style={styles.vehicleIc} />
                  <Text style={styles.vehicleName}>{item.name}</Text>
                </View>
              })}
            </ScrollView>}
        </View>
        {renderSearchInput(isSelectedType, title)}
      </View>
    </View>)
  }

  const renderVehicleType = () => {
    return (
      <ScrollView showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false} horizontal>
        {carLineType.map((item, index) => vehicleTypeItem(item, index))}
      </ScrollView>
    )
  }

  const vehicleTypeItem = (item, index) => {
    return (
      <TouchableOpacity key={index} style={[styles.vehicleBtn, { borderWidth: isSelectedIndex === index ? 1 : 0 }]}
        onPress={() => onChangeCarLineType(item, index)}>
        <Image source={item.icon} style={styles.vehicleIc}></Image>
        <Text style={styles.vehicleName}>{item.name}</Text>
      </TouchableOpacity>
    )
  }

  // function pressItem(item) {
  //   onCloseModal()
  //   checkShowPopup(item._id)
  //   setIsPickerSelectVisible(true)
  //   setCategoryId(item._id)
  // }

  const onPressItemBrand = (item) => {
    setBrandLabel(item.attributes.name)
    carStore.getCarLine(item.attributes.name)
    onChangeText('car_brands', item.id)
    modalThuonghieu.current.close()
  }
  const onPressItemCarLine = (item) => {
    setCarLineLabel(item.attributes.name)
    onChangeText('car_line', item.id)
    modalDongxe.current.close()
  }
  const onPressItemCarType = (item) => {
    setCarTypeLabel(item.attributes.name)
    onChangeText('car_categories', item.id)
    modalLoaixe.current.close()
  }

  const renderCarLine = () => {
    return (<View style={{ paddingVertical: 20 }}>
      {dataCarLine.map((item, index) => renderCarLineItem(item, index))}
    </View>)
  }

  const renderCarBrand = () => {
    return (<View style={{ paddingVertical: 20 }}>
      {dataCarBrand.map((item, index) => renderVehicleTypeItem(item, index))}
    </View>)
  }
  const renderBikeBrand = () => {
    return (<View style={{ paddingVertical: 20 }}>
      {dataBikeBrand.map((item, index) => renderVehicleTypeItem(item, index))}
    </View>)
  }
  const renderOtherBrand = () => {
    return (<View style={{ paddingVertical: 20 }}>
      {dataOtherBrand.map((item, index) => renderVehicleTypeItem(item, index))}
    </View>)
  }

  const renderSearchInput = (isSelectedType, title) => {
    return (
      <View style={styles.sAvView2}>
        <TouchableOpacity style={styles.textIP}>
          <Icon name={'search-outline'} size={24} color={'#a0a0a0'} />
        </TouchableOpacity>
        <TextInput
          placeholder={t('SEARCH')}
          placeholderTextColor='#46474D'
          underlineColorAndroid='transparent'
          defaultValue={search}
          onChangeText={(text) => {
            if (isSelectedType === 0) {
              setSearchTextCar(text)
            }
            if (isSelectedType === 1) {
              setSearchTextBike(text)
            }
            if (isSelectedType === 2) {
              setSearchTextOther(text)
            }
            if (title === modalTitle.dongxe) {
              setSearchTextCarLine(text)
            }
            if (title === modalTitle.loaixe) {
              setSearchTextCarType(text)
            }
          }}
          style={styles.input}
        />
      </View>
    )
  }

  const renderCarLineItem = (item, index) => {
    return (
      <TouchableOpacity
        style={styles.itemGender}
        key={index}
        onPress={() => {
          onPressItemCarLine(item)
        }
        }
      >
        <Text style={styles.carLineName}>{item.attributes.name}</Text>

      </TouchableOpacity>
    )
  }
  const renderCarTypeItem = (item, index) => {
    return (
      <TouchableOpacity
        style={styles.itemGender}
        key={index}
        onPress={() => {
          onPressItemCarType(item)
        }
        }
      >
        <Text style={styles.carLineName}>{item.attributes.name}</Text>

      </TouchableOpacity>
    )
  }
  const renderVehicleTypeItem = (item, index) => {
    return (
      <TouchableOpacity
        style={styles.itemGender}
        key={index}
        onPress={() => {
          onPressItemBrand(item)
        }
        }
      >
        <Text style={styles.carLineName}>{item.attributes.name}</Text>

      </TouchableOpacity>
    )
  }

  const iconRight = <Icon name={'chevron-down-outline'} size={20} color={'#333'} style={{ marginRight: 8 }} />
  const iconLeft = <Image source={icAddRed} style={{ width: 22, height: 22, marginRight: 8 }}></Image>

  return (
    <Fragment>
      <SafeAreaView style={styles.wrapper}>
        <Header
          leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack} />}
          centerComponent={{ text: t(t('add_pet')), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
          containerStyle={common.headerContainer}
          statusBarProps={{ barStyle: 'light-content' }}
          ViewComponent={LinearGradient}
          linearGradientProps={linearGradientProps}
        />
        <KeyboardAwareScrollView showsVerticalScrollIndicator={false} style={{ paddingHorizontal: 16 }}>
          <View style={styles.mainContainer}>
            <TouchableOpacity
              onPress={getPicture}
            >
              <Text style={styles.titleLabel}>Hình đại diện xe</Text>
              <View style={styles.mainUser}>
                {base64Upload.length > 0 ? <View style={styles.touchUser}>
                  <FastImage
                    resizeMode='cover'
                    style={styles.image}
                    source={{ uri: base64Upload }}
                  />
                  <Text style={styles.textChooeseImg}>{t('Đổi ảnh đại diện')}</Text>
                  {/* <Icon style={styles.add_circle} size={24} color={'#00e096'} name={'add-circle'}/> */}
                </View> : <View style={styles.touchUser}>
                  <View style={styles.viewCapture}><Image source={icCapture} style={{ width: 30, height: 20 }}></Image></View>
                  <Text style={styles.textChooeseImg}>{t('Tải ảnh lên')}</Text>
                </View>}
              </View>
            </TouchableOpacity>
            <Text style={styles.titleLabel}>Tên xe</Text>
            <TextInput
              style={styles.textInput}
              selectTextOnFocus={false}
              placeholderTextColor='#a0a0a0'
              placeholder={'Nhập tên xe'}
              value={objValue.name}
              onChangeText={(e) => {
                onChangeText('name', e)
              }}
            />
            <Text style={styles.titleLabel}>Biển số xe</Text>
            <TextInput
              style={styles.textInput}
              selectTextOnFocus={false}
              placeholderTextColor='#a0a0a0'
              placeholder={'Nhập biển số'}
              value={objValue.bsx}
              onChangeText={(e) => {
                onChangeText('bsx', e.toUpperCase())
              }}
            />
            <Text style={styles.titleLabel}>Thương hiệu</Text>
            <TouchableOpacity onPress={() => modalThuonghieu.current.open()} style={styles.viewProvince}>
              <View style={{ flexDirection: 'row' }}>
                {iconLeft}
                {brandLabel === '' ? <Text style={styles.textProvince}>Chọn thương hiệu</Text>
                  : <Text style={[styles.textProvince, { color: '#333' }]}>{brandLabel}</Text>}
              </View>
              {iconRight}
            </TouchableOpacity>
            <Text style={styles.titleLabel}>Dòng xe</Text>
            <TouchableOpacity onPress={() => modalDongxe.current.open()} style={styles.viewProvince}>
              <View style={{ flexDirection: 'row' }}>
                {iconLeft}
                {carLineLabel === '' ? <Text style={styles.textProvince}>Chọn dòng xe</Text>
                  : <Text style={[styles.textProvince, { color: '#333' }]}>{carLineLabel}</Text>}
              </View>
              {iconRight}
            </TouchableOpacity>
            <Text style={styles.titleLabel}>Loại xe</Text>
            <TouchableOpacity onPress={() => modalLoaixe.current.open()} style={styles.viewProvince}>
              <View style={{ flexDirection: 'row' }}>
                {iconLeft}
                {carTypeLabel === '' ? <Text style={styles.textProvince}>Chọn loại xe</Text>
                  : <Text style={[styles.textProvince, { color: '#333' }]}>{carTypeLabel}</Text>}
              </View>
              {iconRight}
            </TouchableOpacity>
          </View>
          <Text style={styles.titleLabel}>Năm sản xuất</Text>
          <TextInput
            style={styles.textInput}
            selectTextOnFocus={false}
            // maxLength={4}
            keyboardType={'number-pad'}
            placeholderTextColor='#a0a0a0'
            placeholder={'Nhập năm sản xuất'}
            value={objValue?.year?.toString()}
            onChangeText={(e) => {
              onChangeText('year', Number(e))
            }}
          />
          <Text style={styles.titleLabel}>Số khung</Text>
          <TextInput
            style={styles.textInput}
            selectTextOnFocus={false}
            // maxLength={4}
            keyboardType={'default'}
            placeholderTextColor='#a0a0a0'
            placeholder={'Nhập số khung của xe'}
            value={objValue.sokhung}
            onChangeText={(e) => {
              onChangeText('sokhung', e.toUpperCase())
            }}
          />
          <Text style={styles.titleLabel}>Số máy</Text>
          <TextInput
            style={styles.textInput}
            selectTextOnFocus={false}
            // maxLength={4}
            keyboardType={'default'}
            placeholderTextColor='#a0a0a0'
            placeholder={'Nhập số máy của xe'}
            value={objValue.somay}
            onChangeText={(e) => {
              onChangeText('somay', e.toUpperCase())
            }}
          />
          <Text style={styles.titleLabel}>GPS ID</Text>
          <TextInput
            style={styles.textInput}
            selectTextOnFocus={false}
            // maxLength={4}
            keyboardType={'number-pad'}
            placeholderTextColor='#a0a0a0'
            placeholder={''}
            value={objValue.gpsId}
            onChangeText={(e) => {
              onChangeText('gpsId', e.trim())
            }}
          />
          {/* <TButton typeRadius={'rounded'} buttonStyle={styles.buttonAdd} title={t('XONG')} onPress={onSubmit} /> */}
        </KeyboardAwareScrollView>
        <TButton loading={isSubmitting} disabled={isSubmitting} typeRadius={'rounded'} title={t('Thêm xe')} onPress={onSubmit}
          buttonStyle={{ marginHorizontal: 16, marginVertical: 10 }} titleStyle={{ fontWeight: '500', fontSize: 16 }} />
        <Modalize
          HeaderComponent={() => renderHeaderModal(modalTitle.thuonghieu)}
          ref={modalThuonghieu}
          modalHeight={(responsiveHeight(80))}
          keyboardAvoidingBehavior={'padding'}
        >
          {isSelectedType === 0 ? renderCarBrand() : isSelectedType === 1 ? renderBikeBrand() : renderOtherBrand()}
        </Modalize>
        <Modalize
          HeaderComponent={() => renderHeaderModal(modalTitle.dongxe)}
          ref={modalDongxe}
          modalHeight={(responsiveHeight(80))}
          keyboardAvoidingBehavior={'padding'}
        >
          {renderCarLine()}
        </Modalize>
        <Modalize
          HeaderComponent={() => renderHeaderModal(modalTitle.loaixe)}
          ref={modalLoaixe}
          modalHeight={(responsiveHeight(80))}
          keyboardAvoidingBehavior={'padding'}
        >
          <View style={{ paddingVertical: 20 }}>
            {dataCarType.map((item, index) => renderCarTypeItem(item, index))}
          </View>
        </Modalize>

      </SafeAreaView>
    </Fragment>
  )
}, { forwardRef: true })
