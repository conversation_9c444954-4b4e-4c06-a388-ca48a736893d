import { StyleSheet } from 'react-native'
import { palette } from '@app/theme/palette'
import { color, typography } from '@app/theme'
import { ifIphoneX } from 'react-native-iphone-x-helper'

const styles = StyleSheet.create({
  btnSubmitCT: {
    backgroundColor: palette.orange,
    marginBottom: 8,
    marginTop: 10,
    width: 160
  },
  btnSubmitCT1: {
    backgroundColor: '#4fc3f7',
  },
  description: {
    color: '#979797',
    fontSize: 14
  },
  discountCode: {
    alignSelf: 'center',
    color: color.primary,
    fontSize: 12,
    fontWeight: '500',
    marginHorizontal: 15,
    marginTop: 2
  },
  discountValue: {
    color: '#fff',
    fontSize: 12,
    paddingHorizontal: 10,
    paddingVertical: 3
  },
  iView: {
    borderBottomColor: '#ccc',
    borderBottomWidth: 1,
    flex: 1,
    padding: 10
  },
  iView1: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center'
  },
  iView1Text: {
    color: '#333333',
    fontSize: 13,
    width: 100
  },
  iView1Text1: {
    color: '#333333',
    flex: 1,
    fontSize: 13,
    marginLeft: 10
  },
  iView2: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 4
  },
  icArrowBack: {
    paddingHorizontal: 10
  },
  icLocation: {
    alignSelf: 'flex-start',
    height: 15,
    marginTop: 2,
    resizeMode: 'contain',
    width: 15
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    paddingVertical: 15,
    ...ifIphoneX({
      marginTop: 0
    }, {
      marginTop: 5
    }),
  },
  rMd: {
    justifyContent: 'flex-end',
    margin: 0
  },
  rMdView: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 10,
    maxHeight: 500,
    padding: 20
  },
  rMdView1: {
    alignItems: 'flex-end',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: 20
  },
  rMdView1Text: {
    color: '#999',
    fontSize: 14,
    textAlign: 'center',
    textDecorationLine: 'underline'
  },
  rMdViewText: {
    color: '#CB1016',
    fontSize: 14,
    marginBottom: 10
  },
  rMdViewText1: {
    color: '#333',
    fontSize: 13,
    marginBottom: 4,
    marginLeft: 10
  },
  rMdViewText2: {
    alignSelf: 'center',
    color: '#999',
    fontSize: 13,
    marginBottom: 4,
    marginTop: 30,
    paddingBottom: 30,
    textAlign: 'center'
  },
  rSp: {
    alignItems: 'flex-start',
    // borderBottomColor: '#E9E9E9',
    // borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 20,
    marginBottom: 1,
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    flex: 1
  },
  rSpView: {
    borderColor: '#E2E2E2',
    // borderRadius: 4,
    borderWidth: 1,
    height: 100,
    position: 'relative',
    width: 100
  },
  rSpView1: {
    flex: 1,
    justifyContent: 'space-between',
    paddingLeft: 20
  },
  rSpView1Input: {
    alignSelf: 'flex-end',
    height: 40,
    marginTop: 5,
    width: 130
  },
  rSpView1Text: {
    color: '#333333',
    flex: 1,
    fontSize: 14,
    lineHeight: 18
  },
  rSpView1Text1: {
    color: '#999999',
    fontSize: 12,
    marginTop: 10
  },
  rSpView1Text2: {
    alignSelf: 'center',
    color: '#999999',
    fontSize: 12,
    marginTop: 5
  },
  rSpView2: {
    alignItems: 'center',
    flexDirection: 'column',
    justifyContent: 'space-between'
  },
  rSpView2Cb: {
    height: 15,
    width: 15
  },
  rSpView2Top: {
    justifyContent: 'flex-end',
    // marginTop: 10,
    // padding: 4
  },
  rSpView2TopImage: {
    height: 24,
    width: 24
  },
  rSpViewImage: {
    height: '100%',
    resizeMode: 'contain',
    width: '100%'
  },
  rsCb: {
    height: 20,
    width: 20
  },
  rsView: {
    backgroundColor: '#fff',
    borderBottomColor: '#F6F6F7',
    marginBottom: 5,
    // paddingHorizontal: 15,
    paddingTop: 20
  },
  rsView1: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 10,
  },
  rsView1Text: {
    color: '#333',
    fontSize: 16,
    fontWeight: '500'
  },
  rsView1Top: {
    borderColor: 'green',
    borderRadius: 20,
    borderStyle: 'dotted',
    borderWidth: 1,
    marginLeft: 4,
    paddingBottom: 3,
    paddingLeft: 10,
    paddingRight: 10,
    paddingTop: 3
  },
  rsView1TopText:
    {
      color: '#000',
      fontSize: 11
    },
  rsView2: {
    alignItems: 'center',
    borderBottomColor: '#E9E9E9',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 5,
    paddingBottom: 10
  },
  rsView2Image: {
    height: 16,
    resizeMode: 'contain',
    width: 12
  },
  rsView2Text: {
    color: '#333',
    flex: 1,
    fontSize: 14,
    marginLeft: 10
  },
  sAv: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  sAvView: {
    backgroundColor: color.primaryBackground,
    flex: 1,
  },
  sAvView0Image: {
    height: 14,
    resizeMode: 'contain',
    width: 24
  },
  sAvView0Text: {
    flex: 1,
    fontSize: 14,
  },
  sAvView1: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flex: 1,
    justifyContent: 'center'
  },
  sAvView1Image: {
    height: 100,
    marginBottom: 15,
    resizeMode: 'contain',
    width: 100
  },
  sAvView1Text: {
    color: '#333',
    fontSize: 16,
    fontWeight: '400'
  },
  sAvView2: {
    alignItems: 'center',
    borderBottomColor: '#E9E9E9',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 15
  },
  sAvView2Text: {
    borderColor: '#E9E9E9',
    borderRadius: 4,
    borderWidth: 1,
    color: '#ccc',
    flex: 1,
    fontSize: 13,
    height: 45,
    marginRight: 10,
    paddingLeft: 12,
    textAlignVertical: 'center',
  },
  sAvView2Top: {
    alignItems: 'center',
    backgroundColor: '#1274E7',
    borderRadius: 4,
    height: 42,
    justifyContent: 'center',
    paddingLeft: 15,
    paddingRight: 15
  },
  sAvView2TopText: {
    color: '#fff',
    fontSize: 13,
    padding: 5,
  },
  sAvView3: {
    backgroundColor: '#fff',
    borderTopColor: '#E9E9E9',
    borderTopWidth: 1,
    // paddingBottom: 40,
    paddingHorizontal: 16,
    paddingVertical: 5,
    flexDirection: 'row',
    justifyContent: 'space-between',

  },
  sAvView31: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  sAvView31Text: {
    color: '#979797',
    // flex: 1,
    fontSize: 14
  },
  sAvView31Text1: {
    color: '#333',
    fontSize: 14,
  },
  sAvView4: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center'
  },

  sAvView5: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10
  },
  sAvView5Text: {
    color: '#979797',
    // flex: 1,
    fontSize: 14
  },
  sAvView5Text1: {
    color: color.primary,
    fontSize: 16,
    fontWeight: '600'
  },
  sAvView5TopText: {
    color: '#FFFFFF',
    fontSize: 14,
    lineHeight: 18,
    textAlign: 'center'
  },
  sPText: {
    color: '#CB1016',
    fontSize: 13,
    marginTop: 5,
  },
  textDone: {
    color: color.primary
  },
  textInputNote: {
    backgroundColor: color.primaryBackground,
    borderRadius: 4,
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 14,
    marginTop: 6,
    padding: 15,
    textAlign: 'left',
    textAlignVertical: 'top'
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  viewDiscount: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
    paddingHorizontal: 16,
    paddingVertical: 16
  },
  viewDiscountValue: {
    backgroundColor: color.primary,
    borderRadius: 3
  },
  viewStoreAddress: {
    paddingHorizontal: 16
  },
  viewTtinput:
    {
      marginRight: 10,
      width: '78%'
    }
})
export default styles
