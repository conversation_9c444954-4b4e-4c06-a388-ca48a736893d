import { SafeAreaView } from 'react-native-safe-area-context'
import React, { useState, useEffect, useRef, useContext } from 'react'
import {

  View,
  Text,
  TouchableOpacity,
  Platform,
  ScrollView,
  FlatList
} from 'react-native'
// import this
import ScrollingButtonMenu from 'react-native-scroll-menu'

import styles from './styles'
import { useTranslation, withTranslation } from 'react-i18next'
import { useNavigation } from '@react-navigation/native'
import { Modalize } from 'react-native-modalize'
import { observer } from 'mobx-react-lite'
import { useStores } from '@app/models'
import moment from 'moment'
import 'moment/locale/vi'
import {
  FloatActionButton,
  PlaceHolderDetails,
  ButtonBack,
  TButton,
  TabRenderListScreen, TabRenderProductScreen, useLoading
} from '../../components'
import { SCREENS } from '@app/navigation'
import { ModalContext } from '@app/context'

import { responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import ListBranch from './tabrender/Branch/listBranch'

import { RenderChooseService } from '../render-choose-service/choose-service-render'
import { useAbortableEffect } from '@app/use-hooks'
import Icon from 'react-native-vector-icons/Ionicons'
import { useAuth } from '@app/use-hooks/use-auth'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import { BookingType } from '@app/constants/bookingType'

import { TabBar } from 'react-native-tab-view'
import { color } from '@app/theme'
import LinearGradient from 'react-native-linear-gradient'
import RenderHeaderChird from '@app/screens/service-detail-screen/tabrender/renderHeaderChird/renderHeaderChird'

const HEAD_HEIGHT = Platform.OS === 'ios' ? 600 : 545

moment.locale('vi')

const TabNames = {
  SHOP: 'SANPHAM',
  CLINIC: 'GARAGE',
  HOTEL: 'HOTEL',
  SPA: 'SPA',
  PARKING: 'PARKING',
  SHOWROOM: 'SHOWROOM',
  GAS: 'GAS',
  // REVIEW: 'REVIEW'
}

const VirtualizedList = ({ children }) => {
  return (
    <FlatList
      data={[]}
      keyExtractor={() => 'key'}
      renderItem={null}
      nestedScrollEnabled
      showsVerticalScrollIndicator={false}
      ListHeaderComponent={
        <>{children}</>
      }
    />
  )
}

export const ServiceDetail = observer((props:any) => {
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()
  const modalBranch = useRef<Modalize>(null)
  const modalizeAddService = useRef<Modalize>(null)
  const contentRef = useRef(null)
  const [refreshing] = useState(false)
  const [isFetched, setIsFetched] = useState(true)
  const navigation = useNavigation()
  const { serviceStore, bookingStore, profileStore, productStore, notificationStore } = useStores()
  const [chooseData, setChooseData] = useState(null)
  const { id, item, spaId } = props.route.params
  const [dataBranch, setDataBranch] = useState([])
  const { showError } = useContext(ModalContext)
  const [index, setIndex] = useState(TabNames.SHOP)
  const [routes, setRoutes] = useState([])
  // const { user, fetchUserData } = useContext(WebSocketContext)
  const refChooseService = useRef(null)
  const { signOut } = useAuth() // should be signUp
  const [isSubmitting, setSubmitting] = useState(true)
  const { show, hide } = useLoading()

  // const TabNames = {
  //   SHOP: 'SANPHAM',
  //   CLINIC: 'GARAGE',
  //   HOTEL: 'HOTEL',
  //   SPA: 'SPA',
  //   PARKING: 'PARKING',
  //   SHOWROOM: 'SHOWROOM',
  //   GAS: 'GAS',
  //   // REVIEW: 'REVIEW'
  // }

  useEffect(() => {
    return () => {

    }
  }, [])

  const getApiData = async () => {
    return Promise.all([
      serviceStore.getStoreById(id),
      serviceStore.getLastestComments(id, profileStore._id),
      bookingStore.getServiceOfSpa(id),
      bookingStore.getRoomOfHotel(id),
      bookingStore.getServiceOfClinic(id),
      productStore.getProductOfStore(id),
      bookingStore.getClassificationByType(id, 'show-room'),
      bookingStore.getClassificationByType(id, 'gas')
    ])
  }

  const loadData = async () => {
    resetStore()
    setIsFetched(true)
    getApiData().then(data => {
      setDataBranch(bookingStore.branchStore || [])
      setIsFetched(false)
      serviceStore.setIsLastestComments(false)
      // serviceStore.setTypeBooking(3)
      tabNames() // calculate tab name
      if (item) {
        __DEV__ && console.log('item passing', item)
        handlerOpenModal(3, item)
      }

      // const spaId = '623fc5b451282810737ff2e4'
      if (spaId) {
        const services = [
          { type: BookingType.SPA, item: bookingStore.dataServiceOfSpa.find(x => x._id === spaId) },
          { type: BookingType.CLINIC, item: bookingStore.dataServiceOfClinic.find(x => x._id === spaId) },
          { type: BookingType.PARKING, item: bookingStore.dataRoomOfHotel.find(x => x._id === spaId) }
        ]

        const service = services.find(s => s.item)
        if (service) {
          handlerOpenModal(service.type, service.item)
        }
      }
    }).catch(err => {
      __DEV__ && console.log(err)
    })
  }

  const goLoginScreenRequired = () => {
    navigation.navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  const onOpenAddService = async (item) => {
    if (profileStore.isSignedIn()) {
      // check token valid
      const rs = await profileStore.getProfile()
      if (rs?.data?.error) {
        logOut()
      }
      const checkExist = bookingStore.cart.find(x => x._id == item._id)
      __DEV__ && console.log('checkExist', checkExist)
      if (checkExist) {
        showError(t('FAIL'), t('EXIST_ITEM_CART'))
      } else {
        __DEV__ && console.log('setChooseData', item)
        setChooseData(item)
        bookingStore.setBookingStatus(false)
        modalizeAddService.current?.open()
      }
    } else {
      goLoginScreenRequired()
    }
  }

  const logOut = async () => {
    try {
      profileStore.clearFields()
      notificationStore.clearFields()
      signOut() // gọi hàm signout global
      navigate(SCREENS.homeStack)
    } catch (e) {
      __DEV__ && console.log(e)
    }
  }

  const onCloseAddService = () => {
    modalizeAddService.current?.close()
  }

  const onOpenModalBranch = () => {
    modalBranch.current?.open()
  }
  const onCloseModalBranch = () => {
    modalBranch.current?.close()
  }
  const onRefresh = () => {
    if (refreshing) return
    loadData().then(r => {
    })
  }

  const onCreateChat = async () => {
    console.log('props.route.params', props.route.params)
    console.log('userIdStores', serviceStore.userStoreId)
    if (!serviceStore.userStoreId) {
      showError(t('FAIL'), t('Có lôi xảy ra không thể gửi tin nhắn'))
      return
    }
    navigate(SCREENS.chatDetailScreen, { id: profileStore._id, toUserId: serviceStore.userStoreId, roomName: serviceStore.name, rId: '' })
  }

  useAbortableEffect(() => {
    // if (!user) {
    // nếu chưa sync kịp thì cố tình gọi check xem, không có token nghĩa là những user ở version cũ thì cần đăng nhập lại
    // fetchUserData().then(rs => {
    //   if (!rs || !rs.authToken) {
    //     logOut()
    //   }
    // }).catch(e => {
    //   logOut()
    // })
    // }
    loadData().then(r => {})
  }, [])

  useEffect(() => {
    if (bookingStore.bookingStatus) {
      onCloseAddService()
    }
  }, [bookingStore.bookingStatus])

  useAbortableEffect(() => {
    if (serviceStore.isLastestComments) {
      loadData().then(r => {})
    }
  }, [serviceStore.isLastestComments])

  /**
   * tinh toán tab có kết quả hiển thị
   */
  /**
   * Thay đổi giải pháp do lỗi cuộn flatList
   */
  const tabNames = () => {
    const tabs = []
    // kiếm tra được check chọn type kinh doanh trong admin thì mới hiển thị
    if (serviceStore.businessTypes.includes(`${BookingType.SPA}`) && bookingStore.dataServiceOfSpa.length) {
      tabs.push({ id: TabNames.SPA, name: t(TabNames.SPA) })
    }
    // if (bookingStore.dataRoomOfHotel.length) {
    //   tabs.push({ key: 'hotel', title: t(TabNames.HOTEL) })
    // }
    if (serviceStore.businessTypes.includes(`${BookingType.PARKING}`) && bookingStore.dataRoomOfHotel.length) {
      tabs.push({ id: TabNames.PARKING, name: t(TabNames.PARKING) })
    }
    if (serviceStore.businessTypes.includes(`${BookingType.CLINIC}`) && bookingStore.dataServiceOfClinic.length) {
      tabs.push({ id: TabNames.CLINIC, name: t(TabNames.CLINIC) })
    }
    if (serviceStore.businessTypes.includes(`${BookingType.SHOP}`) && productStore.dataProduct.length) {
      tabs.push({ id: TabNames.SHOP, name: t(TabNames.SHOP) })
    }

    if (serviceStore.businessTypes.includes(`${BookingType.SHOWROOM}`) && bookingStore.dataShowrooms.length) {
      tabs.push({ id: TabNames.SHOWROOM, name: t(TabNames.SHOWROOM) })
    }
    if (serviceStore.businessTypes.includes(`${BookingType.GAS}`) && bookingStore.dataGas.length) {
      tabs.push({ id: TabNames.GAS, name: t(TabNames.GAS) })
    }
    // tabs.push({ key: 'product', title: t(TabNames.SHOP) + ` (${productStore.totalProducts})` })
    // tabs.push({ key: 'review', title: t(TabNames.REVIEW) })
    setRoutes(tabs)
    setIndex(tabs?.length ? tabs[0].id : 0)
  }

  useEffect(() => {
    defaultTabView(routes)
  }, [routes])

  /**
   * default tab index by key convert from number type
   */
  const defaultTabView = (tabArray) => {
    const { type } = props.route.params
    __DEV__ && console.log('no default tab view', type)
    switch (Number(props.route.params.type)) {
      case BookingType.SPA:
        const spa = tabArray.find((x) => x.name === t(TabNames.SPA))
        if (spa?.id) {
          setIndex(spa?.id)
        }
        break
        /*      case BookingType.HOTEL:
        setIndex(tabArray.findIndex((x) => x.title === t(TabNames.HOTEL)))
        break */
      case BookingType.PARKING:
        const parking = tabArray.find((x) => x.name === t(TabNames.PARKING))
        if (parking?.id) {
          setIndex(parking?.id)
        }
        break
      case BookingType.CLINIC:
        const gara = tabArray.find((x) => x.name === t(TabNames.CLINIC))
        if (gara?.id) {
          setIndex(gara?.id)
        }
        break
      case BookingType.SHOP:
        const shop = tabArray.find((x) => x.name === t(TabNames.SHOP))
        if (shop?.id) {
          setIndex(shop?.id)
        }
        break
      case BookingType.SHOWROOM:
        const showroom = tabArray.find((x) => x.name === t(TabNames.SHOWROOM))
        if (showroom?.id) {
          setIndex(showroom?.id)
        }
        break
      case BookingType.GAS:
        const gas = tabArray.find((x) => x.name === t(TabNames.GAS))
        if (gas?.id) {
          setIndex(gas?.id)
        }
        break
      default:
        // setIndex(0)
        break
    }
  }

  const renderHeaderModalBranch = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onCloseModalBranch} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader }>{t('LIST_BRANCH')}</Text>
      <View style={{ width: 25 }}/>
    </View>
  )

  const makeHeaderHeight = () => HEAD_HEIGHT

  const renderTabBar = (props: any) => {
    return <TabBar
      {...props}
      inactiveColor={'#333'}
      activeColor={'#333'}
      indicatorStyle={{ backgroundColor: color.primary }}
      labelStyle={{
        textTransform: 'capitalize',
      }}
      style={{
        backgroundColor: '#fff',
      }}
    />
  }

  const renderHeaderFlatList = (text, count) => (
    <View style={{ backgroundColor: '#fff' }}>
      {/* <RenderListHeaderFlatlist onSelect={(item) => { */}
      {/*  setChooseData(item) */}
      {/* }} navigation={navigation} storeId={id}/> */}
      <View style={styles.viewtextService}>
        <Text style={styles.textService}>{text}</Text>
        <Text style={{ color: '#333' }}> ({count})</Text>
      </View>
    </View>
  )

  function clearCart() {
    bookingStore.setValueCoupon(0)
    bookingStore.setCoupon('')
    bookingStore.clearCart()
    __DEV__ && console.log('serviceStore.setTypeBooking', serviceStore.typeBooking)
  }

  function setChooseType(type) {
    setChooseData(type)
    serviceStore.setTypeBooking(type)
  }

  const handlerOpenModal = (type, item) => {
    __DEV__ && console.log(type + ' opened')
    setChooseType(type)
    onOpenAddService(item)
  }

  /**
   * chú ý nếu gặp bug scroll bị hở khoảng trắng cần kiểm tra index
   * @param route
   */
  const renderScene = () => {
    switch (index) {
      case TabNames.SPA:
        return <TabRenderListScreen
          index={index}
          key={'service'}
          bookingType={BookingType.SPA}
          header={() => renderHeaderFlatList(t('SERVICE'), bookingStore.totalService)}
          data={bookingStore.dataServiceOfSpa}
          onSelect={({ type, item }) => {
            __DEV__ && console.log('item selected', item)
            handlerOpenModal(type, item)
          }}/>
      case TabNames.PARKING:
        return <TabRenderListScreen
          index={index}
          bookingType={BookingType.PARKING}
          header={() => renderHeaderFlatList(t('PARKING'), bookingStore.totalRooms)}
          data={bookingStore.dataRoomOfHotel}
          onSelect={({ type, item }) => {
            handlerOpenModal(type, item)
          }}/>
      case TabNames.CLINIC:
        return <TabRenderListScreen
          index={index}
          key={'clinic'}
          bookingType={BookingType.CLINIC}
          header={() => renderHeaderFlatList(t('Clinic'), bookingStore.totalServiceClinic)}
          data={bookingStore.dataServiceOfClinic}
          onSelect={({ type, item }) => {
            handlerOpenModal(type, item)
          }}/>
      case TabNames.SHOP:
        return <TabRenderProductScreen
          index={index}
          key={'product'}
          bookingType={BookingType.SHOP}
          data={productStore.dataProduct}
          header={() => renderHeaderFlatList(t('SANPHAM'), productStore.totalProducts)}
        />
      case TabNames.SHOWROOM:
        return <TabRenderListScreen
          index={index}
          key={'clinic'}
          bookingType={BookingType.SHOWROOM}
          header={() => renderHeaderFlatList(t('SHOWROOM'), bookingStore.totalShowrooms)}
          data={bookingStore.dataShowrooms}
          onSelect={({ type, item }) => {
            handlerOpenModal(type, item)
          }}/>
      case TabNames.GAS:
        return <TabRenderListScreen
          index={index}
          key={'clinic'}
          bookingType={BookingType.GAS}
          header={() => renderHeaderFlatList(t('Gas'), bookingStore.totalGas)}
          data={bookingStore.dataGas}
          onSelect={({ type, item }) => {
            // handlerOpenModal(type, item)
          }}/>
      // case 'review':
      //   return <TabRenderReviewScreen index={4} onPress={() => gotoTotalComment()}/>
      default:
        return null
    }
  }

  const renderHeaderModalizeAddService: any = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onCloseAddService} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>{ serviceStore.typeBooking == BookingType.SHOWROOM ? 'Đăng ký mua/lái thử' : t('ADD_SERVICE')}</Text>
      <View style={{ width: 25 }}></View>
    </View>
  )

  const onPaymentPress = () => {
    refChooseService?.current.bookingService(0)
    setSubmitting(true)
  }

  const onRegisterBookingShowRoom = () => {
    refChooseService?.current.bookingCar()
    setSubmitting(true)
  }

  const onAddToCartPress = () => {
    refChooseService?.current.bookingService(1)
    setSubmitting(true)
  }

  /**
   * đối với type = showRooom chỉ hiển thị button Đăng ký
   */
  const renderFooterModalComponent = () => (
    <View style={styles.viewBtnSubmitFooter}>
      { serviceStore.typeBooking != BookingType.SHOWROOM && <TButton buttonStyle={styles.btnAddCT} title={t('THEMVAOGIOHANG')} disabled={isSubmitting} titleStyle={{ color: '#333' }} onPress={onAddToCartPress} typeRadius={'rounded'}/>}
      { serviceStore.typeBooking != BookingType.SHOWROOM && <TButton typeRadius={'rounded'} title={t('THANHTOAN')} onPress={onPaymentPress} disabled={isSubmitting} buttonStyle={styles.btnSubmitCT}/> }
      { serviceStore.typeBooking == BookingType.SHOWROOM && <TButton typeRadius={'rounded'} title={t('Đăng ký ngay')} onPress={onRegisterBookingShowRoom} disabled={isSubmitting} buttonStyle={{ backgroundColor: '#D42023', width: responsiveWidth(95) }}/> }
    </View>
  )

  const onGoBack = () => {
    resetStore()
    goBack()
  }

  const resetStore = () => {
    // serviceStore.reset()
    bookingStore.reset()
    bookingStore.clearCart()
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#fff', marginTop: -4 }}>
      {!isFetched ? <Header
        // statusBarProps={{ barStyle: 'light-content' }}
        // barStyle="light-content" // or directly
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={onGoBack}/>}
        centerComponent={{ text: t(`${serviceStore.name || ''}`), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        rightComponent={<View style={{ flexDirection: 'row' }}>
          <TouchableOpacity style={styles.viewTouchRightChatIcon} onPress={onCreateChat}>
            <Icon
              size={20}
              color={'#fff'}
              name={'chatbubble-ellipses-outline'}
            />
          </TouchableOpacity>
          <TouchableOpacity style={styles.viewTouchRight} onPress={() => navigation.navigate(SCREENS.detailStoreScreen)}>
            <Icon
              size={21}
              color={'#fff'}
              name={'alert-circle-outline'}
            />
          </TouchableOpacity>
        </View>}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
        containerStyle={common.headerContainer}
      /> : null}

      {isFetched ? <PlaceHolderDetails/>
        : <View style={{ flex: 1, backgroundColor: color.primaryBackground }}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            style={{ flex: 1 }}
            contentContainerStyle={{ flexGrow: 1, width: '100%', backgroundColor: '#fff' }}>
            <RenderHeaderChird onOpenModalBranch = {onOpenModalBranch} navigation={navigation} storeId={id} name={serviceStore.name} totalRateValue={serviceStore.rateTotalValue} totalComment={serviceStore.totalComment}/>
            <ScrollingButtonMenu
              items={routes}
              buttonStyle={{ backgroundColor: 'white', borderRadius: 0, borderWidth: 0 }}
              activeButtonStyle={{ borderBottomColor: color.primary, borderBottomWidth: 2 }}
              onPress={(e) => {
                setIndex(e.id)
              }}
              upperCase={false}
              activeBackgroundColor={'#fff'}
              activeColor={'#333'}
              containerStyle={{ backgroundColor: '#fff' }}
              selected={index}
            />
            <View style={{ backgroundColor: '#fff', flex: 1 }}>
              {renderScene()}
            </View>
            {/* <TabView */}
            {/*  key={'tabview'} */}
            {/*  renderTabBar={renderTabBar} */}
            {/*  // makeHeaderHeight={() => makeHeaderHeight()} */}
            {/*  // renderScrollHeader={renderHeader} */}
            {/*  navigationState={{ index, routes }} */}
            {/*  renderScene={renderScene} */}
            {/*  // scrollEnabled={false} */}
            {/*  onIndexChange={(index) => { */}
            {/*    clearCart() */}
            {/*    setIndex(index) */}
            {/*  }} */}
            {/*  // style={{ flex: 1, height: '100%' }} */}
            {/*  // sceneContainerStyle={{ overflow: 'visible' }} */}
            {/*  initialLayout={{ width: responsiveWidth(100) }} */}
            {/* /> */}
          </ScrollView>
          <>
            <Modalize
              HeaderComponent={renderHeaderModalizeAddService}
              ref={modalizeAddService}
              contentRef={contentRef}
              modalTopOffset={40}
              disableScrollIfPossible = {false}
              modalHeight={responsiveHeight(80)}
              // snapPoint={responsiveHeight(80)}
              keyboardAvoidingBehavior={'padding'}
              scrollViewProps={{ showsVerticalScrollIndicator: false }}
              FooterComponent={renderFooterModalComponent}
            >
              {chooseData ? <RenderChooseService bookingData={(bookingData) => {
                navigation.navigate(SCREENS.reviewBooking, {
                  bookingData
                })
              }} onClose={(onClose) => {
                if (onClose == 1) {
                  onCloseAddService()
                }
              }
              } chooseData={chooseData} dataBranch={dataBranch} storeId={id} type={serviceStore.typeBooking} ref={refChooseService} validate={(value) => setSubmitting(value)}/> : null}
            </Modalize>
            <Modalize
              HeaderComponent={renderHeaderModalBranch}
              ref={modalBranch}
              modalHeight={responsiveHeight(80)}
              keyboardAvoidingBehavior={'padding'}
            >
              <ListBranch/>
            </Modalize>
          </>
        </View>}
      {bookingStore.cart.length > 0 ? <FloatActionButton onPress={() => {
        navigate(SCREENS.shoppingCartScreen)
      }}/> : null}
    </SafeAreaView>
  )
})

export default withTranslation()(ServiceDetail)
