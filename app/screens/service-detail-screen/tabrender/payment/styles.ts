import { StyleSheet } from 'react-native'
import { typography } from '../../../../theme'
const styles = StyleSheet.create({
  containerDs: {
    alignItems: 'center',
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    borderStyle: 'solid',
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
    marginLeft: 15,
    marginRight: 15,
    padding: 10
  },
  dsImag: {
    // position: 'absolute',
    // right: 0,
    // top: 0
  },
  dsImage: {
    height: 20,
    resizeMode: 'contain',
    width: 20
  },
  dsView: {
    height: 20,
    width: 20
  },
  dsViewText: {
    color: 'rgba(0, 0, 0, 0.5)',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginTop: 9
  },
  icArrowBack: {
    height: 24,
    marginLeft: 10,
    resizeMode: 'contain',
    width: 24,
  },
  input: {
    flex: 1
  },
  sAv: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  sAvView: {
    backgroundColor: '#fff',
    flex: 1,
    paddingBottom: 45,
    paddingTop: 15
  },
  sAvView1: {
    alignItems: 'center',
    backgroundColor: '#CB1016',
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 10
  },
  sAvView1Image: {
    height: 14,
    resizeMode: 'contain',
    width: 24
  },
  sAvView1Text: {
    color: '#fff',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 14,
    marginLeft: 20
  },
  sAvView1Text1: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold',
  },
  sAvView2: {
    borderColor: '#edf1f7',
    borderRadius: 22,
    borderStyle: 'solid',
    borderWidth: 1,
    flexDirection: 'row',
    height: 44,
    marginBottom: 15,
    marginHorizontal: 15,
  },
  selectPiker: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold'
  },
  textIP: {
    alignItems: 'center',
    justifyContent: 'center',
    margin: 10
  },
  title: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
    letterSpacing: 0,
    paddingHorizontal: 15,
    paddingVertical: 20
  },
  viewBtnTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginRight: 15
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
}
)
export default styles
