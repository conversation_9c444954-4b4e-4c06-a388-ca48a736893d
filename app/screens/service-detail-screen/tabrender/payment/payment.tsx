import {

  Text,
  TextInput,
  TouchableOpacity,
  View, ViewStyle
} from 'react-native'
import Icon from 'react-native-vector-icons/Ionicons'
import React, { useState } from 'react'

import { useTranslation } from 'react-i18next'
import { observer } from 'mobx-react-lite'
import styles from './styles'
import { removeUtf8 } from '../../../../utils/string'

export interface PaymentScreenProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: ViewStyle,
  // isVisible: boolean,
  // data: any
  onSelect: any
  // goBack:any
  oncloseModal?:any
}

export const Payment = observer((props: PaymentScreenProps) => {
  const { t } : any = useTranslation()
  // this.bankCodeCallback = this.props.getParams(this).bankCodeCallback
  // this.bankCode = this.props.getParams(this).bankCode
  const data = [
    {
      bankCode: 'NCB',
      name: '<PERSON><PERSON> hàng NCB',
      logo: '',
    },
    {
      bankCode: 'ABBANK',
      name: '<PERSON><PERSON> hàng thương mại cổ phần <PERSON> (ABBANK)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/abbank_logo.png',
    },
    {
      bankCode: 'ACB',
      name: 'Ngân hàng ACB',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/acb_logo.png',
    },
    {
      bankCode: 'AGRIBANK',
      name: 'Ngân hàng Nông nghiệp (Agribank)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/agribank_logo.png',
    },
    {
      bankCode: 'BACABANK',
      name: 'Ngân Hàng TMCP Bắc Á',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/bacabank_logo.png',
    },
    {
      bankCode: 'BIDV',
      name: 'Ngân hàng đầu tư và phát triển Việt Nam (BIDV)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/bidv_logo.png',
    },
    {
      bankCode: 'DONGABANK',
      name: 'Ngân hàng Đông Á (DongABank)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/dongabank_logo.png',
    },
    {
      bankCode: 'EXIMBANK',
      name: 'Ngân hàng EximBank',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/eximbank_logo.png',
    },
    {
      bankCode: 'HDBANK',
      name: 'Ngân hàng HDBank',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/hdbank_logo.png',
    },
    {
      bankCode: 'IVB',
      name: 'Ngân hàng TNHH Indovina (IVB)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/ivb_logo.png',
    },

    {
      bankCode: 'MBBANK',
      name: 'Ngân hàng thương mại cổ phần Quân đội',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/mbbank_logo.png',
    },

    {
      bankCode: 'MSBANK',
      name: 'Ngân hàng Hàng Hải (MSBANK)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/msbank_logo.png',
    },
    {
      bankCode: 'NAMABANK',
      name: 'Ngân hàng Nam Á (NamABank)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/namabank_logo.png',
    },
    {
      bankCode: 'NCB',
      name: 'Ngân hàng Quốc dân (NCB)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/ncb_logo.png',
    },

    {
      bankCode: 'OCB',
      name: 'Ngân hàng Phương Đông (OCB)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/ocb_logo.png',
    },

    {
      bankCode: 'OJB',
      name: 'Ngân hàng Đại Dương (OceanBank)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/ojb_logo.png',
    },

    {
      bankCode: 'PVCOMBANK',
      name: 'Ngân hàng TMCP Đại Chúng Việt Nam',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/PVComBank_logo.png',
    },

    {
      bankCode: 'SACOMBANK',
      name: 'Ngân hàng TMCP Sài Gòn Thương Tín (SacomBank)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/sacombank_logo.png',
    },

    {
      bankCode: 'SAIGONBANK',
      name: 'Ngân hàng thương mại cổ phần Sài Gòn Công Thương',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/saigonbank.png',
    },

    {
      bankCode: 'SCB',
      name: 'Ngân hàng TMCP Sài Gòn (SCB)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/scb_logo.png',
    },

    {
      bankCode: 'SHB',
      name: 'Ngân hàng Thương mại cổ phần Sài Gòn - Hà Nội(SHB)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/shb_logo.png',
    },
    {
      bankCode: 'TECHCOMBANK',
      name: 'Ngân hàng Kỹ thương Việt Nam (TechcomBank)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/techcombank_logo.png',
    },
    {
      bankCode: 'TPBANK',
      name: 'Ngân hàng Tiên Phong (TPBank)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/tpbank_logo.png',
    },
    {
      bankCode: 'VPBANK',
      name: 'Ngân hàng Việt Nam Thịnh vượng (VPBank)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/vpbank_logo.png',
    },
    {
      bankCode: 'SEABANK',
      name: 'Ngân Hàng TMCP Đông Nam Á',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/seabank_logo.png',
    },

    {
      bankCode: 'VIB',
      name: 'Ngân hàng Thương mại cổ phần Quốc tế Việt Nam (VIB)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/vib_logo.png',
    },

    {
      bankCode: 'VIETABANK',
      name: 'Ngân hàng TMCP Việt Á',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/vietabank_logo.png',
    },
    {
      bankCode: 'VIETBANK',
      name: 'Ngân hàng thương mại cổ phần Việt Nam Thương Tín',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/vietbank_logo.png',
    },

    {
      bankCode: 'VIETCAPITALBANK',
      name: 'Ngân Hàng Bản Việt',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/vccb_logo.png',
    },
    {
      bankCode: 'VIETCOMBANK',
      name: 'Ngân hàng Ngoại thương (Vietcombank)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/vietcombank_logo.png',
    },
    {
      bankCode: 'VIETINBANK',
      name: 'Ngân hàng Công thương (Vietinbank)',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/vietinbank_logo.png',
    },
    {
      bankCode: 'BIDC',
      name: 'Ngân Hàng BIDC',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/bidc_logo.png',
    },
    {
      bankCode: 'LAOVIETBANK',
      name: 'NGÂN HÀNG LIÊN DOANH LÀO - VIỆT',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/laovietbank_logo.png',
    },
    {
      bankCode: 'WOORIBANK',
      name: 'Ngân hàng TNHH MTV Woori Việt Nam',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/woori_logo.png',
    },
    {
      bankCode: 'AMEX',
      name: 'American Express',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/amex_logo.png',
    },
    {
      bankCode: 'VISA',
      name: 'Thẻ quốc tế Visa',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/visa_logo.png',
    },
    {
      bankCode: 'MASTERCARD',
      name: 'Thẻ quốc tế MasterCard',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/mastercard_logo.png',
    },
    {
      bankCode: 'JCB',
      name: 'Thẻ quốc tế JCB',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/jcb_logo.png',
    },
    {
      bankCode: 'UPI',
      name: 'UnionPay International',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/upi_logo.png',
    },
    {
      bankCode: 'VNMART',
      name: 'Ví điện tử VnMart',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/vnmart_logo.png',
    },
    {
      bankCode: 'VNPAYQR',
      name: 'Cổng thanh toán VNPAYQR',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/CTT-VNPAY-QR.png',
    },
    {
      bankCode: '1PAY',
      name: 'Ví điện tử 1Pay',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/1pay_logo.png',
    },
    {
      bankCode: 'FOXPAY',
      name: 'Ví điện tử FOXPAY',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/foxpay_logo.png',
    },
    {
      bankCode: 'VIMASS',
      name: 'Ví điện tử Vimass',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/vimass_logo.png',
    },
    {
      bankCode: 'VINID',
      name: 'Ví điện tử VINID',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/vinid_logo.png',
    },
    {
      bankCode: 'VIVIET',
      name: 'Ví điện tử Ví Việt',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/viviet_logo.png',
    },
    {
      bankCode: 'VNPTPAY',
      name: 'Ví điện tử VNPTPAY',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/vnptpay_logo.png',
    },
    {
      bankCode: 'YOLO',
      name: 'Ví điện tử YOLO',
      logo: 'https://sandbox.vnpayment.vn/apis/assets/images/bank/yolo_logo.png',
    },
  ]
  // this.state = {
  //   search: '',
  //   bankCode: this.bankCode,
  //   nganhangs: this.nganhangs
  // }

  const [search, setSearch] = useState('')
  const [nganhangs, setNganhangs] = useState(data)

  const renderItemBank = (item, index) => {
    if (search.trim().length > 0) {
      const keyPare = removeUtf8(search.trim())
      if (!removeUtf8(item.name).includes(keyPare) && !removeUtf8(item.bankCode).includes(keyPare)) {
        return null
      }
    }
    return (<TouchableOpacity key={index} onPress={() => {
      props.onSelect(item.bankCode)
      // props.oncloseModal()
    }} style={styles.containerDs}>
      <View style={{ width: '100%', flexDirection: 'column' }}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', height: 26 }}>
          <Text style={styles.selectPiker}>{item.bankCode}</Text>
        </View>
        <Text style={styles.dsViewText}>{item.name}</Text>
      </View>

    </TouchableOpacity>)
  }

  return (
    <View style={styles.sAv}>
      <View style={styles.sAvView}>
        <View style={styles.sAvView2}>
          <TouchableOpacity style={styles.textIP}>
            <Icon name={'search-outline'} size={24} color={'#c5cee0'}/>
          </TouchableOpacity>
          <TextInput
            placeholder={t('SEARCH')}
            placeholderTextColor='#46474D'
            underlineColorAndroid="transparent"
            defaultValue={search}
            onChangeText={(e) => setSearch(e)}
            style={styles.input}
          />
        </View>
        {nganhangs.map((item, index) => renderItemBank(item, index))}
      </View>

    </View>
  )
}

)
