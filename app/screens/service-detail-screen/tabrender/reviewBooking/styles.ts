import { StyleSheet, Dimensions } from 'react-native'
import { ifIphoneX } from 'react-native-iphone-x-helper'
import { color, typography } from '../../../../theme'

const { width, height } = Dimensions.get('window')
const styles = StyleSheet.create({
  boxAddress: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    paddingBottom: 12,
    paddingLeft: 11,
  },
  boxCheckInTime: {
    flex: 1,
    flexDirection: 'column'
  },
  boxContact: {
    backgroundColor: '#fff',
    borderRadius: 8,
    elevation: 2,
    height: 125,
    marginBottom: 20,
    marginLeft: 15,
    marginRight: 10,
    marginTop: 24,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10,
    width: 288
  },
  boxService: {
    backgroundColor: '#fff',
    borderBottomColor: '#f6f6f7',
    flexDirection: 'row',
    marginHorizontal: 15,
    marginVertical: 16
  },
  boxViewAddress: {
    backgroundColor: '#ffffff',
    marginBottom: 5,
    // borderRadius: 8,
    // elevation: 2,
    // marginBottom: 26,
    // marginHorizontal: 15,
    // marginVertical: 20,
    // shadowColor: 'rgba(85, 85, 85, 0.1)',
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 1,
    // shadowRadius: 10
  },
  boxViewPhone: {
    flexDirection: 'row',
  },
  btnSubmit: {
    alignContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ff8ba1',
    borderRadius: 8,
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold',
    height: 44,
    justifyContent: 'center',
    letterSpacing: 0,
    marginTop: 26,
    textAlign: 'center'
  },
  containerCoupon: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  containerList: {
    marginBottom: 89,
    marginLeft: 15,
    marginTop: 15
  },
  containerServiceChoose: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    marginBottom: 1,
    padding: 15
  },
  contentCash: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    marginLeft: 15,
  },
  contentDateTime: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginLeft: 15
  },
  contentSelectPayment: {
    color: color.primary,
    // fontFamily: typography.normal,
    fontSize: 14,
  },
  contentText: {
    alignItems: 'flex-start',
    flex: 1,
    position: 'relative',
  },
  couponText: {
    color: 'rgba(0, 0, 0, 0.85)',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 15,
    textAlign: 'center'
  },
  dateTime: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginLeft: 10,
  },
  icArrowBack: {
    marginLeft: 10,
  },
  icLocation: {
    marginTop: 2
  },
  imageChoose: {
    height: 80,
    width: 80
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
    ...ifIphoneX({
      marginTop: 0
    }, {
      marginTop: 5
    }),
  },
  noteContent: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '400',
    paddingHorizontal: 15,
    textAlign: 'left'
  },
  orderNo: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    paddingHorizontal: 15,
    paddingVertical: 20
  },
  price: {
    color: color.primary,
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    marginHorizontal: 15,
    marginVertical: 10,
  },
  rdkTopImage: {
    borderRadius: 8,
    height: 50,
    resizeMode: 'cover',
    width: 50
  },
  rdkTopText: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0,
    paddingBottom: 10
  },
  rdkTopText1: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0
  },
  renderItemListAddress: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 12,
    paddingLeft: 13,
    paddingRight: 13,
  },
  renderItemListPhone: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 18,
    paddingLeft: 13,
    paddingRight: 13,
  },
  renderItemListPrice: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  selectBank: {
    flex: 1,
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginTop: 15,
    alignItems: 'center'
  },
  selectPayment: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
  },
  serviceContent: {
    flex: 1,
    paddingHorizontal: 10
  },
  textAddressBranch: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    marginHorizontal: 7,
    width: '90%'
  },
  textAddressPhone: {
    alignSelf: 'center',
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    marginLeft: 7
  },
  textContent: {
    color: color.primary,
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600'
  },
  textLabel: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    paddingHorizontal: 15,
    paddingVertical: 20
  },
  textLabelPayment: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginLeft: 15
  },
  textName: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    marginTop: 12,
  },
  textPrice_Count: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0,
    marginHorizontal: 15,
    marginVertical: 10,
  },
  textPrice_Shipping: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginHorizontal: 15,
    marginVertical: 10
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#979797',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',

  },
  textTitleService: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
  },
  title: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
    letterSpacing: 0,
    paddingHorizontal: 15,
    paddingVertical: 20
  },
  titleChildLabel: {
    color: '#979797',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginHorizontal: 15,
    marginVertical: 10
  },
  titleLabel: {
    color: '#979797',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '500',
    paddingHorizontal: 15,
    paddingVertical: 20,
    textTransform: 'uppercase'
  },
  titleLabelBranch: {
    color: '#979797',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '500',
    paddingBottom: 10,
    paddingHorizontal: 15,
    paddingTop: 20,
    textTransform: 'uppercase'
  },
  totalMoney: {
    color: '#333',
    fontWeight: '600',
    letterSpacing: 0,
    marginHorizontal: 15,
    marginVertical: 10
  },
  totalMoneyContent: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  viewBtn: {
    backgroundColor: '#fff',
    // borderTopColor: '#eee',
    // borderTopWidth: 1,
    // bottom: 8,
    marginHorizontal: 15,
    // position: 'absolute',
    paddingVertical: 8,
    width: Dimensions.get('window').width - 30
  },
  viewBtnBook: {
    backgroundColor: '#fff',
    borderTopColor: '#eee',
    borderTopWidth: 1,
  },
  viewBtnItem: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  viewFlatList: {
    backgroundColor: color.primaryBackground,
    flex: 1,
    paddingBottom: 60
  },
  viewImageService: {
    alignItems: 'center',
    backgroundColor: color.primaryBackground,
    justifyContent: 'center',
    padding: 2
  },
  viewLabel: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 10,
  },
  viewOrderCode: {
    backgroundColor: '#fff',
    marginBottom: 5
  },
  viewPayment: {
    backgroundColor: '#fff',
    marginVertical: 5,
    // paddingLeft: 8,
    paddingBottom: 15
  },
  viewPrice: {
    backgroundColor: '#fff',
  },
  viewService: {
    flexDirection: 'column',
    flex: 1,
    marginLeft: 10,
    justifyContent: 'space-between'
  },
  viewTextLabel: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flexDirection: 'row',
    height: 30,
  },
  viewTextLabelFooter: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flexDirection: 'row',
    height: 30,
    marginTop: 15
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },

})
export default styles
