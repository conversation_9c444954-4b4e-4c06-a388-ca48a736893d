import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import React, { useContext, useEffect, useRef, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { TButton, Text, LazyImage, ButtonBack } from '../../../../components'
import { FlatList, TouchableOpacity, View } from 'react-native'
import { StackActions, useNavigation, useRoute } from '@react-navigation/native'
import styles from './styles'
import Icon from 'react-native-vector-icons/Ionicons'
import { useStores } from '../../../../models/root-store'
import { useTranslation } from 'react-i18next'
import { ModalContext } from '@app/context'
import { SCREENS } from '../../../../navigation'
import { Modalize } from 'react-native-modalize'
import { responsiveHeight } from 'react-native-responsive-dimensions'
import { Payment } from '../../..'
import _ from 'lodash'
import { Payments } from '../../../../components/payments/payments'
import database from '@react-native-firebase/database'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import { color } from '@app/theme'
import LinearGradient from 'react-native-linear-gradient'

const numeral = require('numeral')

export const ReviewBooking = observer((props) => {
  const { t } : any = useTranslation()
  const { goBack } = useNavigation()
  const route = useRoute()
  const { bookingData } = route.params
  const { profileStore, bookingStore, serviceStore } = useStores()
  const navigation = useNavigation()
  const { showError, showSuccess } = useContext(ModalContext)
  const modalizeBankList = useRef<Modalize>(null)
  const [bankCode, setBankCode] = useState('')
  const [isSubmitting, setSubmitting] = useState(false)
  const [selectPayment, setSelectPayment] = useState(1)

  // useEffect(() => {
  //   if (selectPayment === 0) {
  //     onOpen()
  //   }
  // }, [selectPayment])

  // useEffect(() => {
  //   if (bankCode && selectPayment === 0) {
  //     onSubmit()
  //   } else if (!bankCode && selectPayment === 1) {
  //
  //   }
  // }, [bankCode])

  const formatMoney = (value) => {
    return numeral(value).format('0,0')
  }

  const onBack = () => {
    goBack()
  }

  const onOpen = () => {
    modalizeBankList.current?.open()
  }
  const onClose = () => {
    modalizeBankList.current?.close()
  }

  const renderHeader = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onClose} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>Vui lòng chọn hình thức thanh toán</Text>
    </View>
  )

  const onSelectBank = (item) => {
    __DEV__ && console.log('bankCode Select', item)
    setBankCode(item)
    onClose()
  }

  useEffect(() => {
    bookingStore.setOrderId(generateOrderNo())
  }, [])

  const generateOrderNo = () => {
    const date = new Date()
    const components = [
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      date.getHours(),
      date.getMinutes(),
      date.getSeconds()
    ]
    return components.join('')
  }

  const clearField = () => {
    bookingStore.setGeneralCoupon('')
    bookingStore.setCoupon('')
    bookingStore.setValueCoupon(0)
    bookingStore.setValueCouponG(0)
  }

  // LISTEN CALL BACK IPN PAYMENT VIA QR CODE
  useEffect(() => {
    if (!bookingStore.orderId) {
      return
    }
    const path = `payment/vnpay/${bookingStore.orderId}`
    const onValueChange = database()
      .ref(path)
      .on('value', snapshot => {
        const data = snapshot.val()
        const responsCode = data?.vnp_ResponseCode
        // console.log('orderId data: ', snapshot.val())
        if (data?.vnp_TxnRef == bookingStore.orderId && responsCode == '00') {
          goBookingDetail(bookingStore.orderId)
        }
      })

    // Stop listening for updates when no longer required
    return () =>
      database()
        .ref(path)
        .off('value', onValueChange)
  }, [bookingStore.orderId])

  const goBookingDetail = (orderId) => {
    bookingStore.clearCart()
    goBack()
    navigation.dispatch(
      StackActions.replace(SCREENS.bookingHistoryDetail, { orderId: orderId, bookingType: serviceStore.typeBooking, action: 'order' })
    )
    showSuccess(t('THANHCONG'), t('BOOKING_SPA_OK'))
    bookingStore.setBookingStatus(true)
  }

  const onSubmit = async () => {
    setSubmitting(true)
    const dataOrder = bookingData
    _.assign(dataOrder, { bankCode }) // bankCode selected
    _.assign(dataOrder, { orderId: bookingStore.orderId }) // orderNo
    if (selectPayment === 0 && !bankCode) {
      // TODO: translate
      // showError(t('FAIL'), 'Bạn chưa chọn ngân hàng thanh toán')
      onOpen()
      setSubmitting(false)
    } else if (bankCode && selectPayment === 0) {
      bookingStore.bookingService(dataOrder).then(rs => {
        if (rs && rs.data.error) {
          showError(t('FAIL'), `${rs.data.message}`)
          setSubmitting(false)
        } else if (rs.data.data.vnpUrl) {
          bookingStore.clearCart()
          goBack()
          navigation.navigate(SCREENS.payment, { vnpUrl: rs.data.data.vnpUrl, orderId: bookingStore.orderId, typeBooking: serviceStore.typeBooking })
          bookingStore.setBookingStatus(true)
          setSubmitting(false)
          // showSuccess(t('THANHCONG'), t('BOOKING_SPA_OK'))
          // setTimeout(
          //   onBack, 3000,
          // )
        }
      }).catch(err => {
        showError(t('FAIL'), 'unknown')
        setSubmitting(false)
        console.error(err)
      })
    } else if (selectPayment === 1) {
      // THANH TOAN TIEN MAT
      _.assign(dataOrder, { bankCode: 'TM' }) // bankCode selected
      bookingStore.bookingService(bookingData).then(rs => {
        if (rs && rs.data?.error) {
          showError(t('FAIL'), `${rs.data.message}`)
          setSubmitting(false)
        } else {
          goBookingDetail(bookingStore.orderId)
          setTimeout(() => {
            clearField()
          }, 200)
          setSubmitting(false)
        }
      }).catch(err => {
        showError(t('FAIL'), 'unknown')
        console.error(err)
        setSubmitting(false)
      })
    } else {
      showError(t('FAIL'), 'unknown')
      setSubmitting(false)
    }
  }
  const renderCart = ({ item, index }) => {
    return (
      <View style={styles.containerServiceChoose}>
        <View>
          <View style={styles.viewImageService}>
            <LazyImage style={styles.imageChoose} source={{ uri: item.image }} resizeMode="cover"/>
          </View>
        </View>
        <View style={styles.viewService}>
          <View>
            <Text numberOfLines={1} style={styles.textTitleService}>{item.serviceName}</Text>
          </View>
          <Text style={{ color: '#9d9d9d', fontSize: 12, fontWeight: '400' }}>{item.shortDes}</Text>
          <View style={styles.viewBtnItem}>
            {Number(item?.price > 0) ? <Text style={styles.textContent}>{formatMoney(item?.price)} đ</Text> : null}
          </View>
        </View>
      </View>
    )
  }

  const ListHeader = () => {
    return (
      <View>
        <View style={styles.viewOrderCode}>
          <Text style={styles.orderNo}>Mã dịch vụ: #{bookingStore.orderId}</Text>
        </View>
        <View style={styles.boxViewAddress}>
          <Text style={styles.titleLabelBranch}>{t('BRANCH')}</Text>
          <TouchableOpacity>
            <View style={styles.renderItemListAddress}>
              <Text style={styles.textName}>{bookingData.branchName}</Text>
            </View>
          </TouchableOpacity>
          <View style={styles.boxAddress}>
            <Icon style={styles.icLocation} name={'location-outline'} size={22} color={color.primary}/>
            <Text numberOfLines={2} style={styles.textAddressBranch}>{bookingData.branchAddress}</Text>
          </View>
          <View style={styles.renderItemListPhone}>
            <View style={styles.boxViewPhone}>
              <Icon name={'call-outline'} size={22} color={color.primary}/>
              <Text style={styles.textAddressPhone}>{bookingData.branchPhone}</Text>
            </View>
          </View>
        </View>
        <View style={styles.viewTextLabel}>
          <Text style={styles.titleLabel}>{t('CHITIETLICHHEN_dichvudachon')}</Text>
        </View>
      </View>
    )
  }

  const ListFooter = () => {
    return (
      <View>
        <View style={{ backgroundColor: '#fff', marginBottom: 5, marginTop: 4, paddingBottom: 20 }}>
          <Text style={styles.titleLabel}>{t('NOTE')}</Text>
          <Text style={styles.noteContent}>{bookingData.note}</Text>
        </View>
        { Number(bookingData.price) > 0
          ? <View style={styles.viewPrice}>
            <View style={styles.viewTextLabel}>
              <Text style={styles.titleLabel}>{t('TOTAL_MONEY')}</Text>
            </View>
            <View style={{ paddingVertical: 15 }}>
              <View style={styles.totalMoneyContent}>
                <Text style={styles.titleChildLabel}>{t('CHITIETLICHHEN_giadichvu')}</Text>
                <Text style={styles.price}>{formatMoney(bookingData.totalServicePrice)} <Text style={[styles.price, { textDecorationLine: 'underline' }]}>đ</Text></Text>
              </View>
              <View style={styles.totalMoneyContent}>
                <Text style={styles.titleChildLabel}>{t('CHIPHI_VANCHUYEN')}</Text>
                <Text style={styles.price}>0 <Text style={[styles.price, { textDecorationLine: 'underline' }]}>đ</Text></Text>
              </View>
              {bookingData.valueCoupon > 0 ? <View style={styles.renderItemListPrice}>
                <View style={styles.containerCoupon}>
                  <Text style={styles.titleChildLabel}>{t('Voucher shop')}</Text>
                  {bookingData.coupon
                    ? <Text style={styles.price}>{bookingData.coupon}
                    </Text>
                    : null}
                </View>
                <Text style={styles.price}> - {bookingData.valueCoupon ? formatMoney(bookingData.valueCoupon) : 0} <Text style={[styles.price, { textDecorationLine: 'underline' }]}>đ</Text></Text>
              </View> : null}
              {bookingData.valueCouponG > 0 ? <View style={styles.renderItemListPrice}>
                <View style={styles.containerCoupon}>
                  <Text style={styles.titleChildLabel}>{t('Voucher')}</Text>
                  {bookingData.gCoupon
                    ? <Text style={styles.price}>{bookingData.gCoupon}
                    </Text>
                    : null}
                </View>
                <Text style={styles.price}> - {bookingData.valueCouponG ? formatMoney(bookingData.valueCouponG) : 0} <Text style={[styles.price, { textDecorationLine: 'underline' }]}>đ</Text></Text>
              </View> : null}
              <View style={styles.renderItemListPrice}>
                <Text style={styles.titleChildLabel}>{t('HAVETOPAY')}</Text>
                <Text style={styles.price}>{formatMoney(bookingData.price)} <Text style={[styles.price, { textDecorationLine: 'underline' }]}>đ</Text></Text>
              </View>
              {/* <View style={styles.totalMoneyContent}> */}
              {/*  <Text style={styles.titleChildLabel}>{t('TOTAL_MONEY')}</Text> */}
              {/*  <Text style={styles.price}>{formatMoney(bookingData.price)} <Text style={[styles.price, { textDecorationLine: 'underline' }]}>đ</Text></Text> */}
              {/* </View> */}
            </View>
            {/* <View style={styles.viewTextLabel}> */}
            {/*  <Text style={styles.textLabel}>{t('PHUONGTHUC_THANHTOAN')}</Text> */}
            {/* </View> */}
            {/* <Payments */}
            {/*  defaultPayment={selectPayment} */}
            {/*  onPress={(item) => { */}
            {/*    setSelectPayment(item) */}
            {/*  }} */}
            {/* /> */}
            {/* {selectPayment === 1 ? <View> */}
            {/*  <View style={styles.selectBank}><Text style={styles.contentCash}>{t('PAYMENT_CASH')}</Text></View> */}
            {/* </View> : null} */}
          </View> : null}
        <View style={styles.viewPayment}>
          <Text style={styles.titleLabel}>{t('PHUONGTHUC_THANHTOAN')}</Text>
          <Payments
            defaultPayment={selectPayment}
            onPress={(item) => {
              setSelectPayment(item)
              if (item === 0) {
                onOpen()
              }
              console.log('item--->', item)
            }}
          />
          {selectPayment === 1 ? <View>
            <View style={styles.selectBank}><Text style={styles.contentCash}>{t('PAYMENT_CASH')}</Text></View>
          </View> : <TouchableOpacity style={styles.selectBank}
            onPress={() => {
              onOpen()
            }}>
            <Text style={styles.contentSelectPayment}>{t('CHON_NGAN_HANG')}</Text>
            <View style={styles.dateTime}>
              {bankCode ? <Text style={styles.selectPayment}>{bankCode}</Text> : null}
            </View>
          </TouchableOpacity>}
        </View>
      </View>
    )
  }

  const RenderPaymentMethod = () => {
    return (
      <View style={{ position: 'absolute', bottom: useSafeAreaInsets().bottom }}>
        <View style={styles.viewBtnBook}>
          {/* <Payments */}
          {/*  defaultPayment={selectPayment} */}
          {/*  onPress={(item) => { */}
          {/*    setSelectPayment(item) */}
          {/*    if (item === 0) { */}
          {/*      onOpen() */}
          {/*    } */}
          {/*    console.log('item--->', item) */}
          {/*  }} */}
          {/* /> */}
          {/* {selectPayment === 1 ? <View> */}
          {/*  <View style={styles.selectBank}><Text style={styles.contentCash}>{t('PAYMENT_CASH')}</Text></View> */}
          {/* </View> : null} */}
          <View style={styles.viewBtn} >
            <TButton typeRadius={'rounded'} loading={isSubmitting} disabled={isSubmitting} title={t('BOOKING') + ' ' + `- ${formatMoney(bookingData.price)} đ`} onPress={onSubmit} />
          </View>
        </View>
      </View>
    )
  }

  return (
    <>
      <SafeAreaView style={{
        backgroundColor: '#fff',
        flex: 1,
        marginTop: -4
      }} edges={['right', 'top', 'left']}>
        <Header
          leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
          centerComponent={{ text: t(t('CHITIET')), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
          containerStyle={common.headerContainer}
          statusBarProps={{ barStyle: 'light-content' }}
          ViewComponent={LinearGradient}
          linearGradientProps={linearGradientProps}
        />
        <View style={[styles.viewFlatList, { paddingBottom: 60 + useSafeAreaInsets().bottom }]}>
          <FlatList
            showsVerticalScrollIndicator={false}
            data={bookingStore.cart}
            renderItem={renderCart}
            keyExtractor={(item, index) => item._id}
            ListHeaderComponent={ListHeader()}
            ListFooterComponent={ListFooter()}
          />
        </View>
        {RenderPaymentMethod()}
        <Modalize
          HeaderComponent={renderHeader}
          ref={modalizeBankList}
          modalHeight={responsiveHeight(85)}
          keyboardAvoidingBehavior={'padding'}
        >
          {/* {renderListPayment()} */}
          <View style={{ marginBottom: 60 }}>
            <Payment onSelect={onSelectBank}/>
          </View>
        </Modalize>
      </SafeAreaView>
    </>
  )
})
