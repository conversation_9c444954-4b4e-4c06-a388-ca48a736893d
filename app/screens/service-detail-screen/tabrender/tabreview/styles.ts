import { StyleSheet } from 'react-native'
import { ifIphoneX } from 'react-native-iphone-x-helper'
import { typography } from '../../../../theme'

const styles = StyleSheet.create({
  boxviewImage: {
    width: 50,
  },
  container: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 1,
    paddingHorizontal: 16,
    paddingVertical: 15
  },
  containerComment: {
    flex: 1,
    marginLeft: 8,
    // flexDirection: "column"
  },
  containerPadding: { marginBottom: 89 },
  customStar: {
    marginRight: 4,
    marginTop: 3,
  },
  imageUser: {
    borderRadius: 22,
    height: 44,
    width: 44,
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
    ...ifIphoneX({
      marginTop: 15
    }, {
      marginTop: 5
    }),
  },
  textContent: {
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
  },
  textTime: {
    color: '#c5cee0',
    fontFamily: typography.normal,
    fontSize: 12,
    justifyContent: 'flex-end',
    letterSpacing: 0,
    textAlign: 'right',
  },
  textTitle: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',

  },
  txtEdit: {
    color: '#333',
    fontSize: 14,
    marginHorizontal: 15,
    marginVertical: 15
  },
  viewIcon: {
    alignItems: 'flex-end',
    flex: 1,
    justifyContent: 'flex-end',
  },
  viewImage: {
    alignItems: 'center',
    flex: 1,
    height: 44,
    width: 44,
  },
  viewService: {
    flexDirection: 'column',
    marginTop: 17,
  },
  viewStar: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },

})
export default styles
