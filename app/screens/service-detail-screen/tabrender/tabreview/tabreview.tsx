import React from 'react'
import {
  View,
  Text, TouchableOpacity,
} from 'react-native'
import styles from './styles'
import StarRating from 'react-native-star-rating'
import moment from 'moment'
import 'moment/locale/vi'

moment.locale('vi')
const renderTabReview = ({ item, index }) => {
  return (
    <View style={styles.container}>
      {/* <View style={styles.boxviewImage}> */}
      {/*  <TouchableOpacity> */}
      {/*    <View style={styles.viewImage}> */}
      {/*      <LazyImage */}
      {/*        type='avatar' */}
      {/*        resizeMode="cover" */}
      {/*        style={styles.imageUser} */}
      {/*        source={{ uri: item.image }} */}
      {/*      /> */}
      {/*    </View> */}
      {/*  </TouchableOpacity> */}
      {/* </View> */}
      <View style={styles.containerComment}>
        <View>
          <TouchableOpacity>
            <Text style={styles.textTitle}>{item.name}</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.viewStar}>
          {item.rate != 0
            ? <StarRating
              fullStarColor={'#ff8900'}
              disabled={true}
              maxStars={5}
              rating={item.rate}
              emptyStarColor={'#edf1f7'}
              emptyStar={'star'}
              fullStar={'star'}
              halfStar={'star-half-o'}
              iconSet={'FontAwesome'}
              starSize={12}
              starStyle={styles.customStar}
            /> : null}
          <View style={{ flex: 1 }}>
            <Text style={styles.textTime}>{moment(item.modifyAt).locale('vi').fromNow()}</Text>
          </View>
        </View>
        {item.content ? <View style={styles.viewService}>
          <Text style={styles.textContent}>{item.content}</Text>
        </View> : null}
      </View>
    </View>
  )
}
export default renderTabReview
