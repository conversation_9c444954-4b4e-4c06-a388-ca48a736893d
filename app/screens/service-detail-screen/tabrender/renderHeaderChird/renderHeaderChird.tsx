import { Dimensions, FlatList, Text, TouchableOpacity, View } from 'react-native'
import styles from '../../styles'
import React, { useEffect, useState } from 'react'
import Icon from 'react-native-vector-icons/MaterialCommunityIcons'
import { useNavigation } from '@react-navigation/native'
import { useStores } from '@app/models'
import { observer } from 'mobx-react-lite'

import Carousel from 'react-native-snap-carousel'
import { useTranslation } from 'react-i18next'
import { LazyImage } from '@app/components'
import { color } from '@app/theme'
import { SCREENS } from '@app/navigation'
import { IconCuuHo } from '@app/assets/icons'

const HEAD_HEIGHT = 275
const G_WIN_WIDTH = Dimensions.get('window').width

export interface RenderHeaderChirdProps {
  storeId?: any,
  name: any,
  totalRateValue: any,
  totalComment: any,
  onOpenModalBranch?: any,
}

const RenderHeaderChird: any = observer((props: RenderHeaderChirdProps) => {
  const { t } : any = useTranslation()
  const navigation = useNavigation()
  const { serviceStore, bookingStore } = useStores()
  const { storeId } = props
  const [index, setIndex] = useState(0)
  const [dataBranch, setDataBranch] = useState([])
  // const { sendJsonMessage, currentMessage, user, createDirectMessage } = useContext(WebSocketContext)
  // useAbortableEffect(() => {
  //   loadData().then(r => {
  //   })
  // }, [])

  useEffect(() => {
    // if (bookingStore.branchStore && bookingStore.branchStore.length > 0) {
    //   if (bookingStore.branchStore.length > 3) {
    //     setDataBranch(bookingStore.branchStore.slice(0, 2))
    //   } else {
    //     setDataBranch(bookingStore.branchStore)
    //   }
    // }
    setDataBranch(bookingStore.branchStore)
  }, [bookingStore.branchStore])

  const renderPagination = () => {
    // __DEV__ && console.log('renderPagination-----')
    const total = serviceStore.bannerStore.length
    return (
      <View style={styles.paginationStyle}>
        <Text style={styles.paginationText}>{index + 1} / {total}</Text>
      </View>
    )
  }

  const _renderCarouselItem = ({ item, index }: any) => {
    return (
      <View style={styles.slide}>
        <LazyImage
          style={styles.image}
          resizeMode={'cover'}
          source={{ uri: item.image }}
        />
      </View>
    )
  }
  const renderBranchAddress = ({ item }) => {
    return (
      <View style={styles.viewIconLeft}>
        <View style={{ flexDirection: 'row', marginTop: 10, width: '100%', minHeight: 40 }}>
          {/* <Image style={styles.icLocation} source={IcMap}/> */}
          <View style={{ position: 'absolute', left: 0, top: 2 }}>
            <Icon name={'map-marker-radius-outline'} size={20} color={color.primary}/>
            <Text style={{ color: color.primary, fontSize: 12 }}>{item?.distance} km</Text>
          </View>
          <Text style={styles.textAdress}>{item?.address} </Text>
          {/* <Text style={styles.textPoinLocation}>{item?.hotline}</Text> */}
        </View>
        {/* <Text style={styles.textAdress}>{item.address}</Text> */}
      </View>
    )
  }

  const ServiceInfo = () => {
    return (
      <View style={styles.viewInfo}>
        <Text numberOfLines={1} style={styles.textTitle}>{props?.name}</Text>
        { !bookingStore.branchStore || bookingStore.branchStore.length === 0 ? <View style={styles.viewIconLeft}>
          <Icon name={'map-marker-radius-outline'} size={20} color={color.primary}/>
          <Text numberOfLines={2} style={styles.textAdress}>{serviceStore.adress}</Text>
        </View> : <View>
          <FlatList
            showsVerticalScrollIndicator={false}
            data={dataBranch}
            renderItem={renderBranchAddress}
            keyExtractor={(item, index) => item._id}
            // ListFooterComponent={() => (
            //   <TouchableOpacity
            //     onPress={props.onOpenModalBranch}>
            //     <Text style={styles.seeMoreAddress}>{t('See_map')}</Text>
            //   </TouchableOpacity>
            // )}
          />
        </View>}
        <View style={[styles.viewIconLeft, { paddingTop: 10 }]}>
          <Icon
            size={18}
            name={'alarm'}
            color={color.primary}
          />
          <Text style={styles.timeOpen}>{serviceStore?.timeOpen} - {serviceStore?.timeClose}</Text>
        </View>
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginTop: 15 }}>
          <TouchableOpacity style={styles.seeMoreAddress}
            onPress={props.onOpenModalBranch}>
            <Text style={{ color: '#fff', fontSize: 12 }}>{t('See_map')}</Text>
          </TouchableOpacity>
          <TouchableOpacity style={{ flexDirection: 'row', alignItems: 'center' }} onPress={props.onOpenModalBranch}>
            <IconCuuHo/>
            <Text style={{ fontSize: 12, marginLeft: 4, color: color.primary }}>Cứu hộ ngay</Text>
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  const gotoTotalComment = () => navigation.navigate(SCREENS.totalComment, { storeId: storeId, serviceStore: serviceStore })

  return (
    <View style={styles.container}>
      <View style={styles.containerSwiper}>
        <View>
          <View style={{ height: HEAD_HEIGHT }}>
            <Carousel
              layout={'default'}
              data={serviceStore.bannerStore}
              renderItem={_renderCarouselItem}
              sliderWidth={G_WIN_WIDTH}
              itemWidth={G_WIN_WIDTH}
              inactiveSlideScale={1}
              inactiveSlideOpacity={1}
              loop={true}
              autoplay={true}
              autoplayDelay={500}
              autoplayInterval={6000}
              onSnapToItem={(index) => setIndex(index)}
            />
            {renderPagination()}
          </View>
        </View>
      </View>
      {ServiceInfo()}
      <View style={styles.review}>
        {/* <Text style={styles.titleLabel}>Đánh giá dịch vụ</Text> */}
        <View style={styles.viewIconLeft}>
          <Icon
            size={14}
            name={'star-outline'}
            color={'#e9a71d'}
          />
          <Text style={styles.textPoin}>{props.totalRateValue}</Text>
          <Text style={styles.textCountrate}>({props.totalComment} {t('REVIEW')})</Text>
        </View>
        <TouchableOpacity onPress={() => gotoTotalComment()}><Text style={styles.textBtnViewDetail}>Chi tiết đánh giá</Text></TouchableOpacity>
      </View>
    </View>
  )
})
export default RenderHeaderChird
