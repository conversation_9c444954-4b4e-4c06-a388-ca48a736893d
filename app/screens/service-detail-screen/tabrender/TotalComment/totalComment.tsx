import { SafeAreaView } from 'react-native-safe-area-context'
import React, { useState, useEffect, useRef, useContext } from 'react'
import {

  View,
  Text,
  TouchableOpacity, FlatList, Alert,
} from 'react-native'
import styles from './styles'
import { useTranslation } from 'react-i18next'
import StarRating from 'react-native-star-rating'
import { useNavigation, useRoute } from '@react-navigation/native'
import { useStores } from '@app/models'
import moment from 'moment'

import { observer } from 'mobx-react-lite'

import { PlaceHolder, LazyImage, ButtonBack, EmptyData } from '@app/components'
import { Modalize } from 'react-native-modalize'
import EditComment from '../Comment/Edit-Comment'
import { ModalContext } from '@app/context'
import { SCREENS } from '@app/navigation'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import RenderStar from '@app/screens/service-detail-screen/tabrender/RenderStar/renderStar'
import { color } from '@app/theme'
import LinearGradient from 'react-native-linear-gradient'

export const RenderTotalComment = observer((props) => {
  const { t } : any = useTranslation()
  const modalizeRef = useRef<Modalize>(null)
  const modalizeRefEdit = useRef<Modalize>(null)
  const [data, setData] = useState([])
  const [refreshing, setRefreshing] = useState(false)
  const [loadMore, setLoadMore] = useState(false)
  const [page, setPage] = useState(1)
  const [totalPage, setTotalPage] = useState(0)
  const route = useRoute()
  const { storeId } = route.params
  const { commentStore, profileStore, serviceStore } = useStores()
  const [isFetched, setIsFetched] = useState(true) // event view placeholder
  const [idComment, setIdComment] = useState('')
  const [idUserComment, setIdUserComment] = useState('')
  const [item, setItem] = useState(null)
  const { showError, showSuccess } = useContext(ModalContext)
  const { navigate, goBack } = useNavigation()
  //   commentStore.getMoreCommentsListById(storeId, page, false)
  //   setTotalPage(commentStore.totalPage) // set total page
  //   setData(commentStore.dataComment)
  //   setLoading(false)
  //   setRefreshing(false)
  // }, [])

  useEffect(() => {
    loadData().then(r => {
    })
  }, [page])

  useEffect(() => {
    // __DEV__ && console.log('useEffect handleLoadMore', page)
    handleLoadMore()
  }, [page, loadMore])
  /**
   * call Store
   */
  const loadData = async () => {
    const isLoadMore = page > 1
    await commentStore.getMoreCommentsListById(storeId, page, isLoadMore)
    setTotalPage(commentStore.totalPage) // set total page
    setData(commentStore.dataComment)
    setIsFetched(false)
    // setLoading(false)
    setRefreshing(false)
  }

  const goLoginScreenRequired = () => {
    navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  const onOpen = (item) => {
    setIdComment(item.id)
    setItem(item)
    setIdUserComment(item.userIdComment)
    if (profileStore.isSignedIn()) {
      modalizeRef.current?.open()
    } else {
      goLoginScreenRequired()
    }
  }
  const onOpenEdit = () => {
    modalizeRefEdit.current?.open()
  }
  const onCloseEdit = () => {
    modalizeRefEdit.current?.close()
  }
  const deleteComment = () => {
    Alert.alert(
      t('THANHTOAN_alert_thongbao'),
      t('ALERT_DELETE'),
      [{ text: t('CANCEL'), onPress: () => { onClose() } }, {
        text: t('DONGY'),
        onPress: async () => {
          await commentStore.deleteComment(idComment)
          loadData().then(r => {
          })
          onClose()
        },
      }], { cancelable: true },
    )
  }
  const report = () => {
    Alert.alert(
      t('THANHTOAN_alert_thongbao'),
      t('REPORT_COMMENT'),
      [{ text: t('CANCEL'), onPress: () => { onClose() } }, {
        text: t('DONGY'),
        onPress: () => {
          onRefresh()
          onClose()
        },
      }], { cancelable: true },
    )
  }
  const onClose = () => {
    modalizeRef.current?.close()
  }

  const refreshData = async () => {
    setIsFetched(true)
    await commentStore.getMoreCommentsListById(storeId, 1, false)
    setTotalPage(commentStore.totalPage) // set total page
    setData(commentStore.dataComment)
    // setLoading(false)
    setRefreshing(false)
    setIsFetched(false)
  }

  /**
   * onRefresh
   */
  const onRefresh = () => {
    __DEV__ && console.log('onRefresh ', page)
    setRefreshing(true)
    if (page > 1) {
      setPage(1)
    } else {
      refreshData().then(r => {
      })
    }
  }
  /**
   * onLoadMore Data
   */
  const handleLoadMore = () => {
    if (!loadMore) return
    if (page < totalPage) {
      setPage(page + 1)
      setLoadMore(false)
    }
    if (page === totalPage) {
      setLoadMore(false)
    }
  }
  /**
   * render Footer UI
   */

  const renderFooter = () => {
    const Spinner = require('react-native-spinkit')
    return loadMore === true ? (
      <View
        style={{
          marginTop: 10,
          alignItems: 'center',
        }}
      >
        <Spinner isVisible={true} size={40} type='ThreeBounce' color={color.primary}/>
      </View>
    ) : <Text style={{ padding: 16, color: color.primary, textAlign: 'center' }}>{t('No_more_data')}</Text>
  }

  const renderItem = ({ item, index }) => {
    return (
      <View style={styles.container}>
        {/* <View style={styles.boxviewImage}> */}
        {/*  <TouchableOpacity> */}
        {/*    <View style={styles.viewImage}> */}
        {/*      <LazyImage */}
        {/*        type='avatar' */}
        {/*        resizeMode="cover" */}
        {/*        style={styles.imageUser} */}
        {/*        source={{ uri: item.image }} */}
        {/*      /> */}
        {/*    </View> */}
        {/*  </TouchableOpacity> */}
        {/* </View> */}
        <View style={styles.containerComment}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <TouchableOpacity>
              <Text style={styles.textTitle}>{item.name}</Text>
            </TouchableOpacity>
            <View style={styles.viewStarComment}>
              {item.rate !== 0
                ? <StarRating
                  fullStarColor={'#FFC107'}
                  disabled={true}
                  maxStars={5}
                  rating={Number(item.rate)}
                  emptyStarColor={'#edf1f7'}
                  emptyStar={'star'}
                  fullStar={'star'}
                  halfStar={'star-half-o'}
                  iconSet={'FontAwesome'}
                  starSize={12}
                  starStyle={styles.customStar}
                /> : null}
              <View style={{ flex: 1 }}>
                <Text style={styles.textTime}>{moment(item.modifyAt).locale('vi').fromNow()}</Text>
              </View>
            </View>
            {/* <View style={{ */}
            {/*  justifyContent: 'space-between', */}
            {/*  alignItems: 'flex-end', */}
            {/* }}> */}
            {/*  <Icon onPress={() => { onOpen(item) }} name={'ellipsis-horizontal-outline'} size={24} color={'#d8d8d8'} /> */}
            {/* </View> */}
          </View>
          {item.content ? <View style={styles.viewService}>
            <Text style={styles.textContent}>{item.content}</Text>
          </View> : null}
        </View>

      </View>
    )
  }

  const renderHeader = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onClose} style={styles.viewTouchButtonTop}/>
      <Text style={styles.textTitleHeader}>{t('OPTION')}</Text>
    </View>
  )

  const renderHeaderEdit = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onCloseEdit} style={styles.viewTouchButtonTop}/>
      <Text style={styles.textTitleHeader}>Chỉnh sửa nhận xét</Text>
    </View>
  )

  const renderStoreInfo = () => {
    return (
      <View style={styles.viewStoreInfo}>
        <LazyImage style={styles.storeImg} source={{ uri: serviceStore.bannerStore[0].image }}></LazyImage>
        <View style={styles.viewContent}>
          <Text style={styles.storeName}>{serviceStore.name}</Text>
          <View>
            <Text style={styles.storeAddress}>{serviceStore.adress}</Text>
            {/* <Text style={{ fontSize: 12, fontWeight: '400' }}>{serviceStore.p}</Text> */}
          </View>
          <View style={{ flexDirection: 'row' }}>
            <View style={styles.viewStar}>
              {serviceStore.rateTotalValue !== 0
                ? <StarRating
                  fullStarColor={'#FFC107'}
                  disabled={true}
                  maxStars={5}
                  rating={Number(serviceStore.rateTotalValue)}
                  emptyStarColor={'#edf1f7'}
                  emptyStar={'star'}
                  fullStar={'star'}
                  halfStar={'star-half-o'}
                  iconSet={'FontAwesome'}
                  starSize={13}
                  starStyle={styles.customStar}
                /> : null}
            </View>
            <Text style={styles.textRate}>{serviceStore.rateTotalValue}</Text>
            <Text style={styles.textRate}>({serviceStore.totalComment} đánh giá)</Text>
          </View>
        </View>
      </View>
    )
  }

  const renderHeaderTabReview = () => {
    return (<View>
      {renderStoreInfo()}
      <RenderStar type={'store'}/>
      <View style={{ backgroundColor: '#fff', paddingHorizontal: 16, paddingTop: 20, paddingBottom: 5 }}>
        <Text style={styles.titleLabel}>{t('COMMENT')} ({serviceStore.totalComment})</Text>
      </View>
    </View>)
  }

  return (<>
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <Header
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={() => {
          goBack()
          // serviceStore.setIsLastestComments(true)
        }}/>}
        centerComponent={{ text: t(t('Đánh giá cửa hàng')), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      {isFetched ? <PlaceHolder/> : <View style={styles.viewFlatlist}>
        {!data || !data.length
          ? <View style={{ backgroundColor: '#fff', flex: 1 }}><EmptyData title={'Không có nhận xét'} message={'Tất cả nhận xét sẽ được hiển thị ở đây'}/></View>
          : <FlatList
            showsVerticalScrollIndicator={false}
            data={data}
            onRefresh={() => onRefresh()}
            refreshing={refreshing}
            keyExtractor={(item, index) => item.id + index}
            renderItem={renderItem}
            ListHeaderComponent={renderHeaderTabReview}
            onScrollEndDrag={(e) => {
              setLoadMore(true)
            }}
            onEndReachedThreshold={0.5}
            ListFooterComponent={renderFooter}
            onMomentumScrollEnd={handleLoadMore}
          />}
      </View>}
      <Modalize
        HeaderComponent={renderHeader}
        ref={modalizeRef}
        adjustToContentHeight
        keyboardAvoidingBehavior={'padding'}
      >
        {idComment && idUserComment && profileStore._id === idUserComment ? <View style={styles.containerPadding}>
          <Text onPress={() => { onOpenEdit() }} style={styles.txtEdit} >Chỉnh Sửa</Text>
          <Text onPress={() => { deleteComment() }} style={styles.txtEdit} >Xóa</Text>
        </View> : <View style={styles.containerPadding}><Text onPress={() => { report() }} style={styles.txtEdit} >Báo cáo</Text></View> }
      </Modalize>
      <Modalize
        HeaderComponent={renderHeaderEdit}
        ref={modalizeRefEdit}
        adjustToContentHeight
        keyboardAvoidingBehavior={'padding'}
      >
        <View style={styles.containerPadding}>
          <EditComment item={item} storeId={storeId} closeModal ={onCloseEdit} onClose={onClose} onRefresh ={onRefresh} />
        </View>
      </Modalize>
    </SafeAreaView></>)
})
