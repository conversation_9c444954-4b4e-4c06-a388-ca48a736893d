import { StyleSheet, Dimensions } from 'react-native'
import { ifIphoneX } from 'react-native-iphone-x-helper'
import { color, typography } from '../../../../theme'

const tab1ItemSize = (Dimensions.get('window').width - 30) / 5
const { height } = Dimensions.get('window')
const styles = StyleSheet.create({
  boxviewImage: {
    width: 50
  },
  container: {
    borderBottomColor: color.primaryBackground,
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  containerComment: {
    backgroundColor: '#fff',
    flex: 1,
    marginBottom: 1,
    paddingHorizontal: 16,
    paddingVertical: 20
    // flexDirection: "column"
  },
  containerPadding: {
    marginBottom: 89
  },
  customStar: {
    marginRight: 2,
    marginTop: 3
  },
  icArrowBack: {
    margin: 11,
  },
  imageUser: {
    borderRadius: 22,
    height: 44,
    width: 44,
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
    ...ifIphoneX({
      marginTop: 15
    }, {
      marginTop: 5
    }),
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  storeAddress: {
    color: '#555',
    fontSize: 12,
    fontWeight: '400'
  },
  storeImg: {
    height: 100,
    width: 100
  },
  storeName: {
    color: '#333',
    fontSize: 16,
    fontWeight: '400'
  },
  textContent: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '400',
    letterSpacing: 0.5
  },
  textRate: {
    color: '#3f3f3f',
    fontSize: 14,
    fontWeight: '400',
    marginRight: 5
  },
  textTime: {
    color: '#979797',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '400',
    justifyContent: 'flex-end',
    textAlign: 'right'
  },
  textTitle: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600'
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',

  },
  textTitleTotal: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
    marginLeft: 15,
    marginTop: 4
  },
  titleLabel: {
    color: '#979797',
    fontSize: 14,
    fontWeight: '500',
    textTransform: 'uppercase'
  },
  txtEdit: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    marginHorizontal: 15,
    marginVertical: 15
  },
  viewContent: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: 15
  },
  viewFlatlist: {
    flex: 1,
    height: height,
  },
  viewIcon: {
    alignItems: 'flex-end',
    flex: 1,
    justifyContent: 'flex-end',
  },
  viewImage: {
    alignItems: 'center',
    flex: 1,
    height: 44,
    width: 44,
  },
  viewService: {
    flexDirection: 'column',
    marginTop: 17
  },
  viewStar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginRight: 10
  },
  viewStarComment: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
    marginHorizontal: 10
  },
  viewStoreInfo: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    marginBottom: 5,
    marginTop: 4,
    paddingHorizontal: 16,
    paddingVertical: 20
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },

})
export default styles
