import React, { useContext, useEffect, useState } from 'react'
import {
  View, TouchableOpacity, Text, ScrollView, Linking, Platform
} from 'react-native'
import styles from './styles'
import { useStores } from '@app/models'
import { ModalContext } from '@app/components/modal-success'
import Icon from 'react-native-vector-icons/MaterialCommunityIcons'
import { color } from '@app/theme'
import { showLocation } from 'react-native-map-link'
import { IconCuuHoSmall } from '@app/assets/icons'

export const rounded = {
  borderRadius: 4,
  width: '100%',
  padding: 15,
  marginTop: 11,
  flexDirection: 'row',
}
export const ListBranch = (props: any) => {
  const storeId = props
  const { showError, showSuccess } = useContext(ModalContext)
  const [dataBranch, setDataBranch] = useState([])
  const { bookingStore, serviceStore } = useStores()

  useEffect(() => {
    loadData().then(r => {
      const cloneData = bookingStore.branchStore || []
      setDataBranch(cloneData)
    })
  }, [])

  const loadData = async () => {
  }

  const renderBranchAddress = (item, index) => {
    return (
      <View style={styles.viewBranch} key={index}>
        <View style={styles.boxViewAddress}>
          <View style={styles.renderItemListAddress}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', flex: 1 }}>
              <Text numberOfLines={2} style={styles.textName}>{item.name}</Text>
              <Text style={styles.kilometer}>{item.distance} km</Text>
            </View>
          </View>
          <View style={styles.boxAddress}>
            <Icon name={'map-marker-radius-outline'} size={24} color={color.primary}/>
            <Text numberOfLines={2} style={styles.textAddressBranch}>{item.address}</Text>
          </View>
          <TouchableOpacity style={styles.boxViewPhone} onPress={() => {
            if (item?.phone) {
              Linking.openURL(`tel:${item?.phone}`)
            }
          }}>
            <Icon name={'phone'} size={24} color={color.primary}/>
            <Text style={styles.textAddressPhone}>{item?.phone || 'Không có'}</Text>
          </TouchableOpacity>
          { item?.hotline ? <TouchableOpacity style={styles.boxViewPhone} onPress={() => {
            if (item?.hotline) {
              Linking.openURL(`tel:${item?.hotline}`)
            }
          }}>
            {/* <Icon name={'cellphone'} size={20} color={color.primary}/> */}
            <IconCuuHoSmall />
            <Text style={styles.textAddressPhone}>{`${item?.hotline} (Cứu hộ 24/7)`}</Text>
          </TouchableOpacity> : null }
          {/* <Text style={styles.kilometer}>{item.distance} km</Text> */}
          <TouchableOpacity style={[styles.iconMap, { flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }]} onPress={() => {
            const options: any = {
              latitude: item.lat,
              longitude: item.lng,
              // title: item.name,
              // dialogTitle: 'This is the dialog Title',
              // dialogMessage: 'This is the amazing dialog Message',
              // cancelText: 'This is the cancel button text'
            }
            if (Platform.OS === 'ios') {
              options.title = item.name
            }
            showLocation(options).then((r) => {})
          }}>
            <Icon name={'directions'} size={18} color={'#fff'}/>
            <Text style={styles.textSeeMap}>Chỉ đường</Text>
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  return (
    <ScrollView style={{
      flex: 1
    }}>
      {dataBranch.length
        ? <View style={{ flex: 1, marginBottom: 10 }}>
          {dataBranch.map((item, index) => renderBranchAddress(item, index))}
        </View>
        : null}
    </ScrollView>
  )
}
export default ListBranch
