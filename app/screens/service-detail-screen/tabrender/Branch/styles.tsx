import { StyleSheet } from 'react-native'
import { color, typography } from '../../../../theme'
import { responsiveWidth } from 'react-native-responsive-dimensions'

const styles = StyleSheet.create({
  background: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flex: 1,
    justifyContent: 'center',
    // ...ifIphoneX({
    //   paddingBottom: 89,
    // }, {
    //   paddingBottom: 60,
    // }),
    paddingLeft: 30,
    paddingRight: 30
  },
  boxAddress: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    marginLeft: -2,
    paddingVertical: 2
  },
  boxCheckInTime: {
    flex: 1,
    flexDirection: 'column'
  },
  boxContact: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    height: 125,
    marginBottom: 20,
    marginLeft: 15,
    marginRight: 10,
    marginTop: 24,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10,
    width: 288
  },
  boxService: {
    backgroundColor: '#fff',
    borderBottomColor: '#f6f6f7',
    flexDirection: 'row',
    marginHorizontal: 15,
    marginVertical: 16
  },
  boxViewAddress: {
    backgroundColor: '#ffffff',
    // borderRadius: 8,
    // elevation: 3,
    flex: 1,
    // marginHorizontal: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 0.2,
    borderColor: '#e2e2e2',
    paddingBottom: 20,
    // shadowColor: 'rgba(85, 85, 85, 0.1)',
    // shadowOffset: {
    //   width: 0,
    //   height: 0,
    // },
    // shadowOpacity: 1,
    // shadowRadius: 4
  },
  boxViewPhone: {
    flexDirection: 'row',
    paddingVertical: 8
  },
  icArrowBack: {
    height: 24,
    marginLeft: 10,
    resizeMode: 'contain',
    width: 24,
  },
  icLocation: {
    alignSelf: 'flex-start',
    height: 15,
    marginTop: 2,
    resizeMode: 'contain',
    width: 15
  },
  iconMap: {
    alignItems: 'center',
    backgroundColor: color.primary,
    borderRadius: 4,
    // flex: 1,
    height: 32,
    justifyContent: 'center',
    marginTop: 12,
    width: 140
  },
  kilometer: {
    color: '#9d9d9d',
    fontSize: 14,
    fontWeight: '400',
    marginTop: 3
  },
  renderItemListAddress: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 12,
  },
  renderItemListPhone: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
    paddingLeft: 3
  },
  scrollView: {
    width: '100%',
  },
  textAddressBranch: {
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 14,
    marginLeft: 5
  },
  textAddressPhone: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginLeft: 7
  },
  textAdress: {
    color: '#000000',
    fontFamily: typography.normal,
    fontSize: 12,
    letterSpacing: 0,
    marginHorizontal: 5,
    width: 300
  },
  textName: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 24,
    maxWidth: responsiveWidth(70)
  },
  textSeeMap: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    paddingHorizontal: 16,
    paddingVertical: 8
  },
  viewBranch: {
    alignItems: 'center',
    flexDirection: 'row',
    // marginLeft: 1,
    marginTop: 20,
  }

})
export default styles
