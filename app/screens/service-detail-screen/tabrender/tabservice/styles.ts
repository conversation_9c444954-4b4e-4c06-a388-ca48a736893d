import { StyleSheet, Dimensions } from 'react-native'
const tab1ItemSize = (Dimensions.get('window').width - 30) / 5
const styles = StyleSheet.create({
  boxContainer: {
    borderBottomColor: '#e0e0e0',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 15,
    marginRight: 15,
    marginTop: 20,
    paddingBottom: 15,
  },
  container: {
    flexDirection: 'row',
    marginLeft: 15,
    marginRight: 15,
    marginTop: 14
  },
  textContent: {
    color: '#46474D',
    fontSize: 12,
    width: 243
  },
  textTitle: {
    color: '#333',
    fontSize: 14,
    fontWeight: '600'
  },
  viewIcon: {
    alignItems: 'flex-end',
    flex: 1,
    justifyContent: 'flex-end',
  },
  viewImage: {
    alignItems: 'center',
    borderRadius: 16,
    height: tab1ItemSize,
    justifyContent: 'center',
    marginLeft: 0,
    width: tab1ItemSize,
  },
  viewService: {
    flexDirection: 'column',
    marginLeft: 10
  }

})
export default styles
