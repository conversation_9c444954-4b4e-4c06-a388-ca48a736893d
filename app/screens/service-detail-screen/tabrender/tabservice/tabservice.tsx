import React from 'react'
import {
  View,
  Text, Image, TouchableOpacity, FlatList
} from 'react-native'
import styles from './styles'
import Icon from 'react-native-vector-icons/Ionicons'
import { observer } from 'mobx-react-lite'
import { useStores } from '../../../../models/root-store'
const renderItem = ({ item, index }) => {
  return (
    <View style={styles.boxContainer}>
      <View
        style={styles.viewImage}>
        <Image style={{
          width: '100%',
          height: '100%',
          borderRadius: 22,
        }} source={{ uri: item.image }} resizeMode="cover"/>
      </View>
      <View style={styles.viewService}>
        <TouchableOpacity>
          <Text style={styles.textTitle}>{item.name}</Text>
        </TouchableOpacity>
        <Text style={styles.textContent}>{item.description}</Text>
      </View>
      <View style={styles.viewIcon}>
        <TouchableOpacity>
          <Icon name={'add-circle-outline'} size={24} color={'#ffb45e'} />
        </TouchableOpacity>
      </View>
    </View>
  )
}
export const RendertabService = observer((props) => {
  // const [tab1Data, setTab1Data] = useState(dataTab1)
  const { bookingStore } = useStores()
  return (<View {...props}>
    <View style={styles.container}>
      <FlatList
        showsVerticalScrollIndicator={false}
        data={bookingStore.dataServiceOfSpa}
        keyExtractor={(item) => item.id}
        renderItem={renderItem}
      />
    </View>
  </View>

  )
})
export default RendertabService
