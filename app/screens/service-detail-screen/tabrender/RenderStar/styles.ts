import { StyleSheet, Dimensions } from 'react-native'
import { typography } from '../../../../theme'

const { width } = Dimensions.get('window')
const styles = StyleSheet.create({
  boxContainerStar: {
    // flexDirection: 'column',
  },
  boxStar: {
    marginTop: 20,
  },
  containerFlatlist: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    marginBottom: 5,
    paddingBottom: 10,
    paddingHorizontal: 16
  },
  customStar: {
    marginRight: 2,
    marginTop: 3
  },
  renderStar: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: 10
  },
  startContainer: {
    // marginLeft: 30,
  },
  textLabelPoin: {
    color: '#979797',
    fontSize: 14,
    fontWeight: '500',
    textTransform: 'uppercase'
  },
  textPoinRate: {
    alignItems: 'center',
    color: '#3f3f3f',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '400',
    marginLeft: 10,
    marginTop: 2,
    textAlign: 'center'
  },
  textTitleFlatlist: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    justifyContent: 'flex-start',
    letterSpacing: 0,
    textAlign: 'left'
  },
  textTop: {
    color: '#ff8900',
    fontFamily: typography.normal,
    fontSize: 14,
    justifyContent: 'flex-end',
    letterSpacing: 0,
    marginLeft: 2,
    textAlign: 'right',
    textDecorationLine: 'underline'
  },
  viewContainerStar: {
    // flexDirection: 'row',
    // height: 100
  },
  viewTextLabelPoin: {
    flexDirection: 'row',
    marginTop: 23
  },
  viewtextTitleFlatlist: {
    flexDirection: 'row',
    flex: 1,
    marginTop: 32
  }
})
export default styles
