import React, { useState } from 'react'
import {
  View,
  Text
} from 'react-native'
import styles from './styles'
import { useTranslation } from 'react-i18next'
import StarRating from 'react-native-star-rating'
import { useStores } from '../../../../models/root-store'

export interface RenderStar {
  type: any
}

const RenderStar = (props: RenderStar) => {
  const { t } : any = useTranslation()
  const { serviceStore, productStore } = useStores()
  const [rate, setRate] = useState()
  const ratingCompleted = (rating) => {
    // console.log('Rating is: ' + rating)
    setRate(rating)
  }
  return (
    <View style={styles.containerFlatlist}>
      <View style={styles.viewContainerStar}>
        <View style={styles.viewTextLabelPoin}>
          <Text style={styles.textLabelPoin}>{t('Score')}</Text>
        </View>
        <View style={styles.boxStar}>
          <View style={styles.boxContainerStar}>
            <View style={styles.renderStar}>
              <StarRating
                fullStarColor={'#FFC107'}
                disabled={true}
                maxStars={5}
                rating={5}
                emptyStarColor={'#e4e4e4'}
                emptyStar={'star'}
                fullStar={'star'}
                halfStar={'star-half-o'}
                iconSet={'FontAwesome'}
                starSize={13}
                containerStyle={styles.startContainer}
                starStyle={styles.customStar}
                selectedStar={(rating) => ratingCompleted(rating)}
              />
              <Text style={styles.textPoinRate}>{props.type === 'store' ? serviceStore.rateTotal5 : productStore.rateTotal5}</Text>
            </View>
            <View style={styles.renderStar}>
              <StarRating
                fullStarColor={'#FFC107'}
                disabled={true}
                maxStars={5}
                rating={4}
                emptyStarColor={'#e4e4e4'}
                emptyStar={'star'}
                fullStar={'star'}
                halfStar={'star-half-o'}
                iconSet={'FontAwesome'}
                starSize={13}
                containerStyle={styles.startContainer}
                starStyle={styles.customStar}
                selectedStar={(rating) => ratingCompleted(rating)}
              />
              <Text style={styles.textPoinRate}>{props.type === 'store' ? serviceStore.rateTotal4 : productStore.rateTotal4}</Text>
            </View>
            <View style={styles.renderStar}>
              <StarRating
                fullStarColor={'#FFC107'}
                disabled={true}
                maxStars={5}
                rating={3}
                emptyStarColor={'#e4e4e4'}
                emptyStar={'star'}
                fullStar={'star'}
                halfStar={'star-half-o'}
                iconSet={'FontAwesome'}
                starSize={13}
                containerStyle={styles.startContainer}
                starStyle={styles.customStar}
                selectedStar={(rating) => ratingCompleted(rating)}
              />
              <Text style={styles.textPoinRate}>{props.type === 'store' ? serviceStore.rateTotal3 : productStore.rateTotal3}</Text>
            </View>
            <View style={styles.renderStar}>
              <StarRating
                fullStarColor={'#FFC107'}
                disabled={true}
                maxStars={5}
                rating={2}
                emptyStarColor={'#e4e4e4'}
                emptyStar={'star'}
                fullStar={'star'}
                halfStar={'star-half-o'}
                iconSet={'FontAwesome'}
                starSize={13}
                containerStyle={styles.startContainer}
                starStyle={styles.customStar}
                selectedStar={(rating) => ratingCompleted(rating)}
              />
              <Text style={styles.textPoinRate}>{props.type === 'store' ? serviceStore.rateTotal2 : productStore.rateTotal2}</Text>
            </View>
            <View style={styles.renderStar}>
              <StarRating
                fullStarColor={'#FFC107'}
                disabled={true}
                maxStars={5}
                rating={1}
                emptyStarColor={'#e4e4e4'}
                emptyStar={'star'}
                fullStar={'star'}
                halfStar={'star-half-o'}
                iconSet={'FontAwesome'}
                starSize={13}
                containerStyle={styles.startContainer}
                starStyle={styles.customStar}
                selectedStar={(rating) => ratingCompleted(rating)}
              />
              <Text style={styles.textPoinRate}>{props.type === 'store' ? serviceStore.rateTotal1 : productStore.rateTotal1}</Text>
            </View>
          </View>
        </View>
      </View>
    </View>

  )
}
export default RenderStar
