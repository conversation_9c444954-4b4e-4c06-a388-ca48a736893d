import React, { useContext, useEffect, useState } from 'react'
import {
  View, Text, ScrollView, TextInput,
} from 'react-native'
import StarRating from 'react-native-star-rating'
import styles from './styles'
import { useStores } from '../../../../models/root-store'
import { TButton } from '@app/components'
import { useTranslation } from 'react-i18next'
import validate from 'validate.js'
import { ModalContext } from '@app/context'

export const rounded = {
  borderRadius: 4,
  width: '100%',
  padding: 15,
  marginTop: 11,
  flexDirection: 'row',
}
export const CommentProduct = (props: any) => {
  const [rate, setRate] = useState(0)
  const [content, setContent] = useState('')
  const [disabled, setDisabled] = useState(false)
  const { commentStore, productStore } = useStores()
  const { t } : any = useTranslation()
  const [isSubmitting, setSubmitting] = useState(false)
  const { showError, showSuccess } = useContext(ModalContext)
  const point = commentStore.getRatingByUserId > 0 ? 0 : rate

  const validateFields = () => {
    if (commentStore.getRatingByUserId <= 0) {
      return validate.isEmpty(content) || point === 0
    } else {
      return validate.isEmpty(content)
    }
  }

  useEffect(() => {
    if (commentStore.getRatingByUserId > 0) {
      setRate(commentStore.getRatingByUserId)
      setDisabled(true)
    }
  }, [])

  const createComment = async () => {
    setSubmitting(true)
    if (!content) {
      showError(t('FAIL'), 'Nội dung bị trống!')
      setTimeout(() => {
        props.closeModal()
      }, 2000)
      return
    }
    const commentCreate = {
      productId: productStore.productId,
      content: content,
      rate: point,
      like: 2,
    }
    await commentStore.createCommentProduct(commentCreate).then(rs => {
      if (rs && rs.data.error) {
        setSubmitting(false)
        showError(t('FAIL'), `${rs.data.message}`)
      } else {
        showSuccess(t('THANHCONG'), t('ALERT_commenthanhcong'))
        setContent('')
        setTimeout(() => {
          props.closeModal()
          props.onRefresh()
        }, 2000)
        setSubmitting(false)
      }
    }).catch(err => {
      setSubmitting(false)
      console.error(err)
    })
  }

  const ratingCompleted = (rating) => {
    // truong hop neu nhu load rating co san. thi ko can set so diem
    if (commentStore.getRatingByUserId > 0) return
    setRate(rating)
  }
  return (<View style={styles.background}>
    <ScrollView
      bounces={false}
      style={styles.scrollView}
      showsVerticalScrollIndicator={false}>
      <View>
        <View style={styles.viewTextPoinStar}>
          <Text style={styles.textPoint}>Bạn đã đánh giá {rate} sao</Text>
        </View>
        <View style={styles.viewStar}>
          <StarRating
            fullStarColor={'#ff8900'}
            disabled={disabled}
            maxStars={5}
            rating={rate}
            emptyStarColor={'#edf1f7'}
            emptyStar={'star'}
            fullStar={'star'}
            halfStar={'star-half-o'}
            iconSet={'FontAwesome'}
            starSize={32}
            containerStyle={styles.startContainer}
            starStyle={styles.customStar}
            selectedStar={(rating) => ratingCompleted(rating)}
          />
        </View>
      </View>
      <View style={styles.formInput}>
        <TextInput
          style={styles.textInputNote}
          numberOfLines={4}
          multiline={true}
          placeholder={'Bạn nghĩ sao về chúng tôi?'}
          onChangeText={e => setContent(e)}/>

        <TButton typeRadius={'rounded'} disabled={validateFields() || isSubmitting} loading={isSubmitting} buttonStyle={styles.btnLogin}
          title={t('XONG')} onPress={createComment}/>
      </View>
    </ScrollView>
  </View>
  )
}
export default CommentProduct
