import { Platform, StyleSheet } from 'react-native'
import { color, typography } from '../../../../theme'

const styles = StyleSheet.create({
  background: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flex: 1,
    justifyContent: 'center',
    // ...ifIphoneX({
    //   paddingBottom: 89,
    // }, {
    //   paddingBottom: 60,
    // }),
    paddingBottom: 30
  },
  btnLogin: {
    backgroundColor: color.primary,
    // borderRadius: 22,
    flexDirection: 'row',
    marginTop: 27,
    width: '100%'
  },
  buttonLogin: {
    alignItems: 'center',
    backgroundColor: '#ff8ba1',
    borderRadius: 22,
    flexDirection: 'row',
    height: 44,
    justifyContent: 'center',
  },
  customStar: {
    marginRight: 12,
    marginTop: 3,
  },
  flexCenter: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'center',
  },
  formInput: {
    flex: 1,
    paddingHorizontal: 16
  },
  logoRed: {
    height: 90,
    marginTop: Platform.OS == 'ios' ? 50 : 25,
    resizeMode: 'contain',
    width: 80,
  },
  passwordInput: {
    flex: 1,
    width: '100%',
  },
  scrollView: {
    width: '100%',
  },
  startContainer: {
    alignItems: 'center',
    marginLeft: 12,
  },
  textButton: {
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 13,
    letterSpacing: 0,
    marginLeft: 8,
    textAlign: 'center',
  },
  textInputNote: {
    backgroundColor: '#fff',
    borderColor: '#EFEFEF',
    borderRadius: 4,
    borderWidth: 1,
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 13,
    letterSpacing: 0,
    marginTop: 6,
    minHeight: 80,
    padding: 12,
    textAlign: 'left',
    textAlignVertical: 'top',
  },
  textInputtitle: {
    backgroundColor: '#fff',
    borderColor: '#EFEFEF',
    borderRadius: 4,
    borderWidth: 1,
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 13,
    letterSpacing: 0,
    marginTop: 6,
    minHeight: 20,
    padding: 12,
    textAlign: 'left',
    textAlignVertical: 'top',
  },
  textNote: {
    color: '#999999',
    fontFamily: typography.normal,
    fontSize: 11,
    marginTop: 20,
  },
  textPoinRate: {
    alignItems: 'center',
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '500',
    letterSpacing: 0,
    marginLeft: 5,
    textAlign: 'center',
  },
  textPoint: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
  },
  txtDangKy: {
    color: '#ff1614',
    fontFamily: typography.normal,
    fontSize: 12,
  },
  txtDangNhap: {
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 24,
    letterSpacing: 0,
    marginBottom: 20,
    marginTop: 10,
    textAlign: 'left',
    width: '100%',
  },
  txtEnd: {
    color: '#000000',
    fontFamily: typography.normal,
    fontSize: 12,
  },
  txtQuenMatKhau: {
    color: '#CB1016',
    fontFamily: typography.normal,
    fontSize: 11,
    letterSpacing: 0.2,
    textAlign: 'right',
  },
  viewInputPassword: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 30,
    position: 'relative',
    width: '100%',
  },
  viewStar: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    marginTop: 34,
  },
  viewTextPoinStar: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 15,
  },
  viewTxtEnd: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
    marginTop: 20,
  },
  viewtxtQuenMatKhau: {
    marginTop: 20,
    position: 'absolute',
    right: 0,
  },
})
export default styles
