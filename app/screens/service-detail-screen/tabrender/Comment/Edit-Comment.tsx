import React, { useContext, useState } from 'react'
import {
  View, ScrollView, TextInput,
} from 'react-native'
import styles from './styles'
import { useStores } from '@app/models'
import { TButton } from '@app/components'
import { ModalContext } from '@app/context'
import { useTranslation } from 'react-i18next'
import validate from 'validate.js'

export const rounded = {
  borderRadius: 4,
  width: '100%',
  padding: 15,
  marginTop: 11,
  flexDirection: 'row',
}
export const EditComment = (props: any) => {
  const [rate, setRate] = useState(0)
  const [content, setContent] = useState('')
  const [disabled, setDisabled] = useState(false)
  const { commentStore, serviceStore, accountStore } = useStores()
  const { t } : any = useTranslation()
  const [isSubmitting, setSubmitting] = useState(false)
  const { showError, showSuccess } = useContext(ModalContext)
  const item = props.item

  const validateFields = () => validate.isEmpty(content)

  // serviceStore.createComment(page:pagestatkhauboa,storeid:sukienclick)
  const editComment = async () => {
    setSubmitting(true)
    if (!content) {
      showError(t('FAIL'), 'Nội dung bị trống!')
      setTimeout(() => {
        props.closeModal()
      }, 3000)
      return
    }

    await commentStore.editComment(item.id, {
      userId: accountStore._id,
      storeId: props.storeId,
      content: content,
      like: 2,
      watched: 1
    }).then(rs => {
      if (rs && rs.data.error) {
        setSubmitting(false)
        showError(t('FAIL'), `${rs.data.message}`)
      } else {
        showSuccess(t('THANHCONG'), t('ALERT_commenthanhcong'))
        setContent('')
        setTimeout(() => {
          props.closeModal()
          props.onRefresh()
          props.onClose()
        }, 3000)
        setSubmitting(false)
      }
    }).catch(err => {
      setSubmitting(false)
      console.error(err)
    })
  }
  return (<View style={styles.background}>
    <ScrollView
      bounces={false}
      style={styles.scrollView}
      showsVerticalScrollIndicator={false}>
      <View style={styles.formInput}>
        <TextInput
          style={styles.textInputNote}
          numberOfLines={4}
          multiline={true}
          placeholder={'Viết nhận xét'}
          defaultValue={item.content}
          onChangeText={e => setContent(e)}/>
        <TButton typeRadius={'rounded'} disabled={validateFields() || isSubmitting} loading={isSubmitting} buttonStyle={styles.btnLogin}
          title={t('XONG')} onPress={editComment}/>
      </View>
    </ScrollView>
  </View>
  )
}
export default EditComment
