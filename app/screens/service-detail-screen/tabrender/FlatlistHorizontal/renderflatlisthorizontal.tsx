
import React, { useEffect } from 'react'
import {
  View,
  Text, TouchableOpacity, FlatList,
} from 'react-native'
import styles from './styles'
import { useStores } from '../../../../models/root-store'
import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import { LazyImage } from '@app/components'

export interface RenderHeaderFlatlist {
  onSelect: any
  navigation: any
  storeId: any
}

const RenderListHeaderFlatlist = observer((props: RenderHeaderFlatlist) => {
  const { t } : any = useTranslation()
  const storeId = props.storeId
  const { serviceStore } = useStores()
  useEffect(() => {
    let cancel = true
    if (cancel) {
      fetchData().then((e) => {
        cancel = false
      })
    }
    return () => {
      cancel = false
    }
  }, [])

  const fetchData = async () => {
    await serviceStore.getFeatured(storeId || '')
  }

  const renderFeatured = ({ item }) => {
    __DEV__ && console.log('>>>>>>>>>>>', item)
    return (
      <TouchableOpacity
        onPress={() => props.onSelect(item)}
        style={styles.viewRenderItem}>
        <View>
          <LazyImage style={styles.img} source={{ uri: item.image }}/>
          {/* <View style={styles.viewCoupe}><Text style={styles.textCoupe}>{item.coupon}</Text></View> */}
          {/* <Icon name={'bookmark-outline'} size={24} color={'#ffffff'} style={styles.iconBookmark}/> */}
        </View>
        <View style={styles.viewSeverNamecoupel}>
          <Text numberOfLines={1} style={styles.textSeverNamecoupel}>{item.name}</Text>
        </View>
        <View style={styles.viewAdressCoupel}>
          <Text numberOfLines={2} style={styles.textAdressCoupel}>{item.shortDes}</Text>
        </View>
      </TouchableOpacity>
    )
  }

  return (
    <>
      {serviceStore.uudai && serviceStore.uudai.length ? <View style={styles.containerFlatlist}>
        <View style={styles.viewtextTitleFlatlist}>
          <Text style={styles.textTitleFlatlist}>{t('FEATURED')}</Text>
        </View>
        <View style={styles.containerItem}>
          <FlatList
            showsVerticalScrollIndicator={false}
            horizontal={true}
            data={serviceStore.uudai}
            showsHorizontalScrollIndicator={false}
            renderItem={renderFeatured}
            keyExtractor={item => item.id}/>
        </View>
      </View> : null}
    </>
  )
})
export default RenderListHeaderFlatlist
