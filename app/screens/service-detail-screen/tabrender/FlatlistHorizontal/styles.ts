import { StyleSheet, Dimensions } from 'react-native'
import { typography } from '../../../../theme'

const { width } = Dimensions.get('window')
const styles = StyleSheet.create({
  boxTop: {
    flex: 1
  },
  containerFlatlist: {
    flexDirection: 'column',
    marginLeft: 15,
    marginRight: 15
  },
  containerItem: {
    marginTop: 16,
  },
  iconBookmark: {
    position: 'absolute',
    right: 13,
    top: 9
  },
  img: {
    borderTopLeftRadius: 8,
    borderTopRightRadius: 10,
    height: 100,
    resizeMode: 'cover',
    width: 138
  },
  textAdressCoupel: {
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 12,
    lineHeight: 14,
    marginLeft: 1,
    width: 120,
  },
  textCoupe: {
    color: '#ef87be',
    fontFamily: typography.normal,
    fontSize: 10,
    letterSpacing: 0,
    textAlign: 'center'
  },
  textSeverNamecoupel: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 13,
    fontWeight: '600',
    letterSpacing: 0,
    width: 120,
  },
  textTitleFlatlist: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    justifyContent: 'flex-start',
    letterSpacing: 0,
    lineHeight: 16,
    textAlign: 'left'
  },
  textTop: {
    color: '#ffa8b4',
    fontFamily: typography.normal,
    fontSize: 14,
    justifyContent: 'flex-end',
    letterSpacing: 0,
    lineHeight: 16,
    textAlign: 'right'
  },
  viewAdressCoupel: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: 13,
    marginLeft: 8
  },
  viewCoupe: {

    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 9.5,
    bottom: 7,
    elevation: 2,
    height: 19,
    justifyContent: 'center',
    left: 8,
    position: 'absolute',
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 1,
    shadowRadius: 4,
    width: 94
  },
  viewRenderItem: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    marginHorizontal: 8,
    marginVertical: 15,
    shadowColor: '#e6e8ef',
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 0.5,
    shadowRadius: 10,
  },
  viewSeverNamecoupel: {
    marginBottom: 8,
    marginLeft: 7,
    marginRight: 7,
    marginTop: 8
  },
  viewtextTitleFlatlist: {
    flexDirection: 'row',
    flex: 1,
    marginTop: 32
  }
  // tabservice

})
export default styles
