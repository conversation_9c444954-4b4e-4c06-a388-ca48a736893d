import { SafeAreaView } from 'react-native-safe-area-context'
import React, { useState } from 'react'
import {
  ScrollView, useWindowDimensions,
  View
} from 'react-native'
import RenderHtml from 'react-native-render-html'
import { useStores } from '../../../../models/root-store'
import { useNavigation } from '@react-navigation/native'
import { observer } from 'mobx-react-lite'
import styles from './styles'
import { ButtonBack, PlaceHolder } from '@app/components'
import { Header } from 'react-native-elements'
import common, { linearGradientProps } from '@app/theme/styles/common'
import LinearGradient from 'react-native-linear-gradient'
const renderersProps = {
  img: {
    enableExperimentalPercentWidth: true
  }
}

export const RenderTabDetail = observer((props) => {
  const [item, setItem] = useState(null)
  const { serviceStore } = useStores()
  const navigation = useNavigation()
  const goBack = () => navigation.goBack()
  const { width } = useWindowDimensions()

  return (
    <SafeAreaView style={styles.background} edges={['right', 'top', 'left']}>
      <Header
        // statusBarProps={{ barStyle: 'light-content' }}
        // barStyle="light-content" // or directly
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
        centerComponent={{ text: serviceStore.name, style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
        //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
        // </TouchableOpacity> : null}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      <ScrollView showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
        {!serviceStore ? <PlaceHolder/>
          : <View>
            <View style={{ flexGrow: 0, backgroundColor: '#fff', paddingBottom: 15, paddingHorizontal: 16, marginTop: 10 }}>
              <RenderHtml
                contentWidth={width}
                source={{ html: serviceStore.dataDetail }}
                renderersProps={{
                  img: {
                    enableExperimentalPercentWidth: true
                  }
                }}
                ignoredTags={['script']}
                ignoredStyles={['font-family']}
              />
            </View>
          </View>
        }
      </ScrollView>

    </SafeAreaView>
  )
}
)
