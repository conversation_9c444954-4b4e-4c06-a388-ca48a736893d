import React, { useState } from 'react'
import { observer } from 'mobx-react-lite'
import { Image, Text, TouchableOpacity, View } from 'react-native'

import styles from './styles'
import { LazyImage } from '@app/components'
import StarRating from 'react-native-star-rating'
import { useTranslation } from 'react-i18next'
import { freeShipIcon, saleIcon } from '@app/assets/images'
import { numberFormat } from '@app/utils/number'

export interface ProductByCategoryScreenProps {
  /**
   * An optional style override useful for padding & margin.
   */
  item: any
  onPress :any
  type?:any
}

export const ProductByCategoryScreen = observer(function ProductByCategoryScreen(props: ProductByCategoryScreenProps) {
  const { t } : any = useTranslation()
  const item = props.item
  const [rateValue, setRateValue] = useState(0)
  // const [loading, setLoading] = useState(false)
  // const { navigate, goBack } = useNavigation()

  const discount = (item.price - item.priceOld) / item.priceOld

  return (
    <TouchableOpacity
      onPress={() => { props.onPress(item) }}
      style={styles.renderProduct}>
      <View style={styles.viewImage}>
        {/* <LazyImage source={{ uri: item.picture }} style={styles.productImg} ><View style={styles.discount}><Text style={styles.textDiscount}>-10%</Text></View></LazyImage> */}
        <LazyImage source={{ uri: item.picture }} style={styles.productImg} />
        <View style={styles.discountViewRow}>
          {item.typeShip === 0 && <Image source={freeShipIcon} style={styles.freeShipIc}></Image>}
          {item.priceOld && <Image source={saleIcon} style={styles.saleIcon}></Image>}
        </View>
      </View>
      <View style={{ flex: 1 }}>
        <Text numberOfLines={2}
          ellipsizeMode="tail"
          style={styles.rspTopViewText}>{item.name}</Text>
        <View>
          <Text style={styles.rspTopViewTextPrice}>{numberFormat(item.price)} đ</Text>
          {item.priceOld && item.priceOld > 0 && <View style={{ flexDirection: 'row', marginLeft: 8 }}>
            <Text style={styles.textOldPrice}>{numberFormat(item.priceOld)} đ</Text>
            <Text style={styles.textSaleOff}>{numberFormat(discount * 100)}%</Text>
          </View>}
        </View>
        <View style={styles.sectionRate}>
          <Text style={styles.textAddress}>{item.storeName}</Text>
          {rateValue > 0 ? <View style={styles.renderStar}>
            <StarRating
              fullStarColor={'#FFC107'}
              disabled={true}
              maxStars={5}
              rating={rateValue}
              emptyStarColor={'#edf1f7'}
              emptyStar={'star'}
              fullStar={'star'}
              halfStar={'star-half-o'}
              iconSet={'FontAwesome'}
              starSize={10}
              // containerStyle={styles.startContainer}
              starStyle={styles.customStar}
              // selectedStar={(rating) => ratingCompleted(rating)}
            />
          </View> : <Text style={styles.textAddress}>Chưa có đánh giá</Text>}
        </View>
      </View>
      <TouchableOpacity onPress={() => { props.onPress(item) }} style={{ justifyContent: 'center' }}>
        {/* <Text style={styles.textBtnAdd}>Thêm vào giỏ</Text> */}
        <Text style={styles.textBtnAdd}>Mua ngay</Text>
      </TouchableOpacity>
    </TouchableOpacity>
  )
})
