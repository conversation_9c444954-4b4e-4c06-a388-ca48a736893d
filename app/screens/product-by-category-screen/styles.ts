import { Dimensions, StyleSheet } from 'react-native'
import { color, typography } from '@app/theme'

const styles = StyleSheet.create({
  background: {
    backgroundColor: '#f6f6f7',
    flex: 1,
  },
  customStar: {
    marginRight: 2,
    marginTop: 3
  },
  discount: {
    backgroundColor: color.primary,
    borderRadius: 5,
    padding: 3,
    position: 'absolute',
    right: 3,
    top: 3
  },
  discountViewRow: {
    bottom: 8,
    flexDirection: 'row',
    left: 8,
    position: 'absolute'
  },
  freeShipIc: {
    height: 14,
    marginRight: 5,
    width: 55
  },
  icArrowBack: {
    justifyContent: 'flex-start',
    marginLeft: 10,
    marginTop: 2
  },
  loadMore: {
    alignItems: 'center',
    marginTop: 10,
  },
  price: {
    color: '#f3373a',
    fontSize: 12,
    fontWeight: 'bold',
    marginTop: 6,
    // textAlign: 'center'
  },
  priceSale: {
    color: '#9D9D9D',
    fontSize: 12,
    fontWeight: '400',
    marginBottom: 5,
    marginLeft: 8,
    textAlign: 'left',
    textDecorationLine: 'line-through'
  },
  productImg: {
    // borderRadius: 3,
    height: '100%',
    resizeMode: 'cover',
    width: '100%'
  },
  productName: {
    color: '#333',
    fontSize: 13,
    height: 40,
    marginTop: 11,
    textAlign: 'center'
  },
  renderProduct: {
    backgroundColor: '#F7F7F7',
    borderRadius: 4,
    height: 360,
    marginBottom: 10,
    // marginLeft: 16,
    // flex: 1 / 2,
    // margin: 15,
    width: Dimensions.get('window').width / 2 - 20,
  },
  renderStar: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  rspTopViewText: {
    color: '#333',
    fontSize: 14,
    fontWeight: '400',
    height: 35,
    marginLeft: 8,
    marginVertical: 10,
    textAlign: 'left',
    width: '90%'
  },
  rspTopViewTextPrice: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 5,
    marginLeft: 8,
    textAlign: 'left'
  },
  safeAreaView: {
    backgroundColor: '#fff',
    // flex: 1,
    marginLeft: 8,
    marginTop: -4
  },
  saleIcon: {
    height: 14,
    width: 38
  },
  sectionRate: {
    // flexDirection: 'row',
    // justifyContent: 'space-between',
    marginHorizontal: 8
  },
  storeName: {
    color: '#999999',
    fontSize: 11,
    marginTop: 4,
    textAlign: 'center'
  },
  textAddress: {
    color: '#9D9D9D',
    fontSize: 12,
    marginTop: 5
  },
  textBtnAdd: {
    alignItems: 'center',
    borderColor: color.primary,
    borderRadius: 3,
    borderWidth: 1,
    color: color.primary,
    justifyContent: 'center',
    margin: 6,
    marginBottom: 8,
    padding: 8,
    textAlign: 'center'
  },
  textDiscount: {
    color: '#fff',
    fontSize: 12,
  },
  textNonProduct: {
    color: '#333',
    flex: 1,
    fontSize: 14,
    marginTop: 30,
    textAlign: 'center'
  },
  textOldPrice: {
    color: '#9D9D9D',
    fontFamily: typography.normal,
    fontSize: 10,
    textDecorationLine: 'line-through'
  },
  textSaleOff: {
    color: color.primary,
    fontFamily: typography.normal,
    fontSize: 10,
    marginLeft: 8
  },
  textTitle: {
    color: '#333',
    flex: 1,
    fontSize: 14,
    marginLeft: 10,
  },
  titleBar: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 6
  },
  viewIcon: {
    bottom: 2,
    position: 'absolute',
    right: 2
  },
  viewImage: {
    borderRadius: 2,
    height: 170,
    padding: 4,
    resizeMode: 'cover',
    width: '100%'
  }
})
export default styles
