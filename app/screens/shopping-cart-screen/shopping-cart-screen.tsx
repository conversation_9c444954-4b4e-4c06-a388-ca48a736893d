import React, { useContext, useEffect, useRef, useState } from 'react'
import { TouchableOpacity, View, Text, FlatList, TextInput, Image, Appearance } from 'react-native'
import Icon from 'react-native-vector-icons/Ionicons'
import styles from './styles'
// import DialogInput from 'react-native-dialog-input'
import { TButton, RenderBranch, LazyImage, ButtonBack, TTextInput, ConfirmDialog } from '@app/components'
import { useTranslation } from 'react-i18next'
import { useStores } from '@app/models'
import { ModalContext } from '@app/context'
import { SCREENS } from '@app/navigation'
import { StackActions, useNavigation } from '@react-navigation/native'

import { observer } from 'mobx-react-lite'

import _ from 'lodash'
import { Header } from 'react-native-elements'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Modalize } from 'react-native-modalize'
import { color } from '@app/theme'
import { numberFormat } from '@app/utils/number'
import { imageDiscount } from '@app/assets/images'
import LinearGradient from 'react-native-linear-gradient'
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import { BookingType } from '@app/constants/bookingType'

const numeral = require('numeral')

export interface RenderChooseServiceProps {
  /**
   * An optional style override useful for padding & margin.
   */
  storeId:any
  chooseData:any
  type:any
}

const colorScheme = Appearance.getColorScheme()
const isDarkModeEnabled = colorScheme === 'dark'

export const ShoppingCartScreen = observer(function ShoppingCartScreen(props) {
  const { t } : any = useTranslation()
  const regex = new RegExp('VND', 'g')
  const { navigate, goBack } = useNavigation()
  const { serviceStore, bookingStore, profileStore, productStore } = useStores()
  const [note, setNote] = useState('')
  const [noteSave, setNoteSave] = useState('')
  const storeId = serviceStore.storeId
  const [selectedAddressIndex, setSelectedAddressIndex] = useState(0)
  const [selectedAddressUserIndex, setSelectedAddressUserIndex] = useState(0)
  const [priceBooking, setPriceBooking] = useState(0)
  const [branchName, setBranchName] = useState('')
  const [branchAddress, setBranchAddress] = useState('')
  const [branchPhone, setBranchPhone] = useState('')
  const [dataBranch, setDataBranch] = useState<any>(bookingStore.branchStore)
  const [isSubmitting, setSubmitting] = useState(false)
  const [isValidCoupon, setIsValidCoupon] = useState(false)
  const [feeShip, setFeeShip] = useState(0)
  const [totalPrice, setTotalPrice] = useState(0)
  const [isDialogVisible, setIsDialogVisible] = useState(false)
  const [addressPickup, setAddressPickup] = useState('')
  const navigation = useNavigation()
  const { showError, showCustomError, showSuccess } = useContext(ModalContext)
  const modalInputNote = useRef<Modalize>(null)
  const modalInputCoupon = useRef<Modalize>(null)
  const [inputText, setInputText] = useState('')
  const [isShowConfirmDeleteProd, setIsShowConfirmDeleteProd] = useState(false)
  const [id, setId] = useState('')
  const [refreshAddress, setRefreshAddress] = useState(false)
  const [discountShop, setDiscountShop] = useState(0)
  const [discountG, setDiscountG] = useState(0)
  const [gCoupon, setGCoupon] = useState('')

  useEffect(() => {
    if (profileStore.reloadData) {
      setRefreshAddress(true)
      profileStore.setReloadData(false)
    }
  }, [profileStore.reloadData])

  const onGoBack = () => {
    goBack()
    setTimeout(() => {
      clearCouponG()
      onClearCoupon()
    }, 100)
  }

  useEffect(() => {
    if (bookingStore.valueCouponG && bookingStore.valueCoupon) {
      setTotalPrice(priceBooking - (bookingStore.valueCoupon + bookingStore.valueCouponG))
    } else if (bookingStore.valueCouponG && !bookingStore.valueCoupon) {
      setTotalPrice(priceBooking - bookingStore.valueCouponG)
    } else if (bookingStore.valueCoupon && !bookingStore.valueCouponG) {
      setTotalPrice(priceBooking - bookingStore.valueCoupon)
    } else if (!bookingStore.valueCouponG && !bookingStore.valueCoupon) {
      setTotalPrice(priceBooking - (bookingStore.valueCoupon + bookingStore.valueCouponG))
    }
  }, [bookingStore.valueCouponG, bookingStore.valueCoupon])

  const formatMoney = (value) => {
    return numeral(value).format('0,0')
  }

  const getChooseData = (chooseData) => {
    return chooseData && chooseData.classify && chooseData.classify.length
  }

  const onOpen = () => {
    // __DEV__ && console.log('onOpen')
    modalInputNote.current?.open()
  }
  const onClose = () => {
    // __DEV__ && console.log('onClose')
    modalInputNote.current?.close()
  }

  const onOpenModalInputCoupon = () => {
    modalInputCoupon.current?.open()
  }
  const onCloseModalInputCoupon = () => {
    modalInputCoupon.current?.close()
  }

  useEffect(() => {
    // __DEV__ && console.log('selectedAddressIndex, dataBranch')
    if (dataBranch.length) {
      setBranchName(dataBranch[selectedAddressIndex].name)
      setBranchAddress(dataBranch[selectedAddressIndex].address)
      setBranchPhone(dataBranch[selectedAddressIndex].phone)
    }
  }, [selectedAddressIndex, dataBranch])

  useEffect(() => {
    // __DEV__ && console.log('selectedAddressUserIndex')
    if (profileStore.addressList.length) {
      setAddressPickup(profileStore.addressList[selectedAddressUserIndex].address)
    }
  }, [selectedAddressUserIndex])

  const goBackServiceDetail = () => {
    if (bookingStore.cart && bookingStore.cart.length == 0) {
      bookingStore.setValueCoupon(0)
      bookingStore.setCoupon('')
      setTotalPrice(0)
      setPriceBooking(0)
      goBack()
    }
  }

  useEffect(() => {
    goBackServiceDetail()
  }, [])

  useEffect(() => {
    // console.log('=-=-=-=-=', bookingStore.cart.length)
    goBackServiceDetail()
    if (bookingStore.cart.length) {
      const profit = _.sumBy(bookingStore.cart, function (price) {
        return price.price
      })
      setPriceBooking(profit)
      setTotalPrice(profit)
      const branchsHaveItem = bookingStore.cart[0].branchList
      // chỉnh sửa chỉ hiển thị những cơ sở nào sản phẩm được cấu hình
      setDataBranch(bookingStore.branchStore.filter(b => branchsHaveItem.includes(b._id)))
      // __DEV__ && console.log('dasjkdhskajhdjksahjkdsa', profit)
    }
    onClearCoupon()
    clearCouponG()
  }, [bookingStore.cart.length])

  // useEffect(() => {
  //   onCalculate().then(r => {})
  //   onCheckCouponG().then(r => {})
  // }, [priceBooking])

  const onClearCoupon = () => {
    bookingStore.setValueCoupon(0)
    bookingStore.setCoupon('')
  }

  const clearCouponG = () => {
    bookingStore.setValueCouponG(0)
    bookingStore.setGeneralCoupon('')
  }

  const bookingService = async () => {
    setSubmitting(true)
    if (serviceStore.typeBooking == 1 && note == '') {
      showError(t('FAIL'), t('ALERT_chuanhap_trieuchung'))
      setSubmitting(false)
      return
    }
    const replaceNote = note || 'Không có ghi chú'
    const bookingData = {
      userId: profileStore._id,
      storeId: serviceStore.storeId,
      phone: profileStore.phone,
      items: bookingStore.cart,
      note: replaceNote,
      branchName: branchName,
      branchAddress: branchAddress,
      price: totalPrice,
      totalServicePrice: priceBooking,
      branchPhone: branchPhone,
      coupon: bookingStore.coupon,
      gCoupon: bookingStore.generalCoupon,
      valueCoupon: bookingStore.valueCoupon,
      valueCouponG: bookingStore.valueCouponG,
      addressPickup: addressPickup,
      bookingType: serviceStore.typeBooking,
    }
    navigation.navigate(SCREENS.reviewBooking, {
      bookingData
    })
    setSubmitting(false)
  }

  const bookingShowroom = async () => {
    setSubmitting(true)
    const bookingData = {
      userId: profileStore._id,
      storeId: serviceStore.storeId,
      phone: profileStore.phone,
      items: bookingStore.cart,
      note: note || 'Không có ghi chú',
      branchName: branchName,
      branchAddress: branchAddress,
      price: totalPrice,
      totalServicePrice: priceBooking,
      branchPhone: branchPhone,
      addressPickup: addressPickup,
      bookingType: serviceStore.typeBooking,
    }
    // navigation.navigate(SCREENS.reviewBooking, {
    //   bookingData
    // })
    // call api submit thẳng
    bookingStore.bookingShowroom(bookingData).then(rs => {
      if (rs && rs.data?.error) {
        showError(t('FAIL'), `${rs.data.message}`)
        setSubmitting(false)
      } else {
        goBookingDetail(rs?.data?.data?.orderId)
        // setTimeout(() => {
        //   clearField()
        // }, 200)
        setSubmitting(false)
      }
    }).catch(err => {
      showError(t('FAIL'), 'unknown')
      console.error(err)
      setSubmitting(false)
    })
    setSubmitting(false)
  }

  const goBookingDetail = (orderId) => {
    bookingStore.clearCart()
    goBack()
    navigation.dispatch(
      StackActions.replace(SCREENS.bookingHistoryDetail, { orderId: orderId, bookingType: serviceStore.typeBooking, action: 'order' })
    )
    showSuccess(t('THANHCONG'), t('BOOKING_SPA_OK'))
    bookingStore.setBookingStatus(true)
  }

  const onCalculate = async () => {
    const rs = await bookingStore.checkCoupon(inputText, priceBooking, feeShip, storeId) // TODO goi api tinh gia
    if (rs) {
      if (rs && rs.data.error) {
        showCustomError(t('FAIL'), `${rs.data.message}`)
        onClearCoupon()
      } else {
        const data = rs?.data?.data
        setDiscountShop(data.discount)
        bookingStore.setValueCoupon(data.discount)
        if (data.coupon && !data.rsCoupon.error) {
          bookingStore.setCoupon(data.coupon.code)
          setInputText('')
        }
        if (data.coupon === null) {
          onClearCoupon()
        }
        // setTotalPrice(data.totalPrice)
        setIsValidCoupon(data.discount && data.discount > 0)
        if (data.rsCoupon.error) {
          bookingStore.setCoupon('')
          showCustomError(t('FAIL'), `${data.rsCoupon.msg}`)
        }
      }
    }
  }

  const onCheckCouponG = async () => {
    const rs = await bookingStore.checkCoupon(gCoupon, priceBooking, feeShip, '') // TODO goi api tinh gia
    if (rs) {
      if (rs && rs.data.error) {
        showCustomError(t('FAIL'), `${rs.data.message}`)
        clearCouponG()
      } else {
        const data = rs?.data?.data
        // setTotalPrice(data.totalPrice)
        setDiscountG(data.discount)
        if (data.coupon && !data.rsCoupon.error) {
          bookingStore.setGeneralCoupon(data.coupon.code)
          setGCoupon('')
        }
        bookingStore.setValueCouponG(data.discount)
        // setIsValidCoupon(data.discount && data.discount > 0)
        if (data.rsCoupon.error) {
          showCustomError(t('FAIL'), `${data.rsCoupon.msg}`)
        }
      }
    }
  }

  const onEnterCoupon = async () => {
    if (inputText === '' || !inputText) {
      setSubmitting(false)
      showCustomError(t('FAIL'), t('INPUT_COUPON'))
    } else if (inputText) {
      setIsDialogVisible(false)
      bookingStore.setCoupon(inputText)
    }
  }

  const renderHeaderModalInput = () => (
    <View style={styles.modal__header}>
      <Text onPress={onClose}>{t('CANCEL')}</Text>
      {/* <ButtonBack onPress={onClose} style={styles.viewTouchButtonTop} /> */}
      <Text style={styles.textTitleHeader}>{ serviceStore.typeBooking == 1 ? t('Nhập tình trạng') : t('Add_note')}</Text>
      <TouchableOpacity
        onPress={() => {
          setNoteSave(note)
          onClose()
        }}
      >
        <Text>{t('XONG')}</Text>
      </TouchableOpacity>

    </View>
  )

  const renderInputNote = () => {
    return (
      <View style={{ paddingBottom: 70, paddingHorizontal: 8 }}>
        <TextInput
          placeholder={t('NHAPNOIDUNG')}
          defaultValue={noteSave}
          underlineColorAndroid="transparent"
          autoCapitalize='none'
          style={styles.textInputNote}
          numberOfLines={4}
          maxLength={200}
          multiline={true}
          onChangeText={e => setNote(e.trim())}
        />
      </View>
    )
  }

  const showAlert = () => {
    setIsShowConfirmDeleteProd(true)
  }

  const renderCart = ({ item, index }) => {
    __DEV__ && console.log('renderCart', item)
    return (
      <View style={styles.containerServiceChoose}>
        <View style={styles.viewImageService}>
          <LazyImage style={styles.imageChoose} source={{ uri: item?.image }} resizeMode="cover"/>
        </View>
        <View style={styles.viewService}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <TouchableOpacity>
              <Text numberOfLines={2} style={styles.textTitleService}>{item?.serviceName}</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => {
              setId(item._id)
              showAlert()
            }}>
              <Icon name={'close-outline'} size={20} color={'#9d9d9d'}/>
            </TouchableOpacity>
          </View>
          <Text numberOfLines={2} style={{ color: '#9d9d9d', width: '90%' }}>{item.shortDes}</Text>
          <View style={styles.viewBtnItem}>
            {Number(item?.price > 0) ? <Text style={styles.textContent}>{formatMoney(item?.price)} <Text style={[styles.textContent, { textDecorationLine: 'underline' }]}>đ</Text></Text> : null}
          </View>
        </View>
      </View>
    )
  }

  const renderBtnInputCoupon = () => {
    return (
      <View style={styles.viewDiscount}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text style={styles.textLabelDiscount}>{t('DISCOUNT')}</Text>
          {bookingStore.generalCoupon ? <View style={{ flexDirection: 'row' }}>
            <Text style={styles.discountCode}>{bookingStore.generalCoupon}</Text>
            <View style={styles.viewDiscountValue}>
              <Text style={styles.discountValue}> - {numberFormat(bookingStore.valueCouponG)} VNĐ</Text>
            </View>
          </View> : null}
        </View>
        {bookingStore.generalCoupon ? <TouchableOpacity
          onPress={() => clearCouponG()}
        >
          <Icon name={'close-outline'} color={color.primary} size={20}/>
        </TouchableOpacity> : <TouchableOpacity
          onPress={onOpenModalInputCoupon}
          style={{ flexDirection: 'row' }}>
          <Text style={{ fontWeight: '500', fontSize: 14, color: '#979797' }}>Nhập mã giảm giá</Text>
          <Icon name={'chevron-forward-outline'} color={'#979797'} size={17}/>
        </TouchableOpacity>}
      </View>
    )
  }

  const renderHeaderModalInputCoupon = () => (
    <View style={styles.modal__header}>
      <TouchableOpacity onPress={onCloseModalInputCoupon}>
        <Text >{t('CANCEL')}</Text>
      </TouchableOpacity>
      {/* <ButtonBack onPress={onClose} style={styles.viewTouchButtonTop} /> */}
      <Text style={styles.textTitleHeader}>{t('DISCOUNT')}</Text>
      <TouchableOpacity
        onPress={() => {
          onCloseModalInputCoupon()
          setTimeout(() => { onCheckCouponG() }, 100)
        }}
      >
        <Text style={styles.textDone}>{t('XONG')}</Text>
      </TouchableOpacity>

    </View>
  )

  const ListFooter = () => {
    return (
      <View>
        {/* <View style={styles.viewFlatListBranch}> */}
        {/*  <RenderClassify chooseData={chooseData} selected0ClassifyIndex={(selectedClassifyIndex) => { setSelectedClassifyIndex(selectedClassifyIndex) }}/> */}
        {/* </View> */}
        <View style={{ marginBottom: 5, marginTop: 4 }}>
          <View style={styles.viewFlatListBranch}>
            <View style={styles.viewTextLabelFooter}>
              <Text style={styles.textLabel}>{t('CHOOSE_BRANCH')}</Text>
            </View>
            <RenderBranch defaultIndex={0} dataBranch={dataBranch} selectedAddressIndex={(selectedAddressIndex) => { setSelectedAddressIndex(selectedAddressIndex) }}/>
            {serviceStore.typeBooking != BookingType.SHOWROOM ? renderInputCoupon() : null}
          </View>
        </View>
        <View style={styles.viewAddressUser}>
          <View style={styles.viewTextLabelAddressUser}>
            <View style={styles.containerAddressUS}>
              <Text style={styles.textLabel}>{t('customer_information')}</Text>
              <Text onPress={() => { navigate(SCREENS.renderAddAddress) }} style={styles.textAddaddress}>{t('ADDADDRESS')}</Text>
            </View>
          </View>
          <RenderBranch dataBranch={profileStore.addressList} selectedAddressIndex={(selectedAddressUserIndex) => { setSelectedAddressUserIndex(selectedAddressUserIndex) }} refreshAddress={refreshAddress}/>
        </View>
        <View style={{ backgroundColor: '#fff', marginBottom: 5, paddingHorizontal: 15 }}>
          <View style={styles.viewTextLabel}>
            <Text style={styles.textLabel}>{serviceStore.typeBooking == BookingType.CLINIC ? t('TRIEUCHUNG') : t('NOTE')}</Text>
          </View>
          <TouchableOpacity
            onPress={() => onOpen()}
            style={styles.viewTextNote}>
            <Icon name={'chatbox-ellipses-outline'} size={20} color={'#333'} />
            {noteSave ? <Text numberOfLines={1} style={styles.btnNote}>{noteSave}</Text> : <Text numberOfLines={1} style={styles.btnNote}>{serviceStore.typeBooking == 1 ? t('Thêm tình trạng') : t('Add_note')}</Text>}
            {/* <TextInput */}
            {/*  placeholder={serviceStore.typeBooking == 1 ? t('TRIEUCHUNG') : t('INPUT_NOTE')} */}
            {/*  underlineColorAndroid="transparent" */}
            {/*  autoCapitalize='none' */}
            {/*  style={styles.textInputNote} */}
            {/*  numberOfLines={4} */}
            {/*  multiline={true} */}
            {/*  onChangeText={e => setNote(e)} */}
            {/* /> */}
          </TouchableOpacity>
        </View>
        {serviceStore.typeBooking != BookingType.SHOWROOM ? renderBtnInputCoupon() : null}
        { priceBooking && serviceStore.typeBooking != BookingType.SHOWROOM ? <View style={styles.viewFlatListPrice}>
          <View style={styles.viewTextLabel}>
            <Text style={styles.textLabel}>{t('TOTAL_MONEY')} dịch vụ</Text>
          </View>
          <View style={styles.renderItemListPrice}>
            <Text style={styles.label}>{t('SERVICE_PRICE')}</Text>
            <Text style={styles.price}>{formatMoney(priceBooking)} <Text style={[styles.price, { textDecorationLine: 'underline' }]}>đ</Text></Text>
          </View>
          <View style={styles.renderItemListPrice}>
            <Text style={styles.label}>{t('CHIPHI_VANCHUYEN')}</Text>
            <Text style={styles.price}>{feeShip} <Text style={[styles.price, { textDecorationLine: 'underline' }]}>đ</Text></Text>
          </View>
          {bookingStore.valueCoupon > 0 ? <View style={styles.renderItemListPrice}>
            <View style={styles.containerCoupon}>
              <Text style={styles.label}>{t('Voucher cửa hàng')}</Text>
            </View>
            <Text style={styles.price}> - {formatMoney(bookingStore.valueCoupon)} <Text style={[styles.price, { textDecorationLine: 'underline' }]}>đ</Text></Text>
          </View> : null}
          {bookingStore.valueCouponG > 0 ? <View style={styles.renderItemListPrice}>
            <Text style={styles.label}>{t('Voucher')}</Text>
            <Text style={styles.price}> - {formatMoney(bookingStore.valueCouponG)} <Text style={[styles.price, { textDecorationLine: 'underline' }]}>đ</Text></Text>
          </View> : null}
          <View style={styles.renderItemListPrice}>
            <Text style={styles.label}>{t('HAVETOPAY')}</Text>
            <Text style={styles.price}>{formatMoney(totalPrice)} <Text style={[styles.price, { textDecorationLine: 'underline' }]}>đ</Text></Text>
          </View>
          {/* <View style={styles.renderItemListPrice}> */}
          {/*  <Text style={styles.label}>{t('TOTAL_MONEY')}</Text> */}
          {/*  <Text style={styles.price}>{formatMoney(totalPrice)} đ</Text> */}
          {/* </View> */}
        </View> : null}
      </View>
    )
  }

  const ListHeader = () => {
    return (
      <View>
        {/* <View style={{ flexDirection: 'row', paddingBottom: 10, justifyContent: 'space-between' }}> */}
        {/*  <ButtonBack onPress={goBack} style={styles.icArrowBack}/> */}
        {/*  <Text style={styles.textTitleTotal}>{t('GIOHANG')}</Text> */}
        {/*  <TouchableOpacity */}
        {/*    onPress={() => navigate(SCREENS.home)} */}
        {/*    style={styles.iconHome} */}
        {/*  > */}
        {/*    <Image style={styles.iconImage} source={iconHomeActive4} /> */}
        {/*  </TouchableOpacity> */}
        {/* </View> */}
        {/* <View style={styles.viewTextLabelHeader}> */}
        {/*  <Text style={styles.textLabel}>{t('CART_CHOOSE')}</Text> */}
        {/* </View> */}
        <Text style={styles.titleListService}>{serviceStore.typeBooking == BookingType.SHOWROOM ? 'Bạn đã chọn xe' : 'Dịch vụ đã chọn' }</Text>
      </View>
    )
  }

  const renderInputCoupon = () => {
    return (
      <View>
        <View style={styles.viewTtinput}>
          {/* <TextInput */}
          {/*  keyboardType="default" */}
          {/*  maxLength={16} */}
          {/*  autoCapitalize={'none'} */}
          {/*  // value={productStore.coupon} */}
          {/*  placeholderTextColor={'#a0a0a0'} */}
          {/*  placeholder={t('NHAPMAGIAMGIA')} */}
          {/*  underlineColorAndroid="transparent" */}
          {/*  style={styles.textInputCoupon} */}
          {/*  onChangeText={text => setInputText(text)} */}
          {/* /> */}
          <View style={styles.textInputCoupon}>
            <TTextInput
              typeInput={'code'}
              typeRadius={'rounded'}
              keyboardType="default"
              maxLength={12}
              autoCapitalize={'none'}
              defaultValue={inputText || ''}
              placeholder={t('NHAPMAGIAMGIA')}
              placeholderStyle={{ textAlign: 'center' }}
              onChangeText={text => setInputText(text)}
              iconRightClick={() => setInputText('')}
              iconRight={<Icon name='close-circle-outline' size={24} color='#a0a0a0' />}
            />
          </View>
          {bookingStore.valueCoupon && isValidCoupon ? <TButton typeRadius={'rounded'} title={t('DELETE')} onPress={onClearCoupon} buttonStyle={{ flex: 1, minWidth: 80 }}/> : <TButton typeRadius={'rounded'} title={t('APDUNG')} onPress={onCalculate} buttonStyle={{ flex: 1, minWidth: 80 }}/>}
        </View>
        <View style={styles.viewInputCoupon}>
          <View style={styles.containerCoupon}>
            <Text style={styles.subTitle}>{t('DISCOUNT')}</Text>
            {bookingStore.coupon && isValidCoupon ? <Text onPress={() => { setIsDialogVisible(true) }} style={styles.discountCode}>{bookingStore.coupon}</Text>
              : null}
            { bookingStore.valueCoupon && isValidCoupon ? <View style={styles.viewDiscountValue}>
              <Text style={styles.discountValue}>- {numberFormat(bookingStore.valueCoupon)} VNĐ</Text>
            </View> : null}
            {bookingStore.coupon && isValidCoupon ? <TouchableOpacity onPress={() => { onClearCoupon() }}><Icon style={{ marginLeft: 10 }} name={'close-outline'} size={20} color={color.primary}/></TouchableOpacity> : null}
          </View>
          {/* {bookingStore.valueCoupon && isValidCoupon ? <Text style={styles.subTitle}>(Giảm {bookingStore.valueCoupon && isValidCoupon ? bookingStore.valueCoupon : 0}{bookingStore.discountType === 1 ? ' %' : ''} giá trị đơn hàng)</Text> : null} */}
        </View>
      </View>
    )
  }

  const renderInputCouponG = () => {
    return (
      <View style={{ paddingBottom: 70, paddingHorizontal: 16 }}>
        <View style={{ alignItems: 'center', paddingVertical: 35 }}>
          <Image source={imageDiscount} style={{ width: 135, height: 120, marginBottom: 10 }}></Image>
          <Text style={styles.description}>Bạn không có mã giảm giá có sẵn nào</Text>
          <Text style={styles.description}>Nhập mã giảm giá của bạn ở bên dưới</Text>
        </View>
        <TTextInput
          typeInput={'code'}
          typeRadius={'rounded'}
          // keyboardType="decimal-pad"
          maxLength={12}
          autoCapitalize={'none'}
          defaultValue={gCoupon || ''}
          placeholder={t('NHAPMAGIAMGIA')}
          placeholderStyle={{ textAlign: 'center' }}
          onChangeText={e => setGCoupon(e)}
          iconRightClick={() => setGCoupon('')}
          iconRight={<Icon name='close-circle-outline' size={24} color='#a0a0a0' />}
        />
      </View>
    )
  }

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left',]}>
      <Header
        // statusBarProps={{ barStyle: 'light-content' }}
        // barStyle="light-content" // or directly
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={onGoBack}/>}
        centerComponent={{ text: serviceStore.typeBooking == BookingType.SHOWROOM ? 'Đăng ký mua/ Lái thử' : t('SERVICE'), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        rightComponent={<TouchableOpacity
          onPress={() => navigate(SCREENS.home)}
          style={styles.iconHome}
        >
          {/* <Image style={styles.iconImage} source={iconHomeActive4} /> */}
        </TouchableOpacity>}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      <View style={{ backgroundColor: color.primaryBackground, flex: 1, paddingBottom: 60 + useSafeAreaInsets().bottom }}>
        {/* <Text style={styles.titleListService}>Dịch vụ đã chọn</Text> */}
        <FlatList
          data={bookingStore.cart}
          renderItem={renderCart}
          keyExtractor={(item, index) => item._id}
          showsVerticalScrollIndicator={false}
          ListHeaderComponent={ListHeader()}
          ListFooterComponent={ListFooter()}
        />
      </View>
      <View style={[styles.viewBtnSubmit, { paddingBottom: useSafeAreaInsets().bottom }]}>
        {serviceStore.typeBooking != BookingType.SHOWROOM && <TButton buttonStyle={styles.btn} disabled={ isSubmitting} loading={isSubmitting} title={serviceStore.typeBooking == BookingType.CLINIC ? t('BOOKING') : t('THANHTOAN')} onPress={bookingService} typeRadius={'rounded'} /> }
        {serviceStore.typeBooking == BookingType.SHOWROOM && <TButton buttonStyle={styles.btn} disabled={ isSubmitting} loading={isSubmitting} title={'Tiếp theo'} onPress={bookingShowroom} typeRadius={'rounded'} /> }
      </View>
      <>
        <Modalize
          HeaderComponent={renderHeaderModalInput}
          ref={modalInputNote}
          adjustToContentHeight
          // snapPoint={405}
          // modalHeight={405}
          // onClosed={() => {}}
          keyboardAvoidingBehavior={'padding'}
        >
          {renderInputNote()}
        </Modalize>
      </>
      <Modalize
        HeaderComponent={renderHeaderModalInputCoupon}
        ref={modalInputCoupon}
        adjustToContentHeight
        // snapPoint={405}
        // modalHeight={405}
        // onClosed={() => {}}
        keyboardAvoidingBehavior={'padding'}
      >
        {serviceStore.typeBooking != BookingType.SHOWROOM && renderInputCouponG() }
      </Modalize>
      <ConfirmDialog confirmText={t('DONGY')} cancelText={t('CANCEL')} isVisible={isShowConfirmDeleteProd} message={t('CHACCHANMUONXOABOSP')}
        onConfirm={() => {
          setIsShowConfirmDeleteProd(false)
          setTimeout(() => {
            clearCouponG()
            onClearCoupon()
            bookingStore.deleteCartOfList(id)
          }, 200)
        }}
        onClosed={() => setIsShowConfirmDeleteProd(false)}
      />
    </SafeAreaView>
  )
})
