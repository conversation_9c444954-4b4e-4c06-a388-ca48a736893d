
import React, { useContext, useEffect, useState } from 'react'
import {
  View,
  Text,
  ScrollView, TextInput, TouchableOpacity, Dimensions
} from 'react-native'
import { useTranslation } from 'react-i18next'
import { observer } from 'mobx-react-lite'
import { ConfirmDialog, LazyImage, TButton } from '@app/components'
import styles from './styles'
import { Api } from '../../services/api'
import { ModalContext } from '@app/context'
import { useNavigation } from '@react-navigation/native'

import { useStores } from '@app/models'
import validate from 'validate.js'
import { typography } from '@app/theme'
import { PickerSelect } from '@app/components/picker-select/picker-select'

export const CancelOrderScreen = observer((props: any) => {
  const { t } : any = useTranslation()
  const numeral = require('numeral')
  const bookingData = props.bookingData
  const bookingType = props.bookingType
  const [serviceDetail, setServiceDetail] = useState([])
  const { goBack } = useNavigation()
  const { showError, showSuccess } = useContext(ModalContext)
  const [noteCancel, setNoteCancel] = useState('')
  const [reasonCancel, setReasonCancel] = useState('')
  const [isPickerSelectVisible, setIsPickerSelectVisible] = useState(false)
  const { profileStore } = useStores()
  const [isShowConfirmDeleteProd, setIsShowConfirmDeleteProd] = useState(false)

  const formatMoney = (value) => {
    return numeral(value).format('0,0')
  }
  let index = 0
  const data = [
    { _id: index++, label: t('transportation_staff_arrived_too_slow'), value: '' },
    { _id: index++, label: t('change_the_address'), value: '' },
    { _id: index++, label: t('schedule_change'), value: '' },
    { _id: index++, label: t('wrong_operation'), value: '' },
    { _id: index++, label: t('change_your_mind'), value: '' },
    { _id: index++, label: t('other_reason'), value: '' },
  ]

  useEffect(() => {
    setServiceDetail(bookingData.items)
    if (!bookingData.orderId) {
      goBack()
    }
    loadData().then(r => {})
  }, [])

  const validateFields = () => validate.isEmpty(reasonCancel)

  const loadData = async () => {
    const api = new Api()
    if (bookingType == 0) {
      const rs = await api.getBookingProductDetail(bookingData.orderId)
      __DEV__ && console.log('LOAD DATA BOOKING DETAIL', rs)
      if (rs && rs.data) {
        setServiceDetail(bookingData.items)
      }
    }
    if (bookingType == 1) {
      const rs = await api.getBookingClinicDetail(bookingData.orderId)
      __DEV__ && console.log('LOAD DATA BOOKING DETAIL', rs)
      if (rs && rs.data) {
        setServiceDetail(bookingData.items)
      }
    }

    if (bookingType === 2) {
      const rs = await api.getBookingHotelDetail(bookingData.orderId)
      __DEV__ && console.log('LOAD DATA BOOKING HOTEL DETAIL', rs)
      if (rs && rs.data) {
        setServiceDetail(bookingData.items)
      }
    }
    if (bookingType === 3) {
      const rs = await api.getBookingSpaDetail(bookingData.orderId)
      __DEV__ && console.log('LOAD DATA BOOKING DETAIL', rs)
      if (rs && rs.data) {
        setServiceDetail(bookingData.items)
      }
    }
  }

  const showAlert = () => {
    setIsShowConfirmDeleteProd(true)
  }

  const cancelBooking = async () => {
    // __DEV__ && console.log('CANCEL BOOKING')
    const api = new Api()
    if (bookingType === 0) {
      const rs = await api.cancelBookingProduct(bookingData._id, reasonCancel, noteCancel)
      if (rs?.data && rs?.kind === 'ok') {
        loadData().then(r => {
        })
        showSuccess(t('THANHCONG'), t('The_order_has_been_canceled'))
        setTimeout(() => {
          profileStore.setReLoadStatus(true)
          goBack()
        }
        , 2500,
        )
      } else {
        showError(t('FAIL'), 'unknown')
      }
    } else {
      const rs = await api.cancelBooking(bookingData._id, bookingType, reasonCancel, noteCancel)
      if (rs?.data && rs?.kind === 'ok') {
        loadData().then(r => {
        })
        showSuccess(t('THANHCONG'), t('CANCEL_BOOKING_COMPLETE'))
        setTimeout(() => {
          profileStore.setReLoadStatus(true)
          goBack()
        }
        , 2500,
        )
      } else {
        showError(t('FAIL'), 'unknown')
      }
    }
  }

  return (
    <View >
      <View style={styles.background}>
        <ScrollView
          bounces={false}
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}>
          <View style={styles.viewStatusText}>
            <Text style={styles.titleTextStatus}>{t('ODER_STATUS')}</Text>
            {bookingData && bookingData.status === 0 ? <Text style={{ color: 'orange', fontSize: 12, fontFamily: typography.normal, }}>{t('CHOXACNHAN')}</Text> : bookingData.status === 1 ? <Text style={{ color: 'blue', fontSize: 12 }}>{t('CHOLAMDV')}</Text> : bookingData.status === 2 ? <Text style={{ color: 'red', fontSize: 12 }}>{t('CANCELED')}</Text> : bookingData.status === 3 ? <Text style={{ color: 'green', fontSize: 12 }}>{t('DONE')}</Text> : null }
          </View>
          {serviceDetail?.map((item, index) => {
            return (<View key={index} style={styles.containerServiceChoose}>
              <View>
                <View style={styles.viewImageService}>
                  <LazyImage style={styles.imageChoose} source={{ uri: item.image }} resizeMode="cover"/>
                </View>
              </View>
              <View style={styles.viewService}>
                <TouchableOpacity>
                  <Text numberOfLines={1} style={styles.textTitleService}>{item.serviceName}</Text>
                </TouchableOpacity>
                <View style={styles.viewBtnItem}>
                  <Text style={styles.textContent}>{formatMoney(item.price)}</Text>
                </View>
              </View>
            </View>)
          })}
        </ScrollView>
      </View>
      <View style={styles.viewContentRefund}>
        <Text style={styles.titleTextRefund}>{t('REFUND_REGULATION')}</Text>
        <Text style={styles.contentTextRefund}>1. {t('REFUND_ONLY_FOR_CUSTOMER_PAYING_ONLINE')}</Text>
        <Text style={styles.contentTextRefund}>2. {t('REFUND_REQUEST_WHEN_THE_PET_HAS_NOT_BEEN_SHIPPED')}</Text>
        <Text style={styles.contentTextRefund}>3. {t('CUSTOMER_MUST_PROVIDE_THE_CORRECT-ACCOUNT_NUMBER')}</Text>
      </View>
      <View style={styles.viewTextLabel}>
        <Text style={styles.textLabel}>{t('reason_for_cancellation')}</Text>
      </View>
      <TouchableOpacity onPress={() => { setIsPickerSelectVisible(true) }}>
        <Text style={styles.reason}>{reasonCancel || t('TOUCH_TO_CHOOSE_THE_REASON')}</Text>
      </TouchableOpacity>
      <View style={styles.viewTextLabel}>
        <Text style={styles.textLabel}>{t('clearly_explain_the_reason')}</Text>
      </View>
      <TextInput
        onChangeText={(e) => { setNoteCancel(e) }}
        multiline={true}
        placeholder={t('enter_the_reason')}
        style={styles.noteContent}/>
      <View style={styles.groupBtn}>
        <TButton disabled={validateFields()} typeRadius={'rounded'} title= {t('send_request')} buttonStyle={{ width: Dimensions.get('window').width - 32 }} onPress={showAlert}></TButton>
      </View>
      <PickerSelect data={data} title={t('CHOOSE_THE_REASON')} isVisible={isPickerSelectVisible} onSelect={(e) => {
        setIsPickerSelectVisible(!isPickerSelectVisible)
        setReasonCancel(e.label)
      }}
      callBackVisible={() => {
        setIsPickerSelectVisible(!isPickerSelectVisible)
      }}
      goBack={() => {
        setIsPickerSelectVisible(!isPickerSelectVisible)
      }}
      />
      <ConfirmDialog confirmText={t('DONGY')} cancelText={t('CANCEL')} isVisible={isShowConfirmDeleteProd} message={t('CHITIETLICHHEN_alerthuylichhen')} title={'Nhắc nhở'}
        onConfirm={() => {
          setIsShowConfirmDeleteProd(false)
          setTimeout(() => { cancelBooking() }, 100)
        }}
        onClosed={() => setIsShowConfirmDeleteProd(false)}
      />
    </View>
  )
})
export default CancelOrderScreen
