import { StyleSheet } from 'react-native'
import { typography } from '../../theme'

const styles = StyleSheet.create({
  background: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flex: 1,
    justifyContent: 'center',
  },
  containerServiceChoose: {
    flexDirection: 'row',
    marginLeft: 15,
    marginTop: 15
  },
  contentText: {
    alignItems: 'flex-start',
    flexDirection: 'column',
    flex: 1,
    position: 'relative',
    justifyContent: 'space-between'
  },
  contentTextRefund: {
    letterSpacing: 0.5,
    marginVertical: 8,
    width: '100%'
  },
  date: {
    alignSelf: 'flex-end',
    color: '#46474D',
    fontSize: 10,
    paddingHorizontal: 8,
    paddingVertical: 5
  },
  groupBtn: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 10
  },
  imageChoose: {
    borderRadius: 8,
    height: 50,
    width: 50
  },
  noteContent: {
    backgroundColor: '#f7f9fc',
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginHorizontal: 15,
    marginVertical: 10,
    paddingHorizontal: 10,
    paddingVertical: 10
  },
  rdkTop: {
    backgroundColor: '#fff',
    borderRadius: 8,
    elevation: 3,
    flex: 1,
    flexDirection: 'row',
    height: 84,
    justifyContent: 'space-between',
    marginHorizontal: 15,
    marginVertical: 20,

  },
  rdkTopImage: {
    borderRadius: 8,
    height: 84,
    resizeMode: 'cover',
    width: 87
  },
  rdkTopText: {
    color: '#333',
    fontSize: 14,
    fontWeight: '600',
    paddingTop: 5
  },
  rdkTopText1: {
    color: '#46474D',
    fontSize: 12,
    paddingLeft: 8,
    paddingRight: 10,
    paddingTop: 3
  },
  reason: {
    color: '#333',
    fontSize: 14,
    paddingHorizontal: 15,
    paddingVertical: 15,
    textDecorationLine: 'underline'
  },
  scrollView: {
    width: '100%',
  },
  textBtnLogin: {
    color: '#fff',
  },
  textButton: {
    alignItems: 'center',
    fontSize: 14,
    marginLeft: 15,
    marginTop: 5
  },
  textContent: {
    color: '#ff8ba1',
    fontSize: 13,
    fontWeight: '600'
  },
  textLabel: {
    color: '#333',
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0,
    marginLeft: 15
  },
  textTitleService: {
    color: '#333',
    fontSize: 14,
  },
  titleTextRefund: {
    alignSelf: 'center',
    fontSize: 14,
    fontWeight: 'bold',
    letterSpacing: 0.5,
    marginBottom: 10
  },
  titleTextStatus: {
    alignSelf: 'center',
    fontSize: 14,
    fontWeight: 'bold',
    letterSpacing: 0.5
  },
  viewBtnItem: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 15
  },
  viewContentRefund: {
    backgroundColor: '#E3F2FD',
    borderRadius: 8,
    marginBottom: 15,
    marginHorizontal: 15,
    marginTop: 10,
    paddingHorizontal: 15,
    paddingVertical: 8
  },
  viewImageService: {
    alignItems: 'center',
    borderRadius: 16,
    justifyContent: 'center',
    marginLeft: 0,
  },
  viewService: {
    flexDirection: 'column',
    flex: 1,
    marginLeft: 10
  },
  viewStatus: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
    width: '100%',
  },
  viewStatusText: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 15,
    marginRight: 15,
    marginTop: 10,
  },
  viewTextLabel: {
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    flexDirection: 'row',
    height: 30,
  },

})
export default styles
