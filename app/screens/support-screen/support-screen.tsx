import {

  Text,
  View,
  TouchableOpacity,
  ScrollView,
} from 'react-native'
import React, { Fragment } from 'react'
import styles from './styles'
import { useTranslation } from 'react-i18next'
import Icon from 'react-native-vector-icons/Ionicons'
import { useNavigation } from '@react-navigation/native'
import { observer } from 'mobx-react-lite'
import { SCREENS } from '@app/navigation'
import { ButtonBack } from '@app/components'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import LinearGradient from 'react-native-linear-gradient'
import { SafeAreaView } from 'react-native-safe-area-context'

export const SupportScreen = observer(() => {
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()

  const onBack = () => {
    goBack()
  }

  return (
    <Fragment>
      <SafeAreaView style={{ flex: 1, backgroundColor: '#fff', marginTop: -4 }}>
        <Header
          // statusBarProps={{ barStyle: 'light-content' }}
          // barStyle="light-content" // or directly
          leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
          centerComponent={{ text: t(t('TAIKHOAN_trogiup')), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
          // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
          //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
          // </TouchableOpacity> : null}
          containerStyle={common.headerContainer}
          statusBarProps={{ barStyle: 'light-content' }}
          ViewComponent={LinearGradient}
          linearGradientProps={linearGradientProps}
        />
        <ScrollView showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
          <View style={styles.mainContainer}>
            {/* <View style={styles.renderTitle}> */}
            {/*  <ButtonBack onPress={onBack}/> */}
            {/* </View> */}
            {/* <Text style={styles.TitleText}>{t('TAIKHOAN_trogiup')}</Text> */}
            <View style={styles.viewThird}>
              <TouchableOpacity
                onPress={() => navigate(SCREENS.supportCenterScreen, { typeSupport: 'trungTamHoTro', title: t('TT_HO_TRO') })}
                style={styles.boxButton}>
                <Icon name="alert-circle-outline" size={18} color="#c5cee0" style={styles.iconLeft} />
                <Text style={styles.textButton}>{t('TT_HO_TRO')}</Text>
                <Icon
                  size={18}
                  color={'#c5cee0'}
                  style={styles.iconRight}
                  name={'chevron-forward-outline'}
                />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => navigate(SCREENS.supportCenterScreen, { typeSupport: 'chinhSachBaoMat', title: t('CS_BAO_MAT') })}
                style={styles.boxButton}>
                <Icon name="alert-circle-outline" size={18} color="#c5cee0" style={styles.iconLeft} />
                <Text style={styles.textButton}>{t('CS_BAO_MAT')}</Text>
                <Icon
                  size={18}
                  color={'#c5cee0'}
                  style={styles.iconRight}
                  name={'chevron-forward-outline'}
                />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => navigate(SCREENS.supportCenterScreen, { typeSupport: 'dieuKhoanDichVu', title: t('DIEU_KHOAN_DV') })}
                style={styles.boxButton}>
                <Icon name="alert-circle-outline" size={18} color="#c5cee0" style={styles.iconLeft} />
                <Text style={styles.textButton}>{t('DIEU_KHOAN_DV')}</Text>
                <Icon
                  size={18}
                  color={'#c5cee0'}
                  style={styles.iconRight}
                  name={'chevron-forward-outline'}
                />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => navigate(SCREENS.supportCenterScreen, { typeSupport: 'gioiThieu', title: t('GIOI_THIEU') })}
                style={{ ...styles.boxButton, marginBottom: 22 }}>
                <Icon name="alert-circle-outline" size={18} color="#c5cee0" style={styles.iconLeft} />
                <Text style={styles.textButton}>{t('GIOI_THIEU')}</Text>
                <Icon
                  size={18}
                  color={'#c5cee0'}
                  style={styles.iconRight}
                  name={'chevron-forward-outline'}
                />
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </Fragment>
  )
})
