import { StyleSheet, Dimensions } from 'react-native'
import { typography } from '../../theme'

const { width, height } = Dimensions.get('window')
const styles = StyleSheet.create({
  TitleText: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
    marginHorizontal: 15,
    marginTop: 6
  },
  boxButton: {
    flexDirection: 'row',
    marginLeft: 15,
    marginRight: 15
  },
  btnLogin: {
    alignItems: 'center',
    backgroundColor: '#ffa8b4',
    borderRadius: 4,
    elevation: 3,
    height: 44,
    justifyContent: 'center',
    margin: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    width: 100
  },
  btnRegister: {
    alignItems: 'center',
    backgroundColor: '#d2d2d2',
    borderRadius: 4,
    elevation: 5,
    height: 44,
    justifyContent: 'center',
    margin: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,

    shadowRadius: 2.22,
    width: 100
  },
  groupBtn: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 10
  },
  icArrowBack: {
    margin: 11,
  },
  icon: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginRight: 15,
    marginTop: 50,

  },
  iconLeft: {
    marginLeft: 0,
    marginRight: 15,
    marginTop: 25,
  },
  iconRight: {
    marginLeft: 15,
    marginRight: 0,
    marginTop: 25,
  },
  image: {
    borderRadius: 15,
    height: 68,
    marginTop: 15,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10,
    width: 68
  },
  imageNoUser: {
    borderRadius: 15,
    elevation: 2,
    height: 80,
    justifyContent: 'flex-start',
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10,
    width: 80
  },
  mainContainer: {
    backgroundColor: '#fff',
    flex: 1,
    justifyContent: 'flex-start',
  },
  mainTitle: {
    backgroundColor: '#ffffff',
    paddingBottom: 16,
    paddingLeft: 15,
    paddingTop: 16
  },
  mainTitleText: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
  },
  mainUser: {
    backgroundColor: '#ffffff',
    flexDirection: 'column',
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
  },
  profileBox: {
    alignItems: 'center'
  },
  renderTitle: {
    flexDirection: 'row',
    flex: 1,
    paddingTop: 15
  },
  textBtnLogin: {
    color: '#fff',
  },
  textBtnRegister: {
  },
  textButton: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 14,
    justifyContent: 'center',
    letterSpacing: 0,
    marginTop: 25
  },
  textName: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 20,
    fontWeight: '600',
    height: 24,
    marginLeft: 15,

  },
  textPhone: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
    height: 18,
    marginLeft: 15,
    marginTop: 6,
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },

  touchUser: {
    flexDirection: 'row',
    marginLeft: 15,
    marginRight: 15,
    paddingBottom: 10
  },
  viewName: {
    flexDirection: 'column',
    flex: 1,
    justifyContent: 'flex-start',
    marginTop: 29,
  },
  viewSecond: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    flexDirection: 'column',
    marginLeft: 15,
    marginRight: 15,
    marginTop: 20,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10
  },
  viewThird: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    flexDirection: 'column',
    margin: 15,
    marginTop: 20,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  }
})
export default styles
