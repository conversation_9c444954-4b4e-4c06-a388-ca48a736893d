import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import React, { useState, useEffect, useRef, useContext } from 'react'
import {

  View,
  Platform,
  Dimensions,
  Image,
  Text,
  TouchableOpacity,
  ImageBackground,
  ScrollView,
  StatusBar,
} from 'react-native'

import styles from './styles'
import { useTranslation, withTranslation } from 'react-i18next'
import { useNavigation } from '@react-navigation/native'
import { Modalize } from 'react-native-modalize'
import { observer } from 'mobx-react-lite'
import { useStores } from '@app/models'
import moment from 'moment'
import 'moment/locale/vi'
import {
  ButtonBack

} from '../../components'
import { SCREENS } from '@app/navigation'
import { ModalContext } from '@app/context'

import { useAbortableEffect } from '@app/use-hooks'
import { useAuth } from '@app/use-hooks/use-auth'

import { Api } from '@app/services/api'
import { walletIcon, ticketStarIcon } from '@app/assets/images'
import RenderListHeaderFlatlist from '@app/components/FlatlistHorizontal/renderflatlisthorizontal'
import { getStatusBarHeight } from 'react-native-iphone-x-helper'

const api = new Api()

const HEAD_HEIGHT = Platform.OS === 'ios' ? 600 : 545
const { width, height } = Dimensions.get('window')

moment.locale('vi')

export const WalletScreen = observer((props:any) => {
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()
  const modalBranch = useRef<Modalize>(null)
  const modalizeAddService = useRef<Modalize>(null)
  const contentRef = useRef(null)
  const [refreshing] = useState(false)
  const [isFetched, setIsFetched] = useState(true)
  const navigation = useNavigation()
  const { serviceStore, bookingStore, profileStore, notificationStore, homeStore } = useStores()
  //   const storeId = props.route.params.storeId
  //   const userIdOfBrand = props.route.params.userId // user ID create create brand
  const [dataBranch, setDataBranch] = useState([])
  const { showError } = useContext(ModalContext)
  const [index, setIndex] = useState(0)
  //   const [routes, setRoutes] = useState([])
  const { signOut } = useAuth() // should be signUp
  const [isSubmitting, setSubmitting] = useState(true)

  const [bgImageUrl, setBgImageUrl] = useState<any>('')
  const [listProducts, setListProducts] = useState([])
  const [imgHeight, setImgHeight] = useState<any>(280)
  const bgImageHeight = 190 * (width / 375)

  useEffect(() => {
    return () => {

    }
  }, [])

  const getApiData = async () => {
    return Promise.all([
      getApiConfig(),
      getRewardPointProducts()
    ])
  }

  const getApiConfig = async () => {
    const rs = await api.getAppConfig()

    if (rs && rs?.data?.data.attributes?.bg_header_user_wallet.data) {
      setBgImageUrl(rs?.data?.data?.attributes?.bg_header_user_wallet.data.attributes.url)
      setImgHeight(rs?.data?.data?.attributes?.bg_header_user_wallet.data.attributes.height)
    }
  }
  const getRewardPointProducts = async () => {
    const rs = await api.getRewardPointProducts()
    if (rs && rs?.data?.data) {
      setListProducts(rs?.data?.data)
    }
  }

  const loadData = async () => {
    setIsFetched(true)
    getApiData().then(data => {
      setIsFetched(false)
      // serviceStore.setTypeBooking(3)
    //   tabNames() // calculate tab name
    }).catch(err => {
      __DEV__ && console.log(err)
    })
  }

  const goLoginScreenRequired = () => {
    navigation.navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  const logOut = async () => {
    try {
      profileStore.clearFields()
      notificationStore.clearFields()
      signOut() // gọi hàm signout global
      navigate(SCREENS.homeStack)
    } catch (e) {
      __DEV__ && console.log(e)
    }
  }

  const onCloseAddService = () => {
    modalizeAddService.current?.close()
  }

  useAbortableEffect(() => {
    loadData().then(r => {})
  }, [])

  useEffect(() => {
    if (bookingStore.bookingStatus) {
      onCloseAddService()
    }
  }, [bookingStore.bookingStatus])

  useAbortableEffect(() => {
    if (serviceStore.isLastestComments) {
      loadData().then(r => {})
    }
  }, [serviceStore.isLastestComments])

  const renderHeader = () => {
    return (<View style={styles.top}>
      {bgImageUrl && <ImageBackground style={{ width: width, height: bgImageHeight, borderRadius: 24, overflow: 'hidden' }} source={{ uri: bgImageUrl }}>
        <View style={styles.header}>
          <ButtonBack style={{ color: '#fff', position: 'absolute', left: 16, top: getStatusBarHeight() }} onPress={goBack}/>
          <Text style={styles.headerTitle}>Ví của tôi</Text>
        </View>
        <View style={{ alignItems: 'center', marginTop: 5 }}>
          <Text style={{ color: 'white' }}>Số dư thẻ</Text>
          <Text style={styles.balance}>0<Text style={{ fontSize: 24 }}>đ</Text></Text>
        </View>
      </ImageBackground>}
    </View>
    )
  }

  return (
    <SafeAreaView style={{ flex: 1, marginTop: -useSafeAreaInsets().top + 20 }}>
      <StatusBar backgroundColor='transparent' barStyle={'dark-content'} />
      <View style={styles.container}>
        {renderHeader()}
        <View style={styles.topActionBtn}>
          {/* <TouchableOpacity style={styles.actionButton} onPress={() => navigate(SCREENS.transactionHistoryScreen)}> */}
          {/*  <Image source={walletIcon} style={styles.actionBtn} /> */}
          {/*  <Text style={styles.txtBtn}>Tài khoản</Text> */}
          {/* </TouchableOpacity> */}
          <TouchableOpacity style={styles.actionButton} onPress={() => navigate(SCREENS.rewardPointScreen)}>
            <Image source={ticketStarIcon} style={styles.actionBtn} />
            <Text style={styles.txtBtn}>Đổi thưởng</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={() => navigate(SCREENS.openCardScreen)}>
            <Image source={walletIcon} style={styles.actionBtn} />
            <Text style={styles.txtBtn}>Mở tài khoản</Text>
          </TouchableOpacity>
        </View>
        <ScrollView>
          {homeStore.newsForHome ? <View style={styles.sectionHomeProduct}>
            <RenderListHeaderFlatlist news={homeStore.newsForHome} />
          </View> : null}
          {homeStore.promotions?.length ? <View style={styles.sectionHomeProduct}>
            <RenderListHeaderFlatlist promotion={homeStore.promotions} />
          </View> : null}
        </ScrollView>
      </View>
    </SafeAreaView>
  )
})

export default withTranslation()(WalletScreen)
