import { Dimensions, StyleSheet } from 'react-native'
import { getStatusBarHeight } from 'react-native-iphone-x-helper'
import { color } from '../../theme'
const { width } = Dimensions.get('window')

const styles = StyleSheet.create({
  actionBtn: {
    height: 28,
    width: 30
  },
  actionButton: {
    alignItems: 'center'
  },
  balance: {
    color: 'white',
    fontSize: 36,
    fontWeight: 'bold'
  },
  container: {
    backgroundColor: color.primaryBackground,
    flex: 1
  },
  header: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 30,
    paddingHorizontal: 16,
    paddingTop: 30,
  },
  headerTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600'
  },
  imageBg: {
    height: 310,
    width: width
  },
  sectionHomeProduct: {
    backgroundColor: color.primaryBackground,
    marginTop: 5,
    paddingBottom: 10
  },
  top: {
    marginTop: -(getStatusBarHeight()),
  },
  topActionBtn: {
    backgroundColor: 'white',
    borderColor: '#D0D0D0',
    borderRadius: 10,
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginHorizontal: 16,
    marginTop: -40,
    paddingVertical: 14
  },
  txtBtn: {
    color: '#3F3F3F',
    marginTop: 5
  },
})
export default styles
