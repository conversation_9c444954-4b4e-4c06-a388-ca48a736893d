import { StyleSheet, Dimensions } from 'react-native'
import { typography } from '@app/theme'

const { width, height } = Dimensions.get('window')

const styles = StyleSheet.create({
  icArrowBack: {
    height: 24,
    margin: 11,
    resizeMode: 'contain',
    width: 24
  },
  sAv: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  sAvView: {
    backgroundColor: '#fff',
    flex: 1
  },
  sAvView1Image: {
    height: 14,
    resizeMode: 'contain',
    width: 24
  },
  sAvView1Text: {
    color: '#fff',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 20,
    marginLeft: 20
  },
})
export default styles
