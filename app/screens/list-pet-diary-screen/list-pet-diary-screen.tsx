import React, { useEffect, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { View, Text, ScrollView, TouchableOpacity } from 'react-native'
import styles from './styles'
import _ from 'lodash'
import moment from 'moment'
import { ModalRead } from '@app/components'
import { useTranslation } from 'react-i18next'
import { formatCash } from '@app/utils'
import { color } from '@app/theme'
import { useSafeAreaInsets } from 'react-native-safe-area-context'

export const ListPetDiaryScreen = observer(function ListPetDiaryScreen(props: any) {
  const { t } : any = useTranslation()
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [content, setContent] = useState()

  const onPressItem = (x) => {
    setIsModalVisible(true)
    setContent(x)
  }

  useEffect(() => {
    __DEV__ && console.log(props.listMonth)
    return () => {

    }
  }, [props])

  const renderListDiary = (x) => {
    return (
      <TouchableOpacity
        key={x.id}
        onPress={() => onPressItem(x)}
        style={styles.renderItem}>
        <View style={styles.viewDate}>
          <Text style={styles.month}>Th {moment(x.dateTime).format('M')}</Text>
          <Text style={styles.date}>{moment(x.dateTime).format('DD')}</Text>
        </View>
        <View style={styles.viewContent}>
          <Text numberOfLines={1} style={styles.title}>{x.title}</Text>
          <Text numberOfLines={2} style={styles.shortContent}>{x.description}</Text>
        </View>
        <View style={styles.viewCost}>
          <Text style={{ color: color.primary }}>{formatCash(x?.cost_value)}</Text>
        </View>
      </TouchableOpacity>
    )
  }

  const renderMonth = (item) => {
    return (
      <View
        key={item._id}
      >
        <View style={styles.renderMonth}>
          <Text style={styles.textMonth}>{`Tháng ${item.dateYear}`}</Text>
        </View>
        {_.orderBy(item?.data, ['createAt'], ['desc']).map((x) => renderListDiary(x))}
      </View>
    )
  }
  return (
    <View style={[styles.container, { marginBottom: useSafeAreaInsets().bottom }]}>
      <ScrollView showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
        {/* {props.listMonth.map((item) => renderMonth(item))} */}
        {props.listMonth.map((item) => renderListDiary(item))}
      </ScrollView>
      <ModalRead
        data={content}
        isVisible={isModalVisible}
        callBackVisible={() => {
          setIsModalVisible(!isModalVisible)
        }}
        goBack={() => {
          setIsModalVisible(!isModalVisible)
        }}/>
    </View>
  )
})
