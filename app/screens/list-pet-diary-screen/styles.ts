/* eslint-disable */
import {  StyleSheet } from 'react-native'
import { color} from '@app/theme'

const styles = StyleSheet.create({
  item: {
    flexDirection: 'column',
    // height: 40,
    // backgroundColor: palette.pink200,
    // paddingVertical: 8,
    // marginVertical: 4,
    // justifyContent: 'center',
    // alignItems: 'center',
    borderRadius: 10,
    fontWeight: 'bold',
     padding: 0,
    fontSize: 15,
  },
  viewContent: {
    marginVertical: 12,
    marginRight: 16,
    flex: 1
  },
  renderMonth: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f3f3'
  },
  title: {
    fontSize: 14,
    fontWeight: '400',
    color: '#333',
    marginBottom: 5
  },
  shortContent: {
    fontSize: 12,
    fontWeight: '400',
    color: '#9d9d9d',
    lineHeight: 16,
  },
  viewDate: {
    backgroundColor: '#FFF1F1',
    width: 55,
    height: 55,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 16,
    borderRadius: 4,
    marginVertical: 12
  },
  viewCost: {
    width: 55,
    height: 55,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 16,
    borderRadius: 4,
    marginVertical: 12
  },
  date: {
    fontSize: 24,
    fontWeight: '200',
    color: color.primary
  },
  month: {
    fontSize: 12,
    fontWeight: '400',
    color: color.primary
  },
  renderItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    flexDirection: 'row',
  },
  titleDiary: {
    color: 'orange',
    fontSize: 14, marginBottom: 4
  },
  contentDiary: {
    color: '#333',
    fontSize: 12
  },
  itemDiary: {
    // flexDirection: 'column'
    // height: 40,
    padding: 8
  },
  container: {
    // marginBottom: 60,
    // paddingHorizontal: 8,
    flex: 1
  },
  textMonth: {
    fontSize: 18,
    fontWeight: '600'
  }
})


export default styles
