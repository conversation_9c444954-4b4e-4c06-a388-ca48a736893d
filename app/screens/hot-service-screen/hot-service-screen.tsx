import {
  StyleSheet,
  FlatList,
  Text,
  TouchableOpacity,
  View
} from 'react-native'
import React, { useEffect, useState } from 'react'

import { useTranslation } from 'react-i18next'
import { observer } from 'mobx-react-lite'
import { useNavigation, useRoute } from '@react-navigation/native'
import { color, spacing, typography } from '../../theme'
import { ButtonBack, EmptyData, PlaceHolder } from '../../components'
import { useStores } from '@app/models'

import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import LinearGradient from 'react-native-linear-gradient'
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import FastImage from 'react-native-fast-image'
import { noImage } from '@app/assets/images'
import { builderPathImage, formatMoney } from '@app/utils'
import { SCREENS } from '@app/navigation'
import Icon from 'react-native-vector-icons/Ionicons'

export const HotServiceScreen = observer((props: any) => {
  const route = useRoute()
  const { t } : any = useTranslation()
  const { serviceStore, homeStore } = useStores()
  const { categoryId }: any = route?.params
  const [isFetched, setIsFetched] = useState(false)
  const [page, setPage] = useState(1)
  const [categoriesID, setCategoriesID] = useState(categoryId || '0')
  const [cateText, setCateText] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const [loadMore, setLoadMore] = useState(false) // mark scrollEnd to load more

  const { navigate, goBack } = useNavigation()

  useEffect(() => {
    loadData().then(r => {})
  }, [])

  useEffect(() => {
    loadDataFilter()
  }, [categoriesID])

  const loadDataFilter = async () => {
    setIsFetched(true)
    await serviceStore.getServiceFeature(categoriesID, null)
    setIsFetched(false)
  }

  /**
     * call Store
     */
  const loadData = async () => {
    await homeStore.getServicesCategories('3')
  }

  const refreshData = async () => {
    setIsFetched(true)
    console.log('categoryId', categoryId)
    if (categoriesID || categoryId) {
      await serviceStore.getServiceFeature(categoriesID || categoryId, null)
    } else {
      await serviceStore.getServiceFeature('0', null)
    }
    await homeStore.getServicesCategories('3')
    setLoadMore(false)
    setRefreshing(false)
    setIsFetched(false)
  }
  /**
     * onRefresh
     */
  const onRefresh = () => {
    refreshData().then(r => {
    })
  }
  /**
     * onLoadMore Data
     */

  // const handleLoadMore = () => {
  //   // if (!loadMore) return
  //   // khi scroll dừng lại sẽ gọi vào hàm này
  //   __DEV__ && console.log('onMomentumScrollEnd')
  //   // call total page here
  //   const totalPage = postsStore.totalPage
  //   if (page < totalPage) {
  //     setPage(page + 1)
  //   }
  //   if (page === totalPage) {
  //     __DEV__ && console.log('No more data...')
  //     setLoadMore(false)
  //   }
  // }

  // const builderPathImage = (item) => {
  //   __DEV__ && console.log(item)
  //   if (item?.attributes?.image?.data?.attributes.url.indexOf('http') != -1) {
  //     return `${item?.attributes?.image?.data?.attributes.url}`
  //   }
  //   return `${DEFAULT_API_CONFIG.url2}${item?.attributes?.image?.data?.attributes.url}`
  // }

  /**
     * render Item
     */
  const renderListItem = ({ item, index }) => {
    const getChooseData = (item) => (item?.classify?.length > 0)
    const priceMin = getChooseData(item) ? item.classify[0]?.data[0].price : formatMoney(item.price)
    const priceMax = getChooseData(item) ? item.classify[0]?.data[item.classify[0]?.data.length - 1].price : ''

    const getNumberPrice = (value, replaceWith = '') => {
      const trimValue = value.replace(new RegExp('VND', 'g'), replaceWith)
      // console.log('trimValue', trimValue)
      return trimValue
    }

    return <View style={{ borderBottomWidth: 2, borderBottomColor: '#F3F3F3', marginBottom: 10 }}>
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginHorizontal: 16 }}>
        <TouchableOpacity onPress={() => {
          navigate(SCREENS.serviceDetail, { id: item.storeId })
        }}>
          <Text numberOfLines={3} style={{ color: color.primary, fontSize: 12, fontWeight: 'bold' }}>{item?.storeName}</Text>
        </TouchableOpacity>
        <View style={{ flexDirection: 'row', marginTop: 7 }}>
          {item?.distance !== 'NaN' && item?.distance !== '0.0' ? <View style={{ flexDirection: 'row' }}><Text style={{ color: color.primary, fontSize: 12, fontWeight: 'bold', marginLeft: 16 }}> {item?.distance} km</Text></View> : null}
        </View>
      </View>
      <TouchableOpacity
        onPress={() => {
          navigate(SCREENS.serviceDetail, { id: item.storeId, item: item })
        }}
        style={styles.item}>
        <FastImage
          style={styles.image}
          source={item?.image ? { uri: builderPathImage(item?.image) } : noImage} />
        <View style={{ flex: 1, flexDirection: 'column' }}>
          <TouchableOpacity onPress={() => {
            navigate(SCREENS.serviceDetail, { id: item.storeId, item: item })
          }}>
            <Text numberOfLines={3} style={{ color: '#333', fontSize: 14, fontWeight: 'bold' }}>{item?.name}</Text>
            <Text numberOfLines={2} style={styles.textDes}>{item?.shortDes}</Text>
          </TouchableOpacity>
          {/* <RenderHtml */}
          {/*  contentWidth={responsiveWidth(100)} */}
          {/*  source={{ html: item?.description }} */}
          {/*  ignoredTags={['script']} */}
          {/*  ignoredStyles={['font-family']} */}
          {/*  renderersProps={{ */}
          {/*    img: { */}
          {/*      enableExperimentalPercentWidth: true */}
          {/*    } */}
          {/*  }} */}
          {/* /> */}
          <View style={styles.viewPrice}>
            { priceMin?.length > 1 ? <View>
              <Text
                style={styles.textPriceMinMax}>{priceMin ? getNumberPrice(priceMin, 'đ') : ''} {priceMax ? '-' : 'đ'} {priceMax ? getNumberPrice(priceMax, 'đ') : ''}</Text>
            </View> : <View></View>}
            <TouchableOpacity onPress={() => {
              navigate(SCREENS.serviceDetail, { id: item.storeId, item: item })
            }}>
              <Icon name={'add-circle-outline'} size={26} color={color.primary}/>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    </View>
  }

  // const renderFooter = () => {
  //   const Spinner = require('react-native-spinkit')
  //   return loadMore === true ? (
  //     <View
  //       style={{
  //         marginBottom: 20,
  //         alignItems: 'center',
  //       }}
  //     >
  //       <Spinner isVisible={true} size={40} type='ThreeBounce' color={color.primary}/>
  //     </View>
  //   ) : null
  // }

  const renderCategories = ({ item, index }) => {
    return <TouchableOpacity onPress={() => {
      setCategoriesID(item._id)
      if (item._id === 0) {
        // tất cả
        setCateText('')
      } else {
        setCateText(item.name)
      }

      setPage(1)
      // fetchLastestPost()
    }} style={[styles.rdDanhmuc, categoriesID === item._id ? styles.cateSelected : { }]}>
      <Text style={[styles.rdDanhmucText, { fontWeight: categoriesID === item._id ? 'bold' : 'normal' }]}>{item.name}</Text>
    </TouchableOpacity>
  }

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <Header
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack} />}
        centerComponent={{ text: t('Dịch vụ hot'), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      <View style={styles.viewContainer}>
        <View style={{ borderBottomWidth: 5, borderBottomColor: '#F3F3F3' }}>
          <FlatList
            horizontal={true}
            // ItemSeparatorComponent={renderSeparator}
            // nestedScrollEnabled={true}
            data={[{ _id: '0', name: 'Tất cả', picture: null }, ...homeStore.homeHotCategories]}
            extraData={homeStore.homeHotCategories}
            keyExtractor={(item, index) => index + 'category'}
            contentContainerStyle={{ marginLeft: 16 }}
            showsHorizontalScrollIndicator={false}
            // onEndReachedThreshold={0.1}
            renderItem={renderCategories}/>
        </View>
        {isFetched ? <View style={styles.placeHolder}>
          <PlaceHolder/>
        </View> : <View style={{ flex: 1, paddingTop: 0 }}>
          <FlatList
            style={{ paddingTop: 10 }}
            showsVerticalScrollIndicator={false}
            data={serviceStore.servicesFeature}
            horizontal={false}
            scrollEventThrottle={1}
            initialNumToRender={10}
            refreshing={refreshing}
            onRefresh={onRefresh}
            keyExtractor={(item, index) => index + 'news'}
            renderItem={renderListItem}
            extraData={serviceStore.servicesFeature}
            onScrollBeginDrag={e => {
              // onScroll(e)
              __DEV__ && console.log('onScrollBeginDrag')
              setLoadMore(true)
            }}
            contentContainerStyle={{ paddingBottom: useSafeAreaInsets().bottom + 60 }}
            // onScrollEndDrag={onScroll}
            // contentContainerStyle={{ paddingBottom: 120 }}
            // onMomentumScrollEnd={handleLoadMore}
            // ListFooterComponent={renderFooter}
          />
          {serviceStore.servicesFeature && serviceStore.servicesFeature.length ? null : <EmptyData title={t('Không tìm thấy')} message={t('Không có dữ liệu để hiển thị')}/>}
        </View>
        }
      </View>
    </SafeAreaView>
  )
}
)

const styles = StyleSheet.create({
  cateSelected: {
    borderBottomColor: color.primary,
    borderBottomWidth: 3
  },
  image: {
    height: 80,
    marginRight: 10,
    width: 80
  },
  item: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    flex: 1,
    marginBottom: 1,
    paddingHorizontal: 16,
    paddingVertical: 15
  },
  placeHolder: {
    flex: 1,
    marginTop: 20
  },
  postsList: {
    // flex: 1,
    // paddingBottom: 50
  },
  rdDanhmuc: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    marginRight: spacing.large,
    marginVertical: 8
  },
  rdDanhmucText: {
    fontFamily: typography.normal,
    fontSize: 15,
    paddingVertical: 8,
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  textDes: {
    color: '#979797',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 15,
    marginVertical: 5,
    // paddingRight: 40
  },
  textPriceMinMax: {
    color: color.primary,
    fontSize: 14,
    fontWeight: '600',
  },
  viewContainer: {
    // backgroundColor: '#f6f6f7',
    flex: 1,
    // height: responsiveHeight(100),
    // paddingBottom: 140,
  },
  viewPrice: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
})
