import React, { useEffect, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { View, FlatList } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { SCREENS } from '@app/navigation'
import styles from './styles'
import moment from 'moment'
import { ProductByCategoryScreen } from '@app/screens'
import { Api } from '@app/services/api'
import { useStores } from '@app/models'
import { color } from '@app/theme'
import { useTranslation } from 'react-i18next'
import { EmptyData, PlaceHolder } from '@app/components'

export interface TabCategoryProductScreenProps {
  loadMore?: any
  setLoadMore?: any
  handleLoadMore?: any
  product?:any
  index?: any
   type?: number
}
export const TabCategoryProductScreen = observer(function TabCategoryProductScreen(props: TabCategoryProductScreenProps) {
  const { navigate } = useNavigation()
  const { t } : any = useTranslation()
  // const { type } = props
  const [loadMore, setLoadMore] = useState(false) // mark scrollEnd to load more
  const [page, setPage] = useState(1)
  const [totalPage, setTotalPage] = useState(1)
  const [refreshing, setRefreshing] = useState(false)
  const [isFetched, setIsFetched] = useState(true) // event view placeholder
  const { productStore } = useStores()
  const [data, setData] = useState([])

  useEffect(() => {
    setData([])
    loadData()
  }, [page, props.type])

  const loadData = async () => {
    const isLoadMore = page > 1
    if (!isLoadMore) {
      setIsFetched(true)
      productStore.clearFields()
    }
    await productStore.getProductByCategory(props.type, isLoadMore, page)
    if (props.type === 0) {
      setData(productStore.productDog)
      setTotalPage(productStore.totalPageDog)
    }
    if (props.type === 1) {
      setData(productStore.productCat)
      setTotalPage(productStore.totalPageCat)
    }
    if (props.type === 2) {
      setData(productStore.productOther)
      setTotalPage(productStore.totalPageOther)
    }
    setLoadMore(false)
    setRefreshing(false)
    setIsFetched(false)
  }

  const navigateDetails = async (e) => {
    const send = {
      field: 'productIds',
      fieldId: e._id
    }
    const api = new Api()
    api.sendProductViewed(send)
    navigate(SCREENS.productDetails, { id: e._id })
  }

  const handleLoadMore = () => {
    // if (!loadMore) return
    // khi scroll dừng lại sẽ gọi vào hàm này
    __DEV__ && console.log('onMomentumScrollEnd------after')
    if (page < totalPage) {
      // __DEV__ && console.log('totalPage', totalPage)
      setPage(page + 1)
    }
    if (page === totalPage) {
      __DEV__ && console.log('No more data...')
      setLoadMore(false)
    }
  }

  const renderCat = ({ item }) => (
    <ProductByCategoryScreen item={item} onPress={(e) => {
      navigateDetails(e)
    }
    }/>
  )

  const renderFooter = () => {
    const Spinner = require('react-native-spinkit')
    return loadMore === true ? (
      <View
        style={{
          marginBottom: 20,
          alignItems: 'center'
        }}
      >
        <Spinner isVisible={true} size={40} type='ThreeBounce' color={color.primary}/>
      </View>
    ) : null
  }

  return (
    <View>
      {isFetched ? <PlaceHolder/> : <View style={styles.flatListContainer}>
        {data?.length == 0 ? <EmptyData style={{ height: 300 }} title={t('')} message={t('Không có dữ liệu')}/> : <FlatList
          columnWrapperStyle={{ justifyContent: 'space-between' }}
          // index={props.index}
          listKey={moment().valueOf().toString()}
          key={props.type}
          numColumns={2}
          data={data}
          showsHorizontalScrollIndicator={false}
          renderItem={renderCat}
          extraData={data}
          // numColumns={Math.ceil(data.length / 2)}
          keyExtractor={(item, index) => index.toString() }
          onScrollBeginDrag={e => {
            __DEV__ && console.log('onScrollBeginDrag11111')
            setLoadMore(true)
          }}
          onMomentumScrollBegin={e => {
            __DEV__ && console.log('onScrollBeginDrag11111')
          }}
          onMomentumScrollEnd={handleLoadMore}
          ListFooterComponent={renderFooter}
        />}
      </View>}
    </View>
  )
})
