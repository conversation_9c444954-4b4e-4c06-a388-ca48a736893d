import { Safe<PERSON>reaView } from 'react-native-safe-area-context'
import React, { useContext, useEffect, useRef, useState } from 'react'
import { observer } from 'mobx-react-lite'
import {
  Image,
  Text, TextInput,
  TouchableOpacity,
  View,
  Dimensions
} from 'react-native'
import styles from '@app/components/search-tab-rate-screen/style'
import { useTranslation } from 'react-i18next'
import { icCloseOutline, icSearch } from '../../assets/images'
import Icon from 'react-native-vector-icons/Ionicons'
import { useNavigation } from '@react-navigation/native'
import { useStores } from '@app/models'
import {
  ButtonBack,
  Filter, FilterDistrict,
  SearchTabNearScreen,
  SearchTabRateScreen,
  SearchTabFavoriteScreen
} from '@app/components'
import { TabBar, TabView } from 'react-native-tab-view'
import { Header } from 'react-native-elements'
import common, { linearGradientProps } from '@app/theme/styles/common'
// import { PickerSelect } from '@app/components/picker-select/picker-select'
import { remove, saveString } from '@app/utils/storage'
import { LogEvent } from '@app/services/loggingServices'
import { Modalize } from 'react-native-modalize'
// import { DEFAULT_DISTANCE } from '@app/constants/configs'
import { responsiveHeight } from 'react-native-responsive-dimensions'
import LinearGradient from 'react-native-linear-gradient'
import { color } from '@app/theme'
import { BookingType } from '@app/constants/bookingType'
import { ModalContext } from '@app/components/modal-success'
// import Animated from 'react-native-reanimated'

const initialLayout = { width: Dimensions.get('window').width }

export const SearchScreen = observer((props: any) => {
  const modalChooseDistrict = useRef<Modalize>(null)
  const { navigate, goBack } = useNavigation()
  const { t } : any = useTranslation()
  const [value, setValue] = useState('')
  const [keyword, setKeyword] = useState('')
  // const [page, setPage] = useState(1)
  const [type, setType] = useState(props.route?.params?.filterType !== undefined ? Number(props.route?.params?.filterType) : -1)
  const [filter, setFilter] = useState(null)
  const [isOpenFilter, setIsOpenFilter] = useState(false)
  const [titleFilter, setTitleFilter] = useState('')
  const [index, setIndex] = React.useState(0)
  const { searchStore } = useStores()
  // const [isPickerSelectVisible, setIsPickerSelectVisible] = useState(false)
  const [district, setDistrict] = useState(null)
  // const modalizeDistricts = useRef<Modalize>(null)
  const [currentFilter, setCurrentFilter] = useState({ page: 1, type: props.route?.params?.screenType === 'shop' ? 0 : Number(props.route?.params?.filterType) || -1, typeShip: 1 })
  const refNear = useRef(null)
  const refRate = useRef(null)
  const refFavorite = useRef(null)
  const { showError, showSuccess, showCustomError, showCustomSuccess } = useContext(ModalContext)

  const [isFreeShip, setIsFreeShip] = useState(false)

  const [routes] = React.useState([
    { key: 'NEAREST', title: t('NEAREST') },
    { key: 'RATING', title: t('RATING') },
    { key: 'FAVORITE', title: t('FAVORITE') },
  ])

  const data = [
    { id: -1, name: t('FILTERALL') },
    { id: 3, name: t('FILTERSPA') },
    // { id: 2, name: t('FILTERHOTEL'), },
    { id: 2, name: t('FILTERPARKING'), },
    { id: 1, name: t('FILTERCLICNIC') },
    { id: 0, name: t('FILTERPRODUCT') },
    { id: 4, name: t('FILTERSHOWROOM') },
    { id: 5, name: t('FILTERGAS') }
  ]

  const onGoBack = () => {
    goBack()
    searchStore.setKeyword('')
  }

  const handleClear = () => {
    setValue('')
  }
  useEffect(() => {
    __DEV__ && console.log('----', searchStore.typeSearch)
    if (filter) {
      // setPage(1)
      setType(filter.id)
    }
  }, [filter])

  useEffect(() => {
    __DEV__ && console.log('type filter', type)
    __DEV__ && console.log('data filter', data)
    if (data) {
      const t = data.find(x => x.id === type)
      if (t?.name) {
        setTitleFilter(t.name)
      }
    }
  }, [type])

  useEffect(() => {
    if (refNear?.current && refRate?.current) {
      setType(0)
      refNear?.current.loadData({ ...currentFilter, typeShip: isFreeShip ? 0 : 1, type: BookingType.SHOP })
      refRate?.current.loadData({ ...currentFilter, typeShip: isFreeShip ? 0 : 1, type: BookingType.SHOP })
    }
    return () => {

    }
  }, [isFreeShip])

  /**
   * onRefresh
   */
  const onSearch = () => {
    // setKeyword(value)
    searchStore.setKeyword(value)
    reCallApi()
    refFavorite?.current.loadData(currentFilter)
  }

  useEffect(() => {
    loadData()
  }, [])

  const onOpenFilter = async () => {
    setIsOpenFilter(true)
    await searchStore.loadProductFilter()
  }
  const renderScene = ({ route }) => {
    switch (route.key) {
      case 'NEAREST':
        return <SearchTabNearScreen type={type} keyword={keyword} ref={refNear}/>
      case 'RATING':
        return <SearchTabRateScreen type={type} keyword={keyword} ref={refRate}/>
      case 'FAVORITE':
        return <SearchTabFavoriteScreen type={type} keyword={keyword} ref={refFavorite}/>
      default:
        return null
    }
  }

  const renderTabItemButton = (tabBtnInfo: any, { route }) => {
    const { isActive } = tabBtnInfo
    // const tabImage = staticData.TabData[index]
    const activeTextStyle = {
      fontSize: 14,
      color: '#2e2e2e',
    }
    const noActiveTextStyle = {
      fontSize: 13,
      color: '#848484',
    }
    const textStyle = isActive ? activeTextStyle : noActiveTextStyle
    return (
      <View>
        <Text style={textStyle}>{route.title}</Text>
      </View>
    )
  }

  const renderTabBar = (props) => (
    <TabBar
      {...props}
      indicatorStyle={{ backgroundColor: color.primary }}
      style={styles.tabBar}
      renderTabItemButton={renderTabItemButton}
      renderLabel={({ route, focused, color }) => (
        <Text style={[styles.tabBarText, { color: focused ? '#2e2e2e' : '#848484' }]}>
          {route.title}
        </Text>
      )}
    />
  )
  //
  // const checkShowPopup = async () => {
  //   await searchStore.getDistrict()
  //   // setProvinces(searchStore.districts)
  //   // const provinceSave = await loadString('provinceSelected')
  //   // if (!provinceSave) {
  //   //   setIsPickerSelectVisible(true)
  //   // } else {
  //   //   setProvince(provinceSave)
  //   // }
  // }

  const loadData = async () => {
    await remove('districtSelected')
    await searchStore.getDistrict()
  }

  // const onCloseModal = () => {
  //   modalizeDistricts.current?.close()
  // }

  const onOpenModalDistrict = () => {
    modalChooseDistrict.current?.open()
  }
  const onCloseModalDistrict = () => {
    modalChooseDistrict.current?.close()
  }

  const onChangeDistrict = async (value) => {
    setTimeout(() => { onCloseModalDistrict() }, 500)
    try {
      LogEvent('user_select_district', value)
      await saveString('districtSelected', value)
      // setPage(1)
      await reCallApi()
    } catch (error) {
      // Error saving data
    }
  }

  const reCallApi = async () => {
    await refNear?.current.loadData(currentFilter)
    await refRate?.current.loadData(currentFilter)
  }

  useEffect(() => {
    let isLoad = true
    if (isLoad) {
      if (district) {
        onChangeDistrict(district).then(r => {
          isLoad = false
        })
      }
    }
    return () => { isLoad = false }
  }, [district])

  const SearchInput = () => {
    return (
      <View style={styles.background}>
        <View style={{ flexDirection: 'row', justifyContent: 'flex-start', backgroundColor: '#fff', marginBottom: 5 }}>
          <TouchableOpacity
            onPress={
              () => onOpenModalDistrict()
              // () => setIsPickerSelectVisible(true)
            }
            style={styles.viewAddress}>
            <Text style={styles.address}>{district || t('CHON_quanhuyen')}</Text>
            <Icon size={18} style={{ marginTop: 2, marginLeft: 5, marginRight: 8 }} name="chevron-down-outline"/>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={
              () => {
                setIsFreeShip(!isFreeShip)
                setCurrentFilter((prev) => ({ ...prev, typeShip: isFreeShip ? 0 : 1 }))
              }
            }
            style={[styles.btnFilterTop, { borderColor: isFreeShip ? color.primary : '#fff', borderWidth: 1 }]}>
            <Text style={[styles.address, { paddingRight: 8 }]}>Miễn phí vận chuyển</Text>
          </TouchableOpacity>
        </View>
        <TabView
          navigationState={{ index, routes }}
          renderScene={renderScene}
          onIndexChange={setIndex}
          initialLayout={initialLayout}
          renderTabBar={renderTabBar}
          style={{ backgroundColor: '#fff' }}
        />
      </View>
    )
  }

  const renderHeaderModalDistrict: any = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onCloseModalDistrict} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>{t('Quận huyện')}</Text>
      <TouchableOpacity
        onPress={() => {
          onChangeDistrict('')
          setDistrict(null)
        }}
      >
        <Text style={styles.textClose}>{t('Tất cả')}</Text>
      </TouchableOpacity>
      {/* <View style={{ width: 20 }}></View> */}
    </View>
  )

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      {/* <StatusBar */}
      {/*  translucent */}
      {/*  backgroundColor="#fff" */}
      {/*  barStyle="dark-content" */}
      {/*  hidden={false} */}
      {/* /> */}
      {/* <View style={styles.viewBtnBack}> */}
      {/*  <ButtonBack onPress={goBack} style={styles.btnBack}/> */}
      {/*  <Text style={styles.textTitle} numberOfLines={1}>{titleFilter}</Text> */}
      {/* </View> */}
      <Header
        // barStyle="light-content" // or directly
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={onGoBack}/>}
        // centerComponent={{ text: titleFilter, style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        // centerContainerStyle={{flex: 7}}
        centerComponent={<View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 5 }}>
          {/* <View> */}
          {/*  <TouchableOpacity */}
          {/*    onPress={ */}
          {/*      () => onOpenModalDistrict() */}
          {/*      // () => setIsPickerSelectVisible(true) */}
          {/*    } */}
          {/*    style={styles.viewAddress}> */}
          {/*    <Text style={styles.address}>{district || t('Chọn địa điểm')}</Text> */}
          {/*    <Icon size={18} style={{ marginTop: 0, marginLeft: 4, marginRight: 4 }} name="chevron-down-outline"/> */}
          {/*  </TouchableOpacity> */}
          {/* </View> */}
          <View style={[styles.searchInputTopBar, { marginBottom: 3 }]}>
            {/* <Text>{ titleFilter }</Text> */}
            <TouchableOpacity onPress={() => {
              onSearch()
            }}>
              <Image style={styles.icSearch} source={icSearch}/>
            </TouchableOpacity>
            <TextInput
              placeholder={t('BAN_MUON_TIM_GI')}
              placeholderTextColor='#A0A0A0'
              autoCapitalize='none'
              underlineColorAndroid="transparent"
              onChangeText={(e) => setValue(e)}
              defaultValue={value}
              returnKeyType={'search'}
              onSubmitEditing={onSearch}
              keyboardType={'default'}
              autoFocus={false}
              style={styles.input}/>
            <TouchableOpacity onPress={handleClear}>
              <Image style={styles.icClose} source={icCloseOutline}/>
            </TouchableOpacity>
          </View>
        </View>}
        containerStyle={[common.headerContainer]}
        rightComponent={<View><TouchableOpacity onPress={() => {
          onOpenFilter()
        }} style={styles.btnFilter}>
          <Icon size={24} name={'filter'} color={'#fff'} />
          {/* <Image source={icFilter} style={{ width: 24, height: 24 }}></Image> */}
        </TouchableOpacity></View>}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      {SearchInput()}
      <Filter isOpen={isOpenFilter}
        dataFilter={data}
        filter={(item) => {
          setFilter(item)
          setIsOpenFilter(false)
        }}
        onClose={
          (value) => {
            if (value === true) {
              setIsOpenFilter(false)
            }
          }
        }
        onOk={
          (item) => {
            if (item) {
              setCurrentFilter(item)
              refNear?.current.loadData(item)
              refRate?.current.loadData(item)
            }
          }
        }
        rateProduct={searchStore.rateProduct}
        priceRange={searchStore.priceRange}
        shipping={searchStore.shipping}
      />
      {/* <PickerSelect data={searchStore.listDistrict} title={t('CHON_quanhuyen')} isVisible={true} defaultValue={''} onSelect={(e) => { */}
      {/*  // setIsPickerSelectVisible(!isPickerSelectVisible) */}
      {/*  setDistrict(e.label) */}
      {/* }} */}
      {/* callBackVisible={() => { */}
      {/*  // setIsPickerSelectVisible(!isPickerSelectVisible) */}
      {/* }} */}
      {/* goBack={() => { */}
      {/*  // setIsPickerSelectVisible(!isPickerSelectVisible) */}
      {/* }} */}
      {/* /> */}
      <Modalize
        HeaderComponent={renderHeaderModalDistrict}
        ref={modalChooseDistrict}
        modalTopOffset={40}
        // adjustToContentHeight
        // snapPoint={405}
        modalHeight={responsiveHeight(50)}
        // onClosed={() => { props.onClose(true) }}
        keyboardAvoidingBehavior={'padding'}
      >
        <FilterDistrict data={searchStore.listDistrict} onSelect={(e) => {
          setDistrict(e.label)
        }}/>
      </Modalize>
    </SafeAreaView>
  )
})
