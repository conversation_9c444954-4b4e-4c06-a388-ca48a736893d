import React, { Fragment, useContext, useEffect, useRef, useState } from 'react'
import {
  Alert,
  Dimensions,
  FlatList,
  Image, Platform,
  StyleSheet,
  Text, TouchableOpacity, View
} from 'react-native'
import { observer } from 'mobx-react-lite'
import { ButtonBack, TButton, BottomSheetPicker, TTextInput, useLoading } from '@app/components'
import Icon from 'react-native-vector-icons/Ionicons'
import { useTranslation } from 'react-i18next'
import { ModalContext } from '@app/context'
import { useStores } from '@app/models'
import { responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import { useNavigation, useRoute } from '@react-navigation/native'
import {
  icCheckSquare
} from '@app/assets/images'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { CheckB<PERSON>, Header, Slider } from 'react-native-elements'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import LinearGradient from 'react-native-linear-gradient'
import { SafeAreaView } from 'react-native-safe-area-context'
import { color, typography } from '@app/theme'
import { Capitalize, formatMoney, getPhoneOnly, noWhitespace, numberFormat } from '@app/utils'
import DateTimePickerModal from 'react-native-modal-datetime-picker'
import SimpleToast from 'react-native-simple-toast'
import moment from 'moment-timezone'
import validate from 'validate.js'
import BottomSheet, { BottomSheetBackdrop, BottomSheetView } from '@gorhom/bottom-sheet'
import { TermContent } from './Components/TermContent'
import { Payment } from '@app/screens'
import { SCREENS } from '@app/navigation'
import { InAppBrowser } from 'react-native-inappbrowser-reborn'
import { Api } from '@app/services/api'
import RenderHtml from 'react-native-render-html'
import { Modalize } from 'react-native-modalize'

export interface BHTNDSStep1Props {
  handleClosed?: any
}

const isTrue = (value) => (value === 'true')
const { width } = Dimensions.get('window')

export const BHTNDSB2Step1Screen = observer((props: any, ref) => {
  const route = useRoute()
  const { id, isRenew, isViewDetail, dataRenew, prodId }: any = route?.params
  const { navigate, goBack } = useNavigation()
  const { t } = useTranslation()
  const nav = useNavigation()
  const { showError, showSuccess } = useContext(ModalContext)
  const { profileStore, insuranceStore } = useStores()
  const { show, hide } = useLoading()

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [item, setItem] = useState(null)

  const bottomSheetRef = useRef<BottomSheet>(null)
  const bottomSheetBankListRef = useRef<Modalize>(null)

  const [step, setStep] = useState(1)

  // mục đích sử dụng
  const mucDichSuDungRef = useRef(null)
  const [mucDichSuDung, setMucDichSuDung] = useState(null)

  // loai xe
  const loaiXeRef = useRef(null)
  const [loaiXe, setLoaiXe] = useState(null)

  // số chỗ
  const soChoRef = useRef(null)
  const [soCho, setSoCho] = useState(null)

  // ngày bắt đầu
  const [pickerDateVisible, setPickerDateVisible] = useState(false)
  const [date, setDate] = useState(new Date().getTime())

  // thời hạn
  const thoiHanRef = useRef(null)
  const [thoiHan, setThoiHan] = useState(null)

  // so tiền tham gia cho lái phụ
  const soTienBhLaiPhuRef = useRef(null)
  const [soTienBhLaiPhu, setSoTienBhLaiPhu] = useState(null)

  // xuất VAT
  const [xuatVAT, setXuatVAT] = useState(false)
  const [tenCongTy, setTenCongTy] = useState('')
  const [diaChiCongTy, setDiaChiCongTy] = useState('')
  const [mst, setMst] = useState('')
  const [maGioThieu, setMaGioiThieu] = useState('')
  const [khongCoBienSo, setKhongCoBienSo] = useState(false)
  const [paymentGuideHtml, setPaymentGuideHtml] = useState()
  const [bottomSheetType, setSetBottomSheetType] = useState(0) // 1 bank, 2 ảnh hướng dẫn GDDK

  const currentHour = () => {
    return moment(new Date()).format('hh:mm')
  }

  const [objValueCalculatePrice, setObjValueCalculatePrice] = useState({
    ma_trongtai: '',
    so_cho: '', // TODO: remove ''
    ma_mdsd: '1',
    MayKeo: false,
    XeChuyenDung: false,
    XeChoTien: false,
    XePickUp: false,
    XeTaiVan: false,
    XeTapLai: false,
    XeBus: false,
    XeCuuThuong: false,
    Xetaxi: false,
    XeDauKeo: false,
    GioDau: currentHour(),
    GioCuoi: currentHour(),
    NgayDau: '',
    NgayCuoi: '',
    mtn_laiphu: '0',
    so_nguoi: '0',
    philpx_nhap: '0',
    thamgia_laiphu: false,
    thamgia_tndsbb: true,
    ma_loaixe: '',
  })

  const [objValue, setObjValue] = useState({
    TenKH: '',
    DiaChiKH: '',
    TenChuXe: '',
    DiaChiChuXe: '',
    NgayDau: '',
    NgayCuoi: '',
    ThamGiaLaiPhu: false,
    EmailKH: '',
    TrongTai: '',
    MTNLaiPhu: '0',
    SoNguoiToiDa: '0',
    PhiBHTNDSBB: '0',
    PhiBHLaiPhu: '0',
    MayKeo: false,
    XeChuyenDung: false,
    XeChoTien: false,
    XePickUp: false,
    XeTaiVan: false,
    XeTapLai: false,
    XeBus: false,
    XeCuuThuong: false,
    Xetaxi: false,
    XeDauKeo: false,
    NamSD: '',
    AnBKS: false,
    BienKiemSoat: '',
    MucDichSuDung: '',
    HieuXe: '',
    DongXe: '',
    NamSX: '',
    DienThoai: '',
    SoKhung: '',
    SoMay: '',
    AnPhi: false,
    GioDau: currentHour(),
    GioCuoi: currentHour()
  })

  const [value, setValue] = useState<any>(0)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState([true, false])
  const [bankCode, setBankCode] = useState('')

  useEffect(() => {
    __DEV__ && console.log('objValueCalculatePrice', objValueCalculatePrice)
    if (validateStep1()) {
      // goi tinh toán số tiền
      const body = {
        ...objValueCalculatePrice,
        ma_loaixe: loaiXe?.value,
        ma_trongtai: objValueCalculatePrice?.ma_trongtai ? objValueCalculatePrice.ma_trongtai : '99999'
      }
      insuranceStore.tinhPhiTNDSOto(body)
    } else {
      insuranceStore.resetTotalFreeTNDSOto()
    }
  }, [objValueCalculatePrice])

  // useDeepCompareEffect(() => {
  //   __DEV__ && console.log('objValue', objValue)
  // }, [objValue])

  useEffect(() => {
    insuranceStore.clearArr()
    getApiData()
    getApiConfig()
    handleConfirm(new Date())
    if (insuranceStore.catNameSelected == 'BIC') {
      handlerChonThoiHan({ label: '1 Năm', value: '12' })
    }
  }, [])

  useEffect(() => {
    if (insuranceStore.paymentStatus) {
      // gửi đơn hàng sau khi thanh toán thành công và lưu vào db
      createOrderPVI()
      insuranceStore.clearArr()
      // reset
      setStep(1)
      setBankCode('')
    }

    return () => {

    }
  }, [insuranceStore.paymentStatus])

  const getApiData = async () => {
    const rs = await insuranceStore.getInsurancePostDetail(prodId)
    if (rs?.data?.data) {
      __DEV__ && console.log('insuranceStore.getInsurancePostDetail(prodId)', rs)
      setItem(rs?.data?.data)
      insuranceStore.setCatSelected(rs?.data?.data)
    }
    return Promise.all([
      insuranceStore.getMucDichSuDung('MDSD_AUTO'),
      insuranceStore.getLoaiXe(dataRenew?.ma_mdsd, dataRenew?.so_cho, objValueCalculatePrice.ma_trongtai)
    ])
  }

  useEffect(() => {
    if (khongCoBienSo) {
      onChangeText('BienKiemSoat', '')
    }
    return () => {

    }
  }, [khongCoBienSo])

  const onSelectBank = (item) => {
    setBankCode(item)
    closeBottomSheet()
  }

  const openBottomSheet = (type) => {
    setSetBottomSheetType(type)
    bottomSheetBankListRef?.current.open()
  }

  const closeBottomSheet = () => {
    setSetBottomSheetType(0)
    bottomSheetBankListRef?.current.close()
  }

  const hidePicker = () => {
    setPickerDateVisible(false)
  }

  const handleConfirm = (date) => {
    const chooseDate = moment(new Date(date)).set({ hour: 0, minute: 0, second: 0, millisecond: 0 }) // choose date yyyy-mm-dd only
    const dateNow = moment(new Date()).set({ hour: 0, minute: 0, second: 0, millisecond: 0 })

    if (chooseDate.isSameOrAfter(dateNow)) {
      setDate(new Date(date).getTime())
      onChangeObjValueCalculatePrice('NgayDau', moment(date).format('DD/MM/YYYY'))
      hidePicker()
    } else {
      // showError(t('FAIL'), t('Bạn không thể chọn ngày chưa tới'))
      SimpleToast.show('Bạn không thể chọn ngày đã qua')
      setDate(null)
      onChangeObjValueCalculatePrice('NgayDau', '')
      return Platform.OS === 'android' ? setPickerDateVisible(false) : null
    }
  }

  const handlerChonThoiHan = (item) => {
    const { value } = item
    if (!date) {
      showError(t('FAIL'), t('Bạn cần chọn ngày bắt đầu trước'))
    } else if (value) {
      setThoiHan(item)
      const endDate = moment(date).add(parseInt(value), 'M').format('DD/MM/YYYY')
      __DEV__ && console.log('endDate', endDate)
      onChangeObjValueCalculatePrice('NgayCuoi', endDate)
    }
  }

  const onGoBack = () => {
    if (!isRenew) {
      if (step === 1) {
        goBack()
      } else if (step === 2) {
        setStep(1)
      } else if (step === 3) {
        setStep(2)
      }
    } else {
      goBack()
    }
  }

  const validateStep1 = () => {
    let valid: boolean
    if (objValueCalculatePrice.thamgia_laiphu) {
      valid = !validate.isEmpty(objValueCalculatePrice.NgayDau) &&
        !validate.isEmpty(objValueCalculatePrice.NgayCuoi) &&
        !validate.isEmpty(objValueCalculatePrice?.so_cho) &&
        !validate.isEmpty(objValueCalculatePrice.so_nguoi) &&
        !validate.isEmpty(soTienBhLaiPhu?.value) &&
        objValueCalculatePrice?.so_cho !== '0'
    } else {
      valid = !validate.isEmpty(objValueCalculatePrice.NgayDau) &&
        !validate.isEmpty(objValueCalculatePrice.NgayCuoi) &&
        !validate.isEmpty(objValueCalculatePrice?.so_cho) &&
        objValueCalculatePrice?.so_cho !== '0'
    }

    const year = Math.ceil(moment(objValueCalculatePrice.NgayCuoi, 'DD/MM/YYYY').diff(moment(objValueCalculatePrice.NgayDau, 'DD/MM/YYYY'), 'years', true))
    if (year === 0) {
      showError(t('FAIL'), 'Thời gian không hợp lệ: ' + objValueCalculatePrice.NgayDau + ' - ' + objValueCalculatePrice.NgayCuoi)
    }
    return (valid && year >= 1)
  }

  const validateStep2 = () => {
    if (!khongCoBienSo) {
      return !validate.isEmpty(objValue.TenChuXe) &&
        !validate.isEmpty(objValue.BienKiemSoat) &&
        !validate.isEmpty(objValue.DiaChiChuXe) &&
        validateVat()
    } else {
      return !validate.isEmpty(objValue.TenChuXe) &&
        !validate.isEmpty(objValue.DiaChiChuXe) &&
        !validate.isEmpty(objValue.SoKhung) &&
        !validate.isEmpty(objValue.SoMay) &&
        validateVat()
    }
  }

  const validateVat = () => {
    if (xuatVAT) {
      return !validate.isEmpty(tenCongTy) &&
        !validate.isEmpty(diaChiCongTy) &&
        !validate.isEmpty(mst)
    } else {
      return true
    }
  }

  useEffect(() => {
    if (isRenew === true) {
      setStep(2)
      __DEV__ && console.log('dataRenew', dataRenew)
      // tang thoi gian
      const year = Math.ceil(moment(dataRenew.NgayCuoi, 'DD/MM/YYYY').diff(moment(dataRenew.NgayDau, 'DD/MM/YYYY'), 'years', true))
      __DEV__ && console.log('diff Year', year)
      const body = {
        ...objValue,
        ...dataRenew,
        NgayDau: dataRenew.NgayCuoi, // ngày đầu gia hạn = ngày cuối hết hạn
        NgayCuoi: moment(dataRenew.NgayCuoi, 'DD/MM/YYYY').add(year, 'y').format('DD/MM/YYYY')
      }
      __DEV__ && console.log('********isRenew****** body', body)
      setObjValue(body)
      setObjValueCalculatePrice(body)
    }
    return () => {
    }
  }, [isRenew])

  useEffect(() => {
    if (isViewDetail === true) {
      setStep(3)
      const body = {
        ...objValue,
        ...dataRenew
      }
      __DEV__ && console.log('********isViewDetail****** body', body)
      setObjValue(body)
      setObjValueCalculatePrice(body)
    }
    return () => {
    }
  }, [isViewDetail])

  const createOrderInsuranceHistory = async (data) => {
    const bodyHistory: any = {
      data: {
        TenCongTy: '',
        DiaChiCongTy: '',
        MaSoThue: '',
        MaGioiThieu: '',
        user_id: profileStore._id,
        MucDichSD: mucDichSuDung?.value || '',
        name: item?.attributes?.name,
        hangXe: loaiXe?.value,
        dongXe: loaiXe?.value || '',
        loai_bao_hiem: item?.attributes?.loai_bao_hiems?.data?.length > 0 ? item?.attributes?.loai_bao_hiems?.data[0].id : 1,
        ...data,
        type: 'BẢO HIỂM TNDS',
        dataRenew: { ...data, paymentId: data?.ma_giaodich },
        PaymentId: data.ma_giaodich,
        ProductId: item?.id + '',
      }
    }
    const rs = await insuranceStore.createOrderInsuranceHistory(bodyHistory)
  }

  const createOrderPVI = async () => {
    let body = {
      ...insuranceStore.dataCreateOrder,
      ma_giaodich: insuranceStore.paymentId,
      ChoNgoi: insuranceStore.dataCreateOrder?.so_cho,
      LoaiXe: insuranceStore.dataCreateOrder?.ma_loaixe,
      MaMucDichSD: insuranceStore.dataCreateOrder.ma_mdsd,
      PhiBHTNDSBB: `${insuranceStore.rsTinhPhiTNDSOto?.TotalFee}`,
      TongPhi: `${insuranceStore.rsTinhPhiTNDSOto?.TotalFee}`,
      TotalFeeNoVAT: `${insuranceStore.rsTinhPhiTNDSOto?.TotalFeeNoVAT}`,
      ma_loaixe_txt: loaiXe?.label || '',
      type: 'tnds'
    }
    __DEV__ && console.log('dataCreateOrder', insuranceStore.dataCreateOrder)
    if (insuranceStore.catNameSelected == 'BIC') {
      body = { ...body, ...insuranceStore.rsTinhPhiTNDSOto }
    }
    const rsCreate = await insuranceStore.taoDonTNDSOto(body)
    __DEV__ && console.log('rsCreate', rsCreate)
    // navigate(SCREENS.payment, { vnpUrl: rs.data.data.vnpUrl, orderId: productStore.orderId, paymentId: rs.data.data.paymentId, typeBooking: 0 })
    if (rsCreate && (rsCreate?.Status == '00' || rsCreate?.data?.Status == '00')) {
      createOrderInsuranceHistory({ ...body, Pr_key: rsCreate?.Pr_key || '' + '' })
      // showSuccess(t('THANHCONG'), t('Thành công'))
      // setTimeout(() => onGoBack(), 500)
    } else {
      showError(t('FAIL'), t(`#${body?.ma_giaodich} - ${rsCreate?.Message}`))
      createOrderFail()
    }
  }

  const createOrderFail = async () => {
    const body = {
      ...insuranceStore.dataCreateOrder,
      paymentId: insuranceStore.paymentId,
      ma_giaodich: 'CANCEL',
      ChoNgoi: insuranceStore.dataCreateOrder?.so_cho,
      LoaiXe: insuranceStore.dataCreateOrder?.ma_loaixe,
      MaMucDichSD: insuranceStore.dataCreateOrder?.ma_mdsd,
      PhiBHTNDSBB: `${renderDiscount()}`,
      TongPhi: `${renderDiscount()}`,
      xuatVAT: xuatVAT,
      TenCongTy: tenCongTy,
      DiaChiCongTy: diaChiCongTy,
      MaSoThue: mst,
      MaGioiThieu: maGioThieu,
    }
    createOrderInsuranceHistory(body)
    insuranceStore.clearArr()
  }

  const onSubmit = async () => {
    if (step === 1) {
      // load default data
      onChangeText('EmailKH', profileStore.email)
      onChangeText('DienThoai', getPhoneOnly(profileStore.phoneNumber || profileStore.phone))
      insuranceStore.tinhPhiTNDSOto(objValueCalculatePrice)
      setStep(2)
    }
    if (step === 2) {
      insuranceStore.tinhPhiTNDSOto(objValueCalculatePrice)
      onChangeText('NgayDau', objValueCalculatePrice.NgayDau)
      onChangeText('NgayCuoi', objValueCalculatePrice.NgayCuoi)
      onChangeText('ThamGiaLaiPhu', objValueCalculatePrice.thamgia_laiphu)
      onChangeText('MTNLaiPhu', objValueCalculatePrice.mtn_laiphu)
      onChangeText('TrongTai', objValueCalculatePrice.ma_trongtai)
      setStep(3)
    }

    if (step === 3) {
      setIsSubmitting(true)
      if (!validateStep1() || !validateStep2()) {
        showError(t('FAIL'), t('FILL_OUT_THE_FORM'))
        setIsSubmitting(false)
      }
      if (!bankCode) {
        // open chọn bank nếu chưa chọn
        openBottomSheet(1)
      } else {
        const body = {
          ...objValue,
          ...objValueCalculatePrice
        }
        __DEV__ && console.log('body', body)
        const rsCreate = await insuranceStore.createUrlPayment({ totalAmount: renderDiscount() || 0, bankCode: bankCode, data: body })

        if (rsCreate && rsCreate.data?.data.vnpUrl) {
          const { vnpUrl, paymentId, data } = rsCreate.data?.data
          navigate(SCREENS.payment, {
            vnpUrl: vnpUrl,
            orderId: paymentId,
            paymentId: paymentId,
            typeBooking: 6,
            title: 'Thanh toán bảo hiểm',
            callBackData: data
          })
          // setTimeout(() => onGoBack(), 500)
        } else {
          showError(t('FAIL'), t(`${rsCreate.data?.error}`))
          setIsSubmitting(false)
        }
      }
      setIsSubmitting(false)
    }
  }

  const onChangeText = (field, value) => {
    setObjValue((prev) => ({ ...prev, [field]: value }))
  }

  const onChangeObjValueCalculatePrice = (field, value) => {
    // field = handlerChangeValue(field)
    setObjValueCalculatePrice((prev) => ({ ...prev, [field]: value }))
  }

  const onSelectPaymentMethod = (index) => {
    if (index === 0) {
      setSelectedPaymentMethod([true, false])
    }
    if (index === 1) {
      setBankCode('')
      setSelectedPaymentMethod([false, true])
    }
  }

  // function pressItem(item) {
  //   onCloseModal()
  //   checkShowPopup(item._id)
  //   setIsPickerSelectVisible(true)
  //   setCategoryId(item._id)
  // }

  const iconRight = <Icon name={'chevron-down-outline'} size={20} color={'#333'} style={{ marginRight: 8 }} />
  const iconCalender = <Icon name={'calendar'} size={20} color={color.primary} style={{ marginRight: 8 }} />

  const renderItemLoaiXe = ({ item, index }) => {
    return <CheckBox
      // iconType='ionicon'
      title={item.label}
      checkedIcon='check-square'
      uncheckedIcon='square-o'
      checkedColor={color.primary}
      checked={insuranceStore.arrLoaiHinh.find(e => e.value === item.value)?.checked || false}
      onPress={() => {
        const arr = [...insuranceStore.arrLoaiHinh]
        const checked = !arr.find(e => e.value === item.value)?.checked
        arr.forEach((e, index) => {
          arr[index].checked = false
          onChangeText(e.value, false)
          onChangeObjValueCalculatePrice(item.value, false)
        })
        arr.find(e => e.value === item.value).checked = checked
        insuranceStore.setArrLoaiHinh(arr)
        onChangeText(item.value, checked)
        onChangeObjValueCalculatePrice(item.value, checked)
      }}
      textStyle={{ fontWeight: 'normal' }}
      containerStyle={styles.checkBox}
      wrapperStyle={{ marginVertical: 5, minWidth: responsiveWidth(50) - 20 }}
      size={20}
    />
  }

  const renderStep1 = () => {
    return (<>
      <View style={styles.mainContainer}>
        <>
          <Text style={[styles.titleLabel, { marginBottom: 0 }]}>Mục đích sử dụng</Text>
          <TouchableOpacity onPress={() => mucDichSuDungRef?.current.open()} style={styles.viewProvince}>
            <View style={{ flexDirection: 'row' }}>
              {!mucDichSuDung?.label ? <Text style={styles.textProvince}>Chọn mục đích sử dụng</Text>
                : <Text style={[styles.textProvince, { color: '#333' }]}>{mucDichSuDung?.label}</Text>}
            </View>
            {iconRight}
          </TouchableOpacity>
        </>
        <View flexDirection='row'>
          <View flex={1}>
            <Text style={styles.titleLabel}>Số chỗ ngồi</Text>
            <TTextInput
              containerStyle={{ height: 40, marginTop: 10 }}
              keyboardType='numeric'
              selectTextOnFocus={false}
              placeholderTextColor='#a0a0a0'
              placeholder={'Nhập số chỗ ngồi'}
              value={objValueCalculatePrice?.so_cho}
              onChangeText={(e) => {
                onChangeObjValueCalculatePrice('so_cho', e)
                onChangeObjValueCalculatePrice('so_nguoi', objValueCalculatePrice?.thamgia_laiphu ? e : 0)
                insuranceStore.getLoaiXe(mucDichSuDung?.value || '', e, objValueCalculatePrice.ma_trongtai)
              }}
            />
          </View>
        </View>
        {mucDichSuDung?.value == '3' ? <>
          <Text style={styles.titleLabel}>Trọng tải (kg)</Text>
          <TTextInput
            containerStyle={styles.textInputContainer}
            keyboardType='numeric'
            selectTextOnFocus={false}
            placeholderTextColor='#a0a0a0'
            placeholder={'Nhập trọng tải xe'}
            value={objValue.TrongTai}
            onChangeText={(e) => {
              onChangeText('TrongTai', e)
              onChangeObjValueCalculatePrice('ma_trongtai', e)
              insuranceStore.getLoaiXe(mucDichSuDung?.value || '', objValueCalculatePrice?.so_cho, e)
            }}
          />
        </> : null}
        <View flexDirection='row'>
          <View flex={1}>
            <Text style={styles.titleLabel}>Loại xe</Text>
            <TouchableOpacity onPress={() => loaiXeRef?.current.open()} style={styles.viewProvince}>
              <View style={{ flexDirection: 'row', maxWidth: responsiveWidth((80)) }}>
                {!loaiXe?.label ? <Text style={styles.textProvince}>Chọn loại xe </Text>
                  : <Text style={[styles.textProvince, { color: '#333' }]}>{loaiXe?.label}</Text>}
              </View>
              {iconRight}
            </TouchableOpacity>
          </View>
        </View>
        { insuranceStore.catNameSelected == 'PVI' ? <View style={{ flex: 1, marginTop: insuranceStore.arrLoaiHinh.length ? 15 : 0 }}>
          <FlatList
            horizontal={false}
            data={insuranceStore.arrLoaiHinh}
            numColumns={2}
            extraData={insuranceStore.arrLoaiHinh}
            keyExtractor={(item, index) => item.key + index.toString()}
            renderItem={renderItemLoaiXe}
          />
        </View> : null}

        <View flexDirection='row'>
          <View flex={1} marginRight={12}>
            <Text style={styles.titleLabel}>Ngày bắt đầu</Text>
            <TouchableOpacity onPress={() => setPickerDateVisible(true)} style={styles.viewProvince}>
              <View style={{ flexDirection: 'row' }}>
                {iconCalender}
                {!objValueCalculatePrice.NgayDau ? <Text style={styles.textProvince}>Ngày bắt đầu</Text>
                  : <Text style={[styles.textProvince, { color: '#333' }]}>{objValueCalculatePrice?.NgayDau}</Text>}
              </View>
              {iconRight}
            </TouchableOpacity>
          </View>
          <View flex={1}>
            <Text style={styles.titleLabel}>Thời hạn bảo hiểm</Text>
            <TouchableOpacity onPress={() => thoiHanRef?.current.open()} style={styles.viewProvince}>
              <View style={{ flexDirection: 'row' }}>
                {!thoiHan?.label ? <Text style={styles.textProvince}>Thời hạn </Text>
                  : <Text style={[styles.textProvince, { color: '#333' }]}>{thoiHan?.label}</Text>}
              </View>
              {iconRight}
            </TouchableOpacity>
          </View>
        </View>
        <CheckBox
          // iconType='ionicon'
          title={'Bảo hiểm tai nạn lái, phụ xe, người ngồi trên xe'}
          checkedIcon='check-square'
          uncheckedIcon='square-o'
          checkedColor={color.primary}
          checked={objValueCalculatePrice.thamgia_laiphu}
          onPress={() => {
            onChangeObjValueCalculatePrice('thamgia_laiphu', !objValueCalculatePrice.thamgia_laiphu)
            onChangeObjValueCalculatePrice('so_nguoi', objValueCalculatePrice?.so_cho)
          }}
          textStyle={{ fontWeight: 'normal' }}
          containerStyle={{ padding: 0, margin: 0, marginLeft: 0, borderWidth: 0, marginTop: 20, backgroundColor: '#fff' }}
          size={20}
        />
        {objValueCalculatePrice.thamgia_laiphu && <View flexDirection='row'>
          <View flex={1} marginRight={12}>
            <Text style={styles.titleLabel}>Số người tham gia BH</Text>
            <TTextInput
              containerStyle={{ height: 40, marginTop: 10 }}
              keyboardType='numeric'
              selectTextOnFocus={false}
              placeholderTextColor='#a0a0a0'
              placeholder={'Số người tham gia'}
              value={objValueCalculatePrice.so_nguoi}
              onChangeText={(e) => {
                if (parseInt(e) > parseInt(objValueCalculatePrice?.so_cho)) {
                  onChangeObjValueCalculatePrice('so_nguoi', '')
                  showError(t('FAIL'), t('Số người tham gia không thể lớn hơn số chỗ trên xe'))
                } else {
                  onChangeObjValueCalculatePrice('so_nguoi', e)
                }
              }}
            />
          </View>
          <View flex={1}>
            <Text style={styles.titleLabel}>Số tiền BH(đ/người/vụ)</Text>
            <TouchableOpacity onPress={() => soTienBhLaiPhuRef?.current.open()} style={styles.viewProvince}>
              <View style={{ flexDirection: 'row' }}>
                {!soTienBhLaiPhu?.label ? <Text style={styles.textProvince}>Số tiền BH</Text>
                  : <Text style={[styles.textProvince, { color: '#333' }]}>{soTienBhLaiPhu?.label}</Text>}
              </View>
              {iconRight}
            </TouchableOpacity>
          </View>
        </View>}
      </View>
      {/* <Text style={styles.txtRisk}>Quý khách hàng vui lòng chọn đúng loại xe, Doanh nghiệp Bảo hiểm và Đại lý không chịu trách nhiệm đối với việc chọn đăng ký thông tin xe không chính xác</Text> */}
      <View marginTop={20} style={styles.divider} />
      <View style={{ paddingHorizontal: 16 }}>
        <TermContent termContent={item?.attributes?.term} />
      </View>
    </>)
  }

  const renderStep2 = () => {
    return (<View style={{ marginBottom: 20 }}>
      <View style={styles.mainContainer}>
        <>
          <Text style={styles.titleLabel}>Tên chủ xe (theo đăng ký xe)</Text>
          <TTextInput
            containerStyle={styles.textInputContainer}
            selectTextOnFocus={false}
            placeholderTextColor='#a0a0a0'
            placeholder={'Nhập tên chủ xe'}
            value={objValue?.TenChuXe}
            onChangeText={(e) => {
              onChangeText('TenChuXe', Capitalize(e || ''))
              onChangeText('TenKH', Capitalize(e || ''))
            }}
          />
        </>
        <View flexDirection='row'>
          <View flex={1}>
            <Text style={styles.titleLabel}>Email</Text>
            <TTextInput
              keyboardType='email-address'
              containerStyle={styles.textInputContainer}
              selectTextOnFocus={false}
              placeholderTextColor='#a0a0a0'
              placeholder={'Nhập email'}
              value={objValue?.EmailKH}
              onChangeText={(e) => {
                onChangeText('EmailKH', e)
              }}
            />
          </View>
        </View>
        <View flexDirection='row'>
          <View flex={1}>
            <Text style={styles.titleLabel}>Số điện thoại</Text>
            <TTextInput
              keyboardType='numeric'
              containerStyle={styles.textInputContainer}
              selectTextOnFocus={false}
              placeholderTextColor='#a0a0a0'
              placeholder={'Nhập số điện thoại'}
              value={objValue?.DienThoai}
              onChangeText={(e) => {
                onChangeText('DienThoai', e)
              }}
            />
          </View>
        </View>
        <>
          <Text style={styles.titleLabel}>Địa chỉ (địa chỉ theo đăng ký xe)</Text>
          <TTextInput
            containerStyle={styles.textInputContainer}
            keyboardType='default'
            selectTextOnFocus={false}
            placeholderTextColor='#a0a0a0'
            placeholder={'Nhập địa chỉ'}
            value={objValue?.DiaChiChuXe}
            onChangeText={(e) => {
              onChangeText('DiaChiChuXe', Capitalize(e || ''))
              onChangeText('DiaChiKH', Capitalize(e || ''))
            }}
          />
        </>
        <View flexDirection='row'>
          <View flex={1} marginRight={12}>
            <Text style={styles.titleLabel}>Biển số xe</Text>
            <TTextInput
              containerStyle={styles.textInputContainer}
              keyboardType='default'
              selectTextOnFocus={false}
              placeholderTextColor='#a0a0a0'
              placeholder={'Nhập biển số'}
              value={objValue?.BienKiemSoat}
              onChangeText={(e) => {
                onChangeText('BienKiemSoat', noWhitespace(e.toUpperCase()))
              }}
            />
          </View>
          <View flex={1}>
            <Text style={styles.titleLabel}>Xe chưa có biển số</Text>
            <View style={styles.textInput}>
              <CheckBox
                // iconType='ionicon'
                // title={'Xe chưa có biển số'}
                checkedIcon='check-square'
                uncheckedIcon='square-o'
                checkedColor={color.primary}
                checked={khongCoBienSo}
                onPress={() => {
                  setKhongCoBienSo(!khongCoBienSo)
                }}
                textStyle={{ fontWeight: 'normal' }}
                containerStyle={[styles.checkBox, { backgroundColor: '#f3f3f3' }]}
                size={20}
              />
            </View>
          </View>
        </View>
        {khongCoBienSo && <View flexDirection='row'>
          <View flex={1} marginRight={12}>
            <Text style={styles.titleLabel}>Số khung</Text>
            <TTextInput
              keyboardType='default'
              containerStyle={styles.textInputContainer}
              selectTextOnFocus={false}
              placeholderTextColor='#a0a0a0'
              placeholder={'Nhập số khung'}
              value={objValue.SoKhung}
              onChangeText={(e) => {
                onChangeText('SoKhung', e)
              }}
            />
          </View>
          <View flex={1}>
            <Text style={styles.titleLabel}>Số máy</Text>
            <TTextInput
              keyboardType='default'
              containerStyle={styles.textInputContainer}
              selectTextOnFocus={false}
              placeholderTextColor='#a0a0a0'
              placeholder={'Nhập số máy'}
              value={objValue.SoMay}
              onChangeText={(e) => {
                onChangeText('SoMay', e)
              }}
            />
          </View>
        </View>}

        <CheckBox
          // iconType='ionicon'
          title={'Tôi muốn xuất hoá đơn'}
          checkedIcon='check-square'
          uncheckedIcon='square-o'
          checkedColor={color.primary}
          checked={xuatVAT}
          onPress={() => {
            setXuatVAT(!xuatVAT)
          }}
          textStyle={{ fontWeight: 'normal' }}
          containerStyle={{ padding: 0, margin: 0, marginLeft: 0, borderWidth: 0, marginTop: 20, backgroundColor: '#fff' }}
          size={20}
        />
        {xuatVAT && <>
          <Text style={styles.titleLabel}>Tên công ty</Text>
          <TTextInput
            containerStyle={styles.textInputContainer}
            selectTextOnFocus={false}
            // maxLength={4}
            placeholderTextColor='#a0a0a0'
            placeholder={'Nhập tên công ty'}
            value={tenCongTy}
            onChangeText={(e) => {
              setTenCongTy(Capitalize(e || ''))
            }}
          />
          <Text style={styles.titleLabel}>Địa chỉ công ty</Text>
          <TTextInput
            containerStyle={styles.textInputContainer}
            selectTextOnFocus={false}
            // maxLength={4}
            keyboardType={'default'}
            placeholderTextColor='#a0a0a0'
            placeholder={'Nhập địa chỉ công ty'}
            value={diaChiCongTy}
            onChangeText={(e) => {
              setDiaChiCongTy(Capitalize(e || ''))
            }}
          />
          <Text style={styles.titleLabel}>Mã số thuế</Text>
          <TTextInput
            containerStyle={styles.textInputContainer}
            keyboardType='numeric'
            selectTextOnFocus={false}
            // maxLength={4}
            placeholderTextColor='#a0a0a0'
            placeholder={'Nhập mã số thuế'}
            value={mst}
            onChangeText={(e) => {
              setMst(e)
            }}
          />
        </>}
        { !isRenew && <>
          <Text style={styles.titleLabel}>Mã giới thiệu</Text>
          <TTextInput
            containerStyle={{ ...styles.textInputContainer, ...{ backgroundColor: '#FFF1F1', borderWidth: 1, borderColor: color.primary } }}
            selectTextOnFocus={false}
            // maxLength={4}
            // keyboardType={'number-pad'}
            placeholderTextColor='#a0a0a0'
            placeholder={'Nhập mã giới thiệu'}
            value={maGioThieu}
            onChangeText={(e) => {
              setMaGioiThieu(e)
            }}
          />
        </> }
      </View>

    </View>)
  }

  useEffect(() => {
    __DEV__ && console.log('createOrderFail start 2')
    if (insuranceStore.paymentCancel) {
      __DEV__ && console.log('createOrderFail true 2')
      insuranceStore.setPaymentCancel(false)
      // gửi đơn hàng sau khi thanh toán thành công và lưu vào db
      createOrderFail()
    }

    return () => {

    }
  }, [insuranceStore.paymentCancel])

  const openLink = async (link) => {
    if (link) {
      try {
        await InAppBrowser.close()
        await InAppBrowser.open(link, {
          toolbarColor: color.primary,
          dismissButtonStyle: 'cancel',
          // preferredBarTintColor: color.primary,
          // preferredControlTintColor: '#fff',
          // preferredControlTintColor: 'white',
          readerMode: false,
          animated: true,
          // modalPresentationStyle: 'fullScreen',
          modalTransitionStyle: 'coverVertical',
          modalEnabled: true,
          enableBarCollapsing: false,
        })
      } catch (error) {
        Alert.alert(error.message)
      }
    }
  }

  const onViewOnline = async () => {
    show()
    if (insuranceStore.catNameSelected == 'BIC') {
      const rs = await insuranceStore.getUrlPvi(dataRenew?.Pr_key + '', 'tnds')
      if (rs?.data) {
        openLink(rs?.data?.URL)
      }
    } else {
      const rs = await insuranceStore.getUrlPvi(dataRenew?.paymentId || dataRenew?.ma_giaodich, 'tnds')
      if (rs?.data) {
        openLink(rs?.data?.URL)
      }
    }
    hide()
  }

  const getApiConfig = async () => {
    const api = new Api()
    const rs = await api.getAppConfig()
    if (rs && rs?.data?.data.attributes) {
      setPaymentGuideHtml(rs?.data?.data?.attributes?.content_guide_payment)
    }
  }

  const renderStep3 = () => {
    return (<>
      <View style={styles.mainContainer}>
        <View>
          <Text style={styles.txtTitle}>Thông tin chủ sở hữu</Text>
          {renderDetailRowLine('Tên chủ xe', objValue.TenChuXe)}
          {/* {renderDetailRowLine('Số CMND/CCCD', ' '} */}
          {renderDetailRowLine('Số điện thoại', objValue.DienThoai)}
          {renderDetailRowLine('Email', objValue.EmailKH)}
          {renderDetailRowLine('Địa chỉ', objValue.DiaChiChuXe)}
          {/* <View style={styles.detailInfoRow}> */}
          {/*  <Text style={styles.label}>Địa chỉ: <Text style={styles.value}>43 Nguyễn Chí Thanh - Ba Đình - Hà Nội fadfdf ssdfd sfds fdsfsfds</Text></Text> */}
          {/* </View> */}
        </View>
        <View marginTop={20} style={styles.divider} />
        <View>
          <Text style={styles.txtTitle}>Thông tin xe</Text>
          {/* {renderDetailRowLine('Hãng xe', 'BMW')} */}
          {/* {renderDetailRowLine('Năm SX', '2022')} */}
          {renderDetailRowLine('Mục đích SD', mucDichSuDung?.label || objValue.MucDichSuDung)}
          {insuranceStore.catNameSelected == 'PVI' && renderDetailRowLine('Loại xe', loaiXe?.label || insuranceStore.arrLoaiXe.find(x => x.value == dataRenew?.ma_loaixe)?.label)}
          {insuranceStore.catNameSelected == 'BIC' && renderDetailRowLine('Loại xe', loaiXe?.label || dataRenew?.ma_loaixe_txt) }
          {/* {renderDetailRowLine('Chức năng sử dụng', 'Khác')} */}
          {renderDetailRowLine('Trọng tải', objValue.TrongTai != '99999' ? objValue.TrongTai : '-')}
          {renderDetailRowLine('Biển số xe', objValue.BienKiemSoat)}
          {renderDetailRowLine('Số chỗ ngồi', `${objValueCalculatePrice?.so_cho} chỗ`)}
          {/* {renderDetailRowLine('Mức trách nhiệm về tài sản', `triệu/người/vụ`)} */}
          {soTienBhLaiPhu?.value && renderDetailRowLine('Mức trách nhiệm về người', `${formatMoney(soTienBhLaiPhu?.value)} triệu/người/vụ`)}
          {renderDetailRowLine('Ngày bắt đầu', objValue.NgayDau)}
          {thoiHan?.value && renderDetailRowLine('Thời hạn bảo hiểm', `${thoiHan?.value / 12} năm`)}
          {renderDetailRowLine('Ngày hết hạn', objValue?.NgayCuoi)}
          {renderDetailRowLine('Số người tham gia bảo hiểm', `${objValueCalculatePrice.so_nguoi} người`)}
        </View>
        { !isViewDetail && <>
          <View marginTop={20} style={styles.divider} />
          <View>
            <Text style={styles.txtRisk}>Bấm xác nhận đồng nghĩa với việc bạn đồng ý với <Text style={{ fontWeight: '700' }}>Điều kiện và điều
              khoản</Text> của maxQ và Đối tác cung cấp bảo hiểm.</Text>
            <View marginTop={20} style={styles.divider} />
            <Text style={styles.txtTitle}>Phương thức thanh toán</Text>
            <View flexDirection='row' flex={1}>
              <TouchableOpacity style={[styles.btnSelectYear, selectedPaymentMethod[0] && styles.selectedItemStyles]}
                onPress={() => onSelectPaymentMethod(0)}>
                {selectedPaymentMethod[0] &&
                  <Icon style={{ marginRight: 8 }} name={'radio-button-on-sharp'} size={20} color={color.primary} />}
                <Text style={selectedPaymentMethod[0] && { fontWeight: '500' }}>Chuyển khoản</Text>
              </TouchableOpacity>
              <TouchableOpacity style={[styles.btnSelectYear, styles.selectedItemStyles]}
                onPress={() => { openBottomSheet(2) }}>
                <Text>Hướng dẫn thanh toán</Text>
              </TouchableOpacity>
              {/* <TouchableOpacity style={[styles.btnSelectYear, selectedPaymentMethod[1] && styles.selectedItemStyles]} */}
              {/*  onPress={() => onSelectPaymentMethod(1)}> */}
              {/*  {selectedPaymentMethod[1] && */}
              {/*    <Icon style={{ marginRight: 8 }} name={'radio-button-on-sharp'} size={20} color={color.primary} />} */}
              {/*  <Text style={selectedPaymentMethod[1] && { fontWeight: '500' }}>Ví Momo, VN Pay</Text> */}
              {/* </TouchableOpacity> */}
            </View>
            <View flexDirection='row' flex={1} style={{ justifyContent: 'space-between', paddingBottom: 20 }}>
              {selectedPaymentMethod[0] && <TouchableOpacity onPress={() => {
                openBottomSheet(1)
              }}><Text style={{ color: color.primary }}>Bấm chọn ngân hàng</Text></TouchableOpacity>}
              <View>
                {bankCode ? <Text>(Đã chọn thanh toán qua) {bankCode}</Text> : null}
              </View>
            </View>

          </View>
        </>}
      </View>
    </>)
  }

  const renderDetailRowLine = (label, value) => {
    return (
      <View style={styles.detailInfoRow}>
        <Text style={styles.label}>{label}</Text>
        <Text style={styles.value}>{value}</Text>
      </View>
    )
  }

  const renderDiscount = () => {
    const discount = item?.attributes?.discount || 0
    const discountValue = (insuranceStore.rsTinhPhiTNDSOto?.TotalFee * discount) / 100
    return Math.ceil(((insuranceStore.rsTinhPhiTNDSOto?.TotalFee || 0) - discountValue) / 1000) * 1000
  }

  function onSelectItemMucDichSuDung(i) {
    setMucDichSuDung(i)
    onChangeText('MucDichSuDung', i?.label || '')
    onChangeText('ma_mdsd', i?.value || '')
    onChangeObjValueCalculatePrice('ma_mdsd', i?.value || '')
    insuranceStore.getLoaiHinh(i?.value || '')
    insuranceStore.getLoaiXe(i?.value || '', objValueCalculatePrice?.so_cho, objValueCalculatePrice.ma_trongtai)
  }

  return (
    <Fragment>
      <SafeAreaView style={styles.wrapper}>
        <Header
          leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={onGoBack} />}
          centerComponent={{ text: item?.attributes?.name, style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
          containerStyle={common.headerContainer}
          statusBarProps={{ barStyle: 'light-content' }}
          ViewComponent={LinearGradient}
          linearGradientProps={linearGradientProps}
        />
        <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
          {step === 1 && renderStep1()}
          {step === 2 && renderStep2()}
          {step === 3 && renderStep3()}
        </KeyboardAwareScrollView>
        { !isViewDetail ? <>
          <View style={[styles.divider, { height: 2 }]} />
          <View>
            <View flexDirection='row' justifyContent='space-between' marginHorizontal={16} marginVertical={10}>
              <Text>Phí bảo hiểm</Text>
              <Text style={styles.oldPrice}>{numberFormat(insuranceStore.rsTinhPhiTNDSOto?.TotalFee)} đ</Text>
              <Text style={styles.price}>{formatMoney(renderDiscount())} đ</Text>
            </View>
            <View flexDirection="row" marginHorizontal={16} style={{ alignItems: 'center', marginVertical: 10 }}>
              <Image source={icCheckSquare} style={{ width: 16, height: 16, marginRight: 8 }}></Image>
              <Text>{`maxQ hỗ trợ ${item?.attributes.discount}% phí bảo hiểm TNDS`}</Text>
            </View>
            {step === 1 &&
              <TButton loading={isSubmitting} disabled={isSubmitting || !validateStep1()} typeRadius={'rounded'} title={t('Tiếp tục')}
                onPress={onSubmit} buttonStyle={{ marginHorizontal: 16, marginVertical: 10 }}
                titleStyle={{ fontWeight: '500', fontSize: 16 }} />}
            {step === 2 &&
              <TButton loading={isSubmitting} disabled={isSubmitting || !validateStep2()} typeRadius={'rounded'} title={t('Thanh toán')}
                onPress={onSubmit} buttonStyle={{ marginHorizontal: 16, marginVertical: 10 }}
                titleStyle={{ fontWeight: '500', fontSize: 16 }} />}
            {step === 3 &&
              <TButton loading={isSubmitting} disabled={isSubmitting || !validateStep1()} typeRadius={'rounded'} title={t('Xác nhận')}
                onPress={onSubmit} buttonStyle={{ marginHorizontal: 16, marginVertical: 10 }}
                titleStyle={{ fontWeight: '500', fontSize: 16 }} />}
            {__DEV__ && <TButton loading={isSubmitting} typeRadius={'rounded'} title={t('Tiếp tục __DEV__')}
              onPress={onSubmit} buttonStyle={{ marginHorizontal: 16, marginVertical: 10 }}
              titleStyle={{ fontWeight: '500', fontSize: 16 }} />}

          </View>
        </> : <TouchableOpacity style={{ alignSelf: 'center' }} onPress={onViewOnline}>
          <Text style={{
            textAlign: 'center',
            alignItems: 'center',
            borderColor: color.primary,
            borderRadius: 3,
            borderWidth: 1,
            color: color.primary,
            justifyContent: 'center',
            margin: 6,
            marginBottom: 8,
            padding: 8,
          }}>Xem bản điện tử</Text>
        </TouchableOpacity>}

        <BottomSheetPicker
          headerText={'Chọn mục đích sử dụng'}
          items={insuranceStore.arrMucDichSuDung}
          onSelectItem={onSelectItemMucDichSuDung}
          ref={mucDichSuDungRef}
          search={true}
        />

        <BottomSheetPicker
          headerText={'Chọn số năm tham gia'}
          items={insuranceStore.catNameSelected == 'PVI' ? [
            { label: '1 Năm', value: '12' },
            { label: '1,5 Năm', value: '18' },
            { label: '2 Năm', value: '24' },
            { label: '2,5 Năm', value: '30' }
          ] : [
            { label: '1 Năm', value: '12' }
          ]}
          onSelectItem={(i) => handlerChonThoiHan(i)}
          ref={thoiHanRef}
        />
        <BottomSheetPicker
          headerText={'Số tiền bảo hiểm'}
          items={[
            { label: '50,000,000', value: '50000000' },
            { label: '100,000,000', value: '100000000' },
            { label: '150,000,000', value: '150000000' },
            { label: '200,000,000', value: '200000000' },
            { label: '250,000,000', value: '250000000' },
            { label: '300,000,000', value: '300000000' },
            { label: '350,000,000', value: '350000000' },
            { label: '400,000,000', value: '400000000' },
            { label: '450,000,000', value: '450000000' },
            { label: '500,000,000', value: '500000000' }
          ]}
          onSelectItem={(i) => {
            setSoTienBhLaiPhu(i)
            onChangeObjValueCalculatePrice('mtn_laiphu', i?.value || '')
          }}
          ref={soTienBhLaiPhuRef}
        />
        <BottomSheetPicker
          headerText={'Chọn loại xe'}
          items={insuranceStore.arrLoaiXe}
          onSelectItem={(i) => {
            setLoaiXe(i)
            onChangeObjValueCalculatePrice('ma_loaixe', i?.value || '')
          }}
          ref={loaiXeRef}
        />
        <DateTimePickerModal
          isVisible={pickerDateVisible}
          // mode={pickerDate}
          locale='vi_VN'
          cancelTextIOS={t('CANCEL')}
          confirmTextIOS={t('XACNHAN')}
          onConfirm={handleConfirm}
          onCancel={hidePicker}
          isDarkModeEnabled={false}
        />
        <BottomSheet
          index={-1}
          ref={bottomSheetRef}
          snapPoints={['50%']}
          enableHeaderGestures={true}
          enableContentGestures={false}
          enablePanDownToClose={false}
          // backdropComponent={() => <View style={{ flex: 1, backgroundColor: '#333', width: 200, height: 200 }}/>}
          backdropComponent={(props) => (
            <BottomSheetBackdrop
              {...props}
              appearsOnIndex={0}
              disappearsOnIndex={-1}
            />
          )}
          {...props}
        >
          <BottomSheetView style={{ flex: 1 }}>
            <View style={{ paddingBottom: 10, borderBottomColor: '#f2f2f2', borderBottomWidth: 1 }}>
              <ButtonBack onPress={() => {
                bottomSheetRef?.current.close()
              }} style={{ position: 'absolute', left: 16, top: 0, zIndex: 99999 }} />
              <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: 10 }}>
                <Text style={{ color: '#333', fontSize: 14 }}>Tái tục bảo hiểm</Text>
              </View>
            </View>
            <View margin={16}>
              <Text style={{ color: '#333', fontSize: 14, lineHeight: 20 }}>Giá trị xe của bạn đang được bảo hiểm là <Text
                style={{ fontWeight: '700' }}>500.000.000đ</Text> bạn có thể điều chỉnh giảm 5% so với giá này</Text>
              <View flexDirection='row' alignItems='center' marginTop={10}>
                <View style={styles.rangeSlider}>
                  <Slider
                    thumbTintColor={color.primary}
                    value={value}
                    onValueChange={setValue}
                    maximumValue={5000000}
                    minimumValue={50000}
                    step={10000}
                    allowTouchTrack
                    minimumTrackTintColor={color.primary}
                    thumbStyle={{ height: 20, width: 20 }}
                    // thumbProps={}
                  />

                </View>
                <View width={width / 3} marginLeft={16}>
                  <Text>Giá trị xe (đ)</Text>
                  <View style={styles.viewPrice}>
                    <Text>{numberFormat(value)}</Text>
                  </View>
                </View>
              </View>
              <TButton loading={isSubmitting} disabled={isSubmitting} typeRadius={'rounded'} title={t('Thanh toán')} onPress={() => {
              }} buttonStyle={{ marginHorizontal: 16, marginTop: 50 }} titleStyle={{ fontWeight: '500', fontSize: 16 }} />
            </View>
          </BottomSheetView>
        </BottomSheet>
        <Modalize
          HeaderComponent={<View style={{ paddingTop: 15 }}>
            <ButtonBack onPress={() => { closeBottomSheet() }} style={{ position: 'absolute', left: 16, top: 15, zIndex: 99999 }} />
            <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: 0 }}>
              <Text style={{ color: '#333', fontSize: 14 }}>
                { bottomSheetType === 1 && 'Chọn ngân hàng'}
                { bottomSheetType === 2 && 'Hướng dẫn thanh toán'}
              </Text>
            </View>
            <View style={{ marginTop: 15, height: 1, width: responsiveWidth(100), backgroundColor: '#f2f2f2' }}></View>
          </View>}
          ref={bottomSheetBankListRef}
          modalHeight={responsiveHeight(80)}
          disableScrollIfPossible = {false}
          onClosed={() => { setSetBottomSheetType(0) }}
          keyboardAvoidingBehavior={'padding'}
        >
          <View style={{ flex: 1 }}>
            { bottomSheetType === 1 && <>
              <Payment onSelect={onSelectBank} oncloseModal={() => closeBottomSheet()} /></>}
            { bottomSheetType === 2 && paymentGuideHtml && <View style={{ marginHorizontal: 16, marginTop: 16 }}>
              <RenderHtml
                contentWidth={width}
                source={{ html: paymentGuideHtml }}
                ignoredTags={['script']}
                ignoredStyles={['font-family']}
                renderersProps={{
                  img: {
                    enableExperimentalPercentWidth: true
                  }
                }}
              />
            </View>}
          </View>
        </Modalize>
      </SafeAreaView>
    </Fragment>
  )
}, { forwardRef: true })

const styles = StyleSheet.create({
  btnSelectYear: {
    alignItems: 'center',
    backgroundColor: '#F3F3F3',
    borderRadius: 5,
    flexDirection: 'row',
    flex: 1,
    marginBottom: 16,
    marginHorizontal: 4,
    paddingHorizontal: 14,
    paddingVertical: 8
  },
  checkBox: {
    backgroundColor: '#fff',
    borderWidth: 0,
    margin: 0,
    marginLeft: 0,
    padding: 0
  },
  detailInfoRow: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16
  },
  divider: {
    backgroundColor: '#E6E6E6',
    height: 5
  },
  label: {
    color: '#A0A0A0',
    // lineHeight: 20,
    marginRight: 10
  },
  mainContainer: {
    // backgroundColor: 'red',
    flex: 1,
    justifyContent: 'flex-start',
    // paddingBottom: 89,
    paddingTop: 15,
    paddingHorizontal: 16
    // height: '100%'
  },
  oldPrice: {
    color: color.dim,
    textDecorationLine: 'line-through'
  },
  price: {
    color: color.primary,
    fontSize: 16,
    fontWeight: '600'
  },
  rangeSlider: {
    alignItems: 'stretch',
    flex: 1,
    justifyContent: 'center',
    marginTop: 30
  },
  selectedItemStyles: {
    // backgroundColor: '#F1F1F1',
    borderColor: color.primary,
    borderWidth: 1
  },
  textInput: {
    alignItems: 'center',
    backgroundColor: '#f3f3f3',
    // borderColor: '#edf1f7',
    borderRadius: 4,
    // borderStyle: 'solid',
    // borderWidth: 1,
    color: '#333',
    flexDirection: 'row',
    fontSize: 15,
    height: 40,
    justifyContent: 'space-between',
    marginTop: 10,
    paddingLeft: 14
  },
  textInputContainer: {
    height: 40,
    marginTop: 10,
  },
  textProvince: {
    alignSelf: 'center',
    color: '#a0a0a0',
    fontFamily: typography.normal,
    fontSize: 14
  },
  titleLabel: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    paddingTop: 20
  },
  txtRisk: {
    color: color.primary,
    paddingTop: 20
  },
  txtTitle: {
    color: '#3F3F3F',
    fontSize: 16,
    fontWeight: 'bold',
    marginVertical: 16
  },
  value: {
    color: '#2D384C',
    lineHeight: 20
  },
  viewPrice: {
    alignItems: 'center',
    backgroundColor: '#F3F3F3',
    borderRadius: 5,
    marginTop: 10,
    paddingHorizontal: 20,
    paddingVertical: 12
  },
  viewProvince: {
    alignItems: 'center',
    backgroundColor: '#f3f3f3',
    // borderColor: '#edf1f7',
    borderRadius: 4,
    // borderStyle: 'solid',
    // borderWidth: 1,
    flexDirection: 'row',
    height: 40,
    justifyContent: 'space-between',
    marginTop: 10,
    paddingLeft: 14
  },
  wrapper: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
})
