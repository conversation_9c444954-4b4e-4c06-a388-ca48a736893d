import React, { useContext, useEffect, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { StyleSheet, View, Text, FlatList, TouchableOpacity } from 'react-native'
import { styled } from 'nativewind'
import FastImage from 'react-native-fast-image'
import { responsiveHeight } from 'react-native-responsive-dimensions'
import Icon from 'react-native-vector-icons/Ionicons'
import { useStores } from '@app/models'
import { ModalContext } from '@app/components/modal-success'
import { useNavigation } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import { SCREENS } from '@app/navigation'
import { color } from '@app/theme'
import { ConfirmDialog } from '@app/components'
// import { useNavigation } from "@react-navigation/native"
// import { useStores } from "../models"

const SView = styled(View)
const SText = styled(Text)

// STOP! READ ME FIRST!
// To fix the TS error below, you'll need to add the following things in your navigation config:
// - Add `MyInsurance: undefined` to AppStackParamList
// - Import your screen, and add it to the stack:
//     `<Stack.Screen name="MyInsurance" component={MyInsuranceScreen} />`
// Hint: Look for the 🔥!

// REMOVE ME! ⬇️ This TS ignore will not be necessary after you've added the correct navigator param type
// @ts-ignore
export const MyInsuranceScreen = observer(function MyInsuranceScreen() {
  // Pull in one of our MST stores
  const { insuranceStore, profileStore } = useStores()
  const [refreshing, setRefreshing] = useState(false)
  const [loadMore, setLoadMore] = useState(false)
  const [page, setPage] = useState(1)
  const [isFetched, setIsFetched] = useState(true)
  const { showError, showSuccess } = useContext(ModalContext)
  const { navigate, goBack } = useNavigation()
  const { t } : any = useTranslation()
  const [isShowConfirm, setIsShowConfirm] = useState(false)
  const [selectedId, setSelectedId] = useState(null)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    const isLoadMore = page > 1
    if (!isLoadMore) {
      setIsFetched(true)
    }
    await insuranceStore.getLichSuMuaBHByUserId(profileStore._id)
    // if (index == BookingType.CLINIC) {
    //   __DEV__ && console.log('LoadData getBookingHistory', index)
    //   await profileStore.getBookingHistory(page, isLoadMore)
    //   serviceStore.setTypeBooking(BookingType.CLINIC)
    // }
    // if (index == BookingType.PARKING) {
    //   __DEV__ && console.log('LoadData getBookingClinic', index)
    //   await profileStore.getBookingClinic(page, isLoadMore)
    //   serviceStore.setTypeBooking(BookingType.PARKING)
    // }
    // if (index == BookingType.SHOP) {
    //   __DEV__ && console.log('LoadData get--Booking--ProDuct', index)
    //   await profileStore.getBookingProduct(page, isLoadMore)
    //   serviceStore.setTypeBooking(BookingType.SHOP)
    // }
    // if (index == BookingType.SHOWROOM) {
    //   __DEV__ && console.log('LoadData SHOWROOM', index)
    //   await profileStore.getBookingShowRoom(page, isLoadMore)
    //   serviceStore.setTypeBooking(BookingType.SHOWROOM)
    // }
    // setLoadMore(false)
    setRefreshing(false)
    setIsFetched(false)
  }

  const onRefresh = () => {
    __DEV__ && console.log('onRefresh ', page)
    setRefreshing(true)
    loadData()
  }

  const colorBackground = (value) => {
    if (value == 'PVI') {
      return '#186DB5'
    }
    if (value == 'BIC') {
      return '#08655A'
    }
    if (value == 'BIC') {
      return '#08655A'
    }
    if (value == 'PIJICO') {
      return '#F68824'
    }
    return '#F68824'
  }

  const taiTucBH = async (item) => {
    __DEV__ && console.log('item selected', item)
    if (item?.attributes?.type == 'BẢO HIỂM VẬT CHẤT') {
      navigate(SCREENS.baoHiemVCXB1, { prodId: item?.attributes?.ProductId, isRenew: true, dataRenew: item?.attributes?.dataRenew })
    }
    if (item?.attributes?.type == 'BẢO HIỂM TNDS') {
      navigate(SCREENS.baoHiemTNDSB1, { prodId: item?.attributes?.ProductId, isRenew: true, dataRenew: item?.attributes?.dataRenew })
    }
    if (item?.attributes?.type == 'BẢO HIỂM TNDS XE MÁY') {
      navigate(SCREENS.baoHiemTNDSXeMayB1, { prodId: item?.attributes?.ProductId, isRenew: true, dataRenew: item?.attributes?.dataRenew })
    }
  }

  const viewDetail = async (item) => {
    __DEV__ && console.log('item selected', item)
    if (item?.attributes?.type == 'BẢO HIỂM VẬT CHẤT') {
      navigate(SCREENS.baoHiemVCXB1, { prodId: item?.attributes?.ProductId, isRenew: true, isViewDetail: true, id: item?.attributes?.PaymentId, dataRenew: item?.attributes?.dataRenew })
    }
    if (item?.attributes?.type == 'BẢO HIỂM TNDS') {
      navigate(SCREENS.baoHiemTNDSB1, { prodId: item?.attributes?.ProductId, isRenew: true, isViewDetail: true, id: item?.attributes?.PaymentId, dataRenew: item?.attributes?.dataRenew })
    }
    if (item?.attributes?.type == 'BẢO HIỂM TNDS XE MÁY') {
      navigate(SCREENS.baoHiemTNDSXeMayB1, { prodId: item?.attributes?.ProductId, isRenew: true, isViewDetail: true, id: item?.attributes?.PaymentId, dataRenew: item?.attributes?.dataRenew })
    }
  }

  const deleteProduct = async () => {
    await insuranceStore.deleteInsuranceLink(selectedId)
    loadData()
  }

  // Pull in navigation via hook
  function renderProduct({ item, index }) {
    const brand = item?.attributes?.loai_bao_hiem?.data?.attributes.name
    return (<TouchableOpacity onPress={() => viewDetail(item)} style={{ marginBottom: 15 }}>
      <SView className="rounded-lg" style={{ height: 220, marginHorizontal: 10, backgroundColor: colorBackground(brand) }}>
        <SView className="absolute" style={{ left: 19, top: 80 }}>
          <SText className="text-white mt-1 uppercase">{ item?.attributes?.TenKH }</SText>
          <SText className="text-white mt-1">{item?.attributes?.BienKiemSoat || '-'}</SText>
          <SText className="text-white mt-1">{`Bắt đầu ${item?.attributes?.GioDau || ''} ${item?.attributes?.NgayDau}`}</SText>
          {/* <SText className="absolute text-xs text-white" style={{ left: 82, top: 53, }}>Hotline: 1900.000</SText> */}
          <SText className="text-white mt-1">{`Hết hạn ${item?.attributes?.GioCuoi || ''} ${item?.attributes?.NgayCuoi}`}</SText>
          <TouchableOpacity onPress={() => { taiTucBH(item) }}><SText className="font-bold text-white  mt-5">Tái tục bảo hiểm</SText></TouchableOpacity>
        </SView>
        <SView style={{ left: 19, top: 17, }}>
          <FastImage resizeMode={'cover'} style={{ width: 50, height: 50, borderRadius: 3 }} source={{ uri: item?.attributes?.loai_bao_hiem?.data?.attributes?.image?.data?.attributes?.url }}/>
        </SView>
        <SText className="absolute text-xs text-white" style={{ left: 82, top: 15, }}>BẢO HIỂM {brand}</SText>
        <SText className="absolute text-xs text-white" style={{ textTransform: 'uppercase', left: 82, top: 35, fontSize: 14 }}>{item?.attributes?.type}</SText>
        <SText className="absolute text-xs text-white" style={{ left: 82, top: 55, fontSize: 14 }}>Hotline: {item?.attributes?.loai_bao_hiem?.data?.attributes?.hotline}</SText>
        <SView className="absolute items-center justify-center" style={{ right: 15, bottom: 15, width: 30, height: 30, backgroundColor: '#E10714', borderRadius: 50 }}>
          <TouchableOpacity onPress={() => {
            setSelectedId(item?.id)
            setIsShowConfirm(true)
          }}>
            <Icon name={'close'} size={24} color={'#fff'}/>
          </TouchableOpacity>
        </SView>
      </SView>
    </TouchableOpacity>)
  }

  const goLoginScreenRequired = () => {
    navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  // const navigation = useNavigation()
  return (
    <SView className="bg-white" style={styles.flatListContainer}>
      { profileStore.isSignedIn() ? <FlatList
        contentContainerStyle={{ backgroundColor: '#fff', marginTop: 15, paddingBottom: 60 }}
        data={insuranceStore.dataInsurancePosts}
        // numColumns={2}
        // initialNumToRender={10}
        refreshing={refreshing}
        onRefresh={onRefresh}
        renderItem={renderProduct}
        keyExtractor={item => item.id + 1}
        extraData={insuranceStore.dataInsurancePosts}
        showsVerticalScrollIndicator={false}
        // onScrollBeginDrag={e => {
        //   __DEV__ && console.log('onScrollBeginDrag')
        //   // show icon loading bottom
        //   onScroll(e)
        //   setLoadMore(true)
        //   // if (page === profileStore.totalPage) {
        //   //   __DEV__ && console.log('No more data...')
        //   //   setLoadMore(false)
        //   // }
        // }}
        // onScrollEndDrag={onScroll}
        // onMomentumScrollEnd={handleLoadMore}
        // ListFooterComponent={renderFooter}
        // ListHeaderComponent={renderHeader}
      /> : <View style={{
        // alignItems: 'center'
      }}>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'center',
          padding: 10,
          marginTop: 40
        }}>
          <Text style={{
            fontSize: 14,
            color: '#333',
          }}>Yêu cầu</Text>
          <TouchableOpacity
            onPress={goLoginScreenRequired}
            // style={[{
            //   alignItems: 'center',
            //   // backgroundColor: '#d2d2d2',
            //   // elevation: 5,
            //   justifyContent: 'center',
            //   margin: 10,
            //   // shadowColor: '#000',
            //   // shadowOffset: {
            //   //   width: 0,
            //   //   height: 1,
            //   // },
            //   // shadowOpacity: 0.22,
            //   // shadowRadius: 2.22,
            // }]}
          >
            <Text style={{
              fontSize: 14,
              color: color.primary,
              fontWeight: 'bold',
            }}> {t('LOGIN')}</Text>
          </TouchableOpacity>
          <Text style={{
            fontSize: 14,
            color: '#333',
          }}> để xem nội dung</Text>
        </View>
      </View> }
      <ConfirmDialog confirmText={t('DONGY')} cancelText={t('CANCEL')} onClosed={() => setIsShowConfirm(false)} isVisible={isShowConfirm} message={'Bạn có chắc chắn muốn xóa ?'} title={'Nhắc nhở'}
        onConfirm={() => {
          setIsShowConfirm(false)
          deleteProduct()
        }
        } />
    </SView>
  )
})

const styles = StyleSheet.create({
  flatListContainer: {
    flex: 1,
    height: responsiveHeight(100),
    // ...ifIphoneX({
    //   paddingBottom: 89,
    // }, {
    //   paddingBottom: 60,
    // }),
    // width: responsiveWidth(100),
    // marginLeft: 15,
    // marginRight: 15
    backgroundColor: '#fff'
  },
})
