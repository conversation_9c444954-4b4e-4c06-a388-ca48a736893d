import React, { useRef, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { View, Text, Dimensions, StyleSheet } from 'react-native'
import { TabBar } from 'react-native-tab-view'
import { useNavigation, useRoute } from '@react-navigation/native'
import { useStores } from '@app/models'
import { useTranslation } from 'react-i18next'
import { typography } from '@app/theme'
import { ButtonBack, EmptyData } from '@app/components'
import { useHeaderFixed } from '@app/use-hooks'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import LinearGradient from 'react-native-linear-gradient'
import { SafeAreaView } from 'react-native-safe-area-context'
import { styled } from 'nativewind'
import { InsuranceProductScreen, MyInsuranceScreen } from '@app/screens'
const saleIconRatio = 38 / 14
const initialLayout = { width: Dimensions.get('window').width }
const StyledView = styled(View)
const StyledText = styled(Text)

const TabNames = {
  SHOP: 'SANPHAM',
  CLINIC: 'GARAGE',
  HOTEL: 'HOTEL',
  SPA: 'SERVICE',
  SHOWROOM: 'SHOWROOM',
}
export const InsuranceScreen = observer((props: any) => {
  const { t } : any = useTranslation()
  const { profileStore, productStore } = useStores()
  const { navigate, goBack } = useNavigation()
  const route: any = useRoute()
  const { bookingType } = route?.params || { bookingType: 0 }
  const [index, setIndex] = React.useState(0)
  const [refreshing, setRefreshing] = useState(false)
  const [loadMore, setLoadMore] = useState(false)
  const [page, setPage] = useState(1)
  const [isFetched, setIsFetched] = useState(true)
  const refContainer = useRef(null)
  const { offset, onScroll } = useHeaderFixed()
  const [defaultTabIndex, setDefaultTabIndex] = useState(0)
  const [tabs, setTabs] = useState([])
  const [categoriesID, setCategoriesID] = useState(0)
  const [cateText, setCateText] = useState('')

  const [routes, setRoutes] = React.useState([
    { key: 'buy', title: t('Mua bảo hiểm') }, // TODO: chưa rõ chưa fix được text ko đổi
    { key: 'myinsurance', title: t('Bảo hiểm của tôi') },
  ])

  // const refreshData = async () => {
  //   setIsFetched(true)
  //   await profileStore.getBookingHistory(1, false)
  //   setLoadMore(false)
  //   setRefreshing(false)
  //   setIsFetched(false)
  // }

  const onGoBack = () => {
    goBack()
    setTimeout(() => { productStore.setReloadData(true) }, 200)
  }

  const renderScene = ({ route }) => {
    switch (route.key) {
      case 'buy':
        return <InsuranceProductScreen />
      case 'myinsurance':
        return <MyInsuranceScreen/>
      default:
        return null
    }
  }

  const renderTabBar = (props) => (
    <TabBar
      {...props}
      indicatorStyle={{ backgroundColor: '#f3373a' }}
      style={styles.tabBar}
      renderLabel={({ route, focused, color }) => (
        <Text style={[styles.tabBarText, { fontSize: 16 }, { color: focused ? '#2e2e2e' : '#848484' }]}>
          {route.title}
        </Text>
      )}
    />
  )

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <Header
        // statusBarProps={{ barStyle: 'light-content' }}
        // barStyle="light-content" // or directly
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={onGoBack}/>}
        centerComponent={{ text: t(t('Lịch sử giao dịch')), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
        //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
        // </TouchableOpacity> : null}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      <View style={styles.background}>
        {/* <View style={styles.renderTitle}> */}
        {/*  <ButtonBack onPress={goBack}/> */}
        {/*  <Text style={styles.TitleText}>{title}</Text> */}
        {/* </View> */}
        {/* <Header position={offset} title={t('BOOKING_HISTORY')} onPressButtonLeft={goBack}/> */}

        {/* <TabView */}
        {/*  navigationState={{ index, routes }} */}
        {/*  renderScene={renderScene} */}
        {/*  onIndexChange={setIndex} */}
        {/*  initialLayout={initialLayout} */}
        {/*  renderTabBar={renderTabBar} */}
        {/*  // initialPage={defaultTabIndex || 0} */}
        {/*  style={{ backgroundColor: '#f7f9fc' }} */}
        {/* /> */}
        <EmptyData title={''} message={'Đang xây dựng'}/>

      </View>
    </SafeAreaView>
  )
})

const styles = StyleSheet.create({
  background: {
    backgroundColor: '#fff',
    flex: 1
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },

  tabBar: {
    // Remove border top on both android & ios
    backgroundColor: '#fff',
    borderTopColor: 'transparent',
    borderTopWidth: 0,
    elevation: 0,
    shadowColor: '#5bc4ff',
    shadowOffset: {
      height: 0,
      width: 0,
    },
    shadowOpacity: 0,
    shadowRadius: 0,
  },
  tabBarText: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
  },

})
