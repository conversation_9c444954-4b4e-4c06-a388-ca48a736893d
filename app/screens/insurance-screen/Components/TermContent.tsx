import React, { useState } from 'react'
import {
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native'
import { observer } from 'mobx-react-lite'
import Icon from 'react-native-vector-icons/Ionicons'
import RenderHtml from 'react-native-render-html'

/**
 * Describe your component here
 */

export interface TermContentProps {
termContent?: string
}
const { width } = Dimensions.get('window')
export const TermContent = observer(function TermContent(props: TermContentProps) {
  const { termContent } = props
  const [isShowMore, setIsShowMore] = useState(false)

  return (
    <View >
      <Text style={styles.txtTitle}>Thông tin quy định bảo hiểm</Text>
      <View style={styles.content}>
        { isShowMore && <RenderHtml
          contentWidth={width}
          source={{ html: termContent }}
          ignoredTags={['script']}
          ignoredStyles={['font-family']}
          renderersProps={{
            img: {
              enableExperimentalPercentWidth: true
            }
          }}
        /> }
        <TouchableOpacity style={styles.btnXemthem} onPress={() => {
          setIsShowMore(!isShowMore)
        }}>
          <View flexDirection="row">
            <Text style={styles.txtXemthem}>{!isShowMore ? 'Xem thêm' : 'Thu gọn'}</Text>
            <Icon name={!isShowMore ? 'chevron-down-outline' : 'chevron-up-outline'} size={18} color={'#979797'} />
          </View>
        </TouchableOpacity>
      </View>
    </View>
  )
}
)
const styles = StyleSheet.create({
  btnXemthem: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flex: 1,
    paddingVertical: 10,
  },
  content: {
    backgroundColor: '#fff'
  },
  txtTitle: {
    color: '#3F3F3F',
    fontSize: 16,
    fontWeight: 'bold',
    marginVertical: 16
  },
  txtXemthem: {
    color: '#979797',
    fontSize: 14,
    marginRight: 8
  }
})
