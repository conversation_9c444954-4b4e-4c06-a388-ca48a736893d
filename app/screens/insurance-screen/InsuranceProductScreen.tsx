import React, { useEffect, useMemo, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { Dimensions, FlatList, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { color, typography } from '@app/theme'
import { responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import FastImage from 'react-native-fast-image'
import Icon from 'react-native-vector-icons/Ionicons'
import { useStores } from '@app/models'
import { useTranslation } from 'react-i18next'
import { useNavigation, useRoute } from '@react-navigation/native'
import { icCheckSquare, saleIcon } from '@app/assets/images'
import { numberFormat } from '@app/utils'
import { styled } from 'nativewind'
import { SCREENS } from '@app/navigation'
import { useHeaderFixed } from '@app/use-hooks'
import { EmptyData } from '@app/components'
import { useSafeAreaInsets } from 'react-native-safe-area-context'

const saleIconRatio = 38 / 14
const initialLayout = { width: Dimensions.get('window').width }
const StyledView = styled(View)
const StyledText = styled(Text)

// STOP! READ ME FIRST!
// To fix the TS error below, you'll need to add the following things in your navigation config:
// - Add `InsuranceProduct: undefined` to AppStackParamList
// - Import your screen, and add it to the stack:
//     `<Stack.Screen name="InsuranceProduct" component={InsuranceProductScreen} />`
// Hint: Look for the 🔥!

// REMOVE ME! ⬇️ This TS ignore will not be necessary after you've added the correct navigator param type
// @ts-ignore
export const InsuranceProductScreen = observer(function InsuranceProductScreen(props) {
  // Pull in one of our MST stores
  const { insuranceStore, profileStore } = useStores()
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()
  const route: any = useRoute()
  const [index, setIndex] = React.useState(0)
  const [refreshing, setRefreshing] = useState(false)
  const [loadMore, setLoadMore] = useState(false)
  const [page, setPage] = useState(1)
  const [isFetched, setIsFetched] = useState(true)
  const { offset, onScroll } = useHeaderFixed()
  const [categoriesID, setCategoriesID] = useState(0)
  const [cateText, setCateText] = useState('')

  const onRefresh = () => {
    __DEV__ && console.log('onRefresh ', page)
    setRefreshing(true)
    loadData(index)
  }

  useEffect(() => {
    insuranceStore.getInsuranceCategories()
  }, [])

  useEffect(() => {
    loadData(index)
  }, [cateText, index])

  const loadData = async (index) => {
    const isLoadMore = page > 1
    if (!isLoadMore) {
      setIsFetched(true)
    }
    __DEV__ && console.log('LoadData ', index)
    if (index == 0) {
      if (!insuranceStore.dataPostCategories?.length) {
        insuranceStore.getInsuranceCategories()
      }
      await insuranceStore.getInsurancePosts(cateText)
    }
    // if (index == BookingType.CLINIC) {
    //   __DEV__ && console.log('LoadData getBookingHistory', index)
    //   await profileStore.getBookingHistory(page, isLoadMore)
    //   serviceStore.setTypeBooking(BookingType.CLINIC)
    // }
    // if (index == BookingType.PARKING) {
    //   __DEV__ && console.log('LoadData getBookingClinic', index)
    //   await profileStore.getBookingClinic(page, isLoadMore)
    //   serviceStore.setTypeBooking(BookingType.PARKING)
    // }
    // if (index == BookingType.SHOP) {
    //   __DEV__ && console.log('LoadData get--Booking--ProDuct', index)
    //   await profileStore.getBookingProduct(page, isLoadMore)
    //   serviceStore.setTypeBooking(BookingType.SHOP)
    // }
    // if (index == BookingType.SHOWROOM) {
    //   __DEV__ && console.log('LoadData SHOWROOM', index)
    //   await profileStore.getBookingShowRoom(page, isLoadMore)
    //   serviceStore.setTypeBooking(BookingType.SHOWROOM)
    // }
    setLoadMore(false)
    setRefreshing(false)
    setIsFetched(false)
  }

  const handleLoadMore = () => {
    // if (!loadMore) return
    // khi scroll dừng lại sẽ gọi vào hàm này
    const totalPage = insuranceStore.totalPage
    if (page < totalPage) {
      setPage(page + 1)
    }
    if (page === totalPage) {
      __DEV__ && console.log('No more data...')
      // setLoadMore(false)
    }
  }

  const onPressItem = (it) => {
    if (profileStore.isSignedIn()) {
      if (it.attributes?.type == 'BHTNDSCAR') {
        navigate(SCREENS.baoHiemTNDSB1, { prodId: it?.id })
      }
      if (it.attributes?.type == 'BHVCCAR') {
        navigate(SCREENS.baoHiemVCXB1, { prodId: it?.id })
      }
      if (it.attributes?.type == 'BHTNDSBIKE') {
        navigate(SCREENS.baoHiemTNDSXeMayB1, { prodId: it?.id })
      }
    } else {
      navigate(SCREENS.authStack, { screen: SCREENS.login })
    }
  }

  const renderProduct = (props: any) => {
    const { item } = props

    const discount = (item.price - item.priceOld) / item.priceOld
    const percentDiscount = discount * 100
    return (
      <View
        style={styles.renderProduct}>
        <TouchableOpacity
          onPress={() => {
            onPressItem(item)
          }}
          style={styles.viewImage}>
          <FastImage source={{ uri: `${item.attributes.images.data[0].attributes.url}` }}
            style={{ width: '100%', height: 170 }} />
          {/* <LazyImage source={{ uri: item.image || item.thumbail || item.picture }} style={{ width: '100%', height: 170 }} ><View style={styles.discount}><Text style={styles.textDiscount}>-10%</Text></View></LazyImage> */}
          <View style={styles.discountViewRow}>
            {/* {item.typeShip === 0 && <Image source={freeShipIcon} style={styles.freeShipIc}></Image>} */}
            {item.priceOld && <Image source={saleIcon} style={styles.saleIcon}></Image>}
          </View>
        </TouchableOpacity>
        <View style={{ flex: 1 }}>
          <Text numberOfLines={2}
            ellipsizeMode='tail'
            style={styles.rspTopViewText}>{item?.attributes?.name}</Text>
          <View style={{ marginLeft: 8 }}>
            {item.price && <Text style={styles.rspTopViewTextPrice}>{numberFormat(item.price)} đ</Text>}
            {item.priceOld && item.priceOld > 0 && !isNaN(percentDiscount) && percentDiscount != 0 && <View style={{ flexDirection: 'row' }}>
              <Text numberOfLines={2} style={styles.textOldPrice}>{numberFormat(item.priceOld)} đ</Text>
              <Text numberOfLines={2} style={styles.textSaleOff}>{item.attributes.discount}%</Text>
            </View>}
            <View flexDirection="row" style={{ justifyContent: 'space-between' }}>
              <View style={{ flexDirection: 'row' }}>
                <Image source={icCheckSquare} style={{ width: 16, height: 16, marginRight: 8 }}></Image>
                <Text>{'hỗ trợ '}</Text>
              </View>
              <View style={{ backgroundColor: color.primary, width: 40, borderRadius: 3, marginRight: 8 }}>
                <Text numberOfLines={1} style={{ color: '#fff', textAlign: 'center', }}>{item.attributes.discount}%</Text>
              </View>
            </View>
          </View>
          {/* <View style={styles.sectionRate}> */}
          {/*  <Text style={styles.textAddress}>{item.storeName}</Text> */}
          {/*  {rateValue > 0 ? <View style={styles.renderStar}> */}
          {/*    <StarRating */}
          {/*      fullStarColor={'#FFC107'} */}
          {/*      disabled={true} */}
          {/*      maxStars={5} */}
          {/*      rating={rateValue} */}
          {/*      emptyStarColor={'#edf1f7'} */}
          {/*      emptyStar={'star'} */}
          {/*      fullStar={'star'} */}
          {/*      halfStar={'star-half-o'} */}
          {/*      iconSet={'FontAwesome'} */}
          {/*      starSize={10} */}
          {/*      // containerStyle={styles.startContainer} */}
          {/*      starStyle={styles.customStar} */}
          {/*      // selectedStar={(rating) => ratingCompleted(rating)} */}
          {/*    /> */}
          {/*  </View> : <Text style={styles.textAddress}>Chưa có đánh giá</Text>} */}
          {/* </View> */}
        </View>
        <View style={{ flexDirection: 'row', marginTop: 10 }}>
          <TouchableOpacity onPress={() => {
            onPressItem(item)
          }} style={{ justifyContent: 'center', width: '100%' }}>
            {/* <Text style={styles.textBtnAdd}>Thêm vào giỏ</Text> */}
            <Text style={styles.textBtnAdd}>Mua ngay</Text>
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  const renderCategories = ({ item, index }) => {
    return <TouchableOpacity onPress={() => {
      setCategoriesID(item.id)
      if (item.id === 0) {
        setCateText('')
      } else {
        setCateText(item.attributes.name)
      }
      setPage(1)
      // fetchLastestPost()
    }} style={[styles.rdDanhmuc, categoriesID === item.id ? styles.cateSelected : { }]}>
      <FastImage
        resizeMode={'cover'}
        // style={styles.newsPicture}
        style={{ width: 63, height: 63 }}
        source={{ uri: `${item.attributes.image.data.attributes.url}` }} />
      <Text style={[styles.rdDanhmucText, { fontWeight: categoriesID === item.id ? 'bold' : 'normal' }]}>{item.attributes.name}</Text>
    </TouchableOpacity>
  }

  const renderFooter = useMemo(() => {
    __DEV__ && console.log('renderFooter')
    const Spinner = require('react-native-spinkit')
    // if (isFetched) {
    //   return <PlaceHolder/>
    // }
    if (!insuranceStore.dataPosts.length) { return <EmptyData style={{ height: 200 }} title={t('Chưa có dịch vụ cung cấp')} message={t('Quay trở lại sau')}/> }
    return loadMore === true && page !== insuranceStore.totalPage ? (
      <View
        style={{
          marginTop: 10,
          alignItems: 'center'
        }}
      >
        <Spinner isVisible={true} size={40} type='ThreeBounce' color={color.primary}/>
      </View>
    ) : null
  }, [insuranceStore.dataPosts?.length, loadMore, page])

  const renderHeader = () => {
    return (
      <View
        style={{
          marginTop: 10,
          marginHorizontal: 10
        }}
      >
        <StyledView className="flex flex-row justify-between">
          <Text style={{ color: '#9D9D9D', marginLeft: 0, marginVertical: 10, fontSize: 16 }}>Chọn hãng bảo hiểm</Text>
          { cateText
            ? <TouchableOpacity onPress={() => {
              setCateText('')
              setCategoriesID(0)
            }}>
              <StyledView className="flex flex-row items-center">
                <Text style={{ color: '#9D9D9D', marginLeft: 0, marginVertical: 10, fontSize: 16 }}>Bỏ lọc </Text>
                <Icon color={color.primary} name={'close-circle-outline'} size={20}/></StyledView>
            </TouchableOpacity> : null}
        </StyledView>

        <FlatList
          horizontal={true}
          // ItemSeparatorComponent={renderSeparator}
          nestedScrollEnabled={true}
          data={insuranceStore.dataPostCategories}
          keyExtractor={(item, index) => index + 'category'}
          // contentContainerStyle={{ marginLeft: 0 }}
          showsHorizontalScrollIndicator={false}
          onEndReachedThreshold={0.1}
          style={{ width: responsiveWidth(100) - 40 }}
          renderItem={renderCategories}/>

        <View style={{ width: '100%', height: 3, marginVertical: 10, backgroundColor: '#f3f3f3' }} />
        <Text style={{ color: '#9D9D9D', marginVertical: 10, fontSize: 16 }}>{ insuranceStore.dataPosts?.length ? 'Chọn dịch vụ bảo hiểm' : 'Chưa có dịch vụ' }</Text>
      </View>
    )
  }

  return (
    <View style={styles.flatListContainer}>
      {/* {renderHeader()} */}
      <View style={{ alignItems: 'center', flex: 1 }}>
        <FlatList
          contentContainerStyle={{ backgroundColor: '#fff', paddingBottom: 60 + useSafeAreaInsets().bottom }}
          data={insuranceStore.dataPosts}
          numColumns={2}
          initialNumToRender={10}
          refreshing={refreshing}
          onRefresh={onRefresh}
          renderItem={renderProduct}
          // columnWrapperStyle={{ flexShrink: 1 }}
          keyExtractor={item => item.id + 1}
          extraData={insuranceStore.dataPosts}
          showsVerticalScrollIndicator={false}
          onScrollBeginDrag={e => {
            __DEV__ && console.log('onScrollBeginDrag')
            // show icon loading bottom
            onScroll(e)
            // setLoadMore(true)
            // if (page === profileStore.totalPage) {
            //   __DEV__ && console.log('No more data...')
            //   setLoadMore(false)
            // }
          }}
          style={{ flex: 1 }}
          onScrollEndDrag={onScroll}
          onMomentumScrollEnd={handleLoadMore}
          ListFooterComponent={renderFooter}
          ListHeaderComponent={renderHeader}
        />
      </View>
    </View>
  )
})

const styles = StyleSheet.create({
  cateSelected: {
    borderColor: color.primary,
    borderWidth: 1
  },
  discountViewRow: {
    bottom: 2,
    flexDirection: 'row',
    left: 10,
    position: 'absolute'
  },
  flatListContainer: {
    flex: 1,
    height: responsiveHeight(100),
    // ...ifIphoneX({
    //   paddingBottom: 89,
    // }, {
    //   paddingBottom: 60,
    // }),
    // width: responsiveWidth(100),
    backgroundColor: '#fff'
  },
  rdDanhmuc: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderColor: '#DADADA',
    borderRadius: 4,
    borderWidth: 1,
    height: 108,
    justifyContent: 'center',
    marginRight: 10,
    marginVertical: 8,
    width: 80
  },
  rdDanhmucText: {
    fontFamily: typography.normal,
    fontSize: 15,
    paddingVertical: 8,
  },
  renderProduct: {
    borderRadius: 4,
    // height: 360,
    marginBottom: 10,
    // marginLeft: 16,
    width: (Dimensions.get('window').width / 2) - 20,
    marginHorizontal: 5,
    backgroundColor: '#F7F7F7',
  },
  rspTopViewText: {
    color: '#333',
    fontSize: 14,
    fontWeight: '400',
    height: 35,
    marginLeft: 8,
    marginVertical: 10,
    textAlign: 'left',
    width: '90%'
  },
  rspTopViewTextPrice: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 5,
    textAlign: 'left'
  },
  saleIcon: {
    height: 15,
    width: 15 * saleIconRatio
  },
  textBtnAdd: {
    alignItems: 'center',
    borderColor: color.primary,
    borderRadius: 3,
    borderWidth: 1,
    color: color.primary,
    justifyContent: 'center',
    margin: 6,
    marginBottom: 8,
    padding: 8,
    textAlign: 'center'
  },
  textOldPrice: {
    color: '#9D9D9D',
    fontFamily: typography.normal,
    fontSize: 10,
    textDecorationLine: 'line-through'
  },
  textSaleOff: {
    color: color.primary,
    fontFamily: typography.normal,
    fontSize: 10,
    marginLeft: 8
  },
  viewImage: {
    borderRadius: 2,
    height: 170,
    padding: 4,
    resizeMode: 'cover',
    width: '100%'
  }
})
