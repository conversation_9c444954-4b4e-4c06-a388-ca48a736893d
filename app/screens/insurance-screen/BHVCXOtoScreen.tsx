import React, { Fragment, useCallback, useContext, useEffect, useRef, useState } from 'react'
import {
  Alert,
  Dimensions,
  FlatList,
  Image, Platform,
  StyleSheet,
  Text, TouchableOpacity, View
} from 'react-native'
import { observer } from 'mobx-react-lite'
import { ButtonBack, TButton, BottomSheetPicker, TTextInput, useLoading } from '@app/components'
import Icon from 'react-native-vector-icons/Ionicons'
import { useTranslation } from 'react-i18next'
import { ModalContext } from '@app/context'
import { useStores } from '@app/models'
import { StackActions, useNavigation, useRoute } from '@react-navigation/native'
import {
  icCheckSquare
} from '@app/assets/images'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { CheckB<PERSON>, Header, Slider } from 'react-native-elements'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import LinearGradient from 'react-native-linear-gradient'
import { SafeAreaView } from 'react-native-safe-area-context'
import { color, typography } from '@app/theme'
import { Capitalize, formatMoney, getPhoneOnly, noWhitespace, numberFormat } from '@app/utils'
import DateTimePickerModal from 'react-native-modal-datetime-picker'
import moment from 'moment-timezone'
import { TermContent } from './Components/TermContent'
import { SCREENS } from '@app/navigation'
import FastImage from 'react-native-fast-image'
import validate from 'validate.js'
import SimpleToast from 'react-native-simple-toast'
import { responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import useDeepCompareEffect from 'use-deep-compare-effect'
import ImagePicker from 'react-native-image-crop-picker'
import { Payment } from '@app/screens'
import _ from 'lodash'
import CurrencyInput from 'react-native-currency-input'
import { Api } from '@app/services/api'
import RenderHtml from 'react-native-render-html'
import { InAppBrowser } from 'react-native-inappbrowser-reborn'
import MonthPicker from 'react-native-month-year-picker'
import { Modalize } from 'react-native-modalize'

const lastYears = (back) => {
  const year = new Date().getFullYear()
  return _.orderBy(Array.from({ length: back }, (v, i) => year - back + i + 1).map(i => {
    return { label: i, value: i }
  }), ['value'], ['desc'])
}

const { width } = Dimensions.get('window')

export const BHVCXOtoScreen = observer((props: any, ref) => {
  const route = useRoute()
  const { id, isRenew, isViewDetail, dataRenew, prodId }: any = route?.params
  const { navigate, goBack } = useNavigation()
  const { t } = useTranslation()
  const { showError, showSuccess, showCustomSuccess, showModal } = useContext(ModalContext)
  const { profileStore, carStore, insuranceStore } = useStores()
  const { show, hide } = useLoading()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const bottomSheetRef = useRef<Modalize>(null)

  const [step, setStep] = useState(1)
  const [pickerDateVisible, setPickerDateVisible] = useState(false)
  const [date, setDate] = useState(new Date().getTime())

  const [namSX, setNamSX] = useState(new Date())
  const [showNamSX, setShowNamSX] = useState(false)

  const [item, setItem] = useState(null)

  const showPicker = useCallback((value) => setShowNamSX(value), [])

  const onValueChange = useCallback(
    (event, newDate) => {
      const selectedDate = newDate || date
      onChangeText('NamSD', moment(selectedDate).format('MM/YYYY'))
      onChangeText('NamSX', moment(selectedDate).format('MM/YYYY'))
      showPicker(false)
      setNamSX(selectedDate)
    },
    [date, showPicker],
  )

  const tinhThanhRef = useRef(null)
  const hangXeRef = useRef(null)
  const dongXeRef = useRef(null)
  const loaiXeRef = useRef(null)
  const mucDichSuDungRef = useRef(null)
  const namSdRef = useRef(null)
  const thangSdRef = useRef(null)
  const soChoNgoiRef = useRef(null)
  const thoiHanRef = useRef(null)
  const soNguoiThamGiaRef = useRef(null)
  const giaTriBHRef = useRef(null)
  const soTienBhLaiPhuRef = useRef(null)

  const [mucDichSuDung, setMucDichSuDung] = useState(null)
  const [hangXe, setHangXe] = useState(null)
  const [dongXe, setDongXe] = useState(null)
  const [loaiHinhXe, setLoaiHinhXe] = useState(null)
  const [loaiXe, setLoaiXe] = useState(null)
  const [khongCoBienSo, setKhongCoBienSo] = useState(false)

  const [localPhotos, setLocalPhotos] = useState([])
  const [flag, setFlag] = useState(false)
  const [arrMoRong, setArrMoRong] = useState([])

  // const [value, setValue] = useState<any>(0)
  const [giaTriXe, setGiaTriXe] = useState<any>(0)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState([true, false])
  const [bankCode, setBankCode] = useState('')
  const [bottomSheetType, setSetBottomSheetType] = useState(0) // 1 bank, 2 ảnh hướng dẫn GDDK
  const [guideUploadImageHtml, setGuideUploadImageHtml] = useState()
  const [paymentGuideHtml, setPaymentGuideHtml] = useState()
  const navigation = useNavigation()

  const currentHour = () => {
    return moment(new Date()).format('hh:mm')
  }

  useEffect(() => {
    const getPhamViMoRong = async () => {
      const rs = await insuranceStore.getDanhMuc('PHAMVIMORONG')
      setArrMoRong(rs)
    }
    getPhamViMoRong()

    return () => {

    }
  }, [insuranceStore.catNameSelected])

  useEffect(() => {
    if (isRenew === true) {
      setStep(2)
      const images = dataRenew.data_gddk.map((item) => item.file_size)
      setLocalPhotos(images)
      const year = moment(dataRenew.NgayCuoi, 'DD/MM/YYYY').diff(moment(dataRenew.NgayDau, 'DD/MM/YYYY'), 'years')
      __DEV__ && console.log('diff Year', year)
      const body = {
        ...dataRenew,
        NgayDau: dataRenew.NgayCuoi,
        NgayCuoi: moment(dataRenew.NgayCuoi, 'DD/MM/YYYY').add(year, 'y').format('DD/MM/YYYY')
      }
      setObjValue(body)
    }
    return () => {
    }
  }, [isRenew])

  useEffect(() => {
    if (isViewDetail === true) {
      setStep(3)
      const images = dataRenew.data_gddk.map((item) => item.file_size)
      setLocalPhotos(images)
      const body = {
        ...dataRenew
      }
      setObjValue(body)
    }
    return () => {
    }
  }, [isViewDetail])

  const [objValue, setObjValue] = useState({
    TenKH: '', // TODO: remove ''
    DiaChiKH: '',
    TenChuXe: '', // TODO: remove ''
    DiaChiChuXe: '', // TODO: remove ''
    NgayDau: '',
    NgayCuoi: '',
    ThamGiaLaiPhu: false,
    ThamGiaVCX: true,
    EmailKH: '',
    TrongTai: '99999',
    MTNLaiPhu: '0',
    SoNguoiToiDa: '0',
    PhiBHTNDSBB: '0',
    PhiBHLaiPhu: '0',
    // MayKeo: false,
    // XeChuyenDung: false,
    // XeChoTien: false,
    // XePickUp: false,
    // XeTaiVan: false,
    // XeTapLai: false,
    // XeBus: false,
    // XeCuuThuong: false,
    // Xetaxi: false,
    // XeDauKeo: false,
    ThangSD: '',
    NamSD: '',
    AnBKS: false,
    BienKiemSoat: '', // TODO: remove ''
    HieuXe: '',
    DongXe: '',
    NamSX: '',
    DienThoai: '',
    SoKhung: '', // TODO: remove ''
    SoMay: '', // TODO: remove ''
    AnPhi: false,
    GioDau: currentHour(),
    GioCuoi: currentHour(),
    KhuyenMai: '',
    TenTH: '',
    DiaChiTH: '',
    ThamGiaHang: false,
    thamgia_hang: false,
    LoaiDon: '01',
    LoaiXe: '', // chưa có data cần phân tích lại, tương ứng với ma_loaixe khi tính tiền
    ChoNgoi: '',
    MTNHangHoa: '0',
    SoTan: '0',
    GioiHanMTN: '0',
    MTNTNDSNguoi: '0',
    MTNTNDSTaiSan: '0',
    GiaTriBH: '0', // mặc định truyền 500k, KH yêu cầu cần dropdown chọn giá trị
    GiaTriPK: '0',
    TyLePhiVCX: '0',
    PhiBHVatChat: '',
    MucKT: 'MKT005',
    DKBS002: false,
    PhiBHHangHoa: '0',
    MaMucDichSD: '0',
    DongBH: '0',
    PhanBoDT: false,
    KhachVip: false,
    NgayTaiTuc: '',
    TinhTrangXe: '02',
    TinhTrangTHBH: '',
    tick_loaixe: null,
    ToChuc: 'CN',
    chkKDVT: true,
    SoTienTH: '0',
    strMaDKBS: '',
    ThamGiaDKBS: false,
    NgayDangKy: '',
    GiaTriXe: '',
    GhiChuPK: '',
    TongPhi: '',
    ListDKBSKhac: null,
    khach_hang: false,
    data_gddk: [],
    TenCongTy: '',
    MaSoThue: '',
    DiaChiCongTy: '',
    MaGioiThieu: '',
    CheckXuatHoaDon: false,
    TinhThanh: '',
    MucDichSuDung: '',
    CheckXeMoiHoacTaiTuc: false,
    CheckBHBatBuocTNDSCuaChuXe: false,
    HangSanXuat: '',
    ThoiHan: '',
    DongXeText: '',
    ma_mdsd: '1'
  })

  useEffect(() => {
    insuranceStore.clearArr()
    getApiData()
    onChangeNgayBatDau(new Date())
    // if (!__DEV__) {
    //   // reset
    //   setStep(1)
    //   setBankCode('')
    // }
  }, [])

  useDeepCompareEffect(() => {
    __DEV__ && console.log('objValue', objValue)
    const calculate = async () => {
      if (validateStep1()) {
        // goi tinh toán số tiền
        const body: any = {
          ma_donvi: '03',
          ma_trongtai: '0',
          thamgia_vcx: true,
          thamgia_laiphu: objValue.ThamGiaLaiPhu,
          thamgia_hang: objValue.ThamGiaHang,
          strdkbs: objValue.strMaDKBS,
          so_cho: objValue.ChoNgoi,
          so_nguoi: objValue.SoNguoiToiDa,
          nhan_hieu: objValue.HangSanXuat,
          ma_loaixe: objValue.LoaiXe,
          mtn_laiphu: objValue.MTNLaiPhu,
          ...objValue,
          LoaiDon: objValue.CheckXeMoiHoacTaiTuc ? '02' : '01'
        }
        const rs = await insuranceStore.tinhPhiVCXOto(body)
        if (rs && (rs?.Status == '00' || rs?.data?.Status == '00')) {
        } else if (__DEV__) {
          // showError(t('FAIL'), t(`${rs?.data?.Status} - ${rs?.data?.Message}`))
        }
      } else {
        insuranceStore.resetTotalFreeTNDSOto()
      }
    }
    calculate()
  }, [objValue])

  useEffect(() => {
    if (insuranceStore.paymentStatus) {
      // gửi đơn hàng sau khi thanh toán thành công và lưu vào db
      createOrderPVI()

      insuranceStore.clearArr()
      // reset
      setStep(1)
      setBankCode('')
    }

    return () => {

    }
  }, [insuranceStore.paymentStatus])

  useEffect(() => {
    if (insuranceStore.paymentCancel) {
      insuranceStore.setPaymentCancel(false)
      // gửi đơn hàng sau khi thanh toán thành công và lưu vào db
      createOrderFail()
    }

    return () => {

    }
  }, [insuranceStore.paymentCancel])

  const getApiData = async () => {
    const rs = await insuranceStore.getInsurancePostDetail(prodId)
    if (rs?.data?.data) {
      __DEV__ && console.log('insuranceStore.getInsurancePostDetail(prodId)', rs)
      setItem(rs?.data?.data)
      insuranceStore.setCatSelected(rs?.data?.data)
    }
    return Promise.all([
      insuranceStore.getCities(),
      insuranceStore.getMucDichSuDung('MDSD_VCXAUTO'),
      insuranceStore.getHangXe(),
      insuranceStore.getDongXe(''),
      getApiConfig(),
    ])
  }

  useEffect(() => {
    const delayDebounce = setTimeout(() => {
      if (giaTriXe) {
        onChangeText('GiaTriXe', giaTriXe)
        onChangeText('GiaTriBH', giaTriXe)
      }
    }, 1000)
    return () => {
      clearTimeout(delayDebounce)
    }
  }, [giaTriXe])

  const getApiConfig = async () => {
    const api = new Api()
    const rs = await api.getAppConfig()
    if (rs && rs?.data?.data.attributes) {
      setGuideUploadImageHtml(rs?.data?.data?.attributes?.content_guide_upload_image_bhvcxauto)
      setPaymentGuideHtml(rs?.data?.data?.attributes?.content_guide_payment)
    }
  }

  const onChangeText = (field, value) => {
    // field = handlerChangeValue(field)
    setObjValue((prev) => ({ ...prev, [field]: value }))
  }

  const validateStep1 = () => {
    if (objValue.ThamGiaLaiPhu) {
      return !validate.isEmpty(objValue.MucDichSuDung) &&
        !validate.isEmpty(objValue.NgayDau) &&
        !validate.isEmpty(objValue.NgayCuoi) &&
        !validate.isEmpty(objValue.ChoNgoi) &&
        !validate.isEmpty(objValue.NamSD) &&
        !validate.isEmpty(objValue.GiaTriBH) &&
        (objValue.GiaTriBH != '0' && parseInt(objValue.GiaTriBH) > 0) &&
        !validate.isEmpty(objValue.GiaTriXe) &&
        (objValue.GiaTriXe != '0' && parseInt(objValue.GiaTriXe) > 0) &&
        !validate.isEmpty(objValue.SoNguoiToiDa) &&
        !validate.isEmpty(objValue.MTNLaiPhu) &&
        validateDongXe()
    } else {
      return !validate.isEmpty(objValue.MucDichSuDung) &&
        !validate.isEmpty(objValue.NgayDau) &&
        !validate.isEmpty(objValue.NgayCuoi) &&
        !validate.isEmpty(objValue.ChoNgoi) &&
        !validate.isEmpty(objValue.NamSD) &&
        !validate.isEmpty(objValue.GiaTriBH) &&
        (objValue.GiaTriBH != '0' && parseInt(objValue.GiaTriBH) > 0) &&
        !validate.isEmpty(objValue.GiaTriXe) &&
        (objValue.GiaTriXe != '0' && parseInt(objValue.GiaTriXe) > 0) &&
        validateDongXe()
    }
  }

  const validateStep2 = () => {
    if (!khongCoBienSo) {
      return !validate.isEmpty(objValue.TinhThanh) &&
        !validate.isEmpty(objValue.TenChuXe) &&
        !validate.isEmpty(objValue.BienKiemSoat) &&
        !validate.isEmpty(objValue.DiaChiChuXe) &&
        validateVat() &&
        validateImages()
    } else {
      return !validate.isEmpty(objValue.TinhThanh) &&
        !validate.isEmpty(objValue.TenChuXe) &&
        !validate.isEmpty(objValue.DiaChiChuXe) &&
        !validate.isEmpty(objValue.SoKhung) &&
        !validate.isEmpty(objValue.SoMay) &&
        validateVat() &&
        validateImages()
    }
  }

  const validateVat = () => {
    if (objValue.CheckXuatHoaDon) {
      return !validate.isEmpty(objValue.TenCongTy) &&
        !validate.isEmpty(objValue.DiaChiCongTy) &&
        !validate.isEmpty(objValue.MaSoThue)
    } else {
      return true
    }
  }

  const validateImages = () => {
    if (objValue.CheckXeMoiHoacTaiTuc) {
      return localPhotos?.length && localPhotos?.length >= 1
    } else {
      return localPhotos?.length && localPhotos?.length >= 1
    }
  }

  const validateDongXe = () => {
    if (insuranceStore.catNameSelected == 'PVI') {
      return !validate.isEmpty(objValue?.DongXe)
    } else {
      return !validate.isEmpty(objValue?.DongXeText)
    }
  }

  const onSelectedImages = (images: any) => {
    const base64Array = [...localPhotos]
    const files = []
    images.forEach(async image => {
      const path = Platform.OS === 'android' ? image.path : image.path.replace('file://', '')
      files.push([
        {
          uri: path,
          name: image.filename + '.png' || Math.floor(Math.random() * Math.floor(999999999)) + '.jpg',
          type: image.mime
        }
      ])
      // const rs = homeStore.uploadImageOfTopic(`18/topics/${homeStore.topicId}/photos`, files)
      setFlag(true)
      if (base64Array.length <= 10) {
        base64Array.push(image.data)
      }
    })

    setLocalPhotos(base64Array)
  }

  const onViewOnline = async () => {
    show()
    if (insuranceStore.catNameSelected == 'BIC') {
      const rs = await insuranceStore.getUrlPvi(dataRenew?.Pr_key + '', 'vcx')
      if (rs?.data) {
        openLink(rs?.data?.URL)
      }
    } else {
      const rs = await insuranceStore.getUrlPvi((dataRenew?.paymentId || dataRenew?.ma_giaodich) + '', 'vcx')
      if (rs?.data) {
        openLink(rs?.data?.URL)
      }
    }
    hide()
  }

  const onActionSelectPhotoDone = index => {
    switch (index) {
      case 0:
        ImagePicker.openCamera({
          writeTempFile: true,
          compressImageMaxWidth: 1024,
          compressImageQuality: 0.5,
          includeBase64: true
        }).then(image => {
          // __DEV__ && console.warn(image)
          onSelectedImages([image])
        })
        break
      case 1:
        ImagePicker.openPicker({
          writeTempFile: true,
          multiple: true,
          maxFiles: 10,
          mediaType: 'photo',
          compressImageMaxWidth: 1024,
          compressImageQuality: 0.5,
          includeBase64: true
        })
          .then(images => {
            onSelectedImages(images)
          }
          )
          .catch(e => {
            __DEV__ && console.log('error:', e)
            // setLocalPhotos([])
            // onChangeText('data_gddk', [])
          })
        break
      default:
        break
    }
  }

  const createOrderInsuranceHistory = async (data) => {
    const bodyHistory: any = {
      data: {
        TenCongTy: '',
        DiaChiCongTy: '',
        MaSoThue: '',
        MaGioiThieu: '',
        user_id: profileStore._id,
        MucDichSD: mucDichSuDung?.value || '',
        PaymentId: data.ma_giaodich,
        ProductId: item?.id + '',
        name: item?.attributes?.name,
        hangXe: hangXe?.value,
        dongXe: dongXe?.value || '',
        loai_bao_hiem: item?.attributes?.loai_bao_hiems?.data?.length > 0 ? item?.attributes?.loai_bao_hiems?.data[0].id : 1,
        ...data,
        type: 'BẢO HIỂM VẬT CHẤT',
        dataRenew: { ...data, paymentId: data?.ma_giaodich },
        data_gddk: [] // remove data
      }
    }
    delete bodyHistory.data.data_gddk
    const rs = await insuranceStore.createOrderInsuranceHistory(bodyHistory)
    return rs
  }

  const createOrderPVI = async () => {
    let body = {
      ...objValue,
      NamSD: objValue.NamSD,
      ma_giaodich: insuranceStore.paymentId,
      TongPhi: `${insuranceStore.rsTinhPhiTNDSOto?.TotalFee}`,
      PhiBHVatChat: `${insuranceStore.rsTinhPhiTNDSOto?.TotalFee}`,
      ma_loaixe_txt: loaiXe?.label || '',
      type: 'vcx'
      // TyLePhiVCX: insuranceStore.rsTinhPhiTNDSOto?.tylephi_chuanvcx
    }

    if (insuranceStore.catNameSelected == 'BIC') {
      body = {
        ...body,
        ...insuranceStore.rsTinhPhiTNDSOto,
        tylephi: insuranceStore.rsTinhPhiTNDSOto?.data?.liability_insurance.phi_toithieu_rate,
        tylephi_baohiemtainan: insuranceStore.rsTinhPhiTNDSOto?.data?.accident_insurance.fee_rate,
        phi_baohiemvatchat: insuranceStore.rsTinhPhiTNDSOto?.data?.liability_insurance.phi_goc,
      }
    }
    const rsCreate = await insuranceStore.taoDonTNDSOto(body)
    __DEV__ && console.log('rsCreate', rsCreate)
    // navigate(SCREENS.payment, { vnpUrl: rs.data.data.vnpUrl, orderId: productStore.orderId, paymentId: rs.data.data.paymentId, typeBooking: 0 })
    if (rsCreate && (rsCreate?.Status == '00' || rsCreate?.data?.Status == '00')) {
      createOrderInsuranceHistory({ ...body, Pr_key: rsCreate?.Pr_key || '' + '' })
      // showSuccess(t('THANHCONG'), t('Thành công'))
      // setTimeout(() => onGoBack(), 500)
    } else {
      showError(t('FAIL'), t(`ID: - ${body?.ma_giaodich} ${rsCreate?.Message}`))
      createOrderFail()
    }
  }

  const createOrderFail = async () => {
    const body = {
      ...objValue,
      NamSD: objValue.NamSD,
      ma_giaodich: 'CANCEL',
      TongPhi: `${renderDiscount()}`,
      PhiBHVatChat: `${renderDiscount()}`,
      // TyLePhiVCX: insuranceStore.rsTinhPhiTNDSOto?.tylephi_chuanvcx
    }
    createOrderInsuranceHistory(body)
    insuranceStore.clearArr()
  }

  const openBottomSheet = (type) => {
    setSetBottomSheetType(type)
    bottomSheetRef?.current.open()
  }

  const closeBottomSheet = () => {
    setSetBottomSheetType(0)
    bottomSheetRef?.current.close()
  }

  const onSubmit = async () => {
    if (step === 1) {
      // load default data
      onChangeText('EmailKH', profileStore.email)
      onChangeText('DienThoai', getPhoneOnly(profileStore.phoneNumber || profileStore.phone))
      setStep(2)
    }
    if (step === 2) {
      const arrImages = localPhotos.map((image) => {
        return {
          vi_do_chup: '1',
          kinh_do_chup: '1',
          file_size: image,
          file_extension: '.jpg'
        }
      })
      onChangeText('data_gddk', arrImages)
      setStep(3)
    }

    if (step === 3) {
      setIsSubmitting(true)
      if (!validateStep1() || !validateStep2()) {
        showError(t('FAIL'), t('FILL_OUT_THE_FORM'))
        setIsSubmitting(false)
      }

      if (objValue.CheckXeMoiHoacTaiTuc) {
        if (!bankCode) {
          // open chọn bank nếu chưa chọn
          openBottomSheet(1)
        } else {
          const body = {
            ...objValue
          }
          __DEV__ && console.log('body', body)
          // createOrderPVI()
          const rsCreate = await insuranceStore.createUrlPayment({ totalAmount: renderDiscount() || 0, bankCode: bankCode })

          if (rsCreate && rsCreate.data?.data?.vnpUrl) {
            const { vnpUrl, paymentId } = rsCreate.data?.data
            navigate(SCREENS.payment, {
              vnpUrl: vnpUrl,
              orderId: paymentId,
              paymentId: paymentId,
              typeBooking: 6,
              title: 'Thanh toán bảo hiểm vật chất'
            })
            // setTimeout(() => onGoBack(), 500)
          } else {
            showError(t('FAIL'), t(`${rsCreate.data?.error}`))
            setIsSubmitting(false)
          }
        }
        setIsSubmitting(false)
      } else {
        let body: any = {
          ...objValue,
          NamSD: objValue.NamSD,
          ma_giaodich: insuranceStore.paymentId,
          TongPhi: `${insuranceStore.rsTinhPhiTNDSOto?.TotalFee}`,
          PhiBHVatChat: `${insuranceStore.rsTinhPhiTNDSOto?.TotalFee}`,
          ma_loaixe_txt: loaiXe?.label || '',
        }

        if (insuranceStore.catNameSelected == 'BIC') {
          body = {
            ...body,
            tylephi: insuranceStore.rsTinhPhiTNDSOto?.data?.liability_insurance.phi_toithieu_rate,
            tylephi_baohiemtainan: insuranceStore.rsTinhPhiTNDSOto?.data?.accident_insurance.fee_rate,
            phi_baohiemvatchat: insuranceStore.rsTinhPhiTNDSOto?.data?.liability_insurance.phi_goc,
          }
        }

        __DEV__ && console.log('data hoàn thành', body)
        await createOrderInsuranceHistory(body)
        navigation.dispatch(StackActions.popToTop())
        setIsSubmitting(false)
        showModal(t('Đăng ký thành công '), t('Đại lý Bảo hiểm sẽ liên hệ với quý khách để đánh giá tình trạng xe trước khi chấp thuận bảo hiểm.'), 'success')
      }
    }
  }

  const onSelectBank = (item) => {
    setBankCode(item)
    closeBottomSheet()
  }

  const hidePicker = () => {
    setPickerDateVisible(false)
  }

  const handlerChonThoiHan = (item) => {
    const { value } = item
    if (!date) {
      showError(t('FAIL'), t('Bạn cần chọn ngày bắt đầu trước'))
      onChangeText('ThoiHan', '')
    } else if (value) {
      onChangeText('ThoiHan', `${value / 12}`)
      const endDate = moment(date).add(parseInt(value), 'M').format('DD/MM/YYYY')
      __DEV__ && console.log('endDate', endDate)
      onChangeText('NgayCuoi', endDate)
    }
  }

  const onGoBack = () => {
    if (!isRenew) {
      if (step === 1) {
        goBack()
      } else if (step === 2) {
        setStep(1)
      } else if (step === 3) {
        setStep(2)
      }
    } else {
      goBack()
    }
  }

  const onSelectPaymentMethod = (index) => {
    if (index === 0) {
      setSelectedPaymentMethod([true, false])
    }
    if (index === 1) {
      setBankCode('')
      setSelectedPaymentMethod([false, true])
    }
  }

  // const renderItemLoaiXe = ({ item, index }) => {
  //   return <CheckBox
  //     // iconType='ionicon'
  //     title={item.label}
  //     checkedIcon='check-square'
  //     uncheckedIcon='square-o'
  //     checkedColor={color.primary}
  //     checked={insuranceStore.arrLoaiHinh.find(e => e.value === item.value)?.checked || false}
  //     onPress={() => {
  //       const arr = [...insuranceStore.arrLoaiHinh]
  //       const checked = !arr.find(e => e.value === item.value)?.checked
  //       arr.forEach((e, index) => {
  //         arr[index].checked = false
  //         onChangeText(e.value, false)
  //       })
  //       __DEV__ && console.warn(item)
  //       __DEV__ && console.warn(arr)
  //       arr.find(e => e.value === item.value).checked = checked
  //       insuranceStore.setArrLoaiHinh(arr)
  //       // TODO: map với key của object
  //       onChangeText(item.value, checked)
  //       setLoaiHinhXe(item.name)
  //     }}
  //     textStyle={{ fontWeight: 'normal' }}
  //     containerStyle={styles.checkBox}
  //     wrapperStyle={{ marginVertical: 5, minWidth: responsiveWidth(50) - 20 }}
  //     size={20}
  //   />
  // }

  const renderItemMoRong = ({ item, index }) => {
    return <CheckBox
      // iconType='ionicon'
      title={item.label}
      checkedIcon='check-square'
      uncheckedIcon='square-o'
      checkedColor={color.primary}
      checked={arrMoRong.find(e => e.value === item.value)?.checked || false}
      onPress={() => {
        const arr = [...arrMoRong]
        const checked = !arr.find(e => e.value === item.value)?.checked
        arr.find(e => e.value === item.value).checked = checked
        setArrMoRong(arr)
        onChangeText('strMaDKBS', arrMoRong.filter(e => e.checked).map(e => e.value).join(','))
      }}
      textStyle={{ fontWeight: 'normal' }}
      containerStyle={styles.checkBox}
      wrapperStyle={{ marginVertical: 5, minWidth: responsiveWidth(50) - 20 }}
      size={20}
    />
  }

  const onChangeNgayBatDau = (date) => {
    const chooseDate = moment(new Date(date)).set({ hour: 0, minute: 0, second: 0, millisecond: 0 }) // choose date yyyy-mm-dd only
    const dateNow = moment(new Date()).set({ hour: 0, minute: 0, second: 0, millisecond: 0 })

    if (chooseDate.isSameOrAfter(dateNow)) {
      setDate(new Date(date).getTime())
      onChangeText('NgayDau', moment(date).format('DD/MM/YYYY'))
      hidePicker()
    } else {
      // showError(t('FAIL'), t('Bạn không thể chọn ngày chưa tới'))
      SimpleToast.show('Bạn không thể chọn ngày đã qua')
      setDate(null)
      onChangeText('NgayDau', '')
      return Platform.OS === 'android' ? setPickerDateVisible(false) : null
    }
  }

  useEffect(() => {
    __DEV__ && console.log('khongCoBienSo', khongCoBienSo)
    if (khongCoBienSo) {
      onChangeText('BienKiemSoat', '')
    }
    return () => {

    }
  }, [khongCoBienSo])

  const onPressField = (action) => {
    if (action === 'NgayDau') {
      setPickerDateVisible(true)
    }
    if (action === 'TinhThanh') {
      tinhThanhRef?.current.open()
    }
    if (action === 'MucDichSuDung') {
      mucDichSuDungRef?.current.open()
    }
    if (action === 'HangSanXuat') {
      hangXeRef?.current.open()
    }
    if (action === 'DongXe') {
      dongXeRef?.current.open()
    }
    if (action === 'NamSD') {
      namSdRef?.current.open()
    }
    if (action === 'ThangSD') {
      thangSdRef?.current.open()
    }
    if (action === 'ThoiHan') {
      thoiHanRef?.current.open()
    }
    if (action === 'ChoNgoi') {
      soChoNgoiRef?.current.open()
    }
    if (action === 'GiaTriBH') {
      giaTriBHRef?.current.open()
    }
    if (action === 'SoTienBHNguoiThamGia') {
      soTienBhLaiPhuRef?.current.open()
    }
    if (action === 'LoaiXe') {
      loaiXeRef?.current.open()
    }
    if (action === 'ThangNamSX') {
      showPicker(true)
    }
  }

  const TouchField = (title, placeholder, action, leftIc, value, money = false) => {
    return (
      <View style={{ flex: 1 }}>
        <Text style={styles.titleLabel}>{title}</Text>
        <TouchableOpacity onPress={() => onPressField(action)} style={styles.viewProvince}>
          <View style={{ flexDirection: 'row' }}>
            {leftIc && iconCalender}
            {!value ? <Text style={styles.textProvince}>{placeholder}</Text>
              : <Text style={[styles.textProvince, { color: '#333' }]}>{money ? formatMoney(value) : value}</Text>}
          </View>
          {iconRight}
        </TouchableOpacity>
      </View>
    )
  }

  const openLink = async (link) => {
    if (link) {
      try {
        await InAppBrowser.close()
        await InAppBrowser.open(link, {
          toolbarColor: color.primary,
          dismissButtonStyle: 'cancel',
          // preferredBarTintColor: color.primary,
          // preferredControlTintColor: '#fff',
          // preferredControlTintColor: 'white',
          readerMode: false,
          animated: true,
          // modalPresentationStyle: 'fullScreen',
          modalTransitionStyle: 'coverVertical',
          modalEnabled: true,
          enableBarCollapsing: false,
        })
      } catch (error) {
        Alert.alert(error.message)
      }
    }
  }

  const renderStep1 = () => {
    return (
      <View>
        <View style={[styles.mainContainer, { paddingHorizontal: 16 }]}>
          {TouchField('Mục đích sử dụng', 'Chọn mục đích sử dụng', 'MucDichSuDung', null, objValue.MucDichSuDung)}
          <View flexDirection='row'>
            <View flex={1}>
              <Text style={styles.titleLabel}>Số chỗ ngồi</Text>
              <TTextInput
                containerStyle={styles.textInputContainer}
                keyboardType='numeric'
                selectTextOnFocus={false}
                placeholderTextColor='#a0a0a0'
                placeholder={'Nhập số chỗ ngồi'}
                value={objValue.ChoNgoi}
                onChangeText={(e) => {
                  onChangeText('ChoNgoi', e)
                  onChangeText('SoNguoiToiDa', e)
                  insuranceStore.getLoaiXe(mucDichSuDung?.value || '', e, objValue.TrongTai, 'vcx')
                }}
              />
            </View>
          </View>
          {mucDichSuDung?.value == '3' ? <>
            <Text style={styles.titleLabel}>Trọng tải (kg)</Text>
            <TTextInput
              containerStyle={styles.textInputContainer}
              keyboardType='numeric'
              selectTextOnFocus={false}
              placeholderTextColor='#a0a0a0'
              placeholder={'Nhập trọng tải xe'}
              value={objValue.TrongTai}
              onChangeText={(e) => {
                onChangeText('TrongTai', e)
                insuranceStore.getLoaiXe(mucDichSuDung?.value || '', objValue.ChoNgoi, e, 'vcx')
              }}
            />
          </> : null}
          {TouchField('Loại xe', 'Chọn loại xe', 'LoaiXe', null, loaiXe?.label)}
          {/* <View style={{ flex: 1, marginTop: mucDichSuDung?.value && insuranceStore.arrLoaiHinh?.length > 0 ? 15 : 0 }}> */}
          {/*  <FlatList */}
          {/*    horizontal={false} */}
          {/*    data={insuranceStore.arrLoaiHinh} */}
          {/*    numColumns={2} */}
          {/*    extraData={insuranceStore.arrLoaiHinh} */}
          {/*    keyExtractor={(item, index) => item.value + index.toString()} */}
          {/*    renderItem={renderItemLoaiXe} */}
          {/*  /> */}
          {/* </View> */}
          { insuranceStore.catNameSelected == 'PVI' ? <View flexDirection='row'>
            <View flex={1} marginRight={12}>
              {TouchField('Hãng sản xuất', 'Chọn hãng SX', 'HangSanXuat', null, hangXe?.label)}
            </View>
            <View flex={1}>
              {TouchField('Dòng xe', 'Chọn dòng xe', 'DongXe', null, dongXe?.label)}
            </View>
          </View> : null}

          { insuranceStore.catNameSelected == 'BIC' ? <View flexDirection='row'>
            <View flex={1} marginRight={12}>
              {TouchField('Hãng sản xuất', 'Chọn hãng SX', 'HangSanXuat', null, hangXe?.label)}
            </View>
            <View flex={1}>
              <Text style={styles.titleLabel}>Dòng xe</Text>
              <TTextInput
                containerStyle={styles.textInputContainer}
                selectTextOnFocus={false}
                placeholderTextColor='#a0a0a0'
                placeholder={'SANTAFE, CX5...'}
                value={objValue.DongXeText}
                onChangeText={(e) => {
                  onChangeText('DongXeText', e)
                }}
              />
            </View>

          </View> : null}

          <View flexDirection='row'>
            {/* <View flex={1} marginRight={12}> */}
            {/*  {TouchField('Tháng sử dụng', 'Chọn tháng SD', 'ThangSD', null, objValue.ThangSD)} */}
            {/* </View> */}
            <View flex={1}>
              {TouchField('Năm/Tháng sản xuất (hoặc đăng ký lần đầu)', 'Chọn năm/tháng SX', 'ThangNamSX', null, objValue.NamSD)}
            </View>
            {/* <View flex={1}> */}
            {/*  {TouchField('Số chỗ ngồi', 'Chọn số chỗ ngồi', 'ChoNgoi', null, objValue.ChoNgoi)} */}
            {/* </View> */}
          </View>

          <View flexDirection='row'>
            <View flex={1} marginRight={12}>
              {TouchField('Ngày bắt đầu', 'Chọn ngày', 'NgayDau', 'ic', objValue.NgayDau)}
            </View>
            <View flex={1}>
              {TouchField('Thời hạn bảo hiểm', 'Chọn thời hạn', 'ThoiHan', null, objValue.ThoiHan)}
            </View>
          </View>
          {/* <View style={{ backgroundColor: '#FFF1F1', padding: 10, marginTop: 20 }}> */}
          {/*  <Text style={styles.txtRisk}>Giá trị xe của bạn hệ thống định giá tự động là <Text */}
          {/*    style={{ fontWeight: 'bold' }}>500.000.000đ</Text> bạn có thể điều chỉnh giá tăng giảm 10% so với giá hệ thống.</Text> */}
          {/* </View> */}
          <View style={{ backgroundColor: '#FFF1F1', padding: 10, marginTop: 20 }}>
            <Text style={styles.txtRisk}>Nhập giá trị xe của bạn vào ô bên dưới, bạn có thể tham khảo tại.</Text>
            <TouchableOpacity onPress={() => openLink('https://bonbanh.com')}><Text style={{ fontWeight: 'bold' }}>https://bonbanh.com</Text></TouchableOpacity>
          </View>
          <View flexDirection='row' alignItems='center' marginTop={10}>
            <View style={styles.rangeSlider}>
              <Slider
                thumbTintColor={color.primary}
                value={giaTriXe}
                onValueChange={setGiaTriXe}
                onSlidingComplete={() => {
                  // onChangeText('GiaTriXe', 500000000 + value)
                  // onChangeText('GiaTriXe', 500000000 + )
                }}
                maximumValue={1000000000}
                minimumValue={1000000}
                step={1000000}
                allowTouchTrack
                minimumTrackTintColor={color.primary}
                thumbStyle={{ height: 20, width: 20 }}
                // thumbProps={}
              />

            </View>
            <View width={width / 3} marginLeft={16}>
              <Text>Giá trị xe (đ)</Text>
              {/* <View style={styles.viewPrice}> */}
              {/*  <Text>{numberFormat(value) }</Text> */}
              {/* </View> */}
              {/* <CurrencyInput */}
              {/*  style={styles.textInput} */}
              {/*  value={value} */}
              {/*  onChangeValue={setValue} */}
              {/*  delimiter=',' */}
              {/*  separator='.' */}
              {/*  precision={0} */}
              {/*  placeholder={'Nhập số tiền'} /> */}
              <CurrencyInput
                style={styles.textInput}
                value={giaTriXe}
                onChangeValue={setGiaTriXe}
                delimiter=','
                separator='.'
                precision={0}
                placeholder={'Nhập giá trị xe'} />
            </View>
          </View>
        </View>
        <View marginTop={20} style={styles.divider} />
        <View style={{ paddingHorizontal: 16 }}>
          <Text style={styles.titleSection}>QUYỀN LỢI NÂNG CAO</Text>
          <Text style={styles.titleSectionDes}>Tùy chọn để bảo vệ toàn diện hơn cho xe của bạn</Text>
          <View style={{ flex: 1, marginTop: 20 }}>
            <FlatList
              horizontal={false}
              data={arrMoRong}
              extraData={arrMoRong}
              keyExtractor={(item, index) => item.value + index.toString()}
              renderItem={renderItemMoRong}
            />
          </View>
        </View>
        {/* {TouchField('Chọn mức khấu trừ', 'Chọn mức', 'GiaTriBH', '', objValue.GiaTriBH, true)} */}
        <View marginTop={20} style={styles.divider} />
        <View style={{ paddingHorizontal: 16 }}>
          <Text style={styles.titleSection}>QUYỀN LỢI KHÁC</Text>
          <Text style={styles.titleSectionDes}>Tùy chọn để tăng quyền lợi bảo vệ toàn diện hơn</Text>
          <CheckBox
            // iconType='ionicon'
            title={'Bảo hiểm tai nạn lái, phụ xe, người ngồi trên xe'}
            checkedIcon='check-square'
            uncheckedIcon='square-o'
            checkedColor={color.primary}
            checked={objValue.ThamGiaLaiPhu}
            onPress={() => {
              onChangeText('ThamGiaLaiPhu', !objValue.ThamGiaLaiPhu)
            }}
            textStyle={styles.checkBoxTxtStyles}
            containerStyle={styles.checkBoxStyles}
            size={20}
          />
          {
            objValue.ThamGiaLaiPhu && <View flexDirection='row'>
              <View flex={1} marginRight={12}>
                <Text style={styles.titleLabel}>Số người tham gia bảo hiểm VNĐ/người/vụ</Text>
                <TTextInput
                  containerStyle={styles.textInputContainer}
                  keyboardType='numeric'
                  selectTextOnFocus={false}
                  placeholderTextColor='#a0a0a0'
                  placeholder={'Số người tham gia'}
                  value={objValue.SoNguoiToiDa}
                  editable={false}
                  onChangeText={(e) => {
                    if (parseInt(e) > parseInt(objValue.ChoNgoi)) {
                      onChangeText('SoNguoiToiDa', '')
                      showError(t('FAIL'), t('Số người tham gia không thể lớn hơn số chỗ trên xe'))
                    } else {
                      onChangeText('SoNguoiToiDa', e)
                    }
                  }}
                />
              </View>
              {TouchField('Số tiền bảo hiểm VNĐ/người/vụ', 'Số tiền', 'SoTienBHNguoiThamGia', '', objValue.MTNLaiPhu, true)}
            </View>
          }
        </View>

        <View marginTop={20} style={styles.divider} />
        <View style={{ paddingHorizontal: 16 }}>
          <TermContent termContent={item?.attributes?.term} />
        </View>
        {/* <CheckBox */}
        {/*  // iconType='ionicon' */}
        {/*  title={'Bảo hiểm bắt buộc TNDS của chủ xe cơ giới'} */}
        {/*  checkedIcon='check-square' */}
        {/*  uncheckedIcon='square-o' */}
        {/*  checkedColor={color.primary} */}
        {/*  checked={objValue.CheckBHBatBuocTNDSCuaChuXe} */}
        {/*  onPress={() => { */}
        {/*    onChangeText('CheckBHBatBuocTNDSCuaChuXe', !objValue.CheckBHBatBuocTNDSCuaChuXe) */}
        {/*  }} */}
        {/*  textStyle={styles.checkBoxTxtStyles} */}
        {/*  containerStyle={styles.checkBoxStyles} */}
        {/*  size={20} */}
        {/* /> */}
        {/* { objValue.CheckBHBatBuocTNDSCuaChuXe && <View marginTop={8}> */}
        {/*  <Text style={styles.subTxt}>Thiệt hại về người: 150.000.000 đ/người/vụ</Text> */}
        {/*  <Text style={styles.subTxt}>Thiệt hại về tài sản: 100.000.000 đ/vụ </Text> */}
        {/* </View> } */}
      </View>
    )
  }

  const renderStep2 = () => {
    return (
      <View style={[styles.mainContainer, { paddingHorizontal: 16 }]}>
        <>
          {TouchField('Tỉnh / TP', 'Chọn tỉnh/TP', 'TinhThanh', null, objValue.TinhThanh)}
          <Text style={styles.titleLabel}>Tên chủ xe (theo đăng ký xe)</Text>
          <TTextInput
            containerStyle={styles.textInputContainer}
            selectTextOnFocus={false}
            placeholderTextColor='#a0a0a0'
            placeholder={'Nhập tên chủ xe'}
            value={objValue.TenChuXe}
            onChangeText={(e) => {
              onChangeText('TenChuXe', Capitalize(e || ''))
              onChangeText('TenKH', Capitalize(e || ''))
            }}
          />
        </>
        <View flexDirection='row'>
          <View flex={1}>
            <Text style={styles.titleLabel}>Email</Text>
            <TTextInput
              containerStyle={styles.textInputContainer}
              keyboardType='email-address'
              selectTextOnFocus={false}
              placeholderTextColor='#a0a0a0'
              placeholder={'Nhập email'}
              value={objValue?.EmailKH}
              onChangeText={(e) => {
                onChangeText('EmailKH', e)
              }}
            />
          </View>
        </View>
        <View flexDirection='row'>
          <View flex={1}>
            <Text style={styles.titleLabel}>Số điện thoại</Text>
            <TTextInput
              containerStyle={styles.textInputContainer}
              keyboardType='phone-pad'
              selectTextOnFocus={false}
              placeholderTextColor='#a0a0a0'
              placeholder={'Nhập số điện thoại'}
              value={objValue.DienThoai}
              onChangeText={(e) => {
                onChangeText('DienThoai', e)
              }}
            />
          </View>
        </View>
        <>
          <Text style={styles.titleLabel}>Địa chỉ (địa chỉ theo đăng ký xe)</Text>
          <TTextInput
            containerStyle={styles.textInputContainer}
            selectTextOnFocus={false}
            placeholderTextColor='#a0a0a0'
            placeholder={'Nhập địa chỉ'}
            value={objValue?.DiaChiChuXe}
            onChangeText={(e) => {
              onChangeText('DiaChiChuXe', Capitalize(e || ''))
              onChangeText('DiaChiKH', Capitalize(e || ''))
            }}
          />
        </>
        <View flexDirection='row'>
          <View flex={1} marginRight={12}>
            <Text style={styles.titleLabel}>Biển số xe</Text>
            <TTextInput
              containerStyle={styles.textInputContainer}
              keyboardType='default'
              selectTextOnFocus={false}
              placeholderTextColor='#a0a0a0'
              placeholder={'Nhập biển số'}
              value={objValue?.BienKiemSoat}
              onChangeText={(e) => {
                onChangeText('BienKiemSoat', noWhitespace(e.toUpperCase()))
              }}
            />
          </View>

          <View flex={1}>
            <Text style={styles.titleLabel}>Xe chưa có biển số</Text>
            <View style={styles.textInput}>
              <CheckBox
                // iconType='ionicon'
                // title={'Xe chưa có biển số'}
                checkedIcon='check-square'
                uncheckedIcon='square-o'
                checkedColor={color.primary}
                checked={khongCoBienSo}
                onPress={() => {
                  setKhongCoBienSo(!khongCoBienSo)
                }}
                textStyle={{ fontWeight: 'normal' }}
                containerStyle={[styles.checkBox, { backgroundColor: '#f3f3f3' }]}
                size={20}
              />
            </View>
          </View>
        </View>
        {khongCoBienSo && <View flexDirection='row'>
          <View flex={1} marginRight={12}>
            <Text style={styles.titleLabel}>Số khung</Text>
            <TTextInput
              keyboardType='default'
              containerStyle={styles.textInputContainer}
              selectTextOnFocus={false}
              placeholderTextColor='#a0a0a0'
              placeholder={'Nhập số khung'}
              value={objValue.SoKhung}
              onChangeText={(e) => {
                onChangeText('SoKhung', e)
              }}
            />
          </View>
          <View flex={1}>
            <Text style={styles.titleLabel}>Số máy</Text>
            <TTextInput
              keyboardType='default'
              containerStyle={styles.textInputContainer}
              selectTextOnFocus={false}
              placeholderTextColor='#a0a0a0'
              placeholder={'Nhập số máy'}
              value={objValue.SoMay}
              onChangeText={(e) => {
                onChangeText('SoMay', e)
              }}
            />
          </View>
        </View>}

        <CheckBox
          // iconType='ionicon'
          title={'Xe mới 100% hoặc xe tái tục bảo hiểm'}
          checkedIcon='check-square'
          uncheckedIcon='square-o'
          checkedColor={color.primary}
          checked={objValue.CheckXeMoiHoacTaiTuc}
          onPress={() => {
            onChangeText('CheckXeMoiHoacTaiTuc', !objValue.CheckXeMoiHoacTaiTuc)
            onChangeText('NgayTaiTuc', moment(new Date()).format('DD/MM/YYYY'))
          }}
          textStyle={styles.checkBoxTxtStyles}
          containerStyle={styles.checkBoxStyles}
          size={20}
        />
        <View>
          <View flexDirection='row' justifyContent='space-between' marginBottom={12} style={{ marginTop: 15 }}>
            <View style={{ flexDirection: 'row' }}>
              <Text>{'Ảnh xe để GĐĐK'}</Text><Text style={{ color: color.primary }}>{` (${localPhotos?.length | 0}/10)`}</Text>
            </View>
            <TouchableOpacity onPress={() => {
              openBottomSheet(2)
            }} style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Icon name={'warning'} size={18} color={color.primary} /><Text> Quy định ảnh GĐĐK</Text>
            </TouchableOpacity>
          </View>
          <View style={{
            padding: 10,
            borderStyle: 'dashed',
            borderColor: 'silver',
            borderWidth: 1,
            borderRadius: 5,
            flexDirection: 'column',
            justifyContent: 'space-between',
            marginTop: 10
          }}>
            <FlatList
              showsHorizontalScrollIndicator={false}
              data={localPhotos}
              extraData={localPhotos}
              renderItem={renderImages}
              horizontal={true}
              removeClippedSubviews={true}
              keyExtractor={(item, index) => index.toString()}
              ListEmptyComponent={null} />

            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              marginTop: 10
            }}>
              <TouchableOpacity style={styles.btnImportImage} onPress={() => onActionSelectPhotoDone(0)}>
                <Icon name={'camera-outline'} size={24} color={'grey'} />
                <Text style={styles.textImportImage}>{' Chụp ảnh'}</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.btnImportImage} onPress={() => onActionSelectPhotoDone(1)}>
                <Icon name={'cloud-upload-outline'} size={24} color={'grey'} />
                <Text style={styles.textImportImage}>{' Tải ảnh'}</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View>
          </View>
        </View>

        <CheckBox
          // iconType='ionicon'
          title={'Tôi muốn xuất hóa đơn'}
          checkedIcon='check-square'
          uncheckedIcon='square-o'
          checkedColor={color.primary}
          checked={objValue.CheckXuatHoaDon}
          onPress={() => {
            onChangeText('CheckXuatHoaDon', !objValue.CheckXuatHoaDon)
          }}
          textStyle={styles.checkBoxTxtStyles}
          containerStyle={styles.checkBoxStyles}
          size={20}
        />
        {objValue.CheckXuatHoaDon && <>
          <>
            <Text style={styles.titleLabel}>Tên công ty</Text>
            <TTextInput
              containerStyle={styles.textInputContainer}
              selectTextOnFocus={false}
              placeholderTextColor='#a0a0a0'
              placeholder={'Nhập tên công ty'}
              value={objValue.TenCongTy}
              onChangeText={(e) => {
                onChangeText('TenCongTy', Capitalize(e || ''))
              }}
            />
          </>
          <>
            <Text style={styles.titleLabel}>Địa chỉ công ty</Text>
            <TTextInput
              containerStyle={styles.textInputContainer}
              keyboardType='default'
              selectTextOnFocus={false}
              placeholderTextColor='#a0a0a0'
              placeholder={'Nhập địa chỉ công ty'}
              value={objValue.DiaChiCongTy}
              onChangeText={(e) => {
                onChangeText('DiaChiCongTy', Capitalize(e || ''))
              }}
            />
          </>
          <>
            <Text style={styles.titleLabel}>Mã số thuế</Text>
            <TTextInput
              containerStyle={styles.textInputContainer}
              keyboardType='numeric'
              selectTextOnFocus={false}
              placeholderTextColor='#a0a0a0'
              placeholder={'Nhập mã số thuế'}
              value={objValue.MaSoThue}
              onChangeText={(e) => {
                onChangeText('MaSoThue', e)
              }}
            />
          </>
        </>}
        <View style={{ marginBottom: 20 }}>
          <Text style={styles.titleLabel}>Mã giới thiệu</Text>
          <TTextInput
            containerStyle={{ ...styles.textInputContainer, ...{ backgroundColor: '#FFF1F1', borderWidth: 1, borderColor: color.primary } }}
            keyboardType='default'
            selectTextOnFocus={false}
            placeholderTextColor='#a0a0a0'
            placeholder={'Nhập mã số người giới thiệu'}
            value={objValue.MaGioiThieu}
            onChangeText={(e) => {
              onChangeText('MaGioiThieu', e.toUpperCase())
            }}
          />
        </View>

      </View>
    )
  }

  const renderStep3 = () => {
    return (<>
      <View style={[styles.mainContainer, { paddingHorizontal: 16 }]}>
        <View>
          <Text style={styles.txtTitle}>Thông tin chủ sở hữu</Text>
          {renderDetailRowLine('Tên chủ xe', objValue.TenChuXe)}
          {/* {renderDetailRowLine('Số CMND/CCCD', ' '} */}
          {renderDetailRowLine('Số điện thoại', objValue.DienThoai)}
          {renderDetailRowLine('Email', objValue.EmailKH)}
          {renderDetailRowLine('Địa chỉ', objValue.DiaChiChuXe)}
          {/* <View style={styles.detailInfoRow}> */}
          {/*  <Text style={styles.label}>Địa chỉ: <Text style={styles.value}>43 Nguyễn Chí Thanh - Ba Đình - Hà Nội fadfdf ssdfd sfds fdsfsfds</Text></Text> */}
          {/* </View> */}
        </View>
        <View marginTop={20} style={styles.divider} />
        <View>
          <Text style={styles.txtTitle}>Thông tin xe</Text>
          {renderDetailRowLine('Hãng xe', hangXe?.label || insuranceStore.arrHangXe.find(x => x.value == objValue.HangSanXuat)?.label)}
          {renderDetailRowLine('Dòng xe', dongXe?.label || insuranceStore.arrDongXe.find(x => x.value == objValue.DongXe)?.label)}
          {renderDetailRowLine('Năm sử dụng', objValue?.NamSD || '-')}
          {renderDetailRowLine('Mục đích SD', mucDichSuDung?.label || objValue?.MucDichSuDung)}
          {renderDetailRowLine('Loại xe', loaiHinhXe || '-')}
          {/* /!* {renderDetailRowLine('Chức năng sử dụng', 'Khác')} *!/ */}
          {renderDetailRowLine('Trọng tải', objValue.TrongTai != '99999' ? objValue.TrongTai : '-')}
          {renderDetailRowLine('Biển số xe', objValue?.BienKiemSoat)}
          {renderDetailRowLine('Số chỗ ngồi', `${objValue?.ChoNgoi} chỗ`)}
          {/* {renderDetailRowLine('Mức trách nhiệm về tài sản', `${formatMoney(objValue?.MTNHangHoa)} triệu/người/vụ`)} */}
          {renderDetailRowLine('Mức trách nhiệm về người', `${formatMoney(objValue?.MTNLaiPhu)} triệu/người/vụ`)}
          {renderDetailRowLine('Ngày bắt đầu', objValue?.NgayDau)}
          {renderDetailRowLine('Ngày hết hạn', objValue?.NgayCuoi)}
          {objValue?.ThoiHan && renderDetailRowLine('Thời hạn bảo hiểm', `${objValue?.ThoiHan} năm`)}
          {renderDetailRowLine('Số người tham gia bảo hiểm', `${objValue?.SoNguoiToiDa} người`)}
        </View>
        { !isViewDetail ? <>
          <View marginTop={20} style={styles.divider} />
          <View>
            <Text style={[styles.txtRisk, { marginTop: 10, marginBottom: 20 }]}>Bấm xác nhận đồng nghĩa với việc bạn đồng ý với <Text style={{ fontWeight: '700' }}>Điều kiện và điều
              khoản</Text> của maxQ và Đối tác cung cấp bảo hiểm.</Text>
            { objValue.CheckXeMoiHoacTaiTuc && <>
              <View style={styles.divider} />
              <Text style={styles.txtTitle}>Phương thức thanh toán</Text>
              <View flexDirection='row' flex={1}>
                <TouchableOpacity style={[styles.btnSelectYear, selectedPaymentMethod[0] && styles.selectedItemStyles]}
                  onPress={() => onSelectPaymentMethod(0)}>
                  {selectedPaymentMethod[0] &&
                    <Icon style={{ marginRight: 8 }} name={'radio-button-on-sharp'} size={20} color={color.primary} />}
                  <Text style={selectedPaymentMethod[0] && { fontWeight: '500' }}>Chuyển khoản</Text>
                </TouchableOpacity>
                {/* <TouchableOpacity style={[styles.btnSelectYear, selectedPaymentMethod[1] && styles.selectedItemStyles]} */}
                {/*  onPress={() => onSelectPaymentMethod(1)}> */}
                {/*  {selectedPaymentMethod[1] && */}
                {/*    <Icon style={{ marginRight: 8 }} name={'radio-button-on-sharp'} size={20} color={color.primary} />} */}
                {/*  <Text style={selectedPaymentMethod[1] && { fontWeight: '500' }}>Ví Momo, VN Pay</Text> */}
                {/* </TouchableOpacity> */}
                <TouchableOpacity style={[styles.btnSelectYear, styles.selectedItemStyles]}
                  onPress={() => { openBottomSheet(3) }}>
                  <Text>Hướng dẫn thanh toán</Text>
                </TouchableOpacity>
              </View>
              <View flexDirection='row' flex={1} style={{ justifyContent: 'space-between', marginBottom: 20 }}>
                {selectedPaymentMethod[0] && <TouchableOpacity onPress={() => {
                  openBottomSheet(1)
                }}><Text style={{ color: color.primary }}>Bấm chọn ngân hàng</Text></TouchableOpacity>}
                <View>
                  {bankCode ? <Text>(Đã chọn thanh toán qua) {bankCode}</Text> : null}
                </View>
              </View>
            </>}

          </View>
        </> : <TouchableOpacity style={{ alignSelf: 'center' }} onPress={onViewOnline}>
          <Text style={{
            textAlign: 'center',
            alignItems: 'center',
            borderColor: color.primary,
            borderRadius: 3,
            borderWidth: 1,
            color: color.primary,
            justifyContent: 'center',
            margin: 6,
            marginBottom: 8,
            padding: 8,
          }}>Xem bản điện tử</Text>
        </TouchableOpacity>}
      </View>
    </>)
  }

  const renderDetailRowLine = (label, value) => {
    return (
      <View style={styles.detailInfoRow}>
        <Text style={styles.label}>{label || ''}</Text>
        <Text style={styles.value}>{value || ''}</Text>
      </View>
    )
  }

  const onPressRemove = (data) => {
    const index = localPhotos.findIndex(x => x == data)
    setLocalPhotos(localPhotos.filter(x => x != data))
    onChangeText('data_gddk', objValue.data_gddk.filter(x => x.file_size != data))
  }

  const renderImages = ({ item }) => {
    return (
      <View style={styles.imgViewStyles}>
        <TouchableOpacity onPress={() => onPressRemove(item)} style={styles.closeBtn}>
          <View style={{
            backgroundColor: color.primary,
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: 12,
            width: 24,
            height: 24
          }}><Icon name='close' size={24} color={'#fff'} /></View>
        </TouchableOpacity>
        <FastImage source={{ uri: `data:image/jpeg;base64,${item}` }} style={styles.imgStyles} resizeMode='cover' />
      </View>
    )
  }

  const renderDiscount = () => {
    const discount = item?.attributes?.discount || 0
    const discountValue = (insuranceStore.rsTinhPhiTNDSOto?.TotalFee * discount) / 100
    return Math.ceil(((insuranceStore.rsTinhPhiTNDSOto?.TotalFee || 0) - discountValue) / 1000) * 1000
  }

  const iconRight = <Icon name={'chevron-down-outline'} size={20} color={'#333'} style={{ marginRight: 8 }} />
  const iconCalender = <Icon name={'calendar'} size={20} color={color.primary} style={{ marginRight: 8 }} />

  function onSelectItemMucDichSuDung(i) {
    setMucDichSuDung(i)
    onChangeText('MucDichSuDung', i?.label || '')
    onChangeText('ma_mdsd', i?.value || '')
    onChangeText('MaMucDichSD', i?.value || '')
    insuranceStore.getLoaiHinh(i?.value || '')
    insuranceStore.getLoaiXe(i?.value || '', objValue.ChoNgoi, objValue.TrongTai, 'vcx')
  }

  function onSelectItemHangXe(i) {
    // onChangeText('HangSanXuat', `${i.label} - ${i.value}`)
    onChangeText('HangSanXuat', `${i.value}`)
    onChangeText('HangSanXuatText', `${i.label}`)
    onChangeText('HieuXe', `${i.value}`)
    setHangXe(i)
    insuranceStore.getDongXe(i?.value || '')
  }

  return (
    <Fragment>
      <SafeAreaView style={styles.wrapper}>
        <Header
          leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={onGoBack} />}
          centerComponent={{ text: t(t('Bảo hiểm vật chất ô tô')), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
          containerStyle={common.headerContainer}
          statusBarProps={{ barStyle: 'light-content' }}
          ViewComponent={LinearGradient}
          linearGradientProps={linearGradientProps}
        />
        <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
          {step === 1 && renderStep1()}
          {step === 2 && renderStep2()}
          {step === 3 && renderStep3()}
        </KeyboardAwareScrollView>
        { !isViewDetail && <>
          <View style={[styles.divider, { height: 2 }]} />
          <View>
            <View flexDirection='row' justifyContent='space-between' marginHorizontal={16} marginVertical={10}>
              <Text>Phí bảo hiểm</Text>
              <Text style={styles.oldPrice}>{numberFormat(insuranceStore.rsTinhPhiTNDSOto?.TotalFee)} đ</Text>
              <Text style={styles.price}>{formatMoney(renderDiscount())} đ</Text>
            </View>
            <View flexDirection='row' marginHorizontal={16} style={{ alignItems: 'center', marginVertical: 10 }}>
              <Image source={icCheckSquare} style={{ width: 16, height: 16, marginRight: 8 }}></Image>
              <Text>{`maxQ hỗ trợ ${item?.attributes.discount}% phí bảo hiểm`}</Text>
            </View>
            {step === 1 &&
              <TButton loading={isSubmitting} disabled={isSubmitting || !validateStep1()} typeRadius={'rounded'} title={t('Tiếp tục')}
                onPress={onSubmit} buttonStyle={{ marginHorizontal: 16, marginVertical: 10 }}
                titleStyle={{ fontWeight: '500', fontSize: 16 }} />}
            {__DEV__ && <TButton loading={isSubmitting} typeRadius={'rounded'} title={step}
              onPress={onSubmit} buttonStyle={{ marginHorizontal: 16, marginVertical: 10 }}
              titleStyle={{ fontWeight: '500', fontSize: 16 }} />}

            {step === 2 && objValue.CheckXeMoiHoacTaiTuc && <TButton loading={isSubmitting} disabled={isSubmitting || !validateStep2()} typeRadius={'rounded'} title={t('Thanh toán')}
              onPress={onSubmit} buttonStyle={{ marginHorizontal: 16, marginVertical: 10 }}
              titleStyle={{ fontWeight: '500', fontSize: 16 }} />}
            {step === 2 && !objValue.CheckXeMoiHoacTaiTuc && <TButton loading={isSubmitting} disabled={isSubmitting || !validateStep2()} typeRadius={'rounded'} title={t('Hoàn Thành')}
              onPress={onSubmit} buttonStyle={{ marginHorizontal: 16, marginVertical: 10 }}
              titleStyle={{ fontWeight: '500', fontSize: 16 }} />}
            {step === 3 &&
              <TButton loading={isSubmitting} disabled={isSubmitting || !validateStep1() || !validateStep2()} typeRadius={'rounded'}
                title={t('Xác nhận')}
                onPress={onSubmit} buttonStyle={{ marginHorizontal: 16, marginVertical: 10 }}
                titleStyle={{ fontWeight: '500', fontSize: 16 }} />}
          </View>
        </>}
        {showNamSX && (
          <MonthPicker
            onChange={onValueChange}
            value={namSX}
            locale="vi-VN"
          />
        )}
        <DateTimePickerModal
          isVisible={pickerDateVisible}
          // mode={pickerDate}
          locale='vi_VN'
          cancelTextIOS={t('CANCEL')}
          confirmTextIOS={t('XACNHAN')}
          onConfirm={onChangeNgayBatDau}
          onCancel={hidePicker}
          isDarkModeEnabled={false}
        />
        <BottomSheetPicker
          headerText={'Chọn tỉnh/Thành phố'}
          items={insuranceStore.arrCities}
          onSelectItem={(i) => onChangeText('TinhThanh', i.value)}
          ref={tinhThanhRef}
          search={true}
        />

        <BottomSheetPicker
          headerText={'Chọn mục đích sử dụng'}
          items={insuranceStore.arrMucDichSuDung}
          onSelectItem={onSelectItemMucDichSuDung}
          ref={mucDichSuDungRef}
          search={true}
        />
        <BottomSheetPicker
          headerText={'Chọn loại xe'}
          items={insuranceStore.arrLoaiXe}
          onSelectItem={(i) => {
            setLoaiXe(i)
            onChangeText('LoaiXe', i?.value || '')
            onChangeText('xe_loaixe_code', i?.code || '')
          }}
          ref={loaiXeRef}
        />
        <BottomSheetPicker
          headerText={'Chọn hãng xe'}
          items={insuranceStore.arrHangXe}
          onSelectItem={onSelectItemHangXe}
          ref={hangXeRef}
          search={true}
        />
        <BottomSheetPicker
          headerText={'Chọn dòng xe'}
          items={insuranceStore.arrDongXe}
          onSelectItem={(i) => {
            // onChangeText('DongXe', `${i.label} - ${i.value}`)
            onChangeText('DongXe', `${i.value}`)
            setDongXe(i)
          }}
          ref={dongXeRef}
          search={true}
        />
        <BottomSheetPicker
          headerText={'Chọn năm sử dụng'}
          items={lastYears(10).sort()}
          onSelectItem={(i) => onChangeText('NamSD', `${i.value}`)}
          ref={namSdRef}
          search={true}
        />
        <BottomSheetPicker
          headerText={'Danh sách chọn'}
          items={[
            { label: 'Tháng 1', value: '01' },
            { label: 'Tháng 2', value: '02' },
            { label: 'Tháng 3', value: '03' },
            { label: 'Tháng 4', value: '04' },
            { label: 'Tháng 5', value: '05' },
            { label: 'Tháng 6', value: '06' },
            { label: 'Tháng 7', value: '07' },
            { label: 'Tháng 8', value: '08' },
            { label: 'Tháng 9', value: '09' },
            { label: 'Tháng 10', value: '10' },
            { label: 'Tháng 11', value: '11' },
            { label: 'Tháng 12', value: '12' },
          ]}
          onSelectItem={(i) => onChangeText('ThangSD', i.value)}
          ref={thangSdRef}
        />
        <BottomSheetPicker
          headerText={'Danh sách chọn'}
          items={[
            { label: '2 chỗ', value: '2' },
            { label: '3 chỗ', value: '3' },
            { label: '4 chỗ', value: '4' },
            { label: '5 chỗ', value: '5' },
            { label: '7 chỗ', value: '7' }
          ]}
          onSelectItem={(i) => onChangeText('ChoNgoi', i.value)}
          ref={soChoNgoiRef}
        />
        <BottomSheetPicker
          headerText={'Danh sách chọn'}
          items={[
            { label: '2 chỗ', value: '2' },
            { label: '3 chỗ', value: '3' },
            { label: '4 chỗ', value: '4' },
            { label: '5 chỗ', value: '5' },
            { label: '7 chỗ', value: '7' }
          ]}
          onSelectItem={(i) => onChangeText('ChoNgoi', i.value)}
          ref={soChoNgoiRef}
        />
        <BottomSheetPicker
          headerText={'Chọn số năm tham gia'}
          items={[
            { label: '1 Năm', value: '12' },
            { label: '1.5 Năm', value: '18' },
            { label: '2 Năm', value: '24' },
            { label: '2.5 Năm', value: '30' }
          ]}
          onSelectItem={(i) => handlerChonThoiHan(i)}
          ref={thoiHanRef}
        />
        <BottomSheetPicker
          headerText={'Chọn số người tham gia'}
          items={[
            { label: '1 năm', value: '1' },
            { label: '2 năm', value: '2' },
            { label: '3 năm', value: '3' }
          ]}
          onSelectItem={(i) => onChangeText('ThoiHan', i.value)}
          ref={soNguoiThamGiaRef}
        />
        <BottomSheetPicker
          headerText={'Chọn mức khấu trừ'}
          items={[
            { label: '500,000', value: '500000' },
            { label: '1,000,000', value: '1000000' },
            { label: '2,000,000', value: '2000000' },
            { label: '3,000,000', value: '3000000' },
            { label: '4,000,000', value: '4000000' },
            { label: '5,000,000', value: '5000000' },
            { label: '6,000,000', value: '6000000' },
            { label: '7,000,000', value: '7000000' },
            { label: '8,000,000', value: '8000000' },
            { label: '9,000,000', value: '9000000' },
            { label: '10,000,000', value: '10000000' },
            { label: '20,000,000', value: '20000000' },
            { label: '30,000,000', value: '30000000' },
            { label: '40,000,000', value: '40000000' },
            { label: '50,000,000', value: '50000000' }
          ]}
          onSelectItem={(i) => {
            onChangeText('GiaTriBH', i?.value || '')
          }}
          ref={giaTriBHRef}
        />
        <BottomSheetPicker
          headerText={'Chọn số năm tham gia'}
          items={insuranceStore.catNameSelected == 'PVI' ? [
            { label: '1 Năm', value: '12' },
            { label: '1,5 Năm', value: '18' },
            { label: '2 Năm', value: '24' },
            { label: '2,5 Năm', value: '30' }
          ] : [
            { label: '1 Năm', value: '12' },
            { label: '2 Năm', value: '24' },
            { label: '3 Năm', value: '36' }
          ]}
          onSelectItem={(i) => handlerChonThoiHan(i)}
          ref={thoiHanRef}
        />
        <BottomSheetPicker
          headerText={'Số tiền bảo hiểm'}
          items={[
            { label: '50,000,000', value: '50000000' },
            { label: '100,000,000', value: '100000000' },
            { label: '150,000,000', value: '150000000' },
            { label: '200,000,000', value: '200000000' },
            { label: '250,000,000', value: '250000000' },
            { label: '300,000,000', value: '300000000' },
            { label: '350,000,000', value: '350000000' },
            { label: '400,000,000', value: '400000000' },
            { label: '450,000,000', value: '450000000' },
            { label: '500,000,000', value: '500000000' }
          ]}
          onSelectItem={(i) => {
            onChangeText('MTNLaiPhu', i?.value || '')
          }}
          ref={soTienBhLaiPhuRef}
        />
        <Modalize
          HeaderComponent={<View style={{ paddingTop: 15 }}>
            <ButtonBack onPress={() => { closeBottomSheet() }} style={{ position: 'absolute', left: 16, top: 15, zIndex: 99999 }} />
            <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: 0 }}>
              <Text style={{ color: '#333', fontSize: 14 }}>
                { bottomSheetType === 1 && 'Chọn ngân hàng'}
                { bottomSheetType === 2 && 'Hướng dẫn upload ảnh'}
                { bottomSheetType === 3 && 'Hướng dẫn thanh toán'}
              </Text>
            </View>
            <View style={{ marginTop: 15, height: 1, width: responsiveWidth(100), backgroundColor: '#f2f2f2' }}></View>
          </View>}
          ref={bottomSheetRef}
          modalHeight={responsiveHeight(80)}
          disableScrollIfPossible = {false}
          onClosed={() => { setSetBottomSheetType(0) }}
          keyboardAvoidingBehavior={'padding'}
        >
          <View style={{ flex: 1 }}>
            { bottomSheetType === 1 && <>
              <Payment onSelect={onSelectBank} oncloseModal={() => closeBottomSheet()} /></>}
            { bottomSheetType === 2 && guideUploadImageHtml && <View style={{ marginHorizontal: 16, marginTop: 16 }}>
              <RenderHtml
                contentWidth={width}
                source={{ html: guideUploadImageHtml }}
                ignoredTags={['script']}
                ignoredStyles={['font-family']}
                renderersProps={{
                  img: {
                    enableExperimentalPercentWidth: true
                  }
                }}
              />
            </View>}
            { bottomSheetType === 3 && paymentGuideHtml && <View style={{ marginHorizontal: 16, marginTop: 16 }}>
              <RenderHtml
                contentWidth={width}
                source={{ html: paymentGuideHtml }}
                ignoredTags={['script']}
                ignoredStyles={['font-family']}
                renderersProps={{
                  img: {
                    enableExperimentalPercentWidth: true
                  }
                }}
              />
            </View>}
          </View>
        </Modalize>
      </SafeAreaView>
    </Fragment>
  )
}, { forwardRef: true })

const styles = StyleSheet.create({
  btnImportImage: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginHorizontal: 15,
    paddingVertical: 15
  },
  btnSelectYear: {
    alignItems: 'center',
    backgroundColor: '#F3F3F3',
    borderRadius: 5,
    flexDirection: 'row',
    flex: 1,
    marginBottom: 16,
    marginHorizontal: 4,
    paddingHorizontal: 14,
    paddingVertical: 8
  },
  checkBox: {
    backgroundColor: '#fff',
    borderWidth: 0,
    margin: 0,
    marginLeft: 0,
    padding: 0
  },

  checkBoxStyles: {
    backgroundColor: '#fff',
    borderWidth: 0,
    margin: 0,
    marginLeft: 0,
    marginTop: 20,
    padding: 0
  },

  checkBoxTxtStyles: {
    fontWeight: '400'
  },

  closeBtn: {
    position: 'absolute',
    right: 4,
    top: 4,
    zIndex: 2
  },

  detailInfoRow: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16
  },

  divider: {
    backgroundColor: '#E6E6E6',
    height: 5
  },

  imgStyles: {
    height: 128,
    width: 128
  },

  imgViewStyles: {
    marginRight: 16
  },
  label: {
    color: '#A0A0A0',
    // lineHeight: 20,
    marginRight: 10
  },
  mainContainer: {
    flex: 1,
    justifyContent: 'flex-start',
    paddingTop: 15
  },

  oldPrice: {
    color: color.dim,
    textDecorationLine: 'line-through'
  },

  price: {
    color: color.primary,
    fontSize: 16,
    fontWeight: '600'
  },

  rangeSlider: {
    alignItems: 'stretch',
    flex: 1,
    justifyContent: 'center',
    marginTop: 30
  },
  selectedItemStyles: {
    // backgroundColor: '#F1F1F1',
    borderColor: color.primary,
    borderWidth: 1
  },
  subTxt: {
    color: '#2D384C',
    fontWeight: '400',
    lineHeight: 21,
    marginLeft: 30,
    marginTop: 10
  },
  textImportImage: {
    alignSelf: 'center',
    color: 'grey'
  },
  textInput: {
    alignItems: 'center',
    backgroundColor: '#f3f3f3',
    // borderColor: '#edf1f7',
    borderRadius: 4,
    // borderStyle: 'solid',
    // borderWidth: 1,
    color: '#333',
    flexDirection: 'row',
    // fontSize: 14,
    height: 40,
    justifyContent: 'space-between',
    marginTop: 10,
    paddingLeft: 14
  },
  textInputContainer: {
    height: 40,
    marginTop: 10
  },
  textProvince: {
    alignSelf: 'center',
    color: '#a0a0a0',
    fontFamily: typography.normal,
    fontSize: 14
  },
  titleLabel: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    paddingTop: 20
  },
  titleSection: {
    color: '#333',
    fontSize: 14,
    fontWeight: 'bold',
    paddingTop: 20,
    textTransform: 'uppercase'
  },
  titleSectionDes: {
    color: '#333',
    marginTop: 5
  },
  txtRisk: {
    color: '#2D384C',
    fontWeight: '400',
    lineHeight: 21
  },
  txtTitle: {
    color: '#3F3F3F',
    fontSize: 16,
    fontWeight: 'bold',
    marginVertical: 16
  },
  value: {
    color: '#2D384C',
    lineHeight: 20
  },
  viewPrice: {
    alignItems: 'center',
    backgroundColor: '#F3F3F3',
    borderRadius: 5,
    marginTop: 10,
    paddingHorizontal: 20,
    paddingVertical: 12
  },
  viewProvince: {
    alignItems: 'center',
    backgroundColor: '#f3f3f3',
    // borderColor: '#edf1f7',
    borderRadius: 4,
    // borderStyle: 'solid',
    // borderWidth: 1,
    flexDirection: 'row',
    height: 40,
    justifyContent: 'space-between',
    marginTop: 10,
    paddingLeft: 14
  },
  wrapper: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },

})
