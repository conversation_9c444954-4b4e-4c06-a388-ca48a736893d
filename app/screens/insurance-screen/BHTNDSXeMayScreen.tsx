import React, { Fragment, useContext, useEffect, useRef, useState } from 'react'
import {
  Alert,
  Dimensions,
  Image,
  Platform,
  StyleSheet,
  Text, TouchableOpacity, View
} from 'react-native'
import { observer } from 'mobx-react-lite'
import { BottomSheetPicker, ButtonBack, TButton, TTextInput, useLoading } from '@app/components'
import { useTranslation } from 'react-i18next'
import { ModalContext } from '@app/context'
import { useStores } from '@app/models'
import { useNavigation, useRoute } from '@react-navigation/native'
import {
  icCheckSquare,
} from '@app/assets/images'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { CheckBox, Header } from 'react-native-elements'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import LinearGradient from 'react-native-linear-gradient'
import { SafeAreaView } from 'react-native-safe-area-context'
import { color, typography } from '@app/theme'
import { Capitalize, formatMoney, getNumberPrice, getPhoneOnly, numberFormat } from '@app/utils'
import Icon from 'react-native-vector-icons/Ionicons'
import DateTimePickerModal from 'react-native-modal-datetime-picker'
import { TermContent } from './Components/TermContent'
import moment from 'moment'
import SimpleToast from 'react-native-simple-toast'
import validate from 'validate.js'
import useDeepCompareEffect from 'use-deep-compare-effect'
import { SCREENS } from '@app/navigation'
import { Payment } from '@app/screens'
import { InAppBrowser } from 'react-native-inappbrowser-reborn'
import { responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import RenderHtml from 'react-native-render-html'
import { Modalize } from 'react-native-modalize'
import { Api } from '@app/services/api'

const { width } = Dimensions.get('window')
export const BHTNDSXeMayScreen = observer((props: any, ref) => {
  const { navigate, goBack } = useNavigation()
  const route = useRoute()
  const { id, isRenew, isViewDetail, dataRenew, prodId }: any = route?.params
  const { t } = useTranslation()
  const { showError, showSuccess } = useContext(ModalContext)
  const { profileStore, insuranceStore } = useStores()
  const { show, hide } = useLoading()

  const [isSubmitting, setIsSubmitting] = useState(false)
  const bottomSheetRef = useRef<Modalize>(null)

  // ngày bắt đầu
  const [openNgayBatDau, setOpenNgayBatDau] = useState(false)
  const [date, setDate] = useState(new Date().getTime())

  const [selectedDuration, setSelectedDuration] = useState([false, false, false])

  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState([true, false])
  const [bankCode, setBankCode] = useState('')
  const [bottomSheetType, setSetBottomSheetType] = useState(0)
  const [paymentGuideHtml, setPaymentGuideHtml] = useState()

  const [step, setStep] = useState(1)
  const [item, setItem] = useState(null)

  const loaiXeRef = useRef(null)
  const hangXeRef = useRef(null)
  const soNguoiRef = useRef(null)
  const mucTrachNhiemRef = useRef(null)
  const hanMucRef = useRef(null)

  const [loaiXe, setLoaiXe] = useState(null)
  const [hangXe, setHangXe] = useState(null)
  const [hanMuc, setHanMuc] = useState(null)
  const [baoHiemNguoiNgoi, setBaoHiemNguoiNgoi] = useState(false)
  const [khongCoBienSo, setKhongCoBienSo] = useState(false)

  const [objValue, setObjValue] = useState({
    ten_nguoimua_bh: '',
    diachi_nguoimua_bh: '',
    TenKH: '',
    DiaChiKH: '',
    ngay_dau: '', // cal
    ngay_cuoi: '', // cal
    bien_kiemsoat: '',
    so_may: '',
    so_khung: '',
    loai_xe: '', // cal
    nhan_hieu: '',
    nam_sanxuat: '',
    ten_chuxe: '',
    email: '',
    so_dienthoai: '',
    dia_chi: '',
    thamgia_laiphu: false, // cal
    muc_trachnhiem_laiphu: '0', // cal
    so_nguoi_tgia_laiphu: 2, // cal
    tyle_phi_laiphu: 0, // cal
    an_bien_ks: false,
    ThoiHan: '1',
    MaGioiThieu: '',
    han_muc: '0'
  })

  useEffect(() => {
    // load default data
    insuranceStore.clearArr()
    onChangeText('email', profileStore.email)
    onChangeText('so_dienthoai', getPhoneOnly(profileStore.phoneNumber || profileStore.phone))
    onSelectDuration(0)
    getApiData()
    getApiConfig()
  }, [])

  const onSelectDuration = (index) => {
    if (index === 0) {
      setSelectedDuration([true, false, false])
      onChangeText('ThoiHan', '1 năm')
      calDurationDate(1)
    }
    if (index === 1) {
      setSelectedDuration([false, true, false])
      onChangeText('ThoiHan', '2 năm')
      calDurationDate(2)
    }
    if (index === 2) {
      setSelectedDuration([false, false, true])
      onChangeText('ThoiHan', '3 năm')
      calDurationDate(3)
    }
  }

  const getApiData = async () => {
    const rs = await insuranceStore.getInsurancePostDetail(prodId)
    if (rs?.data?.data) {
      __DEV__ && console.log('insuranceStore.getInsurancePostDetail(prodId)', rs)
      setItem(rs?.data?.data)
      insuranceStore.setCatSelected(rs?.data?.data)
    }
    return Promise.all([
      insuranceStore.getLoaiXeMotor(),
      insuranceStore.getHangXeMotor(),
    ])
  }

  const buildBody = () => {
    return {
      ...objValue,
      ten_nguoimua_bh: objValue.ten_chuxe,
      diachi_nguoimua_bh: objValue.dia_chi,
      TenKH: objValue.ten_chuxe,
      DiaChiKH: objValue.dia_chi,
      muc_trachnhiem_laiphu: getNumberPrice(objValue.muc_trachnhiem_laiphu),
      loai_xe: !isRenew ? loaiXe?.value : dataRenew?.loai_xe,
      nhan_hieu: !isRenew ? hangXe?.value : dataRenew?.nhan_hieu,
      han_muc: hanMuc?.value ? hanMuc?.value : '0'
    }
  }

  useDeepCompareEffect(() => {
    __DEV__ && console.log('objValue', objValue)
    if (validateStep1()) {
      // goi tinh toán số tiền
      insuranceStore.tinhPhiTNDSXM(buildBody())
    } else {
      insuranceStore.resetTotalFreeTNDSXM()
    }
  }, [objValue])

  useEffect(() => {
    if (insuranceStore.paymentStatus) {
      // gửi đơn hàng sau khi thanh toán thành công và lưu vào db
      createOrderPVI()
      insuranceStore.clearArr()
      // reset
      setStep(1)
      setBankCode('')
    }

    return () => {

    }
  }, [insuranceStore.paymentStatus])

  const validateStep1 = () => {
    if (!isRenew) {
      if (objValue.thamgia_laiphu) {
        return !validate.isEmpty(objValue.ngay_dau) &&
          !validate.isEmpty(objValue.ngay_cuoi) &&
          !validate.isEmpty(loaiXe?.value) &&
          validateHangXe() &&
          !validate.isEmpty(objValue.so_nguoi_tgia_laiphu) &&
          !validate.isEmpty(objValue.muc_trachnhiem_laiphu) &&
          validateStep2()
      } else {
        return !validate.isEmpty(objValue.ngay_dau) &&
          !validate.isEmpty(objValue.ngay_cuoi) &&
          !validate.isEmpty(loaiXe?.value) &&
          validateHangXe() &&
          validateStep2()
      }
    } else {
      if (objValue.thamgia_laiphu) {
        return !validate.isEmpty(objValue.ngay_dau) &&
          !validate.isEmpty(objValue.ngay_cuoi) &&
          !validate.isEmpty(objValue.so_nguoi_tgia_laiphu) &&
          !validate.isEmpty(objValue.muc_trachnhiem_laiphu)
      } else {
        return !validate.isEmpty(objValue.ngay_dau) &&
          !validate.isEmpty(objValue.ngay_cuoi)
      }
    }
  }

  const validateStep2 = () => {
    if (!khongCoBienSo) {
      return !validate.isEmpty(objValue.ten_chuxe) &&
        !validate.isEmpty(objValue.bien_kiemsoat) &&
        !validate.isEmpty(objValue.dia_chi)
    } else {
      return !validate.isEmpty(objValue.ten_chuxe) &&
        !validate.isEmpty(objValue.dia_chi) &&
        !validate.isEmpty(objValue.so_khung) &&
        !validate.isEmpty(objValue.so_may)
    }
  }

  const validateHangXe = () => {
    if (insuranceStore.catNameSelected == 'BIC') {
      return true
    } else {
      return !validate.isEmpty(hangXe?.value)
    }
  }

  useEffect(() => {
    if (isRenew === true) {
      setStep(2)
      __DEV__ && console.log('dataRenew', dataRenew)
      const year = moment(dataRenew.ngay_cuoi, 'DD/MM/YYYY hh:mm').diff(moment(dataRenew.ngay_dau, 'DD/MM/YYYY hh:mm'), 'years')
      // tang thoi gian
      const body = {
        ...objValue,
        ...dataRenew,
        ngay_dau: dataRenew.ngay_cuoi,
        ngay_cuoi: moment(dataRenew.ngay_cuoi, 'DD/MM/YYYY hh:mm').add(year, 'y').format('DD/MM/YYYY hh:mm')
      }
      setObjValue(body)
    }
    return () => {
    }
  }, [isRenew])

  useEffect(() => {
    if (isViewDetail === true) {
      setStep(2)
      // tang thoi gian
      const body = {
        ...objValue,
        ...dataRenew,
      }
      setObjValue(body)
    }
    return () => {
    }
  }, [isViewDetail])

  const createOrderInsuranceHistory = async (data) => {
    const dataRenew = {
      ...data,
      NgayDau: data?.ngay_dau,
      NgayCuoi: data?.ngay_cuoi,
      EmailKH: data?.email,
      DienThoai: data?.so_dienthoai,
      BienKiemSoat: data?.bien_kiemsoat,
    }
    const bodyHistory: any = {
      data: {
        TenCongTy: '',
        DiaChiCongTy: '',
        MaSoThue: '',
        MaGioiThieu: '',
        user_id: profileStore._id,
        // MucDichSD: mucDichSuDung?.value || '',
        PaymentId: data?.ma_giaodich || '',
        ProductId: item?.id + '',
        name: item?.attributes?.name,
        hangXe: hangXe?.value,
        // dongXe: loaiXe?.label,
        loaiXe: loaiXe?.value,
        loai_bao_hiem: item?.attributes?.loai_bao_hiems?.data?.length > 0 ? item?.attributes?.loai_bao_hiems?.data[0].id : 1,
        ...dataRenew,
        type: 'BẢO HIỂM TNDS XE MÁY',
        PhiBHTNDSBB: `${renderDiscount()}`,
        TongPhi: `${renderDiscount()}`,
        // TotalFeeNoVAT: `${insuranceStore.rsTinhPhiTNDSOto?.TotalFeeNoVAT}`,
        dataRenew: { ...dataRenew, paymentId: data?.ma_giaodich }
      }
    }
    const rs = await insuranceStore.createOrderInsuranceHistory(bodyHistory)
  }

  const createOrderPVI = async () => {
    let body = {
      ...buildBody(),
      ma_giaodich: insuranceStore.paymentId
    }
    if (insuranceStore.catNameSelected == 'BIC') {
      body = { ...body, ...insuranceStore.rsTinhPhiTNDSXM }
    }
    const rsCreate = await insuranceStore.taoDonTNDSXM(body)
    __DEV__ && console.log('rsCreate', rsCreate)
    // navigate(SCREENS.payment, { vnpUrl: rs.data.data.vnpUrl, orderId: productStore.orderId, paymentId: rs.data.data.paymentId, typeBooking: 0 })
    if (rsCreate && rsCreate.data?.Status == '00') {
      createOrderInsuranceHistory({ ...body, Pr_key: rsCreate?.Pr_key || '' + '' })
      // showSuccess(t('THANHCONG'), t('Thành công'))
      // setTimeout(() => onGoBack(), 500)
    } else {
      // showError(t('FAIL'), t(`${rsCreate.data?.Message}`))
      showError(t('FAIL'), t(`ID: - ${body?.ma_giaodich} ${rsCreate?.data?.Message}`))
      createOrderFail()
    }
  }

  const createOrderFail = async () => {
    const body = {
      ...buildBody(),
      paymentId: insuranceStore.paymentId,
      ma_giaodich: 'CANCEL',
    }
    createOrderInsuranceHistory(body)
    insuranceStore.clearArr()
  }

  useEffect(() => {
    __DEV__ && console.log('khongCoBienSo', khongCoBienSo)
    if (khongCoBienSo) {
      onChangeText('bien_kiemsoat', '')
    }
    return () => {

    }
  }, [khongCoBienSo])

  const onSelectPaymentMethod = (index) => {
    if (index === 0) {
      setSelectedPaymentMethod([true, false])
    }
    if (index === 1) {
      setSelectedPaymentMethod([false, true])
    }
  }

  const openLink = async (link) => {
    if (link) {
      try {
        await InAppBrowser.close()
        await InAppBrowser.open(link, {
          toolbarColor: color.primary,
          dismissButtonStyle: 'cancel',
          // preferredBarTintColor: color.primary,
          // preferredControlTintColor: '#fff',
          // preferredControlTintColor: 'white',
          readerMode: false,
          animated: true,
          // modalPresentationStyle: 'fullScreen',
          modalTransitionStyle: 'coverVertical',
          modalEnabled: true,
          enableBarCollapsing: false,
        })
      } catch (error) {
        Alert.alert(error.message)
      }
    }
  }

  useEffect(() => {
    if (insuranceStore.paymentCancel) {
      insuranceStore.setPaymentCancel(false)
      // gửi đơn hàng sau khi thanh toán thành công và lưu vào db
      createOrderFail()
    }

    return () => {

    }
  }, [insuranceStore.paymentCancel])

  const onViewOnline = async () => {
    show()
    if (insuranceStore.catNameSelected == 'BIC') {
      const rs = await insuranceStore.getUrlPvi(dataRenew?.Pr_key + '', 'tndsxm')
      if (rs?.data) {
        openLink(rs?.data?.URL)
      }
    } else {
      const rs = await insuranceStore.getUrlPvi((dataRenew?.paymentId || dataRenew?.ma_giaodich) + '', 'tndsxm')
      if (rs?.data) {
        openLink(rs?.data?.URL)
      }
    }
    hide()
  }

  const renderDiscount = () => {
    const discount = item?.attributes?.discount || 0
    const discountValue = (insuranceStore.rsTinhPhiTNDSXM?.phi_moto * discount) / 100
    return Math.ceil(((insuranceStore.rsTinhPhiTNDSXM?.phi_moto || 0) - discountValue) / 1000) * 1000
  }

  const onSubmit = async () => {
    if (step === 1) {
      setStep(2)
    }

    if (step === 2) {
      setIsSubmitting(true)
      if (!validateStep1()) {
        showError(t('FAIL'), t('FILL_OUT_THE_FORM'))
        setIsSubmitting(false)
      }
      if (!bankCode) {
        // open chọn bank nếu chưa chọn
        openBottomSheet(1)
      } else {
        const body = {
          ...objValue,
        }
        __DEV__ && console.log('body', body)
        const rsCreate = await insuranceStore.createUrlPayment({ totalAmount: renderDiscount() || 0, bankCode: bankCode })

        if (rsCreate && rsCreate.data?.data.vnpUrl) {
          const { vnpUrl, paymentId } = rsCreate.data?.data
          navigate(SCREENS.payment, {
            vnpUrl: vnpUrl,
            orderId: paymentId,
            paymentId: paymentId,
            typeBooking: 6,
            title: 'Thanh toán bảo hiểm'
          })
          // setTimeout(() => onGoBack(), 500)
        } else {
          showError(t('FAIL'), t(`${rsCreate.data?.error}`))
          setIsSubmitting(false)
        }
      }
      setIsSubmitting(false)
    }
  }

  const onSelectBank = (item) => {
    setBankCode(item)
    closeBottomSheet()
  }

  const openBottomSheet = (type) => {
    setSetBottomSheetType(type)
    bottomSheetRef?.current.open()
  }

  const closeBottomSheet = () => {
    setSetBottomSheetType(0)
    bottomSheetRef?.current.close()
  }

  const onChangeText = (field, value) => {
    setObjValue((prev) => ({ ...prev, [field]: value }))
  }

  const hidePicker = () => {
    setOpenNgayBatDau(false)
  }

  const calDurationDate = (y) => {
    const dateTime = date || new Date()
    onChangeText('ngay_dau', moment(dateTime).format('DD/MM/YYYY hh:mm'))
    onChangeText('ngay_cuoi', moment(dateTime).add(parseInt(y), 'y').format('DD/MM/YYYY hh:mm'))
  }

  const onChangeNgayBatDau = (date) => {
    const chooseDate = moment(new Date(date)) // choose date yyyy-mm-dd only
    const dateNow = moment(new Date())

    if (chooseDate.isSameOrAfter(dateNow)) {
      setDate(new Date(date).getTime())
      onChangeText('ngay_dau', moment(date).format('DD/MM/YYYY hh:mm'))
      onChangeText('ngay_cuoi', moment(date).add(parseInt(objValue.ThoiHan), 'y').format('DD/MM/YYYY hh:mm'))
      hidePicker()
    } else {
    // showError(t('FAIL'), t('Bạn không thể chọn ngày chưa tới'))
      SimpleToast.show('Bạn không thể chọn ngày đã qua')
      setDate(null)
      onChangeText('ngay_dau', '')
      onChangeText('ngay_cuoi', '')
      return Platform.OS === 'android' ? setOpenNgayBatDau(false) : null
    }
  }

  const onPressField = (action) => {
    if (action === 'ngay_bat_dau') {
      setOpenNgayBatDau(true)
    }

    if (action === 'chon_loai_xe') {
      loaiXeRef?.current.open()
    }
    if (action === 'chon_hang_xe') {
      hangXeRef?.current.open()
    }
    if (action === 'chon_so_nguoi') {
      soNguoiRef?.current.open()
    }
    if (action === 'muc_trach_nhiem') {
      mucTrachNhiemRef?.current.open()
    }
    if (action === 'han_muc') {
      hanMucRef?.current.open()
    }
  }

  const onGoBack = () => {
    if (!isRenew) {
      if (step === 1) {
        goBack()
      }
      if (step === 2) {
        setStep(1)
      }
    } else {
      goBack()
    }
  }

  const renderDetailRowLine = (label, value) => {
    return (
      <View style={styles.detailInfoRow}>
        <Text style={styles.label}>{label}</Text>
        {value === '' || value === null ? <Text style={styles.label}>Chưa có thông tin</Text> : <Text style={styles.value}>{ value}</Text>}
      </View>
    )
  }

  // const iconLeft = <Image source={icAddRed} style={{ width: 22, height: 22, marginRight: 8 }}></Image>
  const iconRight = <Icon name={'chevron-down-outline'} size={20} color={'#333'} style={{ marginRight: 8 }} />
  const iconCalender = <Icon name={'calendar'} size={20} color={color.primary} style={{ marginRight: 8 }} />

  const TouchField = (title, placeholder, action, leftIc, value) => {
    return (
      <View>
        <Text style={styles.titleLabel}>{title}</Text>
        <TouchableOpacity onPress={() => onPressField(action)} style={styles.viewProvince}>
          <View style={{ flexDirection: 'row' }}>
            {leftIc && iconCalender}
            {value === '' ? <Text style={styles.textProvince}>{placeholder}</Text> : <Text style={[styles.textProvince, { color: '#333' }]}>{value}</Text>}
          </View>
          {iconRight}
        </TouchableOpacity>
      </View>
    )
  }

  const getApiConfig = async () => {
    const api = new Api()
    const rs = await api.getAppConfig()
    if (rs && rs?.data?.data.attributes) {
      setPaymentGuideHtml(rs?.data?.data?.attributes?.content_guide_payment)
    }
  }

  const renderStep1 = () => {
    return (
      <View style={[styles.mainContainer, { paddingBottom: 20 }]}>
        <View paddingHorizontal={16}>
          <View flex={1} flexDirection="row" alignItems="center" justifuContent="space-between">
            <Text style={[styles.titleLabel, { paddingTop: 0, flex: 1 }]}>Thời hạn</Text>
            <View flexDirection="row">
              <TouchableOpacity style={[styles.btnSelectYear, selectedDuration[0] && styles.selectedItemStyles]} onPress={() => onSelectDuration(0)}>
                <Text style={[styles.txtBtnSelectYear, selectedDuration[0] && styles.txtSelectedItemStyles]}>1 năm</Text>
              </TouchableOpacity>
              <TouchableOpacity style={[styles.btnSelectYear, selectedDuration[1] && styles.selectedItemStyles, { marginHorizontal: 16 }]} onPress={() => onSelectDuration(1)}>
                <Text style={[styles.txtBtnSelectYear, selectedDuration[1] && styles.txtSelectedItemStyles]}>2 năm</Text>
              </TouchableOpacity>
              <TouchableOpacity style={[styles.btnSelectYear, selectedDuration[2] && styles.selectedItemStyles]} onPress={() => onSelectDuration(2)}>
                <Text style={[styles.txtBtnSelectYear, selectedDuration[2] && styles.txtSelectedItemStyles]}>3 năm</Text>
              </TouchableOpacity>
            </View>
          </View>
          <View flexDirection="row">
            <View flex={1} marginRight={12}>
              {TouchField('Ngày bắt đầu', 'Chọn ngày', 'ngay_bat_dau', 'ic', objValue.ngay_dau)}
            </View>
            <View flex={1}>
              {TouchField('Ngày kết thúc', 'Chọn ngày', 'ngay_ket_thuc', 'ic', objValue.ngay_cuoi)}
            </View>

          </View>
          <View flexDirection="row">
            <View flex={1}>
              {TouchField('Loại xe', 'Chọn loại xe', 'chon_loai_xe', null, loaiXe?.label || '')}
            </View>
          </View>
          { insuranceStore.catNameSelected == 'PVI' && <View flexDirection="row">
            <View flex={1}>
              {TouchField('Hãng xe', 'Chọn hãng xe', 'chon_hang_xe', null, hangXe?.label || '')}
            </View>
          </View> }
          { insuranceStore.catNameSelected == 'BIC' && <View flexDirection="row">
            <View flex={1}>
              {TouchField('Hạn mức tai nạn người ngồi trên xe', 'Chọn hạn mức', 'han_muc', null, hanMuc?.label || '')}
            </View>
          </View> }
          <View flexDirection="row">
            <View flex={1} marginRight={12}>
              <Text style={styles.titleLabel}>Biển số xe</Text>
              <TTextInput
                containerStyle={styles.textInputContainer}
                keyboardType='default'
                placeholderTextColor="#a0a0a0"
                placeholder={'Nhập biển số'}
                value={objValue.bien_kiemsoat}
                onChangeText={(e) => {
                  onChangeText('bien_kiemsoat', e.toUpperCase())
                }}
              />
            </View>
            <View flex={1}>
              <Text style={styles.titleLabel}>Xe chưa có biển số</Text>
              <View style={styles.textInput}>
                <CheckBox
                  checkedIcon='check-square'
                  uncheckedIcon='square-o'
                  checkedColor={color.primary}
                  checked={khongCoBienSo}
                  onPress={() => {
                    setKhongCoBienSo(!khongCoBienSo)
                  }}
                  textStyle={{ fontWeight: 'normal' }}
                  containerStyle={styles.checkBox}
                  size={20}
                />
              </View>
            </View>
          </View>
          {khongCoBienSo && <View flexDirection="row">
            <View flex={1} marginRight={12}>
              <Text style={styles.titleLabel}>Số khung</Text>
              <TTextInput
                containerStyle={styles.textInputContainer}
                keyboardType='default'

                placeholderTextColor="#a0a0a0"
                placeholder={'Nhập số khung'}
                value={objValue.so_khung}
                onChangeText={(e) => {
                  onChangeText('so_khung', e.toUpperCase())
                }}
              />
            </View>
            <View flex={1}>
              <Text style={styles.titleLabel}>Số máy</Text>
              <TTextInput
                containerStyle={styles.textInputContainer}
                keyboardType='default'

                placeholderTextColor="#a0a0a0"
                placeholder={'Nhập số máy'}
                value={objValue.so_may}
                onChangeText={(e) => {
                  onChangeText('so_may', e.toUpperCase())
                }}
              />
            </View>
          </View>}
          { insuranceStore.catNameSelected == 'PVI' && <CheckBox
            // iconType='ionicon'
            title={'Bảo hiểm tai nạn lái xe và người ngồi trên xe'}
            checkedIcon='check-square'
            uncheckedIcon='square-o'
            checkedColor={color.primary}
            checked={baoHiemNguoiNgoi}
            onPress={() => {
              setBaoHiemNguoiNgoi(!baoHiemNguoiNgoi)
              onChangeText('thamgia_laiphu', !baoHiemNguoiNgoi)
            }}
            textStyle={{ fontWeight: 'normal' }}
            containerStyle={{ padding: 0, margin: 0, marginLeft: 0, borderWidth: 0, marginTop: 20, backgroundColor: '#fff' }}
            size={20}
          />}
          {baoHiemNguoiNgoi && <>
            <View flexDirection="row">
              <View flex={1} marginRight={12}>
                {TouchField('Số người', 'Chọn số người', 'chon_so_nguoi', null, objValue.so_nguoi_tgia_laiphu)}
              </View>
              <View flex={1}>
                {TouchField('Mức trách nhiệm', 'Mức trách nhiệm', 'muc_trach_nhiem', null, objValue.muc_trachnhiem_laiphu)}
              </View>
            </View>
          </>}
        </View>

        <View marginTop={20} style={styles.divider}/>
        <View paddingHorizontal={16}>
          <>
            <Text style={styles.titleLabel}>Tên chủ xe (theo đăng ký xe)</Text>
            <TTextInput
              containerStyle={{ ...styles.textInputContainer }}
              placeholderTextColor="#a0a0a0"
              placeholder={'Nhập tên chủ xe'}
              value={objValue.ten_chuxe}
              onChangeText={(e) => {
                onChangeText('ten_chuxe', Capitalize(e || ''))
              }}
            />
          </>
          <>
            <Text style={styles.titleLabel}>Địa chỉ (địa chỉ theo đăng ký xe)</Text>
            <TTextInput
              containerStyle={styles.textInputContainer}

              placeholderTextColor="#a0a0a0"
              placeholder={'Nhập địa chỉ'}
              value={objValue.dia_chi}
              onChangeText={(e) => {
                onChangeText('dia_chi', Capitalize(e || ''))
              }}
            />
          </>
          <View flexDirection="row">
            <View flex={1}>
              <Text style={styles.titleLabel}>Email</Text>
              <TTextInput
                containerStyle={styles.textInputContainer}
                keyboardType='email-address'

                placeholderTextColor="#a0a0a0"
                placeholder={'Nhập email'}
                value={objValue.email}
                onChangeText={(e) => {
                  onChangeText('email', e)
                }}
              />
            </View>
          </View>
          <View flexDirection="row">
            <View flex={1}>
              <Text style={styles.titleLabel}>Số điện thoại</Text>
              <TTextInput
                containerStyle={styles.textInputContainer}
                keyboardType='phone-pad'

                placeholderTextColor="#a0a0a0"
                placeholder={'Nhập số điện thoại'}
                value={objValue.so_dienthoai}
                onChangeText={(e) => {
                  onChangeText('so_dienthoai', e)
                }}
              />
            </View>
          </View>
          { !isRenew && <>
            <Text style={styles.titleLabel}>Mã giới thiệu</Text>
            <TTextInput
              containerStyle={{ ...styles.textInputContainer, ...{ backgroundColor: '#FFF1F1', borderWidth: 1, borderColor: color.primary } }}
              // maxLength={4}
              placeholderTextColor="#a0a0a0"
              placeholder={'Nhập mã giới thiệu (nếu có)'}
              value={objValue.MaGioiThieu}
              onChangeText={(e) => {
                onChangeText('MaGioiThieu', e)
              }}
            />
          </> }

          <TermContent termContent={item?.attributes?.term} />

          <Text style={styles.txtRisk}>Quý khách hàng vui lòng chọn đúng loại xe, Doanh nghiệp Bảo hiểm và Đại lý không chịu trách nhiệm đối với việc chọn đăng ký thông tin xe không chính xác</Text>
        </View>
      </View>
    )
  }
  const renderStep2 = () => {
    return (
      <View style={[styles.mainContainer, { paddingBottom: 20 }]}>
        <View paddingHorizontal={16}>
          <Text style={styles.txtTitle}>Thông tin chủ sở hữu</Text>
          {renderDetailRowLine('Tên chủ xe', objValue.ten_chuxe)}
          {renderDetailRowLine('Số điện thoại', objValue.so_dienthoai)}
          {renderDetailRowLine('Email', objValue.email)}
          <View style={styles.detailInfoRow}>
            <Text style={styles.label}>Địa chỉ: <Text style={styles.value}>{objValue.dia_chi}</Text></Text>

          </View>
        </View>
        <View marginTop={20} style={styles.divider}/>
        <View paddingHorizontal={16}>
          <Text style={styles.txtTitle}>Thông tin bảo hiểm</Text>
          {renderDetailRowLine('Hãng xe', hangXe?.label || dataRenew?.HangXe)}
          {renderDetailRowLine('Loại xe', loaiXe?.label || dataRenew?.LoaiXe)}
          {renderDetailRowLine('Biển số xe', objValue?.bien_kiemsoat)}
          {/* {renderDetailRowLine('Mức trách nhiệm về người', objValue.MucTrachNhiem)} */}
          {/* {renderDetailRowLine('Mức trách nhiệm tài sản', objValue.MucTrachNhiem)} */}
          {renderDetailRowLine('Số người tham gia', objValue.so_nguoi_tgia_laiphu)}
          {objValue?.muc_trachnhiem_laiphu ? renderDetailRowLine('Mức trách nhiệm bảo hiểm', formatMoney(objValue?.muc_trachnhiem_laiphu || 0)) : null}
          {renderDetailRowLine('Ngày bắt đầu', objValue.ngay_dau)}
          {renderDetailRowLine('Ngày kết thúc', objValue.ngay_cuoi)}
          {renderDetailRowLine('Thời hạn bảo hiểm', objValue?.ThoiHan)}
        </View>
        { !isViewDetail && <>
          <View marginTop={20} style={styles.divider}/>
          <View paddingHorizontal={16}>
            <Text style={styles.txtRisk}>Bấm xác nhận đồng nghĩa với việc bạn đồng ý với <Text style={{ fontWeight: '700' }}>Điều kiện và điều khoản</Text> của maxQ và Đối tác cung cấp bảo hiểm.</Text>
            <View flexDirection="row" flex={1}>
              <TouchableOpacity style={[styles.btnPhuongThucTT, selectedPaymentMethod[0] && styles.selectedItemStylesPayment]} onPress={() => onSelectPaymentMethod(0)}>
                {selectedPaymentMethod[0] && <Icon style={{ marginRight: 8 }} name={'radio-button-on-sharp'} size={24} color={color.primary} />}
                <Text style={selectedPaymentMethod[0] && { fontWeight: '700' }}>Chuyển khoản</Text>
              </TouchableOpacity>
              <TouchableOpacity style={[styles.btnPhuongThucTT, styles.selectedItemStyles]}
                onPress={() => { openBottomSheet(2) }}>
                <Text>Hướng dẫn thanh toán</Text>
              </TouchableOpacity>
              {/* <TouchableOpacity style={[styles.btnPhuongThucTT, selectedPaymentMethod[1] && styles.selectedItemStylesPayment]} onPress={() => onSelectPaymentMethod(1)}> */}
              {/*  {selectedPaymentMethod[1] && <Icon style={{ marginRight: 8 }} name={'radio-button-on-sharp'} size={24} color={color.primary} />} */}
              {/*  <Text style={selectedPaymentMethod[1] && { fontWeight: '700' }}>Ví Momo, VN Pay</Text> */}
              {/* </TouchableOpacity> */}
            </View>
            <View flexDirection='row' flex={1} style={{ justifyContent: 'space-between', marginBottom: 20 }}>
              {selectedPaymentMethod[0] && <TouchableOpacity onPress={() => {
                openBottomSheet(1)
              }}><Text style={{ color: color.primary }}>Bấm chọn ngân hàng</Text></TouchableOpacity>}
              <View>
                {bankCode ? <Text>(Đã chọn thanh toán qua) {bankCode}</Text> : null}
              </View>
            </View>
          </View>
        </>}
      </View>
    )
  }

  return (
    <Fragment>
      <SafeAreaView style={styles.wrapper}>
        <Header
          leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={onGoBack} />}
          centerComponent={{ text: t(t('Bảo hiểm TNDS xe máy')), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
          containerStyle={common.headerContainer}
          statusBarProps={{ barStyle: 'light-content' }}
          ViewComponent={LinearGradient}
          linearGradientProps={linearGradientProps}
        />
        <KeyboardAwareScrollView showsVerticalScrollIndicator={false} >
          {step === 1 && renderStep1()}
          {step === 2 && renderStep2()}
        </KeyboardAwareScrollView>
        { !isViewDetail ? <>
          <View style={[styles.divider, { height: 2 }]} />
          <View>
            <View flexDirection="row" justifyContent="space-between" marginHorizontal={16} marginVertical={10}>
              <Text>Phí bảo hiểm</Text>
              <Text style={styles.oldPrice}>{numberFormat(insuranceStore.rsTinhPhiTNDSXM?.phi_moto)} đ</Text>
              <Text style={styles.price}>{formatMoney(renderDiscount())} đ</Text>
            </View>
            <View flexDirection="row" marginHorizontal={16} style={{ alignItems: 'center', marginVertical: 10 }}>
              <Image source={icCheckSquare} style={{ width: 16, height: 16, marginRight: 8 }}></Image>
              <Text>{`maxQ hỗ trợ ${item?.attributes.discount}% phí bảo hiểm TNDS`}</Text>
            </View>
            {step === 1 && <TButton loading={isSubmitting} disabled={isSubmitting || !validateStep1()} typeRadius={'rounded'} title={t('Tiếp tục')} onPress={onSubmit} buttonStyle={{ marginHorizontal: 16, marginVertical: 10 }} titleStyle={{ fontWeight: '500', fontSize: 16 }} />}
            {step === 2 && <TButton loading={isSubmitting} disabled={isSubmitting} typeRadius={'rounded'} title={t('Xác nhận và thanh toán')} onPress={onSubmit} buttonStyle={{ marginHorizontal: 16, marginVertical: 10 }} titleStyle={{ fontWeight: '500', fontSize: 16 }} />}
            {__DEV__ && <TButton loading={isSubmitting} typeRadius={'rounded'} title={t('Tiếp tục __DEV__')}
              onPress={onSubmit} buttonStyle={{ marginHorizontal: 16, marginVertical: 10 }}
              titleStyle={{ fontWeight: '500', fontSize: 16 }} />}
          </View>
        </> : <TouchableOpacity style={{ alignSelf: 'center' }} onPress={onViewOnline}>
          <Text style={{
            textAlign: 'center',
            alignItems: 'center',
            borderColor: color.primary,
            borderRadius: 3,
            borderWidth: 1,
            color: color.primary,
            justifyContent: 'center',
            margin: 6,
            marginBottom: 8,
            padding: 8,
          }}>Xem bản điện tử</Text>
        </TouchableOpacity>}
        <DateTimePickerModal
          isVisible={openNgayBatDau}
          // mode={pickerDate}
          locale="vi_VN"
          cancelTextIOS={t('CANCEL')}
          confirmTextIOS={t('XACNHAN')}
          onConfirm={onChangeNgayBatDau}
          onCancel={hidePicker}
          isDarkModeEnabled={false}
        />
        <BottomSheetPicker
          headerText={'Chọn loại xe'}
          items={insuranceStore.arrLoaiXeMotor}
          onSelectItem={(i) => {
            onChangeText('LoaiXe', i?.label)
            setLoaiXe(i)
            if (insuranceStore.catNameSelected == 'BIC') {
              setHanMuc(null)
              onChangeText('han_muc', '0')
              __DEV__ && console.log('insuranceStore.bicMotorData?.data?.hanmucbaohiem[i?.value', insuranceStore.bicMotorData?.data?.hanmucbaohiem[i?.value])
              __DEV__ && console.log('insuranceStore.bicMotorData?.data', insuranceStore.bicMotorData?.data)
              if (insuranceStore.bicMotorData?.data?.hanmucbaohiem[i?.value]) {
                insuranceStore.setArrHanMucTrachNhiem(insuranceStore.bicMotorData?.data?.hanmucbaohiem[i?.value])
              }
            }
          }}
          search={true}
          ref={loaiXeRef}
        />
        <BottomSheetPicker
          headerText={'Chọn hãng xe'}
          items={insuranceStore.arrHangXeMotor}
          onSelectItem={(i) => {
            onChangeText('HangXe', i?.label)
            setHangXe(i)
          }}
          search={true}
          ref={hangXeRef}
        />
        <BottomSheetPicker
          headerText={'Chọn số người'}
          items={[
            { label: '1 Người', value: '1' },
            { label: '2 Người', value: '2' },
          ]}
          onSelectItem={(i) => onChangeText('so_nguoi_tgia_laiphu', i.value)}
          ref={soNguoiRef}
        />
        <BottomSheetPicker
          headerText={'Chọn mức trách nhiệm'}
          items={[
            { label: '500,000', value: '500000' },
            { label: '1,000,000', value: '1000000' },
            { label: '2,000,000', value: '2000000' },
            { label: '3,000,000', value: '3000000' },
            { label: '4,000,000', value: '4000000' },
            { label: '5,000,000', value: '5000000' },
            { label: '6,000,000', value: '6000000' },
            { label: '7,000,000', value: '7000000' },
            { label: '8,000,000', value: '8000000' },
            { label: '9,000,000', value: '9000000' },
            { label: '10,000,000', value: '10000000' },
            { label: '20,000,000', value: '20000000' },
            { label: '30,000,000', value: '30000000' },
            { label: '40,000,000', value: '40000000' },
            { label: '50,000,000', value: '50000000' },
          ]}
          onSelectItem={(i) => onChangeText('muc_trachnhiem_laiphu', i.label)}
          ref={mucTrachNhiemRef}
        />
        <BottomSheetPicker
          headerText={'Hạn mức trách nhiệm người ngồi trên xe'}
          items={insuranceStore.arrHanMucTrachNhiem}
          onSelectItem={(i) => {
            setHanMuc(i)
            onChangeText('han_muc', i?.value || '0')
          }}
          ref={hanMucRef}
        />
        <Modalize
          HeaderComponent={<View style={{ paddingTop: 15 }}>
            <ButtonBack onPress={() => { closeBottomSheet() }} style={{ position: 'absolute', left: 16, top: 15, zIndex: 99999 }} />
            <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: 0 }}>
              <Text style={{ color: '#333', fontSize: 14 }}>
                { bottomSheetType === 1 && 'Chọn ngân hàng'}
                { bottomSheetType === 2 && 'Hướng dẫn thanh toán'}
              </Text>
            </View>
            <View style={{ marginTop: 15, height: 1, width: responsiveWidth(100), backgroundColor: '#f2f2f2' }}></View>
          </View>}
          ref={bottomSheetRef}
          modalHeight={responsiveHeight(80)}
          disableScrollIfPossible = {false}
          onClosed={() => { setSetBottomSheetType(0) }}
          keyboardAvoidingBehavior={'padding'}
        >
          <View style={{ flex: 1 }}>
            { bottomSheetType === 1 && <>
              <Payment onSelect={onSelectBank} oncloseModal={() => closeBottomSheet()} /></>}
            { bottomSheetType === 2 && paymentGuideHtml && <View style={{ marginHorizontal: 16, marginTop: 16 }}>
              <RenderHtml
                contentWidth={width}
                source={{ html: paymentGuideHtml }}
                ignoredTags={['script']}
                ignoredStyles={['font-family']}
                renderersProps={{
                  img: {
                    enableExperimentalPercentWidth: true
                  }
                }}
              />
            </View>}
          </View>

        </Modalize>
      </SafeAreaView>
    </Fragment>
  )
}, { forwardRef: true })

const styles = StyleSheet.create({
  btnPhuongThucTT: {
    alignItems: 'center',
    backgroundColor: '#F3F3F3',
    borderRadius: 5,
    borderWidth: 1,
    flexDirection: 'row',
    flex: 1,
    marginHorizontal: 8,
    marginVertical: 16,
    paddingHorizontal: 14,
    paddingVertical: 8,
  },
  btnSelectYear: {
    backgroundColor: '#F3F3F3',
    borderColor: '#979797',
    borderRadius: 5,
    borderWidth: 1,
  },
  checkBox: {
    alignSelf: 'center',
    borderWidth: 0,
    margin: 0,
    marginLeft: 0,
    padding: 0
  },
  detailInfoRow: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  divider: {
    backgroundColor: '#E6E6E6',
    height: 3
  },
  label: {
    color: '#A0A0A0',
    lineHeight: 20
  },
  mainContainer: {
    flex: 1,
    justifyContent: 'flex-start',
    paddingTop: 15,
  },
  oldPrice: {
    color: color.dim,
    textDecorationLine: 'line-through'
  },
  price: {
    color: color.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  selectedItemStyles: {
    backgroundColor: '#F1F1F1',
    borderColor: color.primary,
  },
  selectedItemStylesPayment: {
    // backgroundColor: '#F1F1F1',
    borderColor: color.primary,
    borderWidth: 1
  },
  textInput: {
    alignItems: 'center',
    backgroundColor: '#f3f3f3',
    borderRadius: 4,
    color: '#333',
    flexDirection: 'row',
    fontSize: 15,
    height: 40,
    justifyContent: 'space-between',
    marginTop: 10,
    paddingLeft: 14
  },
  textInputContainer: {
    height: 40,
    marginTop: 10,
  },
  textProvince: {
    alignSelf: 'center',
    color: '#a0a0a0',
    fontFamily: typography.normal,
    fontSize: 15
  },
  titleLabel: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    paddingTop: 20
  },
  txtBtnSelectYear: {
    marginHorizontal: 12,
    marginVertical: 10,
  },
  txtRisk: {
    color: color.primary,
    fontWeight: '500',
    paddingTop: 20
  },
  txtSelectedItemStyles: {
    color: color.primary,
    fontWeight: '700'
  },
  txtTitle: {
    color: '#3F3F3F',
    fontSize: 16,
    fontWeight: 'bold',
    marginVertical: 16
  },
  value: {
    color: '#2D384C',
    lineHeight: 20
  },
  viewProvince: {
    alignItems: 'center',
    backgroundColor: '#f3f3f3',
    borderRadius: 4,
    flexDirection: 'row',
    height: 40,
    justifyContent: 'space-between',
    marginTop: 10,
    paddingLeft: 14
  },
  wrapper: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
})
