import { Dimensions, StyleSheet } from 'react-native'
import { getStatusBarHeight } from 'react-native-iphone-x-helper'
import { color } from '../../theme'
const { width } = Dimensions.get('window')

const styles = StyleSheet.create({
  accName: {
    color: '#3f3f3f',
    textTransform: 'uppercase'
  },
  actionBtn: {
    height: 28,
    width: 30
  },
  balance: {
    color: 'white',
    fontSize: 36,
    fontWeight: 'bold'
  },
  cardNumber: {
    color: '#3f3f3f'
  },
  date: {
    color: '#9D9D9D'
  },
  header: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 30,
    paddingHorizontal: 16,
    paddingTop: 30,
  },
  headerTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600'
  },
  imageBg: {
    height: 310,
    width: width
  },
  label: {
    color: '#2D384C',
    marginVertical: 2
  },
  listTransaction: {
    borderBottomWidth: 0.5,
    borderColor: '#F3F3F3',
    paddingVertical: 10
  },
  sectionHomeProduct: {
    backgroundColor: color.primaryBackground,
    marginTop: 5,
    paddingBottom: 10
  },
  statusView: {
    alignItems: 'center',
    backgroundColor: '#78AF41',
    borderRadius: 3,
    height: 30,
    justifyContent: 'center',
    paddingHorizontal: 10,
    paddingVertical: 4
  },
  top: {
    marginTop: -(getStatusBarHeight())
  },
  topActionBtn: {
    alignItems: 'center',
    backgroundColor: 'white',
    borderColor: '#D0D0D0',
    borderRadius: 10,
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginHorizontal: 16,
    marginTop: -40,
    paddingVertical: 10
  },
  txtBtn: {
    color: '#3F3F3F',
    marginTop: 5
  }
})
export default styles
