import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import React, { useState, useEffect, useRef } from 'react'
import {

  View,

  Image,
  Text,
  TouchableOpacity,
  ImageBackground,
  ScrollView,
  StatusBar, useWindowDimensions

} from 'react-native'

import styles from './styles'
import { useTranslation, withTranslation } from 'react-i18next'
import { useNavigation } from '@react-navigation/native'
import { Modalize } from 'react-native-modalize'
import { observer } from 'mobx-react-lite'
import { useStores } from '@app/models'
import moment from 'moment'
import 'moment/locale/vi'
import { SCREENS } from '@app/navigation'

import { useAbortableEffect } from '@app/use-hooks'
import { useAuth } from '@app/use-hooks/use-auth'
import { Api } from '@app/services/api'
import { walletIcon } from '@app/assets/images'
import { numberFormat } from '@app/utils/number'
import { ButtonBack } from '@app/components'
import { getStatusBarHeight } from 'react-native-iphone-x-helper'

const api = new Api()

moment.locale('vi')

const data = [
  { date: '01/01/2022', tk: '0101893888', gd: 5000000, sdc: 300000000, nd: 'Noi dung chuyen tien tu so tai khoan' },
  { date: '01/01/2022', tk: '0101893888', gd: -5000000, sdc: 300000000, nd: 'Noi dung chuyen tien tu so tai khoan' },
  { date: '01/01/2022', tk: '0101893888', gd: -5000000, sdc: 300000000, nd: 'Noi dung chuyen tien tu so tai khoan' },
  { date: '01/01/2022', tk: '0101893888', gd: -5000000, sdc: 300000000, nd: 'Noi dung chuyen tien tu so tai khoan' },
  { date: '01/01/2022', tk: '0101893888', gd: -5000000, sdc: 300000000, nd: 'Noi dung chuyen tien tu so tai khoan' },
  { date: '01/01/2022', tk: '0101893888', gd: -5000000, sdc: 300000000, nd: 'Noi dung chuyen tien tu so tai khoan' },
  { date: '01/01/2022', tk: '0101893888', gd: -5000000, sdc: 300000000, nd: 'Noi dung chuyen tien tu so tai khoan' },
  { date: '01/01/2022', tk: '0101893888', gd: -5000000, sdc: 300000000, nd: 'Noi dung chuyen tien tu so tai khoan' },
]

export const TransactionHistory = observer((props:any) => {
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()

  const modalizeAddService = useRef<Modalize>(null)

  const [isFetched, setIsFetched] = useState(true)
  const navigation = useNavigation()
  const { serviceStore, bookingStore, profileStore, notificationStore, homeStore } = useStores()

  const { signOut } = useAuth() // should be signUp

  const [bgImageUrl, setBgImageUrl] = useState<any>('')
  const { width } = useWindowDimensions()
  const bgImageHeight = 190 * (width / 375)

  useEffect(() => {
    return () => {

    }
  }, [])

  const getApiData = async () => {
    return Promise.all([
      getApiConfig(),
    ])
  }

  const getApiConfig = async () => {
    const rs = await api.getAppConfig()

    if (rs && rs?.data?.data.attributes) {
      setBgImageUrl(rs?.data?.data?.attributes?.bg_header_user_wallet.data.attributes.url)
    }
  }

  const loadData = async () => {
    setIsFetched(true)
    getApiData().then(data => {
      setIsFetched(false)
    }).catch(err => {
      __DEV__ && console.log(err)
    })
  }

  const goLoginScreenRequired = () => {
    navigation.navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  const logOut = async () => {
    try {
      profileStore.clearFields()
      notificationStore.clearFields()
      signOut() // gọi hàm signout global
      navigate(SCREENS.homeStack)
    } catch (e) {
      __DEV__ && console.log(e)
    }
  }

  const onCloseAddService = () => {
    modalizeAddService.current?.close()
  }

  useAbortableEffect(() => {
    loadData().then(r => {})
  }, [])

  const renderHeader = () => {
    return (<View style={styles.top}>
      {bgImageUrl && <ImageBackground style={{ width: width, height: bgImageHeight, borderRadius: 24, overflow: 'hidden' }} source={{ uri: bgImageUrl }}>
        <View style={styles.header}>
          <ButtonBack style={{ color: '#fff', position: 'absolute', left: 16, top: getStatusBarHeight() }} onPress={goBack}/>
          <Text style={styles.headerTitle}>Tài khoản</Text>
        </View>
        <View style={{ alignItems: 'center', marginTop: 5 }}>
          <Text style={{ color: 'white' }}>Số dư thẻ</Text>
          <Text style={styles.balance}>0<Text style={{ fontSize: 24 }}>đ</Text></Text>
        </View>
      </ImageBackground>}
    </View>
    )
  }

  return (
    <SafeAreaView style={{ flex: 1, marginTop: -useSafeAreaInsets().top + 20 }}>
      <StatusBar backgroundColor='transparent' barStyle={'dark-content'} />
      <View style={{ backgroundColor: 'white', flex: 1 }}>
        {renderHeader()}
        <View style={styles.topActionBtn}>
          <TouchableOpacity style={{ alignItems: 'center' }}>
            <Image source={walletIcon} style={styles.actionBtn} ></Image>
          </TouchableOpacity>
          <View>
            <Text style={styles.cardNumber}>01010007876666</Text>
            <Text style={styles.accName}>DINH MANH TRUNG</Text>
            <Text style={styles.cardNumber}>Thẻ Sống khỏe</Text>
          </View>
          <TouchableOpacity style={styles.statusView}>
            <Text style={{ color: 'white' }}>Hoạt động</Text>
          </TouchableOpacity>
        </View>
        <ScrollView style={{ marginTop: 16 }}>
          {data.map((item, index) => {
            return (
              <View style={styles.listTransaction} key={index}>
                <View style={{ marginHorizontal: 16 }}>
                  <Text style={styles.date}>{item.date}</Text>
                  <Text style={styles.label}>TK: <Text>{item.tk}</Text></Text>
                  <Text style={styles.label}>DG: <Text style={{ fontWeight: 'bold', color: item.gd < 0 ? '#D42023' : '#78AF41' }}>{numberFormat(item.gd)} đ</Text></Text>
                  <Text style={styles.label}>SDC: <Text style={{ fontWeight: 'bold' }}>{numberFormat(item.sdc)} đ</Text></Text>
                  <Text style={styles.label}>ND: <Text>{item.nd}</Text></Text>
                </View>
              </View>
            )
          })}
        </ScrollView>
      </View>
    </SafeAreaView>
  )
})

export default withTranslation()(TransactionHistory)
