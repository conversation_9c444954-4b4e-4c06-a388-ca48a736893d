/* eslint-disable */
import { Dimensions, Platform, StatusBar, StyleSheet } from 'react-native'
import { ifIphoneX } from "react-native-iphone-x-helper"
import { color, typography } from '../../theme'
import {  responsiveWidth } from 'react-native-responsive-dimensions'
import { palette } from '@app/theme/palette'
const { width } = Dimensions.get('window')

const freeShipIcRatio = 55 / 14
const saleIconRatio = 38 / 14

const styles = StyleSheet.create({
  ImageComponentStyle1: {
    borderRadius: 0,
    // marginTop: 15,
  },
  ImageComponentStyle2: {
    // borderBottomRightRadius: 15,
    // borderBottomLeftRadius: 15,
  },
  badge:{
    backgroundColor: 'red',
    minWidth: 20,
    height: 20,
    borderRadius: 20,
    position: 'absolute',
    top: -10,
    right: -8,
    // alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#fff',
  },
  viewIconAdd: { width: 60,
    height: 60,
    borderRadius: 50,
    backgroundColor: '#f6f6f6',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center'
  },
  navigateLogin: {
    flexDirection: 'row'
  },
  sectionHomeProduct: {
    backgroundColor: '#fff',
    marginTop: 5,
    paddingBottom: 10
  },
  sectionBannerHome2:{
    paddingVertical: 10,
    marginVertical: 5,
    backgroundColor: '#fff'
  },
  viewIcRight: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',

  },
  textSaleOff: {
    fontFamily: typography.normal,
    fontSize: 10,
    color: color.primary,
    marginLeft: 8
  },
  btnViewMore: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    padding: 5,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#E4E4E4'
  },
  iconViewMore: {
    color: color.primary,
    marginTop: 1,
    marginLeft: 3
  },
  textTop: {
    color: color.primary,
    fontSize: 12,
    justifyContent: 'flex-end',
    letterSpacing: 0,
    textAlign: 'right',
  },
  titleProd: {
    flex: 1,
    flexDirection: 'row',
    color: '#333',
    fontSize: 17,
    fontWeight: '600',
    fontFamily: typography.bold,
  },
  textNewPrice: {
    paddingVertical: 4,
    fontFamily: typography.bold,
    fontSize: 12,
    color: '#333',
  },
  textOldPrice: {
    fontFamily: typography.normal,
    fontSize: 10,
    color: '#9D9D9D',
    textDecorationLine: 'line-through'
  },
  textBtnAdd: {
    fontSize: 12,
    alignItems: 'center',
    borderColor: color.primary,
    borderRadius: 3,
    borderWidth: 1,
    color: color.primary,
    justifyContent: 'center',
    margin: 8,
    marginBottom: 8,
    padding: 6,
    textAlign: 'center'
  },
  touchItem: {
    width: 120,
    // height: 245,
    marginVertical: 4,
    marginRight: 8,
    borderRadius: 5,
    backgroundColor: '#f7f7f7'
  },
  header: {
    backgroundColor: '#df1b23',
    height: 50,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingTop: 8
  },
  // imgProd: {
  //   width: 120,
  //   height: 120,
  //   borderTopLeftRadius: 8,
  //   borderTopRightRadius: 8
  // },
  textName: {
    width: 110 ,
    fontSize: 12,
    color: '#333',
    height: 30,
    fontFamily: typography.normal,
  },
  textSave: {
    color: color.primary,
    fontFamily: typography.normal,
    fontSize: 15,
    fontWeight: '600',
    marginTop: 2,
    textAlign: 'right'
  },
  petAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30
  },
  icAdd: {
    width: 22,
    height: 22,
  },
  viewFullName: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
    marginBottom: 10,
  },
  viewLabel: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 5,
    marginBottom: 10,
  },
  sectionProfile: {
    backgroundColor: '#fff',
    height: 120,
    marginHorizontal: 8,
    borderRadius: 10,
    marginBottom: 15,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 5,
    elevation: 2,
    justifyContent: 'center',
    marginTop: -80
  },
  SectionListCar: {
    backgroundColor: '#fff',
    height: 150,
    marginHorizontal: 16,
    borderRadius: 4,
    marginBottom: 10,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 2,
    justifyContent: 'center',
    paddingHorizontal: 10,
    marginVertical: 15
  },
  petName: {
    color: '#333',
    fontSize: 12,
    marginTop: 8,
    maxWidth: 100,
    marginBottom: 8,
    textAlign: 'center'
  },
  text: {
    color: '#333',
    fontSize: 12,
    marginTop: 8,
  },
  imgIcon: {
    width: 35,
    height: 35
  },
  imgProd: {
    width: 140,
    height: 140
  },
  fullName: {
    color: '#333',
    fontSize: 17,
    fontWeight: '600',
    fontFamily: typography.bold,
    alignSelf: 'center',
  },
  iconLogin: {
    alignSelf: 'center',
    marginRight: 4,
    marginLeft: -4
  },
  address: {
    marginLeft: 4,
    fontFamily: typography.normal,
    fontSize: 12,
    fontWeight: '500'
  },
  touchIcon: {
    alignItems: 'center',
    marginHorizontal: 15
  },
  touchIconPet: {
    alignItems: 'center',
    // marginLeft: 10,
    marginRight: 38
  },
  slide: {
    width: '100%',
    paddingHorizontal: 4
  },
  image: {
    height: 100,
    width: '100%',
    borderRadius: 4,
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
    fontFamily: typography.normal,
    marginRight: -20,
    marginTop: 3
  },
  boxContainerModal: {
    marginLeft:15,
    marginRight:15,
    marginTop:10,
    ...ifIphoneX({
      paddingBottom: 89,
    }, {
      paddingBottom: 60,
    }),
  },
sectionCategory: {
    backgroundColor: '#fff',
    paddingVertical: 10
},
  tabBar: {
    // Remove border top on both android & ios
    backgroundColor: '#fff',
    borderTopColor: 'transparent',
    borderTopWidth: 0,
    elevation: 0,
    shadowColor: '#5bc4ff',
    shadowOffset: {
      height: 0,
    },
    shadowOpacity: 0,
    shadowRadius: 0,
  },
  tabBarText: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 16,
  },

  selectProvince: {
    fontFamily: typography.normal,
    fontSize: 14,
    color: '#333',
    marginTop: 10,
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
    // backgroundColor: palette.pink100
  },
  bigText: {
    fontFamily: typography.normal,
    alignItems: 'center',
    color: '#fff',
    flexDirection: 'row',
    fontSize: 30,
    marginLeft: 25
  },
  dotStyle1: {
    borderRadius: 5,
    height: 2,
    marginHorizontal: -5,
    margin: 0,
    padding: 0,
    width: 8
  },
  dotStyle2: {
    borderRadius: 5,
    height: 2,
    marginHorizontal: -5,
    margin: 0,
    padding: 0,
    width: 8
  },
  icSearch: {
    height: 18,
    width: 18,

  },
  imageStyleIMGbackgroud: {
    borderRadius: 4
  },
  input: {
    flex: 1,
    color:"#fff",
    fontSize: 12
  },
  inputStyle: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 25,
    flexDirection: 'row',
    height: 36,
    width: 100,
    justifyContent: 'center',
    paddingHorizontal: 8

  },
  paginationBoxStyle1: {
    alignItems: "center",
    alignSelf: "center",
    bottom: 6,
    justifyContent: "center",
    paddingVertical: 0,
    position: "absolute",
  },
  paginationBoxStyle2: {
    alignItems: "center",
    alignSelf: "center",
    bottom: 6,
    justifyContent: "center",
    paddingVertical: 0,
    position: "absolute",
  },
  placeholderContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
    elevation: 1,
    marginBottom: 20,
    marginTop: 10,
    paddingBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 3, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    width: '100%',
  },
  placeholderDanhmuc: {
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
    justifyContent: 'center',
    minHeight: 80,
    paddingLeft: 20,
    padding: 3,
    width: 30,
  },
  rdDanhmuc: {
    backgroundColor: "#ffffff",
    borderRadius: 8,
    height: 197,
    position: 'relative',
    shadowColor: "#e6e8ef",
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 1,
    shadowRadius: 10,
    width: 138,
    elevation: 2
  },
  rdDanhmucImage: {
    borderRadius: 8,
    height: 100,
    width: 138,
  },
  rdDanhmucText: {
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 11,
    height: 40,
    marginTop: 9,
    textAlign: 'center'
  },
  rdSeparator: {
    width: 10,
  },
  rdTTtop: {
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
    elevation: 1,
    marginBottom: 20,
    paddingBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 3, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    width: '100%'
  },
  rdTTtopImage: {
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    height: 160,
    resizeMode: 'cover',
    width: '100%'
  },
  rdTTtopText: {
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold',
    paddingLeft: 15,
    paddingRight: 15,
    paddingTop: 15
  },
  rdTTtopText1: {
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 13,
    paddingLeft: 15,
    paddingRight: 15,
    paddingTop: 15,
  },
  rdTTtopText2: {
    color: '#999999',
    fontFamily: typography.normal,
    fontSize: 11,
    paddingHorizontal: 8,
    paddingTop: 15
  },
  rdView: {
    flex: 1,
    // paddingBottom: 8,
    // marginTop: 5,
    backgroundColor: '#fff'
  },
  safeAreaView: {
    flex: 1,
    paddingBottom: 60,
    backgroundColor: '#fff',
    marginTop: 0,
  },
  AndroidSafeArea: {
    backgroundColor: '#fff',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0
  },
  rdView1: {
    // borderRadius: 4,
    // paddingHorizontal: 8,
    // backgroundColor: 'orange'
  },
  rdView1Text: {
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 15,
    fontWeight: 'bold',
    marginLeft: 5,
    marginTop: 20
  },
  rdView2: {
    flex: 1,
    marginTop: 10,
    padding: 0
  },
  rdView2Top: {
    flex: 1,
  },
  rdView2Top1: {
    flex: 1,
    marginBottom: 10,
    marginTop: 10
  },
  rdView2Top2: {
    flex: 1,
    width: '100%'
  },
  rdView3: {
    paddingLeft: 20,
    paddingRight: 20
  },
  rdView3Text: {
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 15,
    fontWeight: 'bold',
    marginTop: 20
  },
  rdView4: {
    paddingLeft: 20,
    paddingRight: 20
  },
  rdView4Image: {
    alignSelf: 'center',
    height: 200,
    resizeMode: 'stretch',
    width: '100%'
  },
  rdView4Text: {
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 15,
    fontWeight: 'bold',
    marginTop: 20,
  },
  rdView4Text1: {
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 15,
    fontWeight: 'bold',
    marginTop: 20,
    textAlign: 'right'
  },
  rdView4TextContainerStyle: {
    flex: 1,
    marginTop: 20
  },
  renderTopSection: {
    width: responsiveWidth(100),
    zIndex: 9999,
    paddingVertical: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  searchInput: {
    flex: 1,
    flexDirection: 'row'
  },
  smallText: {
    fontFamily: typography.normal,
    alignItems: 'center',
    color: '#fff',
    flexDirection: 'row',
    fontSize: 20,
    marginLeft: 25
  },
  txtGiamgia: {
    fontFamily: typography.normal,
    backgroundColor: 'red',
    borderRadius: 8,
    bottom: 100,
    color: '#fff',
    fontSize: 14,
    left: 10,
    position: 'absolute'
  },
  txtTop: {
    marginBottom: 10,
    marginLeft: 16,
    marginTop: 5
  },
  viewAddress: {
    // flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10,
    height:36,
    borderWidth: 0,
    // borderColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 30,
    paddingHorizontal: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  textCountBr: {
    paddingBottom:5,
    fontSize:12,
    paddingTop:5,
    color:palette.lightGrey,
  },
  viewProvince: {
    borderBottomWidth:1,
    borderColor:palette.lightGrey
  },
  //TOUR
  activeSwitchContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    paddingHorizontal: 40,
  },
  button: {
    backgroundColor: '#2980b9',
    margin: 2,
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  buttonText: {
    color: 'white',
    fontSize: 15,
  },
  container: {
    // alignItems: 'center',
    // backgroundColor: 'red',
    // flex: 1,
    // height: responsiveHeight(100),
    // paddingTop: 40,
  },
  middleView: {
    // alignItems: 'center',
    // flex: 1,
  },
  profilePhoto: {
    borderRadius: 70,
    height: 140,
    marginVertical: 20,
    width: 140,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15,
    width: '100%',
  },
  title: {
    fontSize: 24,
    textAlign: 'center',
  },
  userAvatar: {
    width: 60,
    height: 60,
    borderRadius: 40,
    marginBottom: 4,
  },
  freeShipIc: {
    width: 15 * freeShipIcRatio,
    height: 15,
    marginRight: 8
  },
  saleIcon: {
    width: 15 * saleIconRatio,
    height: 15,
  },
  discountViewRow: {
    bottom: 5,
    flexDirection: 'row',
    left: 5,
    position: 'absolute'
  },
  xinchao: {
    fontSize: 14,
    color: '#ffffff',
    textAlign: 'left',
    textAlignVertical: 'top',
  },
  nguyenthuhien: {
    fontSize: 16,
    color: '#ffffff',
    textAlign: 'left',
    textAlignVertical: 'top',
    fontWeight: 'bold'
  },
  group1000002803: {
    width: 266,
    height: 60,
    marginLeft: 20
  },
})


export default styles
