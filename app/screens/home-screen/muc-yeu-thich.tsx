import { Alert, Dimensions, FlatList, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useContext } from 'react'

import { ModalContext } from '@app/context'
import { SCREENS } from '@app/navigation'
import { useNavigation } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'

import { color, spacing, typography } from '@app/theme'
import { useStores } from '@app/models'
import FastImage from 'react-native-fast-image'
import { observer } from 'mobx-react-lite'
import { InAppBrowser } from 'react-native-inappbrowser-reborn'

const MucYeuThich = observer((props: any) => {
  const { t } : any = useTranslation()
  const { showSuccess } = useContext(ModalContext)
  const { searchStore, profileStore } = useStores()
  const { navigate } = useNavigation()

  __DEV__ && console.log('render Categories MucYeuThich', props)

  const openLink = async (link) => {
    if (link) {
      try {
        await InAppBrowser.close()
        await InAppBrowser.open(link, {
          toolbarColor: color.primary,
          dismissButtonStyle: 'close',
          preferredBarTintColor: color.primary,
          preferredControlTintColor: '#fff',
          readerMode: false,
          enableUrlBarHiding: false,
          enableDefaultShare: false,
          animated: true,
          showTitle: false,
          // modalPresentationStyle: 'formSheet',
          modalTransitionStyle: 'coverVertical',
          modalEnabled: false,
          enableBarCollapsing: false,
        })
      } catch (error) {
        Alert.alert(error.message)
      }
    }
  }

  const goLoginScreenRequired = () => {
    navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  const onClickCat = async (item) => {
    if (item?.nav) {
      navigate(item?.nav)
    }
  }

  const renderIconCategories = ({ item }) => {
    console.log('renderIconCategories', item)
    return (
      <TouchableOpacity
        onPress={() => onClickCat(item)}
      >
        <View style={styles.viewDanhMuc}>
          <FastImage style={styles.imageCat} source={item.img}/>
          <Text style={styles.itemName}>{item?.name}</Text>
        </View>
      </TouchableOpacity>
    )
  }

  return (<View>
    {props?.data?.length > 0 ? <View>
      <View style={styles.frame1000002804}>
        <Text style={styles.chucnanguthich}>Chức năng ưa thích</Text>
        <Text style={styles.xemtatca}>Xem tất cả</Text>
      </View>
      <ScrollView
        horizontal
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
      >
        <FlatList
          style={{ marginLeft: spacing.small }}
          scrollEnabled={false}
          contentContainerStyle={{ backgroundColor: '#fff' }}
          showsHorizontalScrollIndicator={true}
          // horizontal={true}
          key={Math.ceil(props.data.length / 2)}
          data={props.data}
          numColumns={Math.ceil(props.data.length / 2)}
          keyExtractor={(e, i) => (i + 1).toString()}
          renderItem={renderIconCategories} />
      </ScrollView>
    </View> : null}
  </View>
  )
})

export default MucYeuThich

const { width } = Dimensions.get('window')
const styles = StyleSheet.create({

  // imageCat: {
  //   borderColor: '#9E9E9E',
  //   borderRadius: 10,
  //   borderWidth: 0.5,
  //   flex: 1,
  //   height: 45,
  //   width: 45
  // },
  imageCat: {
    // borderRadius: 18,
    height: 45,
    width: 45
  },
  itemName: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 13,
    marginTop: 5,
    width: 80,
    textAlign: 'center'
  },

  viewDanhMuc: {
    alignItems: 'center',
    flex: 1,
    height: 100,
    marginBottom: 0,
    paddingVertical: 8,
    width: (width / 3) - 10,
  },

  chucnanguthich: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#0a2540',
    textAlign: 'left',
    textAlignVertical: 'top'
  },
  xemtatca: {
    fontSize: 14,
    fontWeight: '700',
    color: '#002845',
    textAlign: 'left',
    textAlignVertical: 'top'
  },
  frame1000002804: {
    marginTop: 20,
    marginHorizontal: 30,
    marginBottom: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
})
