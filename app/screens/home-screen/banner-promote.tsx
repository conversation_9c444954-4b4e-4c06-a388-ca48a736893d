import { View } from 'react-native'
import styles from '@app/screens/home-screen/style'
import { LazyImage } from '@app/components'
import React from 'react'
import { responsiveWidth } from 'react-native-responsive-dimensions'
import { SliderBox } from 'react-native-image-slider-box'
import { SCREENS } from '@app/navigation'
import { useNavigation } from '@react-navigation/native'
import FastImage from 'react-native-fast-image';

const responsiveImage = (width, height) => {
  const screenWidth = responsiveWidth(100) - 30
  return {
    width: Math.floor(screenWidth),
    height: Math.floor(height * (screenWidth / width))
  }
}

const BannerPromote = ({ images, promotions }) => {
  const { navigate } = useNavigation()
  return (
    <View style={{ marginTop: 10 }}>
      <SliderBox
        inactiveSlideScale={1}
        inactiveSlideOpacity={1}
        images={images}
        onCurrentImagePressed={index => {
          __DEV__ && console.log(`BannerPromote image ${index} pressed`)
          const item = promotions[index]
          navigate(SCREENS.promotionDetailScreen, { id: item._id })
          // Linking.openURL(item.link)
        }
        }
        dotColor="pink"
        inactiveDotColor="#90A4AE"
        paginationBoxVerticalPadding={20}
        autoplay={true}
        circleLoop={true}
        resizeMode={FastImage.resizeMode.stretch}
        paginationBoxStyle={styles.paginationBoxStyle1}
        dotStyle={styles.dotStyle1}
        ImageComponent={LazyImage}
        imageLoadingColor={'#fff'}
        ImageComponentStyle={{ ...styles.ImageComponentStyle2, ...responsiveImage(345, 102) }}
      />
    </View>
  )
}

export default React.memo(BannerPromote)
