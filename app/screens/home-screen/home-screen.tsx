import {
  Image,
  Text,
  TouchableOpacity,
  View,
  Platform, Dimensions, Linking, FlatList, RefreshControl
} from 'react-native'
import {
  bgHome, icGuest,
  iconAdd, icQrCode, imgChuaChonCongTy,
} from '@app/assets/images'
import React, { useContext, useEffect, useRef, useState } from 'react'
import styles from './style'
import { useTranslation } from 'react-i18next'
import { loadString, remove, saveString } from '@app/utils/storage'

import { useStores } from '@app/models'
import { observer } from 'mobx-react-lite'
import { useNavigation } from '@react-navigation/native'
import { SCREENS } from '@app/navigation'
import { useLoading, LazyImage, PlaceHolder } from '@app/components'
import { Modalize } from 'react-native-modalize'
import Icon from 'react-native-vector-icons/Ionicons'
import { LogEvent } from '@app/services/loggingServices'
import {
  useTourGuideController,
} from 'rn-tourguide'
import { useWhyDidYouUpdate } from '@app/use-hooks'
// import BannerPromote from '@app/screens/home-screen/banner-promote'
import { TabServicesRenderScreen } from '@app/screens'
import { TabBar } from 'react-native-tab-view'
import Carousel from 'react-native-snap-carousel'
import _ from 'lodash'
import { color } from '@app/theme'
// import { Badge } from 'react-native-elements'
import { useAuth } from '@app/use-hooks/use-auth'
import { BookingType } from '@app/constants/bookingType'
import { ModalContext } from '@app/components/modal-success'
import validate from 'validate.js'
import { useForceUpdate } from '@app/use-hooks/useForceUpdate'
import FastImage from 'react-native-fast-image'
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import { isAndroid, isIOS } from '@app/utils/chat/deviceInfo'
import { Api } from '@app/services/api'
// import { getBadgeCount, getNotificationBadgeSetting, setBadgeCount } from 'react-native-notification-badge'
import { Badge, Header } from 'react-native-elements'
import Animated, {
  useSharedValue,
  useAnimatedScrollHandler,
  useAnimatedStyle, interpolateColor, interpolate,
} from 'react-native-reanimated'
import DeviceInfo from 'react-native-device-info'
import { dsDonVi } from '@app/models/home-store/data'
import MucYeuThich from '@app/screens/home-screen/muc-yeu-thich'
import { dsDoanhNghiep, dsYeuThich } from '@app/screens/home-screen/data'
import DanhSachDoanhNghiep from '@app/screens/home-screen/danh-sach-doanh-nghiep'

const initialLayout = { width: Dimensions.get('window').width }
const HEADER_EXPANDED_HEIGHT = 53
const G_WIN_WIDTH = Dimensions.get('window').width

const SectionListCar = observer(({ cars }: any) => {
  const { navigate, goBack } = useNavigation()
  const { profileStore } = useStores()
  const { t } : any = useTranslation()
  __DEV__ && console.log('render SectionListCar')

  const goLoginScreenRequired = () => {
    navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  const goToAddPet = () => {
    if (profileStore.isSignedIn()) {
      // onOpenModalAddPet()
      navigate(SCREENS.addCar)
    } else {
      goLoginScreenRequired()
    }
  }

  const onClickPet = (item) => {
    if (item.id === 'add') {
      goToAddPet()
    } else {
      // navigate(SCREENS.editCar, { item })
      navigate(SCREENS.petProfileScreen, { item })
    }
  }

  const renderCarItem = ({ item }) => {
    // __DEV__ && console.log(`renderCarItem ${item?.attributes?.image?.data?.attributes?.url}`)
    return (
      <TouchableOpacity onPress={() => onClickPet(item)}
        style={styles.touchIconPet}>
        {item?.attributes?.image?.data ? <FastImage style={styles.petAvatar} source={{ uri: `${item.attributes.image.data.attributes.url}` }}/> : <Image source={{ uri: 'data:image/jpeg;base64,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' }}/>}
        {item.id === 'add' ? <View style={styles.viewIconAdd}><Image style={styles.icAdd} source={item.photo}/></View> : null}
        <Text numberOfLines={3} style={styles.petName}>{item?.attributes?.name ? item.attributes.name : item.name}</Text>
      </TouchableOpacity>
    )
  }

  return (
    <View style={{ backgroundColor: '#fff' }}>
      <View style={styles.SectionListCar}>
        <View style={styles.viewLabel}>
          <Text style={styles.fullName}>{t('Your_Car')}</Text>
        </View>
        <View>
          {cars?.length > 0 ? <FlatList
            horizontal={true}
            data={cars}
            extraData={cars}
            showsHorizontalScrollIndicator={false}
            renderItem={renderCarItem}
            keyExtractor={item => item.id + 1}/> : <TouchableOpacity onPress={() => goToAddPet()} style={{ alignItems: 'center' }}>
            <View style={styles.viewIconAdd}><Image style={styles.icAdd} source={iconAdd}></Image></View>
            <Text style={styles.text}>{t('add_pet')}</Text>
          </TouchableOpacity>}
        </View>
      </View>
    </View>
  )
})

const CarouselHome = observer((props: any) => {
  const { navigate, goBack } = useNavigation()
  const { profileStore } = useStores()
  const { t } : any = useTranslation()
  __DEV__ && console.log('CarouselHome ')

  const onPressImage = (item, navigate) => {
    if (item?.screen) {
      const params = item?.params || ''
      const trueParams = params !== '#'
      const isValidURL = validate({ website: params }, { website: { url: true } })
      switch (item.screen) {
        case 'PROMOTION_DETAIL_SCREEN':
          if (trueParams) {
            navigate(item.screen, { id: params })
          }
          break
        case 'SHOPPING_STACK':
          navigate(item.screen)
          break
        case 'PRODUCT_DETAILS':
          if (trueParams) {
            navigate(item.screen, { id: params })
          }
          break
        case 'BLOG_DETAIL_SCREEN':
          if (trueParams) {
            navigate(item.screen, { id: params })
          }
          break
        case 'SERVICE_DETAIL':
          if (trueParams) {
            navigate(item.screen, { id: params })
          }
          break
        case 'POPUP_SERVICE_OF_BRAND':
          if (params && item?.screen_param_spaId) {
            navigate(SCREENS.serviceDetail, { id: params, spaId: item?.screen_param_spaId })
          }
          break
        case 'BLOG_SCREEN':
          if (trueParams) {
            navigate(item.screen)
          }
          break
        case 'BHTNDSCAR':
          if (trueParams) {
            navigate(SCREENS.baoHiemTNDSB1, { prodId: params })
          }
          break
        case 'BHVCCAR':
          if (trueParams) {
            navigate(SCREENS.baoHiemVCXB1, { prodId: params })
          }
          break
        case 'BHTNDSBIKE':
          if (trueParams) {
            navigate(SCREENS.baoHiemTNDSXeMayB1, { prodId: params })
          }
          break
        case 'SCREEN_NAME':
          // eslint-disable-next-line no-case-declarations
          try {
            const data = JSON.parse(params || '{}')
            console.log('********************************', data)
            navigate(data?.screen || '', { ...data?.params })
          } catch (e) {

          }
          break
        default:
          if (trueParams && isValidURL === undefined) {
            Linking.openURL(params)
          }
          break
      }
    }
  }

  const _renderCarouselItem = ({ item }: any) => {
    return (
      <TouchableOpacity
        onPress={() => onPressImage(item, navigate) }
        style={styles.slide}>
        <LazyImage
          style={{
            height: 100,
            width: '100%',
            borderRadius: 4,
          }}
          resizeMode={'cover'}
          source={{ uri: item.picture }}
        />
      </TouchableOpacity>
    )
  }

  return <View style={styles.sectionBannerHome2}><View style={{ height: 100 }}>
    <Carousel
      layout={'default'}
      data={props.data}
      // onCurrentImagePressed={index => onPressImage(index, homeStore.banners, navigate) }
      renderItem={_renderCarouselItem}
      sliderWidth={G_WIN_WIDTH}
      itemWidth={G_WIN_WIDTH - 100}
      inactiveSlideScale={1}
      inactiveSlideOpacity={1}
      loop={true}
      autoplay={true}
      autoplayDelay={500}
      autoplayInterval={4000}
      removeClippedSubviews={false}
      // onSnapToItem={(index) => setIndex(index)}
    />
    {/* {renderPagination()} */}
  </View>
  </View>
})

export const HomeScreen = observer((props) => {
  useWhyDidYouUpdate('HomeScreen =>>>', props)
  const { t } : any = useTranslation()
  const modalizeProvince = useRef<Modalize>(null)
  const { homeStore, renderFlatListStore, searchStore, notificationStore, profileStore, carStore, productStore } = useStores()
  const [refreshing, setRefreshing] = useState(false)
  const [isFetched, setIsFetched] = useState(true)
  const { navigate, goBack } = useNavigation()
  const [province, setProvince] = useState(null)
  const [provinces, setProvinces] = useState([])
  const [isPickerSelectVisible, setIsPickerSelectVisible] = useState(false)
  // const scrollY = new Animated.Value(0)
  const [count, setCount] = useState(0)
  const { show, hide } = useLoading()
  const [index, setIndex] = React.useState(0)
  const [isModalAdsVisible, setIsModalAdsVisible] = useState(false)
  const [bannerAds, setBannerAds] = useState(null)
  const { shoppingCount } = useAuth()
  const { showSuccess } = useContext(ModalContext)
  const refAdd = useRef(null)
  const forceUpdate = useForceUpdate()
  const [reloadData, setReloadData] = useState(false)
  const api = new Api()

  const [routes] = React.useState([
    // { key: 'SHOP', title: t('SANPHAM') },
    { key: 'SPA', title: t('SPA') },
    { key: 'GARAGE', title: t('GARAGE') },
    { key: 'PARKING', title: t('PARKING') },
  ])

  // Use Hooks to control!
  const { start, canStart, eventEmitter } = useTourGuideController()

  useEffect(() => {
    // start at mount
    if (canStart) {
      start()
    }
  }, [canStart]) // wait until everything is registered

  useEffect(() => {
    // call api config
    updateBadgeCount()

    return () => {

    }
  }, [])

  useEffect(() => {
    if (homeStore.reloadData === true) {
      callApiPet()
      homeStore.setReloadData(false)
    }
  }, [homeStore.reloadData])

  useEffect(() => {
    let isLoad = true
    if (isLoad) {
      if (province) {
        onChangeProvince(province).then(r => {
          isLoad = false
        })
      }
    }
    return () => { isLoad = false }
  }, [province])

  useEffect(() => {
    loadData()
  }, [homeStore.reloadData])

  const callApiPet = async () => {
    await carStore.getListPet(profileStore._id)
    forceUpdate()
  }

  const updateBadgeCount = async () => {
    if (isIOS && profileStore.isSignedIn()) {
      // const granted = await requestNotificationPermissions(['badge'])
      // const badgeCount = await getBadgeCount()
      // const permission = await getNotificationBadgeSetting()
      // if (permission === 'enabled') {
      //   if (notificationStore?.notSeen > 0) {
      //     await setBadgeCount(notificationStore.notSeen || 0)
      //   } else {
      //     await setBadgeCount(0)
      //   }
      // } else {
      //   __DEV__ && console.warn("Badge permission has not yet been granted. I'll ask the user later")
      // }
      // __DEV__ && console.log('badgeCount', badgeCount)
    }
  }

  const checkShowPopup = async () => {
    // get data provinces
    await homeStore.getProvinces()
    setProvinces(homeStore.provinces)
    forceUpdate()
    const provinceSave = await loadString('provinceSelected')
    if (!provinceSave) {
      setIsPickerSelectVisible(true)
    }
  }

  // check ads banner from notification database
  const checkHasNotificationBanner = async () => {
    const rs = await notificationStore.getNotificationByType(3)
    if (rs?.notifications) {
      if (_.isArray(rs.notifications) && rs.notifications.length && rs.notifications[0].watched === 0) {
        setIsModalAdsVisible(true)
        // update watched
        notificationStore.watchedNotification(rs.notifications[0]._id)
        // TODO: update support show image
        if (rs.notifications[0]?.urlImage) {
          setBannerAds(rs.notifications[0]?.urlImage)
        }
      }
    }
  }

  useEffect(() => {
    // setCount(0)
    // navigate(SCREENS.bookingHistoryDetail, { orderId: '2020101415853', bookingType: 1 })
    checkShowPopup().then(r => { })
    setTimeout(() => { checkHasNotificationBanner() }, 10000)
  }, [])

  useEffect(() => {
    eventEmitter.on('start', () => __DEV__ && console.log('start'))
    eventEmitter.on('stop', () => __DEV__ && console.log('stop'))
    eventEmitter.on('stepChange', () => __DEV__ && console.log('stepChange'))
    return () => eventEmitter.off('*', null)
  }, [])

  const onChangeProvince = async (value) => {
    try {
      LogEvent('user_select_province', value)
      const province = await loadString('provinceSelected')
      if (province !== value) {
        renderFlatListStore.clearData()
        await saveString('provinceSelected', value)
        homeStore.setProvince(value)
        loadData().then(r => {})
      } else {
        // get all province
        loadData().then(r => { })
      }
      onCloseModal()
    } catch (error) {
      // Error saving data
    }
  }

  const getApiData = async () => {
    __DEV__ && console.log('getApiData')
    await remove('districtSelected')
    const tasks = [
      homeStore.getAppConfig(),
      // homeStore.getBanners(),
      // homeStore.getPromotions(),
      // homeStore.getHomeCategories('menu'),
      // homeStore.getHomeCategories('hot'),
      // homeStore.getServicesCategories('3'), // danh mục dịch vụ hot
      // renderFlatListStore.getService(1, 1, ''),
      // renderFlatListStore.getService(2, 1, ''),
      // renderFlatListStore.getService(3, 1, ''),
      // homeStore.getNewsForHome(),
      // homeStore.getTopBranch(),
      // homeStore.getBannerHome2(),
      // productStore.getProductForHome()
    ]
    if (profileStore.isSignedIn()) {
      tasks.push(carStore.getListPet(profileStore._id))
      tasks.push(notificationStore.getNotification(1, false))
    }
    return Promise.all(tasks)
  }

  const loadData = async () => {
    // setCount(count + 1)
    setIsFetched(true)
    await homeStore.getAppConfig()
    getApiData().then(r => {
      __DEV__ && console.log('GET API DONE ************************************************')
      setIsFetched(false)
      forceUpdate()
    })
  }

  const onRefresh = async () => {
    // show()
    setRefreshing(true)
    setTimeout(() => {
      setRefreshing(false)
    }, 100)
    await getApiData()
    forceUpdate()
  }

  const goLoginScreenRequired = () => {
    navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  const onCloseModal = () => {
    modalizeProvince.current?.close()
  }

  const navigateCart = () => {
    if (profileStore.isSignedIn()) {
      navigate(SCREENS.cartScreen)
    } else {
      goLoginScreenRequired()
    }
  }

  const calculateHeaderH = () : number => {
    let height = 475
    if (productStore?.homeProduct?.length) {
      height += 305
    }
    if (homeStore?.bannerHome2?.length) {
      height += 130
    }
    if (homeStore?.topBranch?.length) {
      height += 265
    }
    if (homeStore?.newsForHome?.length) {
      height += 265
    }
    if (homeStore.promotions?.length) {
      height += 270
    }
    return height
  }

  const SearchBar = () => (<TouchableOpacity onPress={() => {
    navigate(SCREENS.search)
    searchStore.setTypeSearch(-1)
    searchStore.setKeyword('')
  }}>
    <View style={styles.inputStyle}>
      {/* <Image style={styles.icSearch} source={icSearch}/> */}
      <Icon size={14} style={{ color: '#fff', marginRight: 4 }} name="search"/>
      <Text style={styles.input}>{t('Search')}</Text>
    </View>
  </TouchableOpacity>)

  const renderScene = ({ route }) => {
    __DEV__ && console.log('renderScene INDEX =>>>>>>>', index)
    __DEV__ && console.log('isFetched', isFetched)
    switch (route.key) {
      // case 'SHOP':
      // return <TabProductRenderScreen data={productStore.productNears}/>
      case 'SPA':
        return <>{ isFetched ? <PlaceHolder style={{ paddingTop: 10 }}/> : <TabServicesRenderScreen data={renderFlatListStore.spa} type={BookingType.SPA}/> }</>
      case 'GARAGE':
        return <>{ isFetched ? <PlaceHolder style={{ paddingTop: 10 }}/> : <TabServicesRenderScreen data={renderFlatListStore.phongkham} type={BookingType.CLINIC}/> }</>
      case 'PARKING':
        return <>{ isFetched ? <PlaceHolder style={{ paddingTop: 10 }}/> : <TabServicesRenderScreen data={renderFlatListStore.khachsan} type={BookingType.PARKING}/> }</>
      default:
        return null
    }
  }

  const renderTabBar = (props: any) => {
    return <TabBar
      {...props}
      inactiveColor={'#333'}
      activeColor={'#333'}
      indicatorStyle={{ backgroundColor: color.primary }}
      tabStyle={{ minHeight: 30 }}
      labelStyle={{
        textTransform: 'capitalize',
        fontSize: 14
      }}
      style={{
        backgroundColor: '#fff',
        paddingTop: 0
      }}
    />
  }

  const backgroundColor = useSharedValue('#00000000')
  const opacity = useSharedValue(1) // Bắt đầu với opacity là 1

  const scrollHandler = useAnimatedScrollHandler((e) => {
    // Tính toán màu nền dựa trên vị trí cuộn`
    backgroundColor.value = interpolateColor(
      e.contentOffset.y,
      [0, 100], // Đoạn cuộn từ 0 đến 100px
      ['#00000000', '#e10814'] // Từ màu trong suốt sang đỏ
    )
    opacity.value = interpolate(
      e.contentOffset.y,
      [0, 100], // Đoạn cuộn từ 0 đến 100px
      [1, 0] // Từ hiển thị đầy đủ sang ẩn
    )
  })

  const headerStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      backgroundColor: backgroundColor.value,
    }
  })

  const _renderCarouselItem = ({ item }: any) => {
    return (
      <TouchableOpacity
        style={{
          width: 300,
          height: 160,
          paddingHorizontal: 10,
          borderRadius: 8
        }}>
        <FastImage style={{ width: '100%', height: '100%', borderRadius: 8 }} resizeMode={FastImage.resizeMode.stretch} source={item.img}></FastImage>
      </TouchableOpacity>
    )
  }

  return (
    <SafeAreaView style={[styles.safeAreaView, isAndroid ? { paddingTop: 0 } : {}]} edges={['right', 'left']}>
      <Animated.View style={[{ position: 'absolute', top: 0, width: '100%', zIndex: 9999, backgroundColor: '#00000000' }, headerStyle]} >
        <Header
          leftComponent={<View>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginLeft: 20,
              marginTop: 100
            }}>
              { profileStore.isSignedIn() ? <LazyImage resizeMode="cover" source={{ uri: profileStore.avatarUser }} style={styles.userAvatar} ></LazyImage> : <FastImage resizeMode="cover" source={icGuest} style={styles.userAvatar} ></FastImage>}
              <View style={styles.group1000002803}>
                <Text style={styles.xinchao}>Xin chào</Text>
                <Text style={styles.nguyenthuhien}>{ profileStore?.fullName || 'Thành viên mới'}</Text>
              </View>
            </View>
          </View>}
          // backgroundImage={{ uri: homeStore.appConfig?.bg_home_header?.data?.attributes.url }}
          centerComponent={<View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 5 }}>
            {/* <TouchableOpacity */}
            {/*  onPress={() => setIsPickerSelectVisible(true)} */}
            {/*  style={styles.viewAddress}> */}
            {/*  <Icon name={'location-outline'} size={16} color='#fff'/> */}
            {/*  /!* <Image style={styles.icMap} source={IcMap}/> *!/ */}
            {/*  <Text style={[styles.address, { color: '#fff' }]}>{homeStore.province || t('CHOOSE_LOCATION')}</Text> */}
            {/*  <Icon size={14} style={{ marginLeft: 10, color: '#fff' }} name="chevron-down-outline"/> */}
            {/* </TouchableOpacity> */}
            {/* <SearchBar/> */}
          </View>}
          containerStyle={[{
            backgroundColor: '#00000000',
            borderBottomWidth: 0,
          }]}
          rightContainerStyle={{ position: 'absolute', right: 20, top: 92 }}
          rightComponent={
            <View style={styles.viewIcRight}>
              <FastImage resizeMode={'contain'} style={{ width: 20, height: 20, marginRight: 10 }} source={icQrCode} />
              <TouchableOpacity
                style={{ flexDirection: 'row' }}
                onPress={() => navigate(SCREENS.notificationScreen)}>
                <Icon size={22} color='#fff' name={'notifications-outline'} />
                {notificationStore?.notSeen ? <Badge value={notificationStore?.notSeen > 99 ? 99 + '+' : notificationStore?.notSeen} status="error" containerStyle={{ marginLeft: -12, marginTop: -8 }} /> : null}
              </TouchableOpacity>
              {/* <TouchableOpacity */}
              {/*  onPress={navigateCart} */}
              {/*  style={{ marginLeft: 10, flexDirection: 'row' }} */}
              {/* > */}
              {/*  <Icon size={25} color='#fff' name={'cart-outline'}/> */}
              {/*  {shoppingCount && shoppingCount > 0 ? <Badge value={shoppingCount > 99 ? 99 + '+' : shoppingCount} status="error" containerStyle={{ marginLeft: -12, marginTop: -7 }} /> : null} */}
              {/* </TouchableOpacity> */}

              {/* {renderBtnChat()} */}
            </View>
          }
          statusBarProps={{ barStyle: DeviceInfo.hasDynamicIsland ? 'dark-content' : 'light-content', backgroundColor: 'transparent' }}
        />
      </Animated.View>
      <Animated.ScrollView style={[styles.rdView, { marginBottom: useSafeAreaInsets().bottom }]}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
          />
        }
      >
        {Platform.OS === 'ios' ? <View /> : null}
        {/* <BannerSection images={homeStore.images} banners={homeStore.banners}/> */}
        <FastImage resizeMode={FastImage.resizeMode.stretch} style={{ width: '100%', height: 332 }} source={bgHome}></FastImage>

        {/* <SectionListCar cars={carStore.dataListPet}/> */}
        <View style={styles.sectionCategory}>
          <MucYeuThich data={dsYeuThich}/>
        </View>

        {/* <View style={styles.sectionCategory}> */}
        {/*  {homeStore.homeCategories?.length ? <Categories data={homeStore.homeCategories}/> : null} */}
        {/* </View> */}
        {/* <View style={styles.sectionCategory}> */}
        {/*  {homeStore.homeCategories?.length ? <Categories data={homeStore.homeCategories}/> : null} */}
        {/* </View> */}
        {/* <View style={styles.sectionHomeProduct}> */}
        {/*  <RenderListHeaderFlatlist promotion={homeStore.promotions} /> */}
        {/* </View> */}
        {/* <HotServiceCategories data={homeStore.homeHotCategories}/> */}
        {/* {productStore.homeProduct ? <View style={styles.sectionHomeProduct}> */}
        {/*  <RenderProductHome data={productStore?.homeProduct} /> */}
        {/* </View> : null} */}
        {/* {homeStore.newsForHome ? <View style={{ marginTop: 5, backgroundColor: '#fff' }}> */}
        {/*  <RenderListHeaderFlatlist news={homeStore.newsForHome} /> */}
        {/* </View> : null} */}
        {/* {homeStore?.topBranch?.length ? <View style={{ marginTop: 5, backgroundColor: '#fff' }}> */}
        {/*  <RenderListHeaderFlatlist topBranch={homeStore.topBranch} /> */}
        {/* </View> : null} */}
        <View style={{ marginHorizontal: 30 }}>
          <FastImage resizeMode={FastImage.resizeMode.contain} style={{ width: '100%', height: 80 }} source={imgChuaChonCongTy}></FastImage>
        </View>
        <View style={{
          flexDirection: 'column',
          marginHorizontal: 30,
          marginTop: 20,
          marginBottom: 20
        }}>
          <View style={{ minHeight: 100, marginHorizontal: -10 }}>
            <FlatList
              horizontal={true}
              data={dsDonVi}
              extraData={dsDonVi}
              showsHorizontalScrollIndicator={false}
              renderItem={_renderCarouselItem}
              keyExtractor={item => item.img}/>
          </View>
        </View>
        <DanhSachDoanhNghiep data={dsDoanhNghiep}/>
        {/* {homeStore.bannerHome2?.length ? <CarouselDoiTac data={homeStore.bannerHome2}/> : null } */}
        {/* <FastImage style={{ width: '100%', height: 160 }} resizeMode={'contain'} source={imgDoiTac1}></FastImage> */}
        {/* <TabView */}
        {/*  lazy={true} */}
        {/*  renderTabBar={renderTabBar} */}
        {/*  navigationState={{ index, routes }} */}
        {/*  // isRefreshing={refreshing} */}
        {/*  renderScene={renderScene} */}
        {/*  // refreshHeight={100} */}
        {/*  onIndexChange={setIndex} */}
        {/*  initialLayout={{ width: responsiveWidth(100) }} */}
        {/*  // renderRefreshControl={null} */}
        {/*  // renderRefreshControl={() => isIOS ? <ActivityIndicator size="small" /> : <View/>} */}
        {/* /> */}
      </Animated.ScrollView>
    </SafeAreaView>
  )
})
