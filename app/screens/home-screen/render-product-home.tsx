import { observer } from 'mobx-react-lite'
import { FlatList, Text, TouchableOpacity, View, Image } from 'react-native'
import styles from './style'
import React from 'react'
import { spacing } from '@app/theme'
import { LazyImage } from '@app/components'
import { numberFormat } from '@app/utils/number'
import { SCREENS } from '@app/navigation'
import Icon from 'react-native-vector-icons/Ionicons'
import { useNavigation } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import { useStores } from '@app/models'
import { freeShipIcon, saleIcon } from '@app/assets/images'

export const RenderProductHome = observer(function RenderProductHome(props:any) {
  const { t } : any = useTranslation()
  const { navigate } = useNavigation()
  const { searchStore } = useStores()

  const renderItemProd = ({ item, index }) => {
    const discount = (item.price - item.priceOld) / item.priceOld
    return <View>
      <View style={styles.touchItem}>
        <TouchableOpacity onPress={ () => navigate(SCREENS.productDetails, { id: item._id })}>
          <LazyImage style={{ width: 120, height: 120, borderTopLeftRadius: 5, borderTopRightRadius: 5 }}
            source={{ uri: item.thumbail }} resizeMode="cover" />
          <View style={styles.discountViewRow}>
            {item.typeShip === 0 && <Image source={freeShipIcon} style={styles.freeShipIc}></Image>}
            {item.priceOld && <Image source={saleIcon} style={styles.saleIcon}></Image>}
          </View>
        </TouchableOpacity>
        <View style={{ paddingHorizontal: 8, paddingVertical: 8 }}>
          <TouchableOpacity onPress={ () => navigate(SCREENS.productDetails, { id: item._id })}>
            <Text numberOfLines={2} style={styles.textName}>{item.name}</Text>
          </TouchableOpacity>
          <Text numberOfLines={2} style={styles.textNewPrice}>{numberFormat(item.price)} đ</Text>
          {item?.priceOld && item.priceOld > 0 ? <View style={{ flexDirection: 'row' }}>
            <Text numberOfLines={2} style={styles.textOldPrice}>{numberFormat(item.priceOld)} đ</Text>
            <Text numberOfLines={2} style={styles.textSaleOff}>{numberFormat(discount * 100)}%</Text>
          </View> : <View style={{ height: 14 }} />}

        </View>
        <TouchableOpacity onPress={ () => navigate(SCREENS.productDetails, { id: item._id })} style={{ justifyContent: 'center' }}>
          <Text style={styles.textBtnAdd}>{t('MUANGAY')}</Text>
        </TouchableOpacity>
      </View>
    </View>
  }
  return (
    <View>
      {props?.data?.length > 0 ? <View>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingHorizontal: 15, paddingVertical: 10 }}>
          <Text style={styles.titleProd}>Siêu thị phụ kiện</Text>
          <TouchableOpacity
            onPress={() => {
              navigate(SCREENS.search)
              searchStore.setTypeSearch(0)
              navigate(SCREENS.search, { filterType: 0, screenType: 'shop' })
            }}
            style={styles.btnViewMore}
          >
            <Text style={styles.textTop}>{t('KHAMPHADV_xemtatca')}</Text>
            <Icon style={styles.iconViewMore} name={'chevron-forward-outline'}/>
          </TouchableOpacity>
        </View>
        <FlatList
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          scrollEnabled={true}
          style={{ marginLeft: spacing.small }}
          contentContainerStyle={{ paddingHorizontal: 8 }}
          data={props.data}
          keyExtractor={(e, i) => i + 'l1'}
          renderItem={renderItemProd}
        />
      </View> : null}
    </View>
  )
})
