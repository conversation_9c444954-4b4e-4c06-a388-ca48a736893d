import { Linking, View } from 'react-native'
import styles from '@app/screens/home-screen/style'
import { LazyImage } from '@app/components'
import React from 'react'
import { responsiveWidth } from 'react-native-responsive-dimensions'
import { SliderBox } from 'react-native-image-slider-box'
import { useNavigation } from '@react-navigation/native'
import validate from 'validate.js'
import { observer } from 'mobx-react-lite'
import { SCREENS } from '@app/navigation'
import FastImage from 'react-native-fast-image'
const responsiveImage = (width, height) => {
  const screenWidth = responsiveWidth(100)
  return {
    width: Math.floor(screenWidth),
    height: Math.floor(height * (screenWidth / width))
  }
}

const onPressImage = (index, banners, navigate) => {
  __DEV__ && console.log(`Banner shopping image ${index} pressed`)
  const item = banners[index]
  if (item?.screen) {
    const params = item?.params || ''
    const trueParams = params !== '#'
    const isValidURL = validate({ website: params }, { website: { url: true } })
    switch (item.screen) {
      case 'PROMOTION_DETAIL_SCREEN':
        if (trueParams) {
          navigate(item.screen, { id: params })
        }
        break
      case 'SHOPPING_STACK':
        navigate(item.screen)
        break
      case 'PRODUCT_DETAILS':
        if (trueParams) {
          navigate(item.screen, { id: params })
        }
        break
      case 'BLOG_DETAIL_SCREEN':
        if (trueParams) {
          navigate(item.screen, { id: params })
        }
        break
      case 'SERVICE_DETAIL':
        if (trueParams) {
          navigate(item.screen, { id: params })
        }
        break
      case 'POPUP_SERVICE_OF_BRAND':
        if (params && item?.screen_param_spaId) {
          navigate(SCREENS.serviceDetail, { id: params, spaId: item?.screen_param_spaId })
        }
        break
      case 'BLOG_SCREEN':
        if (trueParams) {
          navigate(item.screen)
        }
        break
      case 'BHTNDSCAR':
        if (trueParams) {
          navigate(SCREENS.baoHiemTNDSB1, { prodId: params })
        }
        break
      case 'BHVCCAR':
        if (trueParams) {
          navigate(SCREENS.baoHiemVCXB1, { prodId: params })
        }
        break

      case 'BHTNDSBIKE':
        if (trueParams) {
          navigate(SCREENS.baoHiemTNDSXeMayB1, { prodId: params })
        }
        break
      default:
        if (trueParams && isValidURL === undefined) {
          Linking.openURL(params)
        }
        break
    }
  }
}
const BannerShopping = observer(({ images, banners }) => {
  const { navigate } = useNavigation()
  return (
    <View>
      <SliderBox
        inactiveSlideScale={1}
        inactiveSlideOpacity={1}
        images={images}
        onCurrentImagePressed={index => onPressImage(index, banners, navigate) }
        imageLoadingColor={'#fff'}
        dotColor="#90A4AE"
        inactiveDotColor="#f3373a"
        paginationBoxVerticalPadding={8}
        autoplay={true}
        circleLoop={true}
        resizeMode={FastImage.resizeMode.stretch}
        paginationBoxStyle={styles.paginationBoxStyle2}
        dotStyle={styles.dotStyle2}
        ImageComponent={LazyImage}
        ImageComponentStyle={{ ...styles.ImageComponentStyle2, ...responsiveImage(650, 400) }}
      />
    </View>
  )
})

export default BannerShopping
