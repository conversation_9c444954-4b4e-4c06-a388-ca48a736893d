import * as React from 'react'
import { View, ViewStyle, StyleSheet, TouchableOpacity, Text, Image } from 'react-native'
import { observer } from 'mobx-react-lite'
import Modal from 'react-native-modal'
import { useTranslation } from 'react-i18next'
import { typography } from '@app/theme'
import { useEffect, useState } from 'react'
import { catsmile } from '@app/assets/images'
import { ProductGiftItem } from '@app/models/lucky-wheel-store/lucky-wheel-store'
import FastImage from 'react-native-fast-image'

export interface ModalSuccessProps {
  /**
   * An optional style override useful for padding & margin.
   */
  isVisible: boolean
  style?: ViewStyle
  close?: () => any
  onPress?: () => any
  onClosed?: () => any
  onConfirm?: () => any
  message: any
  urlImage?: string
  title: any
  textButton?:string
  cancelText?:string
  confirmText?:string
  itemSelected?: ProductGiftItem
}

/**
 * Describe your component here
 */
const ModalLuckySuccess = observer((props: ModalSuccessProps) => {
  const { t } : any = useTranslation()
  const [visible, setVisible] = useState(false)

  useEffect(() => {
    setVisible(props.isVisible)
  }, [props.isVisible])

  // trường hợp ko truyền vào sự kiện closed
  const onClosed = () => {
    if (props.close) props.close()
    else {
      setVisible(false)
    }
  }

  return (
    <Modal style={{ marginHorizontal: 60 }}
      isVisible={visible}
      backdropColor="#B4B3DB"
      backdropOpacity={0.8}
      animationIn="zoomIn"
      animationOut="zoomOut"
      animationInTiming={400}
      animationOutTiming={200}
      backdropTransitionInTiming={300}
      backdropTransitionOutTiming={300}
      onModalHide={onClosed}
      avoidKeyboard={false}>
      {/* eslint-disable-next-line react-native/no-inline-styles */}
      <View style={{ flex: 1, }}>
        <View style={styles.modal}>
          <Image style={[styles.image, { marginBottom: 10 }]} source={catsmile} resizeMode="cover"/>
          <Text style={[styles.contentTitle, { marginBottom: 10 }]}>{props.title}</Text>
          <Text style={styles.contentErr}>Bạn đã trúng thưởng phần quà</Text>
          <Text style={styles.contentErr}>{props.itemSelected?.attributes?.productName}</Text>
          <FastImage style={{ width: 150, height: 150, marginBottom: 10, marginTop: 10 }} source={{ uri: props.itemSelected?.attributes?.image?.data?.attributes?.url }} resizeMode="cover"/>
          <Text style={[styles.contentErr, { marginBottom: 10 }]}>Mời bạn bấm nút nhận quà để làm thủ tục nhận quà trúng thưởng</Text>
          <TouchableOpacity style={styles.btnSuccess} onPress={props.onPress ? props.onPress : onClosed }>
            <Text style={styles.textSuccess}>{props?.textButton ? props.textButton : t('Nhận quà')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  )
})

export default ModalLuckySuccess

const styles = StyleSheet.create({
  alertBtn: {
    borderRadius: 4,
    width: 100
  },
  alertConfirm: {
    height: 80,
  },
  btnClose: {
    alignItems: 'center',
    // backgroundColor: '#ff8ba1',
    backgroundColor: '#f3f3f3',
    borderRadius: 4,
    flexDirection: 'row',
    height: 44,
    justifyContent: 'center',
    width: '100%',
  },
  btnSuccess: {
    alignItems: 'center',
    // backgroundColor: '#ff8ba1',
    backgroundColor: '#78AF41',
    borderRadius: 4,
    flexDirection: 'row',
    height: 44,
    justifyContent: 'center',
    width: 120,
  },
  btnTextStyles: {
    fontSize: 14,
    fontWeight: 'bold',
    paddingVertical: 5,
    textAlign: 'center'
  },
  content: {
    alignItems: 'center',
    backgroundColor: 'white',
    borderColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 4,
    justifyContent: 'center',
    padding: 22,
  },
  contentErr: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    textAlign: 'center',
  },
  contentTitle: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  image: {
    height: 40,
    width: 40
  },
  modal: {
    alignItems: 'center',
    backgroundColor: 'white',
    borderColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 8,
    flexDirection: 'column',
    justifyContent: 'space-between',
    marginTop: 220,
    minHeight: 240,
    paddingBottom: 20,
    paddingHorizontal: 22,
    paddingTop: 35
  },
  textClose: {
    color: '#333',
    fontSize: 16,
    fontWeight: '500'
  },
  textSuccess: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500'
  }
})
