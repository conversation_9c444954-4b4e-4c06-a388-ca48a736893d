import { SafeAreaView } from 'react-native-safe-area-context'
import {

  Text,
  View,
  TouchableOpacity,
  ScrollView,
  RefreshControl, Platform
} from 'react-native'
import React, { useState, Fragment, useEffect, useRef, useContext } from 'react'
import styles from './styles'
import { useTranslation } from 'react-i18next'
import Icon from 'react-native-vector-icons/Ionicons'
import { useStores } from '../../models/root-store'
import { CommonActions, useNavigation, useRoute } from '@react-navigation/native'
import { Modalize } from 'react-native-modalize'
// import { EditPassword } from './edit-password-screen'
import { observer } from 'mobx-react-lite'
import ImagePicker from 'react-native-image-crop-picker'
import DateTimePickerModal from 'react-native-modal-datetime-picker'
import moment from 'moment'
import { ButtonBack, TTextInput, TButton, ConfirmDialog } from '@app/components'
import { ModalContext } from '@app/context'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import SimpleToast from 'react-native-simple-toast'
import LinearGradient from 'react-native-linear-gradient'
import { Api } from '@app/services/api'
import DialogInput from 'react-native-dialog-input'
import { useAuth } from '@app/use-hooks/use-auth'
import { iconFile } from '@app/assets/images'
import FastImage from 'react-native-fast-image'
import ModalSuccess from '@app/components/modal-success/modal-success'
import { SCREENS } from '@app/navigation'
const api = new Api()

export const ConfirmAdvanceSalaryScreen = observer((props) => {
  const route = useRoute()
  const { objData } = route.params
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()
  const { accountStore, profileStore } = useStores()
  const modalizeRef = useRef<Modalize>(null)
  const [refreshing, setRefreshing] = useState(false)
  const [avatar, setAvatar] = useState('')
  const [pickerDate, setPickerDate] = useState(null)
  const [pickerDateVisible, setPickerDateVisible] = useState(false)
  const [strPiker, setStrPicker] = useState('')
  const [pickerTime, setPickerTime] = useState(null)
  const [strDate, setStrDate] = useState('')
  const currentName = profileStore.fullName
  const currentEmail = profileStore.email
  const [fullName, setFullName] = useState(profileStore.fullName || '')
  const [email, setEmail] = useState(profileStore.email || '')
  const [date, setDate] = useState('')
  const modalChangeName = useRef<Modalize>(null)
  const modalChangeEmail = useRef<Modalize>(null)
  const { showError, showSuccess } = useContext(ModalContext)
  const [isShowDeleteAccount, setIsShowDeleteAccount] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isShowModalSuccess, setIsShowModalSuccess] = useState(false)
  console.log('🚀 ~ AdvanceSalaryScreen ~ isShowModalSuccess:', isShowModalSuccess)
  const { signOut, userToken } = useAuth() // should be signUp
  const navigation = useNavigation()

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    await profileStore.getProfile()
    // setPhotoUrl(profileStore.photoUrl)
  }

  function onBack() {
    goBack()
  }

  const onOpenModalEditLanguage = () => {
    modalizeRef.current?.open()
  }

  const onCloseModel = () => {
    modalizeRef.current?.close()
  }
  const onOpenModalChangeName = () => {
    modalChangeName.current?.open()
  }
  const onCloseModalChangeName = () => {
    modalChangeName.current?.close()
  }

  const onOpenModalChangeEmail = () => {
    modalChangeEmail.current?.open()
  }
  const onCloseModalChangeEmail = () => {
    modalChangeEmail.current?.close()
  }

  const onRefresh = () => {
    if (refreshing) return
    loadData().then((r) => {
    })
  }

  const getPicture = () => {
    ImagePicker.openPicker({
      writeTempFile: true,
      width: 200,
      mediaType: 'photo',
      height: 200,
      compressImageMaxHeight: 200,
      compressImageMaxWidth: 200,
      cropping: true,
      multiple: false,
    }).then((response: any) => {
      __DEV__ && console.log(response)
      // setAvatar(response.path)
      const files = []
      files.push({
        uri: response.path,
        name: 'picture',
        fileName: response.filename + '.png',
        type: response.mime,
        size: response.size,
      })
      profileStore.uploadImage('/user/api/upload-image', files)
      // profileStore.uploadImageDropBox(files).then(rs => {
      // })
    })
  }

  const showDatePicker = () => {
    setPickerDate('date')
    setStrPicker(t('CHOOSE_A_DATE'))
  }

  const hidePicker = () => {
    setPickerDateVisible(false)
    setPickerDate(null)
    setPickerTime(null)
  }

  const handleConfirm = (date) => {
    const partDate = moment(date).format('DD/MM/YYYY')
    if (Date.parse(date) <= (new Date().getTime())) {
      setStrDate(partDate)
      setDate(partDate)
      profileStore.setBirthDay(partDate)
      hidePicker()
    } else {
      // showError(t('FAIL'), t('Bạn không thể chọn ngày chưa tới'))
      SimpleToast.show('Bạn không thể chọn ngày chưa tới')
      return Platform.OS === 'android' ? setPickerDateVisible(false) : null
    }
  }

  const requestLockAccount = async (password) => {
    const rs = await api.requestLockAccount({ phone: profileStore.phone, password })
    if (rs && rs.data.error) {
      showError(t('FAIL'), t(`${rs.data.message}`))
    } else {
      showSuccess(t('THANHCONG'), t('Yêu cầu xoá tài khoản thành công'))
      onSignOut()
    }
  }

  const onSignOut = async () => {
    try {
      signOut() // gọi hàm signout global
      setTimeout(() => { resetHistory() }, 500)
    } catch (e) {
      __DEV__ && console.log(e)
    }
  }

  const renderHeaderChangeName = () => {
    const str = t('Cập nhật tên người dùng')
    return (<View style={styles.modal__header}>
      <ButtonBack onPress={onCloseModalChangeName} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>{str}</Text>
      <TouchableOpacity onPress={onCloseModalChangeName}>
        <Text style={styles.textSave}>{t('XONG')}</Text>
      </TouchableOpacity>
    </View>)
  }

  const renderHeaderChangeEmail = () => {
    const str = t('Cập nhật email')
    return (<View style={styles.modal__header}>
      <ButtonBack onPress={ () => {
        setEmail('')
        onCloseModalChangeEmail()
      }
      }
      style={styles.viewTouchButtonTop}
      />
      <Text style={styles.textTitleHeader}>{str}</Text>
      <TouchableOpacity onPress={onCloseModalChangeEmail}>
        <Text style={styles.textSave}>{t('XONG')}</Text>
      </TouchableOpacity>
    </View>)
  }

  const renderInfo = () => {
    return (
      <View style={[styles.boxView, { zIndex: -1 }]}>
        <View style={styles.contentInfo}>
          <Text style={styles.label}>{'Mã GD'}</Text>
          <Text numberOfLines={1} style={styles.textDetail}>{objData.ma_gd}</Text>
        </View>
        <View style={styles.contentInfo}>
          <Text style={styles.label}>Người ứng</Text>
          <Text style={[styles.textDetail, { marginTop: 3, alignSelf: 'flex-end' }]}>{ objData.ten_ng_ung}</Text>
        </View>
        <View style={styles.contentInfo}>
          <Text style={styles.label}>Số tk nhận</Text>
          <Text style={styles.textDetail}>{objData.tk_nhan}</Text>
        </View>
        <View style={styles.contentInfo}>
          <Text style={styles.label}>Ngân hàng</Text>
          <Text style={styles.textDetail}>{objData.bank_name}</Text>
        </View>
        <View style={styles.contentInfo}>
          <Text style={styles.label}>Số tiền ứng</Text>
          <Text style={styles.textDetail}>{objData.so_tien}</Text>
        </View>
        <View style={styles.contentInfo}>
          <Text style={styles.label}>Phí giao dịch</Text>
          <Text style={styles.textDetail}>{ objData.phi_gd}</Text>
        </View>
        <View style={styles.contentInfo}>
          <Text style={styles.label}>Phí quản lý 1%</Text>
          <Text style={styles.textDetail}>{ objData.phi_ql}</Text>
        </View>
        <View style={[styles.contentInfo, { borderBottomWidth: 0 }]}>
          <Text style={styles.label}>Tổng tiền nhận</Text>
          <Text style={styles.textDetail}>{ objData.tong_so_tien}</Text>
        </View>
      </View>
    )
  }

  const renderAvatar = () => {
    return (
      <View style={styles.viewAvatar}>
        <TouchableOpacity onPress={getPicture} style={styles.avatarView}>
          <FastImage
            resizeMode="cover"
            style={styles.avatar}
            source={iconFile}
          />

        </TouchableOpacity>
      </View>
    )
  }

  const renderChangeNameInput = () => {
    return (
      <View style={{ padding: 16, marginBottom: 30 }}>
        <Text style={{ fontWeight: '500', color: '#333', marginVertical: 10 }}>Tên người dùng</Text>
        <TTextInput
          typeInput={'code'}
          typeRadius={'rounded'}
          // keyboardType="phone-pad"
          maxLength={30}
          autoCapitalize={'sentences'}
          defaultValue={fullName || ''}
          placeholder={t('Nhập họ tên')}
          placeholderStyle={{ textAlign: 'center' }}
          onChangeText={(e) => setFullName(e.trim())}
          iconRightClick={() => setFullName('')}
          iconRight={<Icon name='close-circle-outline' size={24} color='#a0a0a0' />}
        />
      </View>
    )
  }

  const renderChangeEmailInput = () => {
    return (
      <View style={{ padding: 16, marginBottom: 30 }}>
        <Text style={{ fontWeight: '500', color: '#333', marginVertical: 10 }}>Địa chỉ email</Text>
        <TTextInput
          typeInput={'code'}
          typeRadius={'rounded'}
          keyboardType="email-address"
          maxLength={320}
          autoCapitalize={'sentences'}
          defaultValue={email || ''}
          placeholder={t('Nhập email')}
          placeholderStyle={{ textAlign: 'center' }}
          onChangeText={(e) => setEmail(e.trim())}
          iconRightClick={() => setEmail('')}
          iconRight={<Icon name='close-circle-outline' size={24} color='#a0a0a0' />}
        />
      </View>
    )
  }

  function onCloseModal() {
    // navigation.dispatch(StackActions.popToTop())
    navigation.dispatch(
      CommonActions.reset({
        index: 0, // Đặt index về 0 để chỉ về màn hình đầu tiên
        routes: [{ name: SCREENS.homeStack }], // Thay 'HomeScreen' bằng tên của màn hình đầu tiên của bạn
      })
    )
    setIsShowModalSuccess(false)
  }

  return (
    <Fragment>
      <SafeAreaView style={{ flex: 1, backgroundColor: '#fff', marginTop: -4 }}>
        <Header
          leftComponent={<ButtonBack style={{ color: '#333' }} onPress={goBack}/>}
          centerComponent={{ text: 'Xác nhận', style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
          containerStyle={common.headerContainer}
          // rightComponent={<TouchableOpacity onPress={onUpdateProfile}>
          //   <Text style={styles.textSave}>{t('SAVE')}</Text>
          // </TouchableOpacity>}
          statusBarProps={{ barStyle: 'light-content' }}
          ViewComponent={LinearGradient}
          linearGradientProps={linearGradientProps}
        />
        <ScrollView refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh}/>} style={{ flex: 1 }}>
          {renderAvatar()}
          {renderInfo()}

        </ScrollView>
        {/* <Modalize
          HeaderComponent={renderHeaderModalize}
          ref={modalizeRef}
          adjustToContentHeight
          keyboardAvoidingBehavior={'padding'}
        >
          <EditPassword closeModal={onCloseModel}/>
        </Modalize> */}
        <Modalize
          HeaderComponent={renderHeaderChangeName}
          ref={modalChangeName}
          // modalHeight={responsiveHeight(50)}
          adjustToContentHeight
          keyboardAvoidingBehavior={'padding'}
        >
          {renderChangeNameInput()}
        </Modalize>
        <Modalize
          HeaderComponent={renderHeaderChangeEmail}
          ref={modalChangeEmail}
          // modalHeight={responsiveHeight(50)}
          adjustToContentHeight
          keyboardAvoidingBehavior={'padding'}
        >
          {renderChangeEmailInput()}
        </Modalize>
        <DateTimePickerModal
          isVisible={pickerDateVisible}
          // mode={true}
          locale="vi_VN"
          cancelTextIOS={t('CANCEL')}
          confirmTextIOS={t('XACNHAN')}
          headerTextIOS={strPiker}
          onConfirm={handleConfirm}
          onCancel={hidePicker}
          isDarkModeEnabled={false}
        />
        <ConfirmDialog confirmText={t('DONGY')} cancelText={t('CANCEL')} isVisible={isShowDeleteAccount} message={`Nếu đồng ý xóa tài khoản bạn sẽ không thể đăng nhập hoặc khôi phục lại các thông tin của tài khoản ${profileStore.phone}\n\nBạn chắc chắn đồng ý xóa tài khoản khỏi hệ thống maxQ.`} title={'Xoá tài khoản'}
          onConfirm={() => {
            setIsShowDeleteAccount(false)
            // setTimeout(() => {
            //   setShowConfirmPassword(true)
            // }, 200)
          }}
          onClosed={
            () => setIsShowDeleteAccount(false)
          }
        />
        <DialogInput
          isDialogVisible={showConfirmPassword}
          submitInput={(inputText) => {
            setShowConfirmPassword(false)
            requestLockAccount(inputText)
          }}
          hintInput={t('Nhập mật khẩu xác nhận')}
          hintTextColor={'#acb1c0'}
          closeDialog={() => {
            setShowConfirmPassword(false)
          }}
          title={t('Xác nhận mật khẩu')}
          message={t('Mật khẩu')}
          cancelText={'Hủy'}
          submitText={'Xác nhận'}
        />
        <View style={ styles.footerView}>
          <TButton typeRadius={'rounded'} title="Xác nhận" onPress={() => setIsShowModalSuccess(!isShowModalSuccess)} />
        </View>
        <ModalSuccess type="success" title="Ứng lương thành công!" message="Bạn đã ứng lương thành công tại hệ thống. Mã yêu cầu #************." isVisible={isShowModalSuccess} close={onCloseModal} />
      </SafeAreaView>
    </Fragment>
  )
})
