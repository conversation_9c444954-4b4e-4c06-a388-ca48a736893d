import React from 'react'
import { observer } from 'mobx-react-lite'
import { View } from 'react-native'
import { useTranslation } from 'react-i18next'
import { useStores } from '@app/models'
import GallerySwiper from 'react-native-gallery-swiper'
import { Header } from 'react-native-elements'
import { ButtonBack } from '@app/components'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { useNavigation, useRoute } from '@react-navigation/native'
import LinearGradient from 'react-native-linear-gradient'

export const PetImageDetail = observer(function PetAlbumsScreen(props: any) {
  const { t } : any = useTranslation()
  const route : any = useRoute()
  const { index } = route.params
  const { carStore } = useStores()
  const { goBack } = useNavigation()

  return (
    <View style={{ flex: 1 }}>
      <Header
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
        centerComponent={{ text: t('Gallery'), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        containerStyle={[common.headerContainer]}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      <GallerySwiper
        initialPage={index}
        images={carStore.dataAlbums}
      />
    </View>
  )
})
