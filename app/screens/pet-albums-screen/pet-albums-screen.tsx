import React, { useEffect, useImperative<PERSON>andle, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { View, TouchableOpacity, FlatList, Image, Dimensions, Text } from 'react-native'
import { useTranslation } from 'react-i18next'
import { useStores } from '@app/models'
import { Api } from '@app/services/api'

import { SCREENS } from '@app/navigation'
import { useNavigation } from '@react-navigation/native'
import styles from '@app/screens/car-profile-screen/styles'
import { builderPathImage } from '@app/utils'

const { API_URL } = require('../../config/env')

export const PetAlbumsScreen = observer(function PetAlbumsScreen(props: any, ref) {
  const { t } : any = useTranslation()
  const { navigate } = useNavigation()
  const { carStore } = useStores()
  const [albums, setAlbums] = useState([])

  useEffect(() => {
    loadData()
  }, [])

  useImperativeHandle(ref, () => {
    return {
      reloadAlbums: loadData
    }
  })

  // const handlerNoImage = () => {
  //   if (props.type && props.type === 'avatar') {
  //     setBase64('data:image/jpeg;base64,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')
  //   } else {
  //     setBase64('data:image/png;base64,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')
  //   }
  // }

  const loadData = async () => {
    const api = new Api()
    const result = await api.getAlbums(props.id)
    if (result?.data?.data) {
      const images = await Promise.all(result?.data?.data?.photos.map(async (item) => {
        return item
        // return await getBase64(item)
        // return { source: { uri: imageDownload, dimensions: { width: 1080, height: 1080 } } }
      }))
      __DEV__ && console.log('images', images)
      const imageRebuild = images.map((image) => {
        return { source: { uri: builderPathImage(image) } }
      })
      __DEV__ && console.log('imageRebuild', imageRebuild)
      carStore.setDataAlbums(imageRebuild)
      setAlbums(imageRebuild)
    }
  }

  const renderItem = ({ item, index }) => {
    return (
      <TouchableOpacity
        onPress={() => navigate(SCREENS.petImageDetail, { index: index })}
      >
        <Image source={item.source} style={{ width: (Dimensions.get('window').width - 48) / 3, height: 120, marginRight: 8, marginTop: 8 }}></Image>
      </TouchableOpacity>
    )
  }

  return (albums.length > 0
    ? <View style={{ flex: 1, marginLeft: 16 }}>
      <FlatList
        // horizontal={true}
        data={albums}
        showsHorizontalScrollIndicator={false}
        renderItem={renderItem}
        keyExtractor={item => item._id}
        numColumns={3}
      />
    </View> : <TouchableOpacity style={{ alignItems: 'center' }}>
      {/* <Image source={iconAdd} style={styles.iconAdd}></Image> */}
      <Text style={styles.textAdd}>{t('Press_plus_in_the_right_corner_to_add_picture_to_albums')}</Text>
    </TouchableOpacity>
  )
}, { forwardRef: true })
