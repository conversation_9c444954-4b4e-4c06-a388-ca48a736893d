import { Dimensions, StyleSheet } from 'react-native'
import { ifIphoneX } from 'react-native-iphone-x-helper'
import { responsiveWidth } from 'react-native-responsive-dimensions'
import { color, spacing, typography } from '../../theme'

const tab1ItemSize = (Dimensions.get('window').width - 30) / 5
const { width } = Dimensions.get('window')
const styles = StyleSheet.create({
  unknownDistance: {
    color: '#d2d2d2',
    fontFamily: typography.normal,
    fontSize: 12,
    marginBottom: 5,
  },
  price: {
    color: '#333',
    fontSize: 14,
    fontWeight: '600'
  },
  renderItem: {
    flexDirection: 'row',
  },
  viewTimePicker: {
    backgroundColor: '#fff',
    flexDirection: 'column',
    justifyContent: 'space-between',
    marginBottom: 5,
    paddingHorizontal: 15,
    paddingVertical: 10
  },
  viewTypePet: {
    backgroundColor: color.primaryBackground,
    borderRadius: 3,
    marginRight: 10
  },
  textTypePet: {
    paddingHorizontal: 10,
    paddingVertical: 12
  },
  label: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    textAlign: 'center',
  },
  classifyActive: {
    backgroundColor: '#fff',
    borderColor: color.primary,
    borderRadius: 3,
    borderWidth: 1
  },
  classifyNormal: {
    backgroundColor: color.primary,
    borderColor: '#f3f3f3',
    borderRadius: 3,
    borderWidth: 1
  },
  seeMoreAddress: {
    fontFamily: typography.normal,
    fontSize: 12,
    fontStyle: 'italic',
    paddingBottom: 15,
    paddingHorizontal: 20,
    paddingTop: 10,
    textDecorationLine: 'underline',
  },
  tab: {
    backgroundColor: '#ffffff',
    elevation: 0,
    shadowOpacity: 0,
  },
  indicator: {
    backgroundColor: 'rgba(255, 207, 216, 0.5)',
    borderRadius: 1.5,
    height: 3,
    width: 125,
  },

  // swipe
  container: {
    backgroundColor: color.primaryBackground,
    // paddingBottom: 30
  },
  containerSwiper: {
    flexDirection: 'row',
    height: 275,
    width: '100%',
  },
  wrapper: {
    height: '100%',
  },
  slide: {
    flex: 1,
    width: '100%',
  },
  image: {
    height: '100%',
    width,
  },
  paginationStyle: {
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 8,
    bottom: 9,
    flexDirection: 'row',
    height: 20,
    justifyContent: 'center',
    position: 'absolute',
    right: 15,
    width: 45
  },
  paginationText: {
    alignItems: 'center',
    color: '#ffffff',
    fontFamily: typography.normal,
    fontSize: 12,
    letterSpacing: 0,
    textAlign: 'center',
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  viewTouch: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 15,
    height: 30,
    justifyContent: 'center',
    left: 15,
    position: 'absolute',
    top: 30,
    width: 30,
  },
  viewTouchRight: {
    position: 'absolute',
    right: 15,
    top: 70,
  },
  viewInfo: {
    flexDirection: 'column',
    flex: 1,
    marginLeft: 15,
    marginRight: 15,
    marginTop: 12,
  },
  textTitle: {
    color: '#333',
    elevation: 2,
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
    letterSpacing: 0,
    lineHeight: 30,
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: {
      width: 0,
      height: 2,
    },
    textShadowRadius: 4,
  },
  viewAddress: {
    // alignItems: 'flex-start',
    // flexDirection: 'row',
    marginRight: 40,
    marginTop: 15,
    paddingBottom: 10,
    width: responsiveWidth(70)
  },
  viewIconLeft: {
    alignItems: 'center',
    flexDirection: 'row',
    marginLeft: 1
  },
  viewIconLeft1: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 15,
  },
  viewIconLocation: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  textAdress: {
    color: '#000000',
    fontFamily: typography.normal,
    fontSize: 12,
    letterSpacing: 0,
    marginHorizontal: 5,
  },
  textPoin: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 12,
    letterSpacing: 0,
    marginLeft: 5,
  },
  textPoinLocation: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 12,
    letterSpacing: 0,
    marginLeft: 3,
  },
  textCountrate: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 10,
    letterSpacing: 0,
    opacity: 0.58,
  },
  viewtextService: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: 15,
    marginTop: 14,
    marginBottom: 5
  },
  textService: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    justifyContent: 'flex-start',
    letterSpacing: 0,
    textAlign: 'left',
  },
  textComment: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
  },
  topShowall: {
    flex: 1,
    marginRight: 15,
  },
  textTop: {
    color: '#ff8900',
    fontFamily: typography.normal,
    fontSize: 14,
    justifyContent: 'flex-end',
    letterSpacing: 0,
    marginLeft: 2,
    textAlign: 'right',
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
    ...ifIphoneX({
      marginTop: 0
    }, {
      marginTop: 5
    }),
  },
  containerFlatlist: {
    flexDirection: 'column',
    marginLeft: 15,
    marginRight: 15,
  },
  viewtextTitleFlatlist: {
    flexDirection: 'row',
    flex: 1,
    marginTop: 32,
    width: responsiveWidth(85)
  },
  textTitleFlatlist: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    justifyContent: 'flex-start',
    letterSpacing: 0,
    textAlign: 'left',
  },
  textTopFlatlist: {
    color: '#ff8900',
    fontFamily: typography.normal,
    fontSize: 14,
    justifyContent: 'flex-end',
    letterSpacing: 0,
    marginLeft: 2,
    textAlign: 'right',
    textDecorationLine: 'underline',
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',

  },
  // tabService test sau nay bo di hoac chuyenr sang file khac
  containerService: {
    flexDirection: 'row',
    marginBottom: 15,
    marginHorizontal: spacing.small
  },
  containerServiceChoose: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    marginBottom: 5,
    paddingHorizontal: 15,
    paddingVertical: 20
  },
  boxContainer: {
    borderBottomColor: '#e0e0e0',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: spacing.small,
    marginTop: 20,
    paddingBottom: 15,
  },
  viewImageService: {
    alignItems: 'center',
    borderRadius: 4,
    // height: tab1ItemSize,
    justifyContent: 'center',
    marginLeft: 0,
    padding: 2,
    backgroundColor: '#fff',
    borderColor: color.primaryBackground,
    borderWidth: 1
  },
  imageChoose: {
    borderRadius: 4,
    height: 80,
    width: 80
  },
  viewService: {
    flexDirection: 'column',
    flex: 1,
    justifyContent: 'space-between',
    marginLeft: 10,
  },
  textTitleService: {
    color: '#333',
    fontSize: 14,
    fontWeight: '600',
  },
  textContent: {
    color: '#46474D',
    fontSize: 12,
    marginBottom: 10,
    // width: responsiveWidth(75)
  },
  viewIcon: {
    alignItems: 'flex-end',
    flex: 1,
    justifyContent: 'flex-end',
  },
  textPriceMinMax: {
    color: '#f3373a',
    fontSize: 14,
    fontWeight: '600',
    lineHeight: 16
  },
  viewTextLabel: {
    alignItems: 'center',
    // backgroundColor: '#edf1f7',
    flexDirection: 'row',
    // height: 30,
    paddingVertical: 20,
  },
  viewTextLabelTotalmoney: {
    alignItems: 'center',
    // backgroundColor: '#edf1f7',
    flexDirection: 'row',
    marginBottom: 15
  },
  viewTextLabelAddressUser: {
    alignItems: 'center',
    backgroundColor: '#edf1f7',
    flexDirection: 'row',
    height: 30,
    marginBottom: 10
  },
  imageService: {
    borderRadius: 8,
    height: 80,
    width: 80
  },
  textLabel: {
    color: '#333',
    fontSize: 14,
    fontWeight: '600',
  },
  textAddaddress: {
    color: '#333',
    fontSize: 14,
    marginLeft: 15,
    textAlign: 'right',
    textDecorationLine: 'underline'
  },
  viewPicker: {
    backgroundColor: '#fff',
    flex: 1,
    marginBottom: 5,
    padding: 15
  },
  viewPickerWeight: {
    borderColor: '#edf1f7',
    borderRadius: 22,
    borderStyle: 'solid',
    borderWidth: 1,
    flexDirection: 'row',
    flex: 1,
    height: 44,
    alignItems: 'center',
  },
  viewFlatListPrice: {
    // marginTop: 8,
    backgroundColor: '#fff',
    marginBottom: 5,
    paddingVertical: 10,
    paddingHorizontal: 15
  },
  viewTotalPrice: {
    // marginTop: 8,
    backgroundColor: '#fff',
    paddingVertical: 10,
    paddingHorizontal: 15
  },
  viewFlatListBranch: {
    backgroundColor: '#fff',
    paddingHorizontal: 15
  },
  textInput: {
    backgroundColor: '#ffffff',
    borderColor: '#edf1f7',
    borderRadius: 22,
    borderStyle: 'solid',
    borderWidth: 1,
    height: 44,
    marginTop: '5%',
    paddingLeft: 14,
    width: 315,
  },
  viewContainerDatetime: {
    flexDirection: 'row',
    flex: 1,
    marginTop: 10,
  },
  viewBtnDatetime: {
    flexDirection: 'row',
    flex: 1,
    height: 44,
    borderRadius: 22,
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#edf1f7',
    alignItems: 'center',

  },
  viewBtntime: {
    flexDirection: 'row',
    flex: 1,
    height: 44,
    borderRadius: 22,
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#edf1f7',
    alignItems: 'center',
    marginLeft: 10,
  },
  iconPiker: {
    marginLeft: 10,
  },
  iconPikerRight: {
    marginLeft: 10,
    marginRight: 6
  },
  strPiker: {
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 14,
    marginLeft: 6,
  },
  viewTextNote: {
    marginBottom: 20,
    marginHorizontal: spacing.small,
  },
  viewAddressUser: {
    marginLeft: 15,
    marginRight: 15
  },
  textInputNote: {
    backgroundColor: '#f7f9fc',
    borderColor: '#EFEFEF',
    borderRadius: 4,
    borderWidth: 1,
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginTop: 6,
    minHeight: 80,
    padding: 12,
    textAlign: 'left',
    textAlignVertical: 'top',
  },
  viewBtnSubmit: {
    flex: 1,
    flexDirection: 'row',
    marginLeft: 15,
    marginRight: 15,
    marginTop: 15,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  btnSubmit: {
    alignContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ff8ba1',
    borderRadius: 8,
    flex: 1,
    fontSize: 14,
    fontWeight: 'bold',
    height: 44,
    justifyContent: 'center',
    letterSpacing: 0,
    textAlign: 'center',
  },
  textBtnSubmit: {
    color: '#ffffff',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold',
    height: 20,
    letterSpacing: 0,
    textAlign: 'center',
  },
  renderItemListPrice: {
    alignItems: 'center',
    backgroundColor: '#f3f3f3',
    borderRadius: 4,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 4,
    paddingHorizontal: 15,
    paddingVertical: 12
  },
  totalPrice: {
    alignItems: 'center',
    borderRadius: 4,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 4,
  },
  renderItemListBranch: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 5,
    paddingLeft: 15,
    paddingRight: 15
  },
  renderItemListClassify: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    // marginBottom: 15,
    marginTop: 5,
    paddingRight: 10
  },
  boxAddress: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    paddingBottom: 12,
    paddingLeft: 15,
  },
  renderPhoneNumber: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingLeft: 15,
    paddingRight: 15
  },
  boxViewBranch: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    flexDirection: 'column',
    height: 125,
    justifyContent: 'space-between',
    marginBottom: 25,
    marginLeft: 10,
    marginRight: 20,
    marginTop: 25,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 1,
    shadowRadius: 10
  },
  textBranch: {
    color: '#333',
    fontSize: 14,
    fontWeight: 'bold',
  },
  textClassify: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    paddingHorizontal: 20,
    paddingVertical: 12
  },
  textAddressBranch: {
    color: '#333',
    flex: 1,
    flexWrap: 'wrap',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 7,
    width: '100%',
  },
  textAddressPhone: {
    color: '#acb1c0',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 7,
    paddingBottom: 10
  },
  boxContact: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    height: 125,
    marginBottom: 20,
    marginLeft: 15,
    marginRight: 10,
    marginTop: 24,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 1,
    shadowRadius: 10,
    width: 288
  },
  couponText: {
    color: 'rgba(0, 0, 0, 0.85)',
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 15,
    textDecorationLine: 'underline'
  },
  couponTextNone: {
    color: 'rgba(0, 0, 0, 0.85)',
    fontSize: 14,
    fontStyle: 'italic',
    fontWeight: 'bold',
    marginLeft: 15,
    textDecorationLine: 'underline'
  },
  containerCoupon: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  textSelectWeight: {
    color: '#46474D',
    fontSize: 14,
    width: 120
  },
  textSelectWeightActive: {
    color: '#333',
    fontSize: 14,
    width: 120
  },
  containerAddressUS: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
    marginRight: 15
  },
  btnSubmitCT: {
    width: (responsiveWidth(100) - 45) / 2,
  },
  btnXemthem: {
    // alignSelf: 'flex-start',
    borderColor: color.primaryBackground,
    // borderTopWidth: 1,
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 15,
    // marginTop: 20,
    alignItems: 'center',
    backgroundColor: '#fff',
    // borderBottomWidth: 5
  },
  txtXemthem: {
    color: '#979797',
    // flex: 1,
    fontSize: 14,
    marginRight: 8
  }
})
export default styles
