import React, { useContext, useEffect, useState, forwardRef, useImperativeHandle } from 'react'
import { TouchableOpacity, View, Text, Alert, useWindowDimensions } from 'react-native'
import Icon from 'react-native-vector-icons/Ionicons'
import styles from './styles'
import DialogInput from 'react-native-dialog-input'
import { useTranslation } from 'react-i18next'
import validate from 'validate.js'
import { useStores } from '@app/models'
import { PickerSelect } from '../../components/picker-select/picker-select'
import { DateTimePickerCustom, RenderClassify, LazyImage } from '../../components'
import { SCREENS } from '@app/navigation'
import { useNavigation } from '@react-navigation/native'
import SimpleToast from 'react-native-simple-toast'
import { ModalContext } from '@app/context'
import { color } from '@app/theme'
import moment from 'moment-timezone'
import { responsiveWidth } from 'react-native-responsive-dimensions'
import { BookingType } from '@app/constants/bookingType'
import { CheckBox } from 'react-native-elements'
import RenderHtml from 'react-native-render-html'

const numeral = require('numeral')

export interface RenderChooseServiceProps {
  /**
   * An optional style override useful for padding & margin.
   */
  storeId:any
  chooseData:any
  dataBranch:any
  bookingData:any
  type:any
  onClose?:any,
  ref: any
  validate?: any
}

export const borderActive = {
  borderWidth: 1,
  borderColor: color.primary,
  borderRadius: 3,
  backgroundColor: '#fff'
}

export const RenderChooseService = forwardRef((props: RenderChooseServiceProps, ref) => {
  const { t } : any = useTranslation()
  const regex = new RegExp('VND', 'g')
  // serviceStore.typeBooking
  const { navigate } = useNavigation()
  const { serviceStore, bookingStore, profileStore } = useStores()
  const [chooseData, setChooseData] = useState(props.chooseData)
  const [weight, setWeight] = useState(null)
  const [time, setTime] = useState(0)
  const [timeEnd, setTimeEnd] = useState(0)
  const [note, setNote] = useState('')
  const storeId = props.storeId
  const [selectedAddressIndex, setSelectedAddressIndex] = useState(0)
  const [selectedAddressUserIndex, setSelectedAddressUserIndex] = useState(0)
  const [selectedClassifyIndex, setSelectedClassifyIndex] = useState(0)
  const [priceMin, setPriceMin] = useState('')
  const [priceMax, setPriceMax] = useState('')
  const [priceBooking, setPriceBooking] = useState(0)
  const [itemPriceView, setItemPriceView] = useState([])
  const [branchName, setBranchName] = useState('') // TODO: không sử dụng xoá đi
  const [branchAddress, setBranchAddress] = useState('') // TODO: không sử dụng xoá đi
  const [branchPhone, setBranchPhone] = useState('')
  const [dataBranch, setDataBranch] = useState(props.dataBranch)
  const [itemPrices, setItemPrices] = useState([])
  const [isSubmitting, setSubmitting] = useState(false)
  const [isValidCoupon, setIsValidCoupon] = useState(false)
  const [feeShip, setFeeShip] = useState(0)
  const [totalPrice, setTotalPrice] = useState(0)
  const [isDialogVisible, setIsDialogVisible] = useState(false)
  const [isPickerSelectVisible, setIsPickerSelectVisible] = useState(false)
  const [strTypePet, setStrTypePet] = useState('')
  const [typePet, setTypePet] = useState(null)
  const [itemTypePet, setItemTypePet] = useState(null)
  const [addressPickup, setAddressPickup] = useState('')
  const [classifySubId, setClassifySubId] = useState('')
  const { showError, showCustomError, showCustomSuccess } = useContext(ModalContext)
  const [isErrorStartDate, setIsErrorStartDate] = useState(false)
  const [isErrorEndDate, setIsErrorEndDate] = useState(false)
  const { goBack } = useNavigation()
  const [selectedWeightIndex, setSelectedWeightIndex] = useState(-1)
  const [id, setId] = useState('')
  const [registerType, setRegisterType] = useState({
    isBuy: true,
    isDriveTest: false
  })
  const [isShowMore, setIsShowMore] = useState(false)
  const { width } = useWindowDimensions()

  // 0 Chó || 1 Mèo || 2: xe khác || 3: Tất cả
  const dataTypePet = [
    // { value: '', label: t('DOG'), _id: 'dog' },
    // { value: '', label: t('CAT'), _id: 'cat' },
    { value: '', label: t('OTHERPET'), _id: 'other' }]

  useImperativeHandle(ref, () => {
    return {
      bookingService: bookingService,
      bookingCar: bookingCar,
    }
  })

  useEffect(() => {
    if (new Date(time).getTime() <= new Date().getTime()) {
      console.log('time', new Date(time).getTime())
      console.log('timeNow', new Date().getTime())
    }
    props.validate(validateFields())
    if (props.type === BookingType.PARKING) {
      if (time === 0 || timeEnd === 0) {
        setTotalPrice(0)
      }
    }
  }, [totalPrice, time, timeEnd, weight, typePet])

  const formatMoney = (value) => {
    return numeral(value).format('0,0')
  }

  const getChooseData = (chooseData) => {
    return chooseData && chooseData.classify && chooseData.classify.length
  }

  const validateFields = () => {
    if (props.type === BookingType.PARKING) {
      // parking
      return (time === 0) || validate.isEmpty(weight) || (timeEnd === 0) || (totalPrice === 0)
    } else if (props.type === BookingType.SPA) {
      // spa
      return (time === 0) || validate.isEmpty(weight) || (totalPrice === 0)
    } else if (props.type === BookingType.SHOWROOM) {
      // showRoom
      return totalPrice === 0
    } else {
      // các type còn lại
      return (time === 0) || validate.isEmpty(weight)
    }
  }

  const validateTimes = () => {
    if (props.type === BookingType.PARKING) {
      // parking
      return (time === 0) || (timeEnd === 0)
    } if (props.type === BookingType.SHOWROOM) {
      // showRoom Skip validate
      return false
    } else {
      // spa // clinic
      return (time === 0)
    }
  }

  useEffect(() => {
    __DEV__ && console.log('useEffect  selectedClassifyIndex  chooseData')
    setSelectedWeightIndex(-1)
    setTotalPrice(0)
    if (chooseData) {
      bookingStore.clearFields()
      setItemPriceView(null)
      setItemPrices([])
      setWeight(null)
      setStrTypePet(null)
      setItemPrices(
        getChooseData(chooseData) ? chooseData.classify[selectedClassifyIndex]?.data.map(item => {
          return { value: item.price, label: item.name, _id: item._id }
        }) : [{ value: formatMoney(chooseData.price), label: 'Giá chung', _id: 'giachung' }]
      )
      setPriceMin(getChooseData(chooseData) ? chooseData.classify[selectedClassifyIndex]?.data[0].price : formatMoney(chooseData.price))
      setPriceMax(getChooseData(chooseData) ? chooseData.classify[selectedClassifyIndex]?.data[chooseData.classify[selectedClassifyIndex]?.data.length - 1].price : '')
      setItemPriceView(getChooseData(chooseData) ? chooseData.classify[selectedClassifyIndex]?.data : [{
        price: formatMoney(chooseData.price),
        name: 'Giá chung'
      }])
    }
  }, [chooseData, selectedClassifyIndex])

  useEffect(() => {
    __DEV__ && console.log('selectedAddressIndex, dataBranch')
    if (dataBranch.length) {
      setBranchName(dataBranch[selectedAddressIndex].name)
      setBranchAddress(dataBranch[selectedAddressIndex].address)
      setBranchPhone(dataBranch[selectedAddressIndex].phone)
    }
  }, [selectedAddressIndex, dataBranch])

  useEffect(() => {
    __DEV__ && console.log('selectedAddressUserIndex')
    if (profileStore.addressList.length) {
      setAddressPickup(profileStore.addressList[selectedAddressUserIndex].address)
    }
  }, [selectedAddressUserIndex])

  useEffect(() => {
    __DEV__ && console.log('itemTypePet')
    if (itemTypePet) {
      setStrTypePet(itemTypePet.label)
      setTypePet(itemTypePet._id)
    }
  }, [itemTypePet])

  const resetForm = () => {
    setTime(0)
    setTimeEnd(0)
    setSelectedWeightIndex(-1)
    setWeight(null)
  }

  const resultApiMes = (rs) => {
    if (rs.data.message === 'Cân nặng phải nhỏ hơn 10 ký tự') {
      SimpleToast.show('Dịch vụ đang bị lỗi. Vui lòng chọn cân nặng khác')
      setSelectedWeightIndex(-1)
      setWeight(null)
      setTotalPrice(0)
    } else if (rs.data.message === 'ROOM_TIME_FAILED') {
      SimpleToast.show('Vui lòng chọn lại thời gian')
      setTimeout(() => { resetForm() }, 100)
    }
  }

  const bookingCar = () => {
    setTimeout(() => {
      const timeCheckIn = new Date(time).getTime()
      const timeCheckOut = new Date(timeEnd).getTime()
      const itemCart = {
        _id: chooseData._id,
        storeId: serviceStore.storeId,
        time: timeCheckIn,
        weight: weight,
        serviceName: chooseData.name,
        registerType: registerType.isBuy ? 'Mua' : 'Lái thử',
        branchPhone: branchPhone,
        price: totalPrice,
        timeCheckIn: timeCheckIn,
        timeCheckOut: timeCheckOut || 0,
        image: chooseData.image,
        shortDes: chooseData.shortDes
      }
      bookingStore.addCart(itemCart)
      props.onClose(1)
      navigate(SCREENS.shoppingCartScreen)
      setSubmitting(false)
    }, 500)
  }

  const bookingService = async (btn) => {
    const checkExist = bookingStore.cart.find(x => x._id == chooseData._id)
    setSubmitting(true)
    // if (typePet !== 0 && typePet === null) {
    //   showCustomError(t('CHUY'), t('ALERT_chuachon_loaithucung'))
    //   // Alert.alert(t('ALERT_chuachon_loaithucung'))
    //   setSubmitting(false)
    // } else
    //
    if (validateTimes()) {
      showCustomError(t('CHUY'), t('ERROR_SELECT_TIME'))
      // Alert.alert(t('ERROR_SELECT_TIME'))
      setSubmitting(false)
    } else if (!weight) {
      showCustomError(t('CHUY'), t('ALERT_chuanhap_cannang'))
      // Alert.alert(t('ALERT_chuanhap_cannang'))
      setSubmitting(false)
    } else {
      showCustomSuccess(t('CHUY'), t('Thêm dịch vụ vào giỏ thành công'))
      setTimeout(() => {
        const timeCheckIn = new Date(time).getTime()
        const timeCheckOut = new Date(timeEnd).getTime()
        __DEV__ && console.log('chooseData=>>>>>>>>', chooseData)
        const itemCart = {
          _id: chooseData._id,
          storeId: serviceStore.storeId,
          time: timeCheckIn,
          weight: weight,
          serviceName: chooseData.name,
          typePet: typePet,
          branchPhone: branchPhone,
          price: totalPrice,
          timeCheckIn: timeCheckIn,
          timeCheckOut: timeCheckOut || 0,
          image: chooseData.image,
          shortDes: chooseData.shortDes,
          branchList: chooseData?.branchId || []
        }
        // case : thanh toan ngay
        if (btn === 0) {
          if (checkExist) {
            props.onClose(1)
            navigate(SCREENS.shoppingCartScreen)
          } else {
            bookingStore.addCart(itemCart)
            props.onClose(1)
            navigate(SCREENS.shoppingCartScreen)
          }
        }
        // case: them gio hang
        if (btn === 1) {
          if (checkExist) {
            props.onClose(1)
          } else {
            bookingStore.addCart(itemCart)
            props.onClose(1)
          }
        }
        setSubmitting(false)
      }, 500)
    }
  }

  const onBookingRoomHotel = async () => {
    if (time && timeEnd && weight) {
      const body = {
        storeId: storeId,
        timeCheckOut: new Date(timeEnd).getTime(),
        time: new Date(time).getTime(),
        weight: weight,
        roomId: chooseData._id,
        classifySubId: classifySubId || 'data null',
        classifyId: chooseData?.classify && chooseData.classify.length ? chooseData.classify[selectedClassifyIndex]?._id : 'null',
        code: bookingStore.coupon
      }
      const rs: any = await bookingStore.handleCalculate(body)
      if (rs) {
        if (rs.data.error) {
          // Alert.alert(t('FAIL'), `${rs.data.message}`)
          resultApiMes(rs)
        } else {
          const data = rs?.data?.data
          setTotalPrice(data.totalPrice)
          setIsValidCoupon(data.discount && data.discount > 0)
          if (data.rsCoupon.error) {
            bookingStore.setCoupon('')
            setIsValidCoupon(false)
            setTimeout(() => Alert.alert(t('FAIL'), `${data.rsCoupon.msg}`), 500)
          }
        }
      }
    }
  }

  const renderListPrice = (item, index) => {
    return (
      <View>
        {selectedWeightIndex === index ? <TouchableOpacity
          key={index}
          style={[styles.renderItemListPrice, borderActive]}
          onPress={() => {
            setSelectedWeightIndex(index)
            onChooseWeight(item, index)
          }}>
          <Text style={{ color: '#979797', fontSize: 14, flex: 7 }}>{item.label}</Text>
          <Text style={[styles.price, { flex: 4, textAlign: 'right' }]}>{ getNumberPrice(item.value, 'đ')}</Text>
        </TouchableOpacity> : <TouchableOpacity
          key={index}
          style={styles.renderItemListPrice}
          onPress={() => {
            setSelectedWeightIndex(index)
            onChooseWeight(item, index)
          }}>
          <Text style={{ color: '#979797', fontSize: 14, flex: 7 }}>{item.label}</Text>
          <Text style={[styles.price, { flex: 4, textAlign: 'right' }]}>{ getNumberPrice(item.value, 'đ')}</Text>
        </TouchableOpacity>}
      </View>
    )
  }

  const onCalculate = async () => {
    if (props.type === BookingType.CLINIC || props.type === BookingType.SPA) {
      const rs = await bookingStore.checkCoupon(bookingStore.coupon || '', priceBooking, feeShip, storeId) // TODO goi api tinh gia
      if (rs) {
        if (rs && rs.data.error) {
          resultApiMes(rs)
        } else {
          const data = rs?.data?.data
          setTotalPrice(data.totalPrice)
          setIsValidCoupon(data.discount && data.discount > 0)
          if (data.rsCoupon.error) {
            bookingStore.setCoupon('')
            setIsValidCoupon(false)
            setTimeout(() => Alert.alert(t('FAIL'), `${data.rsCoupon.msg}`), 500)
          }
        }
      }
    }
    if (props.type === BookingType.PARKING) {
      await onBookingRoomHotel()
      // call api tinh gia tien
    }
    if (props.type === BookingType.SHOWROOM) {
      setTotalPrice(priceBooking)
      // call api tinh gia tien
    }
  }

  const onEnterCoupon = async (inputText) => {
    if (inputText === '' || !inputText) {
      setSubmitting(false)
      SimpleToast.show('Bạn chưa nhập mã nhập mã giá')
    } else if (inputText) {
      setIsDialogVisible(false)
      bookingStore.setCoupon(inputText)
    }
  }

  const onChooseWeight = (item, index) => {
    const itemData = itemPrices[index]
    if (itemData && itemData.label && itemData.value) {
      const regex = new RegExp(',', 'g')
      const Value = item.value.replace(regex, '')
      const partValue = Value.replace('VND', '')
      const priceChoose = parseInt(partValue)
      setWeight(itemData.label)
      setPriceBooking(priceChoose)
      setClassifySubId(item._id)
    }
  }

  useEffect(() => {
    console.log('props.type', props.type)
    onCalculate()
  }, [priceBooking, bookingStore.coupon])

  useEffect(() => {
    if (props.type === BookingType.PARKING) {
      validateTime(0)
      onCalculate()
    } else {
      validateTime(1)
      onCalculate()
    }
  }, [time, timeEnd])
  //
  // useEffect(() => {
  //   debugger
  //   if (registerType.isBuy) {
  //     onCalculate()
  //   }
  //   return () => {
  //
  //   }
  // }, [registerType])

  const handlerStartTime = (time) => {
    setTime(time)
  }

  const handlerEndTime = (endTime) => {
    setTimeEnd(endTime)
  }

  const validateTime = (type = 0) => {
    if (time > 0 && timeEnd > 0) {
      const start = (moment(time).format('YY/MM/DD HH:mm'))
      const end = (moment(timeEnd).format('YY/MM/DD HH:mm'))
      console.log('start', start)
      console.log('end', end)
      if (start >= end) {
        if (type === 0) {
          setTime(0)
          setTimeEnd(0)
          setTotalPrice(0)
          setIsErrorStartDate(true)
          setIsErrorEndDate(true)
          setTimeout(() => {
            setIsErrorStartDate(false)
            setIsErrorEndDate(false)
            SimpleToast.show(t('ERROR_SELECT_TIME'))
          }, 500)
          // Alert.alert(t('ERROR_SELECT_TIME'))
        } else if (type === 1) {
          setTotalPrice(0)
          setTimeEnd(0)
          setTime(0)
          setIsErrorStartDate(true)
          setIsErrorEndDate(true)
          // Alert.alert(t('ERROR_SELECT_TIME'))
          setTimeout(() => {
            setIsErrorStartDate(false)
            setIsErrorEndDate(false)
            SimpleToast.show(t('ERROR_SELECT_TIME'))
          }, 500)
        }
        __DEV__ && console.log(t('ERROR_SELECT_TIME'))
      }
    }
  }

  const getNumberPrice = (value, replaceWith = '') => {
    const trimValue = value.replace(regex, replaceWith)
    // console.log('trimValue', trimValue)
    // console.log('value', value)
    // console.log('value', regex)
    return trimValue
  }

  const renderTypePet = () => {
    return (
      <View>
        {dataTypePet ? <View style={styles.renderItem}>
          {dataTypePet.map((item) => renderItemTypePet(item))}
        </View> : null}
      </View>
    )
  }

  const renderItemTypePet = (item) => {
    return (
      <View>
        {id === item._id ? <TouchableOpacity
          key={item._id}
          onPress={() => {
            setId(item._id)
            setItemTypePet(item)
          }}
          style={[styles.viewTypePet, borderActive]}
        >
          <Text style={styles.textTypePet}>{item.label}</Text>
        </TouchableOpacity> : <TouchableOpacity
          onPress={() => {
            setId(item._id)
            setItemTypePet(item)
          }}
          style={styles.viewTypePet}
        >
          <Text style={styles.textTypePet}>{item.label}</Text>
        </TouchableOpacity> }
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <View style={styles.containerServiceChoose}>
        <TouchableOpacity>
          <View style={styles.viewImageService}>
            <LazyImage style={styles.imageChoose} source={{ uri: chooseData.image }} resizeMode="cover"/>
          </View>
        </TouchableOpacity>
        <View style={styles.viewService}>
          <TouchableOpacity>
            <Text style={styles.textTitleService}>{chooseData.name}</Text>
          </TouchableOpacity>
          <Text numberOfLines={3} style={styles.textContent}>{chooseData.shortDes}</Text>
          { priceMin.length > 1 ? <View>
            <Text
              style={styles.textPriceMinMax}>{priceMin ? getNumberPrice(priceMin, 'đ') : ''} {priceMax ? '-' : 'đ'} {priceMax ? getNumberPrice(priceMax, 'đ') : ''}</Text>
          </View> : null}
        </View>
      </View>
      <View style={{ paddingHorizontal: 16, marginBottom: 5, backgroundColor: '#fff', paddingTop: isShowMore ? 10 : 0 }}>
        { isShowMore && <RenderHtml
          contentWidth={width}
          source={{ html: chooseData?.description }}
          ignoredTags={['script']}
          ignoredStyles={['font-family']}
          renderersProps={{
            img: {
              enableExperimentalPercentWidth: true
            }
          }}
        /> }
        <TouchableOpacity style={styles.btnXemthem} onPress={() => {
          setIsShowMore(!isShowMore)
        }}>
          {!isShowMore ? <View style={{ flexDirection: 'row' }}>
            <Text style={styles.txtXemthem}>{t('Xem chi tiết')}</Text>
            <Icon name={'chevron-down-outline'} size={18} color={'#979797'} />
          </View> : <View style={{ flexDirection: 'row' }}>
            <Text style={styles.txtXemthem}>{t('Đóng chi tiết')}</Text>
            <Icon name={'chevron-up-outline'} size={18} color={'#979797'} />
          </View>}
        </TouchableOpacity>
      </View>
      <View style={styles.viewFlatListBranch}>
        {/* <RenderBrand dataBranch={dataBranch} selectedAddressIndex={(selectedAddressIndex) => { */}
        {/*  setSelectedAddressIndex(selectedAddressIndex) */}
        {/* }}/> */}
      </View>
      <View style={styles.viewTimePicker}>
        {props.type === BookingType.CLINIC || props.type === BookingType.PARKING || props.type === BookingType.SPA ? <View style={{ width: responsiveWidth(100) - 29 }}>
          <Text style={styles.textLabel}>{t('Check_in_time')}</Text>
          <View style={styles.viewContainerDatetime}>
            <DateTimePickerCustom isClear={isErrorStartDate} setStrDate={t('Choose_time')}
              setStrTime={t('Choose_time')} time={(time) => handlerStartTime(time)}/>
          </View>
        </View> : null}
        {props.type == BookingType.SHOWROOM
          ? <View style={{ display: 'flex', flexDirection: 'column' }}>
            <View style={{ display: 'flex', flexDirection: 'row', marginVertical: 10 }}>
              <CheckBox
                // iconType='ionicon'
                title={'Đăng ký mua xe'}
                checkedIcon='dot-circle-o'
                uncheckedIcon='circle-o'
                checkedColor={color.primary}
                checked={registerType.isBuy}
                onPress={() => {
                  setRegisterType({
                    isDriveTest: false,
                    isBuy: !registerType.isBuy
                  })
                }}
                textStyle={{ fontWeight: 'normal' }}
                containerStyle={{ padding: 0, margin: 0, marginLeft: 0, alignSelf: 'center', borderWidth: 0, backgroundColor: '#fff' }}
                size={20}
              />
              <CheckBox
                // iconType='ionicon'
                title={'Đăng ký lái thử'}
                checkedIcon='dot-circle-o'
                uncheckedIcon='circle-o'
                checkedColor={color.primary}
                checked={registerType.isDriveTest}
                onPress={() => {
                  setRegisterType({
                    isBuy: false,
                    isDriveTest: !registerType.isDriveTest
                  })
                }}
                textStyle={{ fontWeight: 'normal' }}
                containerStyle={{ padding: 0, margin: 0, marginLeft: 0, alignSelf: 'center', borderWidth: 0, backgroundColor: '#fff' }}
                size={20}
              />
            </View>
            <View style={{ width: responsiveWidth(100) - 29 }}>
              <Text style={styles.textLabel}>{t('Check_in_time')}</Text>
              <View style={styles.viewContainerDatetime}>
                <DateTimePickerCustom isClear={isErrorStartDate} setStrDate={t('Choose_time')}
                  setStrTime={t('Choose_time')} time={(time) => handlerStartTime(time)}/>
              </View>
            </View>
          </View> : null}
        <View style={{ width: responsiveWidth(100) - 29 }}>
          {props.type === BookingType.PARKING ? <View style={{ marginTop: 10 }}>
            <Text style={styles.textLabel}>{t('Check_out_time')}</Text>
            <View style={styles.viewContainerDatetime}>
              <DateTimePickerCustom isClear={isErrorEndDate} setStrDate={t('Choose_time')} setStrTime={t('Choose_time')}
                time={(endTime) => handlerEndTime(endTime)}/>
            </View>
          </View> : null}
        </View>
      </View>
      <View style={styles.viewFlatListPrice}>
        <View style={{ marginBottom: 10 }}>
          {/* <View style={styles.viewTextLabel}> */}
          {/*  /!*<Text style={styles.textLabel}>{t('TYPEPET')}</Text>*!/ */}
          {/* </View> */}
          <RenderClassify chooseData={chooseData} selectedClassifyIndex={(selectedClassifyIndex) => {
            setSelectedClassifyIndex(selectedClassifyIndex)
          }}/>
        </View>
        {/* <View style={{ paddingBottom: 20 }}>
          <Text style={styles.textLabel}>{chooseData.classify && chooseData.classify.length > 0 ? chooseData.classify[selectedClassifyIndex].name : 'N/A'}</Text>
        </View> */}
        {itemPrices.map((item, index) => renderListPrice(item, index))}
      </View>
      { priceMin.length > 1 ? <View style={styles.viewTotalPrice}>
        <View style={styles.viewTextLabelTotalmoney}>
          <Text style={styles.textLabel}>{t('TOTAL_MONEY')}</Text>
        </View>
        <View style={styles.totalPrice}>
          <Text>{t('SERVICE_PRICE')}</Text>
          <Text style={styles.price}>{formatMoney(totalPrice)} đ</Text>
        </View>
      </View> : null}
      {/* <View style={styles.viewBtnSubmit}> */}
      {/*  <TButton typeRadius={'rounded'} disabled={validateFields() || isSubmitting} loading={isSubmitting} title={t('THANHTOAN')} */}
      {/*    onPress={() => bookingService(0)} buttonStyle={styles.btnSubmitCT}/> */}
      {/*  <TButton disabled={ validateFields() || isSubmitting} buttonStyle={styles.btnSubmitCT} loading={isSubmitting} title={t('THEMVAOGIOHANG')} */}
      {/*    onPress={() => bookingService(1)} typeRadius={'rounded'}/> */}
      {/* </View> */}
      <PickerSelect title={chooseData?.name || 'N/A'} isVisible={isPickerSelectVisible} data={itemPrices} onSelect={(item, index) => {
        onChooseWeight(item, index)
        setIsPickerSelectVisible(!isPickerSelectVisible)
      }}
      goBack={() => {
        setIsPickerSelectVisible(!isPickerSelectVisible)
      }}
      callBackVisible={() => {
        setIsPickerSelectVisible(!isPickerSelectVisible)
      }}
      />

      <DialogInput
        isDialogVisible={isDialogVisible}
        submitInput={(inputText) => {
          onEnterCoupon(inputText)
        }}
        hintInput={t('INPUT_COUPON')}
        hintTextColor={'#acb1c0'}
        closeDialog={() => {
          setIsDialogVisible(false)
        }}
        title={t('INPUT_COUPON')}
        message={t('INPUT_COUPON')}
        cancelText={'Hủy'}
        submitText={'Xác nhận'}
      />
    </View>
  )
})
