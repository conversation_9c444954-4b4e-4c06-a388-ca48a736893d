import { useNavigation } from '@react-navigation/native'
import { observer } from 'mobx-react-lite'
import React, { useEffect, useRef, useState } from 'react'
import {
  GiftedChat,
  Bubble,
  InputToolbar,
  ActionsProps,
  Actions,
  SendProps,
  Composer,
  Send
} from 'react-native-gifted-chat' // 0.3.0
// import { useStores } from "../models/root-store"
import { NavigationStackScreenProps } from 'react-navigation-stack'
import { color, spacing } from '@app/theme'
import { View, Keyboard } from 'react-native'
import { useTranslation } from 'react-i18next'
import Icon from 'react-native-vector-icons/Feather'
import EmojiSelector, { Categories } from 'react-native-emoji-selector'
import { getBottomSpace, isIphoneX } from 'react-native-iphone-x-helper'
import ImagePicker from 'react-native-image-crop-picker'
import Thumbs from '@app/screens/share-view/Thumbs'
import { canUploadFile } from '@app/utils/chat/media'
// import { sendFileMessage } from '@app/lib/methods/sendFileMessage'
import { useStores } from '@app/models'
import { Header } from 'react-native-elements'
import { ButtonBack } from '@app/components'
import { common, linearGradientProps } from '@app/theme/styles/common'
import firestore from '@react-native-firebase/firestore'
import { SCREENS } from '@app/navigation'
import LinearGradient from 'react-native-linear-gradient'
import auth from '@react-native-firebase/auth'
import { Api } from '@app/services/api'

const imagePickerConfig = {
  cropping: true,
  writeTempFile: true
}

const libraryPickerConfig = {
  multiple: true,
  maxFiles: 1,
  mediaType: 'photo'
}

// const videoPickerConfig = {
//   mediaType: 'video'
// }

export interface ChatScreenProps extends NavigationStackScreenProps<{}> {}

export type ChatFuncProps = ChatScreenProps

export const ChatScreen: React.FunctionComponent<ChatFuncProps> = observer((props: any) => {
  const { navigate, goBack, canGoBack } = useNavigation()
  const { t } : any = useTranslation()
  const [messages, setMessages] = useState([])
  const [messageText, setMessageText] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [showAccessory, setShowAccessory] = useState(false)
  const { profileStore } = useStores()
  const { toUserId, roomName, rId, id, productId } = props.route.params
  const chatRef = useRef(null)
  // const { sendJsonMessage, currentMessage, user, fetchUserData } = useContext(WebSocketContext)
  const [inputToolbarHeight, setInputToolbarHeight] = useState(44)
  const [reloadData, setReloadData] = useState(false)
  const [attachments, setAttachments] = useState(null)
  const [selected, setSelected] = useState(null)
  const [flagGetAttachment, setFlagGetAttachment] = useState(false)
  const chatRoomsRef = firestore().collection('chatRooms')
  const [docId, setDocId] = useState(null)
  const [roomData, setRoomData] = useState(null)

  // const didShow = (height) => {
  //   console.log('Keyboard show. Height is ' + height)
  // }
  //
  // const didHide = () => {
  //   console.log('Keyboard hide')
  // }
  //
  // const [keyboardHeight] = useKeyboard(didShow, didHide) /* initialize the hook (optional parameters) */

  useEffect(() => {
    setMessages([])
    // getContent()
    createRoomIfNotExist().then(r => {
      markReadAllMessages().then(r => {
      })
    })
  }, [])

  const loginFirebase = async () => {
    const getAuth = auth().currentUser
    if (!getAuth) {
      await auth().signInAnonymously()
    }
  }

  const onGoBack = () => {
    if (canGoBack()) {
      goBack()
    } else {
      navigate(SCREENS.chatRoomScreen)
    }
  }

  const createRoomIfNotExist = async () => {
    await loginFirebase() // đăng nhập firebase guest nếu chưa đăng nhập
    // nếu bắn roomId qua thì dùng luôn
    if (rId && rId !== '') {
      setDocId(rId)
      const roomData = await chatRoomsRef.doc(rId).get()
      if (roomData.exists) {
        setRoomData(roomData.data())
      }
    } else {
      // trường hợp chưa chat lần nào thì tạo mới
      const roomId = profileStore._id + '_' + toUserId
      const checkExistRoom = await chatRoomsRef.where('roomId', '==', roomId).get()
      // nếu chưa có room thì tạo room mới

      if (checkExistRoom.empty) {
        const room: any = {
          name: roomName ?? toUserId,
          roomId: roomId,
          fromId: profileStore._id,
          toUserId: toUserId,
          fromName: profileStore.fullName,
          members: [profileStore._id, toUserId],
        }
        if (productId) {
          room.product_id = productId
        }
        chatRoomsRef.add(room).then((rs) => {
          setDocId(rs.id)
        })
      } else {
        checkExistRoom.forEach((doc) => {
          setDocId(doc.id) // id room chat firebase document
        })
      }
    }
  }

  useEffect(() => {
    const messagesListener = docId !== null ? chatRoomsRef
      .doc(docId)
      .collection('messages')
      .orderBy('createdAt', 'desc')
      .onSnapshot(querySnapshot => {
        const messages = querySnapshot.docs.map(doc => {
          const firebaseData = doc.data()

          const data: any = {
            _id: doc.id,
            text: '',
            createdAt: new Date().getTime(),
            ...firebaseData
          }

          if (!firebaseData.system) {
            data.user = {
              ...firebaseData.user,
              // name: firebaseData.user.name
            }
          }

          return data
        })

        setMessages(messages)
      }) : () => {}

    // Stop listening for updates whenever the component unmounts
    return () => messagesListener()
  }, [docId])

  /**
   * cập nhật số msg chưa đọc cho người nhận
   */
  const updateUnreadCount = () => {
    if (toUserId != 0) {
      let query: any = chatRoomsRef
        .doc(docId)
        .collection('messages')
      query = query.where('unread_' + toUserId, '==', true)
      // query = query.where('read', '==', false)
      query.get().then(function(querySnapshot) {
        const data = {}
        data['unReadCount_' + toUserId] = querySnapshot.size
        updateRoom(data)
      })
    }
  }

  const sendMessage = async (messages) => {
    const text = messages[0].text
    const message = {
      text: text,
      createdAt: new Date().getTime(),
      userId: profileStore._id,
      user: {
        _id: profileStore._id,
        name: profileStore.fullName
      },
      read: false
    }

    message['unread_' + toUserId] = true

    await chatRoomsRef.doc(docId)
      .collection('messages')
      .add(message)

    // update Room
    const roomUpdate = {
      latestMessage: {
        text,
        createdAt: new Date().getTime()
      }
    }

    updateUnreadCount()
    // cập nhật lại avatar
    if (profileStore && profileStore.picture) {
      roomUpdate['avatar_' + profileStore._id] = profileStore.picture
      roomUpdate['displayName_' + profileStore._id] = profileStore.fullName
    }

    await updateRoom(roomUpdate)

    const api = new Api()
    // gửi thông báo
    // TODO: send thông báo notification
    await api.pushFcmNotification(toUserId, profileStore._id, text)
  }

  const updateRoom = async (data) => {
    await chatRoomsRef
      .doc(docId)
      .set(data,
        { merge: true }
      )
  }

  /**
   * update unread
   */
  const markReadAllMessages = async () => {
    await chatRoomsRef
      .doc(docId)
      .collection('messages')
      .get().then(function(querySnapshot) {
        querySnapshot.forEach(function(doc) {
          const data = {}
          data['unread_' + profileStore._id] = false
          __DEV__ && console.log('****', data)
          doc.ref.update(data)
        })
      })

    const room = {}
    room['unReadCount_' + profileStore._id] = 0
    await updateRoom(room)
  }

  useEffect(() => {
    __DEV__ && console.log('showAccessory useEffect ' + showAccessory)
    // tat keyboard khi show emoji
    if (showAccessory) {
      Keyboard.dismiss()
      // chatRef?.current.setKeyboardHeight(0)
    }
  }, [showAccessory])

  const userData = {
    name: profileStore.fullName,
    _id: profileStore._id,
  }

  const renderBubble = props => {
    return (
      <Bubble
        {...props}
        wrapperStyle={{
          right: {
            backgroundColor: color.primary,
          },
        }}
      />
    )
  }

  const onChooseFromLibrary = async() => {
    // logEvent(events.ROOM_BOX_ACTION_LIBRARY);
    try {
      // @ts-ignore
      const attachments = await ImagePicker.openPicker(libraryPickerConfig)
      __DEV__ && console.log(attachments)
      setAttachments(attachments)
      setFlagGetAttachment(true)
    } catch (e) {
      // logEvent(events.ROOM_BOX_ACTION_LIBRARY_F);
    }
  }

  const takePhoto = async() => {
    // logEvent(events.ROOM_BOX_ACTION_PHOTO);
    try {
      const image = await ImagePicker.openCamera(imagePickerConfig)
      __DEV__ && console.log('ImagePicker.openCamera(imagePickerConfig) ', image)
      if (canUploadFile(image)) {
        setAttachments(attachments)
        setFlagGetAttachment(true)
      }
    } catch (e) {
      // logEvent(events.ROOM_BOX_ACTION_PHOTO_F)
    }
  }

  const renderActions = (props: Readonly<ActionsProps>) => {
    return (
      <>
        {/* <Actions */}
        {/*  {...props} */}
        {/*  icon={() => ( */}
        {/*    <Icon name={'plus-circle-outline'} size={24} color={color.dim} /> */}
        {/*  )} */}
        {/*  onPressActionButton={onActionPlus} */}
        {/* /> */}
        <Actions
          {...props}
          icon={() => (
            <Icon name={'image'} size={24} color={color.dim} />
          )}
          onPressActionButton={onChooseFromLibrary}
        />
        <Actions
          {...props}
          icon={() => (
            <Icon name={'camera'} size={24} color={color.dim} />
          )}
          onPressActionButton={takePhoto}
        />
      </>
    )
  }

  const renderSend = (props: SendProps<any>) => {
    return (
      <Send
        {...props}
        disabled={!props.text}
        containerStyle={{
          width: 30,
          height: 44,
          alignItems: 'center',
          justifyContent: 'center',
          marginHorizontal: 8,
        }}
      >
        <Icon name={'send'} size={26} color={!props.text ? color.dim : color.primary} />
      </Send>
    )
  }

  const renderComposer = (props) => {
    return (
      <>
        <Composer
          {...props}
          textInputStyle={{
            color: '#222B45',
            backgroundColor: '#EDF1F7',
            fontSize: 13,
            borderWidth: 1,
            borderRadius: 8,
            borderColor: '#E4E9F2',
            paddingTop: 6,
            paddingHorizontal: 12,
            paddingRight: 40,
            paddingBottom: 6,
            marginLeft: spacing.small,
          }}
        />
        {/* <TouchableOpacity style={{ position: 'absolute', right: 56, bottom: 10 }} onPress={() => setShowAccessory(!showAccessory)}> */}
        {/*  <Icon name={'sticker-emoji'} size={24} color={color.dim} /> */}
        {/* </TouchableOpacity> */}
      </>
    )
  }

  const renderInputToolbar = props => {
    // console.log('renderInputToolbar')
    // console.log('chatRef?.current.getMinInputToolbarHeight() ' + chatRef?.current.getMinInputToolbarHeight())
    return (
      <InputToolbar
        {...props}
        renderComposer={renderComposer}
        // renderActions={renderActions}
        renderSend={renderSend}
        accessoryStyle={{ height: 302 }}
        containerStyle={{
          backgroundColor: '#fff',
          borderTopColor: '#E8E8E8',
          paddingVertical: 5
          // borderTopWidth: 1,
          // marginBottom: isIphoneX ? 8 : 0
        }}
      />
    )
  }

  const renderEmoji = () => {
    return (
      <View style={{ flex: 1 }}>
        <EmojiSelector
          columns={12}
          category={Categories.emotion}
          showSearchBar={false}
          onEmojiSelected={emoji => {
            setMessageText(prev => prev + ' ' + emoji)
            setTimeout(() => {
              chatRef?.current.focusTextInput()
            }, 100)
          }}
        />
      </View>
    )
  }

  const selectFile = (item) => {
    // const { attachments, selected } = this.state
    if (attachments?.length > 0) {
      // const { text } = this.messagebox.current
      const newAttachments = attachments.map((att) => {
        if (selected && (att.path === selected.path)) {
          // att.description = text
        }
        return att
      })
      setAttachments(newAttachments)
      setSelected(item)
      // return this.setState({ attachments: newAttachments, selected: item });
    }
  }

  const removeFile = (item) => {
    // const { selected, attachments } = this.state
    let newSelected
    // if (item.path === selected.path) {
    //   const selectedIndex = attachments.findIndex(att => att.path === selected.path)
    //   // Selects the next one, if available
    //   if (attachments[selectedIndex + 1]?.path) {
    //     newSelected = attachments[selectedIndex + 1]
    //     // If it's the last thumb, selects the previous one
    //   } else {
    //     newSelected = attachments[selectedIndex - 1] || {}
    //   }
    // }
    setAttachments(attachments.filter(att => att.path !== item.path))
    // this.setState({ attachments: attachments.filter(att => att.path !== item.path), selected: newSelected ?? selected });
    setSelected(newSelected ?? selected)
  }

  useEffect(() => {
    __DEV__ && console.log('after delete', attachments)
  }, [attachments])

  const renderFooter = () => {
    return (
      <View style={{ marginBottom: 10 }}>
        <Thumbs
          attachments={attachments}
          theme={'light'}
          isShareExtension={null}
          // isShareExtension={this.isShareExtension}
          onPress={selectFile}
          onRemove={removeFile}
        />
      </View>
    )
  }

  // const renderMessageVideo = (props) => {
  //   console.log(props.currentMessage.video)
  //   return <View style={{ position: 'relative', height: 150, width: 250 }}><Video
  //     style={{
  //       position: 'absolute',
  //       left: 0,
  //       top: 0,
  //       height: 150,
  //       width: 250,
  //       borderRadius: 20
  //     }}
  //     resizeMode="cover"
  //     height={150}
  //     width={250}
  //     muted={true}
  //     source={{ uri: props.currentMessage.video }}
  //     allowsExternalPlayback={false}/></View>
  // }

  // @ts-ignore
  return <View style={{ backgroundColor: '#fff', flex: 1 }}>
    {/* <Header */}
    {/*  // statusBarProps={{ barStyle: 'light-content' }} */}
    {/*  // barStyle="light-content" // or directly */}
    {/*  leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={onGoBack}/>} */}
    {/*  centerComponent={{ text: roomName, style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }} */}
    {/*  containerStyle={common.headerContainer} */}
    {/* /> */}
    <Header
      statusBarProps={{ barStyle: 'light-content' }}
      backgroundColor={'transparent'}
      barStyle="light-content" // or directly
      leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={onGoBack}/>}
      centerComponent={{ text: roomName, style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
      ViewComponent={LinearGradient}
      linearGradientProps={linearGradientProps}
      containerStyle={common.headerContainer}
    />
    <GiftedChat
      ref={chatRef}
      textInputProps={{
        onFocus: () => {
          __DEV__ && console.log('showAccessory ' + showAccessory)
          __DEV__ && console.log('chatRef?.current.getMinInputToolbarHeight() ' + chatRef?.current.getMinInputToolbarHeight())
          if (showAccessory === true) {
            setShowAccessory(false)
            const tempText = messageText
            // reset chạy nếu text !== null nên cần tips
            // if (!messageText || messageText === '') {
            //   setMessageText(' ')
            // }
            setTimeout(() => {
              chatRef?.current.resetInputToolbar()
            }, 1)
            if (chatRef?.current.getMinInputToolbarHeight() > 44) {
              setInputToolbarHeight(44)
            }
            setTimeout(() => {
              setMessageText(tempText)
            }, 100)
          }
        }
      }}
      // isKeyboardInternallyHandled={false}
      isTyping={isTyping}
      isCustomViewBottom={true}
      // renderMessage={renderMessage}
      renderChatFooter={renderFooter}
      // renderMessageVideo={renderMessageVideo}
      renderInputToolbar={renderInputToolbar}
      renderBubble={renderBubble}
      // minInputToolbarHeight={isIphoneX ? 50 : 44}
      // renderAccessory={showAccessory ? renderFooter : null}
      renderAvatarOnTop
      alwaysShowSend={true}
      // renderUsernameOnMessage
      bottomOffset={isIphoneX ? getBottomSpace() : -12}
      // wrapInSafeArea={false}
      onInputTextChanged={setMessageText}
      placeholder={'Nhập nội dung chat...'} // TODO: translate
      messages={messages}
      text={messageText}
      onSend={sendMessage}
      user={userData} />
  </View>
})
