import { StyleSheet, Dimensions } from 'react-native'
import { typography } from '../../theme'
const { width, height } = Dimensions.get('window')
const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    height: '100%',
    marginTop: -4
  },
  des: {
    backgroundColor: '#fff',
    color: '#333',
    fontSize: 12,
    padding: 16,
  },
  icArrowBack: {
    paddingRight: 20
  },
  newsPicture: {
    height: 230,
    width: '100%'
  },
  newsTitle: {
    backgroundColor: '#fff',
    color: '#333',
    fontSize: 17,
    fontWeight: 'bold',
    padding: 16
  },
  title: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 17,
    fontWeight: 'bold',
    marginLeft: -15
  },
  viewBTTop: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between'
  }
})
export default styles
