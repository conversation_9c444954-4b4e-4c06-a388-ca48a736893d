import React, { useEffect, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { View, Text, ScrollView, useWindowDimensions } from 'react-native'
import { useNavigation, useRoute } from '@react-navigation/native'
import { useStores } from '@app/models'
import RenderHtml from 'react-native-render-html'
import { ButtonBack, LazyImage, PlaceHolder } from '@app/components'
import styles from './styles'
import { useTranslation } from 'react-i18next'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import LinearGradient from 'react-native-linear-gradient'
import { SafeAreaView } from 'react-native-safe-area-context'

const renderersProps = {
  img: {
    enableExperimentalPercentWidth: true
  }
}

export const NewsDetailScreen = observer(function NewsDetailScreen() {
  const route: any = useRoute()
  const { id } = route?.params
  const { homeStore } = useStores()
  const [news, setNews] = useState(null)
  const { navigate, goBack } = useNavigation()
  const { t } : any = useTranslation()
  const { width } = useWindowDimensions()

  useEffect(() => {
    loadData()
  }, [])
  const loadData = async () => {
    const rs = await homeStore.getNewsDetail(id)
    if (rs.kind === 'ok' && rs?.data?.data) {
      const data = rs.data.data
      setNews(data)
    }
  }

  function computeEmbeddedMaxWidth(contentWidth, tagName) {
    if (tagName === 'img') {
      return 50
    }
    return contentWidth
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* <View style={styles.viewBTTop}> */}
      {/*  <ButtonBack onPress={goBack} style={styles.icArrowBack}/> */}
      {/*  <Text style={styles.title}>{t('News')}</Text> */}
      {/*  <View></View> */}
      {/* </View> */}
      <Header
        // statusBarProps={{ barStyle: 'light-content' }}
        // barStyle="light-content" // or directly
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
        centerComponent={{ text: t('News'), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
        //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
        // </TouchableOpacity> : null}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      <ScrollView showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
        {!news ? <PlaceHolder/>
          : <View>
            <Text style={styles.newsTitle}>{news.title}</Text>
            <LazyImage style={styles.newsPicture} source={{ uri: news.thumbail }}></LazyImage>
            <Text style={styles.des}>{news.description}</Text>
            <View style={{ flexGrow: 0, backgroundColor: '#fff', paddingBottom: 15, paddingHorizontal: 16 }}>
              <RenderHtml
                contentWidth={width}
                source={{ html: news?.content }}
                renderersProps={renderersProps}
                ignoredTags={['script']}
                ignoredStyles={['font-family']}
              />
            </View>
          </View>
        }
      </ScrollView>
    </SafeAreaView>
  )
})
