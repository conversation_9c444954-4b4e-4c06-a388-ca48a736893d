import React, { useContext, useImperativeHandle } from 'react'
import { observer } from 'mobx-react-lite'
import { Alert, TouchableOpacity, View } from 'react-native'
import { LazyImage, Text } from '../../components'
import styles from '@app/screens/add-diary-screen/styles'
import ImagePicker from 'react-native-image-crop-picker'
import { useStores } from '@app/models'
import { Api } from '@app/services/api'
import { ModalContext } from '@app/components/modal-success'
import { useTranslation } from 'react-i18next'

export interface ModalAddImage {
  handleClose?:any
  id?: any
}

export const UpdatePetAlbumsScreen = observer(function UpdatePetAlbumsScreen(props: ModalAddImage, ref) {
  const { t } : any = useTranslation()
  const { carStore } = useStores()
  const { showError, showSuccess } = useContext(ModalContext)

  useImperativeHandle(ref, () => {
    return {
      albums: update
    }
  })

  const update = async () => {
    if (props.id === '' || carStore.imageOfAlbums === '') {
      showError(t('FAIL'), t('Vui lòng chọn ảnh !'))
    } else {
      const path = carStore.imageOfAlbums
      const picture = !path ? '/user/default-avatar.jpg' : path
      if (picture !== '') {
        carStore.setImageOfAlbums(picture)
      }
      const body = {
        petId: props.id,
        picture: picture,
      }
      const api = new Api()
      const rs = await api.updateAlbums(body)
      if (rs && !rs.data.error) {
        showSuccess(t('THANHCONG'), t('Ảnh của bạn đã được lưu vào albums'))
        props.handleClose(true)
      } else {
        // showError(t('FAIL'), t(`${rs.data.message}`))
        Alert.alert('Lỗi')
      }
    }
  }

  const getPicture = () => {
    ImagePicker.openPicker({
      writeTempFile: true,
      width: 200,
      mediaType: 'photo',
      height: 200,
      compressImageMaxHeight: 200,
      compressImageMaxWidth: 200,
      cropping: true,
      multiple: false,
    }).then((response: any) => {
      console.log(response)
      // setAvatar(response.path)
      const files = []
      files.push({
        uri: response.path,
        name: 'picture',
        fileName: response.filename + '.png',
        type: response.mime,
        size: response.size,
      })
      carStore.updateAlbums('user/api/upload-image', files)
      // profileStore.uploadImageDropBox(files).then(rs => {
      // })
    })
  }

  return (
    <View style={styles.fieldChoosePicture}>
      <Text style={styles.label}>Chọn ảnh:</Text>
      {/* <Image source={iconAdd}></Image> */}
      <View style={{ flexDirection: 'row' }}>
        <TouchableOpacity onPress={getPicture} style={styles.touchUser}>
          <LazyImage
            resizeMode="cover"
            style={styles.image}
            source={{ uri: carStore.imageOfAlbums }}
          />
          {/* <Icon style={styles.add_circle} size={24} color={'#00e096'} name={'add-circle'}/> */}
        </TouchableOpacity>
        <View></View>
      </View>
    </View>
  )
}, { forwardRef: true })
