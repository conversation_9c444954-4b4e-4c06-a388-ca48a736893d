import { KeyboardAvoidingView, Platform, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useState } from 'react'
import { ButtonBack, TButton, TTextInput } from '@app/components'
import LinearGradient from 'react-native-linear-gradient'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { CheckB<PERSON>, Header } from 'react-native-elements'
import { useTranslation } from 'react-i18next'
import { useNavigation } from '@react-navigation/native'
import { SafeAreaView } from 'react-native-safe-area-context'
import FastImage from 'react-native-fast-image'
import {
  cardKH, logoBank
} from '@app/assets/images'

import Icon from 'react-native-vector-icons/Ionicons'
import { recommendAmount } from '@app/screens/salary-advance/data'
import { SCREENS } from '@app/navigation'
export const SalaryAdvanceScreen = () => {
  const { t } : any = useTranslation()
  const { goBack, navigate } = useNavigation()
  const [amount, setAmount] = useState('')
  const [check, setCheck] = useState(false)

  const objData = {
    ma_gd: '#************',
    ten_ng_ung: 'Nguyễn Thục Hiền',
    tk_nhan: '************',
    bank_name: 'PVcomBank',
    so_tien: 1000000,
    tong_so_tien: 1000000,
    phi_gd: 'Miễn phí',
    phi_ql: 'Miễn phí',
  }

  const onGoBack = () => {
    goBack()
  }
  const onChangeText = (str) => {
    setAmount(str)
  }
  const TextInline = ({ previousValue, value }: {previousValue: string, value: string}) => {
    return (
      <View style={{
        borderBottomWidth: 1,
        borderColor: '#ddd',
        padding: 12,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <Text style={{ color: 'black' }}>{previousValue}</Text>
        <Text style={{ color: 'black' }}>{value}</Text>
      </View>
    )
  }
  const addRecommend = (amount) => {
    setAmount(amount.value.toString())
  }
  const addBankAccount = () => {
    navigate(SCREENS.addBankAccount)
  }
  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <Header
        // barStyle="light-content" // or directly
        leftComponent={<ButtonBack style={{ color: 'black' }} onPress={onGoBack}/>}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
        centerComponent={{ text: t(t('Ứng lương')), style: { color: 'black', fontWeight: 'bold', fontSize: 16 } }}
      />
      <KeyboardAvoidingView
        behavior={Platform.OS == 'ios' ? 'padding' : 'height'}
        style={styles.background}
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          <FastImage resizeMode={FastImage.resizeMode.stretch} style={{ width: '100%', height: 158, marginBottom: 28 }} source={cardKH}></FastImage>
          <View style={styles.contentContainer}>
            <View style={{ padding: 12 }}>
              <Text style={{ marginBottom: 10, fontWeight: '600' }}>Nhập số tiền bạn cần ứng</Text>
              <TTextInput
                keyboardType="number-pad"
                maxLength={12}
                autoCapitalize={'none'}
                placeholder={'0'}
                value={amount}
                placeholderStyle={{ textAlign: 'center' }}
                onChangeText={onChangeText}
                style={{ textAlign: 'right' }}
                iconRight={<Text>đ</Text>}
                containerStyle={{ backgroundColor: 'white', borderColor: 'grey', borderWidth: 1, borderRadius: 7 }}
              />
              <View style={styles.recommentContainer}>
                {recommendAmount.map((amount) => (
                  <TouchableOpacity key={amount.id} style={styles.button} onPress={() => addRecommend(amount)}>
                    <Text style={styles.text}>{amount.label}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            <View style={styles.bankSelectContainer}>
              <Text style={{ fontWeight: '600', marginBottom: 10 }}>Chọn ngân hàng nhận tiền</Text>
              <TouchableOpacity style={styles.bankSelecter} onPress={addBankAccount}>
                <FastImage resizeMode={FastImage.resizeMode.stretch} style={{ width: 30, height: 30 }} source={logoBank}></FastImage>
                <Text style={{ flex: 1, paddingLeft: 10 }}>ViettinBank</Text>
                <Icon name="chevron-forward-outline" size={30} color="#a6e7be" />
              </TouchableOpacity>
            </View>
            <TextInline previousValue={'Số tiền ứng'} value={amount}></TextInline>
            <TextInline previousValue={'Phí giao dịch'} value={'Miễn phí'}></TextInline>
            <TextInline previousValue={'Phí quản lý 1%'} value={(Number(amount) / 100).toString()}></TextInline>
            <View style={{
              padding: 12,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <Text style={{ color: 'black', fontWeight: '600' }}>{'Tổng tiền'}</Text>
              <Text style={{ color: 'black', fontWeight: '600' }}>{(Number(amount) / 100 + Number(amount)).toString()}</Text>
            </View>
            <View style={{ flexDirection: 'row', marginBottom: 12 }}>
              <CheckBox
                center
                checked={check}
                onPress={() => setCheck(!check)}
                containerStyle={styles.checkboxContainer}
              />
              <Text style={styles.term}>
                                Tôi đã đọc và đồng ý với các <Text style={styles.termLink}>điều khoản dịch vụ</Text> và <Text style={styles.termLink}>chính sách bảo vệ dữ liệu</Text> và <Text style={styles.termLink}>biểu phí giá dịch vụ</Text>
              </Text>
            </View>
          </View>
          <TButton onPress={() => navigate(SCREENS.confirmAdvanceSalary, { objData })} title={'Ứng lương'} buttonStyle={{ marginTop: 20 }}></TButton>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  background: {
    backgroundColor: '#fff',
    flex: 1,
    padding: 12
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  contentContainer: {
    borderColor: '#ddd',
    borderWidth: 1,
    borderRadius: 10
  },
  button: {
    backgroundColor: '#ddd',
    paddingVertical: 10,
    borderRadius: 5,
    margin: 5,
    width: '30%',
    alignItems: 'center',
    justifyContent: 'center'
  },
  text: {
    color: '#333',
    fontSize: 12,
    fontWeight: '600'
  },
  recommentContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 10
  },
  bankSelectContainer: {
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#ddd',
    padding: 12,
  },
  bankSelecter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#ebfbf1',
    borderColor: '#a6e7be',
    borderWidth: 1,
    paddingLeft: 10,
    paddingVertical: 10,
    borderRadius: 10
  },
  checkboxContainer: {
    alignItems: 'flex-start',
    backgroundColor: 'transparent',
    borderWidth: 0,
    padding: 0,

  },
  term: {
    width: '85%',
    color: 'black',
    fontSize: 14,
  },
  termLink: {
    color: 'red',
    fontSize: 14,
    fontWeight: '600'
  }
})
