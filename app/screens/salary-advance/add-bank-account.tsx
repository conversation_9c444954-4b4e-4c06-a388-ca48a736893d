import { KeyboardAvoidingView, Platform, ScrollView, StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { SafeAreaView } from 'react-native-safe-area-context'
import { color, spacing, typography } from '@app/theme'
import { ButtonBack, TButton, TTextInput } from '@app/components'
import { useTranslation } from 'react-i18next'
import { useNavigation } from '@react-navigation/native'
import { Header } from 'react-native-elements'
import common, { linearGradientProps } from '@app/theme/styles/common'
import LinearGradient from 'react-native-linear-gradient'
import FastImage from 'react-native-fast-image'
import { logoBank } from '@app/assets/images'

export default function AddBankAccount() {
  const { t } : any = useTranslation()
  const { goBack, navigate } = useNavigation()
  const renderInputLabel = (title) => {
    return (
      <Text style={{
        color: '#01061F',
        fontSize: 14,
        fontWeight: '500',
        marginBottom: spacing.small
      }}>{title}</Text>
    )
  }
  const onUpdateProfile = () => {}
  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <Header
        // statusBarProps={{ barStyle: 'light-content' }}
        // barStyle="light-content" // or directly
        leftComponent={<ButtonBack style={{ color: 'black' }} onPress={goBack}/>}
        centerComponent={{ text: 'Thêm tài khoản', style: { color: 'black', fontWeight: 'bold', fontSize: 16 } }}
        // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
        //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
        // </TouchableOpacity> : null}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      <KeyboardAvoidingView
        behavior={Platform.OS == 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <ScrollView showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
          <View style={styles.viewContainer}>
            <View style={styles.bank}>
              <FastImage resizeMode={FastImage.resizeMode.stretch} style={{ width: 30, height: 30 }} source={logoBank}></FastImage>
              <Text style={{ flex: 1, paddingLeft: 10 }}>ViettinBank</Text>
            </View>
            <View style={styles.viewInputField}>
              {renderInputLabel('Số tài khoản')}
              <TTextInput
                typeInput={'code'}
                typeRadius={'rounded'}
                // keyboardType="phone-pad"
                maxLength={255}
                autoCapitalize={'sentences'}
                // defaultValue={street || ''}
                placeholder={'Nhập số tài khoản'}
                // onChangeText={e => setStreet(e)}

              />
            </View>
            <View style={styles.viewInputField}>
              {renderInputLabel('Chủ tài khoản')}
              <TTextInput
                typeInput={'code'}
                typeRadius={'rounded'}
                // keyboardType="phone-pad"
                maxLength={255}
                autoCapitalize={'sentences'}
                // defaultValue={street || ''}
                placeholder={'Chủ tài khoản'}
                // onChangeText={e => setStreet(e)}
              />
            </View>
            <View style={styles.viewInputField}>
              {renderInputLabel('CMND/CCCD')}
              <TTextInput
                typeInput={'code'}
                typeRadius={'rounded'}
                // keyboardType="phone-pad"
                maxLength={255}
                autoCapitalize={'sentences'}
                // defaultValue={street || ''}
                placeholder={'Nhập số CMND/CCCD'}
                // onChangeText={e => setStreet(e)}
              />
            </View>
            <View style={{ marginHorizontal: 26 }}>
              <Text style={{ marginHorizontal: 20, textAlign: 'center', color: '#e5e5e5', marginTop: 14, marginBottom: 24, fontSize: 12 }}>Mọi thông tin của bạn sẽ được bảo mật theo tiêu chuẩn quốc tế PCI DSS và ngân hàng liên kết</Text>
              <Text style={{ color: 'red', fontWeight: '600' }}>ĐIỀU KIỆN LIÊN KẾT</Text>
              <Text style={{ color: 'red', fontSize: 12, marginBottom: 30 }}>{'1. Tài khoản đăng ký dịch vụ SMS Banking hoặc Ngân hàng số\n' +
                                '2. Số điện thoại đăng ký maxQ và ngân hàng phải trùng nhau\n' +
                                '3. Thông tin họ tên, CMND/CCCD khai báo phải trùng với thông tin đăng ký ngân hàng\n' +
                                '4. Tài khoản này không được liên kết với tài khoản maxQ nào khác.'}</Text>
            </View>
            <TButton typeRadius={'rounded'} buttonStyle={styles.buttonAdd} title={'Liên kết ngân hàng'} onPress={onUpdateProfile} />

          </View>
        </ScrollView>
      </KeyboardAvoidingView>

    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  boxContainerModal: {
    // ...ifIphoneX({
    //   paddingBottom: 89,
    // }, {
    //   paddingBottom: 60,
    // }),
    flex: 1,
    paddingBottom: 30
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  buttonAdd: {
    alignItems: 'center',
    backgroundColor: '#0A5936',
    justifyContent: 'center',
    marginHorizontal: 32,
    marginTop: 30,
    textAlign: 'center',
    bottom: 0,
  },
  buttonMap: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderColor: '#edf1f7',
    borderRadius: 8,
    borderStyle: 'solid',
    borderWidth: 1,
    height: 44,
    justifyContent: 'center',
    marginLeft: 12,
    width: 48
  },
  container: {
    flex: 1
  },
  icArrowBack: {
    margin: 10,
  },
  iconLeft: {
    marginRight: 15
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: spacing.small,
    paddingVertical: 15,
  },
  selectProvince: {
    color: '#333',
    fontSize: 14,
    marginLeft: 15,
    marginTop: 26,
  },
  textButton: {
    color: '#ffffff',

    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center'
  },
  textInput: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderColor: '#edf1f7',
    borderRadius: 8,
    borderStyle: 'solid',
    borderWidth: 1,
    color: '#333',
    flexDirection: 'row',
    fontSize: 14,
    height: 44,
    justifyContent: 'space-between',
    marginLeft: 15,
    marginRight: 15,
    marginTop: 15,
    paddingLeft: 14
  },
  textMap: {
    backgroundColor: '#ffffff',
    borderColor: '#edf1f7',
    borderRadius: 8,
    borderStyle: 'solid',
    borderWidth: 1,
    height: 44,
    paddingLeft: 14,
    width: '83%'
  },
  textProvince: {
    color: '#c5cee0',
    fontFamily: typography.normal,
    fontSize: 14
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  textTitleTotal: {
    color: '#333',
    fontSize: 26,
    fontWeight: 'bold',
    marginLeft: 15,
    marginTop: 15,
  },
  viewButtonSubmit: { marginLeft: 15, marginRight: 15, marginTop: 15 },
  viewContainer: {
    justifyContent: 'space-between',
    marginTop: 27,
    paddingBottom: 60
  },
  viewInputField: {
    marginTop: 15,
    paddingHorizontal: 32,
  },
  viewMap: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 15,
    marginRight: 15
  },
  viewProvince: {
    alignItems: 'center',
    backgroundColor: color.primaryBackground,
    borderRadius: 4,
    flexDirection: 'row',
    height: 44,
    justifyContent: 'space-between',
    paddingLeft: 14
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  bank: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 10,
    paddingVertical: 10,
    marginLeft: 14
  },
})
