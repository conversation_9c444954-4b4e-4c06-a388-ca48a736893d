import React from 'react'
import { observer } from 'mobx-react-lite'
import styles from '@app/components/search-tab-rate-screen/style'
import { FlatList, View } from 'react-native'
import { SCREENS } from '@app/navigation'
import { StoreListItem } from '@app/components'
import { useNavigation } from '@react-navigation/native'
import { useStores } from '@app/models'
import { BookingType } from '@app/constants/bookingType'

export const TabProductRenderScreen = observer(function TabProductRenderScreen(props: any) {
  const { navigate } = useNavigation()
  const { productStore } = useStores()

  function gotoScreen(e) {
    navigate(SCREENS.productDetails, { id: e })
  }

  const renderItemProduct = ({ item }) =>
    (<StoreListItem type={BookingType.SHOP} item={{ ...item, serviceName: item.name, id: item._id }} onPress={(e) => {
      gotoScreen(e)
    }
    }/>)

  return (
    <View style={styles.flatListContainer} key={props.type}>
      <View style={styles.containerItem}>
        <FlatList
          data={productStore.productNears}
          showsHorizontalScrollIndicator={false}
          renderItem={renderItemProduct}
          extraData={productStore.productNears}
          keyExtractor={item => item._id.toString() + 'product'}/>
      </View>
    </View>
  )
})
