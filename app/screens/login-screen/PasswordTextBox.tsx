import React from 'react'
import { TextInput, View, StyleSheet } from 'react-native'
import Icon from 'react-native-vector-icons/Ionicons'

export const Password = props => {
  const [value, onChangeText] = React.useState(props.value)
  const [visible, setVisibility] = React.useState(false)
  const [errorStatus, displayErrors] = React.useState(false)

  const icon = !visible ? 'eye-outline' : 'eye-off-outline'
  const color = !visible ? '#c5cee0' : '#18203a'
  const height = (props.height)

  return (
    <View style={styles.passwordContainer}>
      <TextInput
        style={styles.inputStyle}
        onChangeText={text => {
          onChangeText(text)
          props.onChange(text)
        }}
        onBlur={() => {
          displayErrors(true)
        }}
        defaultValue={value}
        placeholder={props.label}
        secureTextEntry={!visible}
      />

      <Icon
        size={22}
        name={icon}
        color={color}
        onPress={() => setVisibility(!visible)}
        style={styles.iconEye}
      />
    </View>
  )
}
Password.defaultProps = {
  label: '',
  height: (20),
}
const styles = StyleSheet.create({
  iconEye: {
    height: 24,
    marginRight: 10,
    // marginTop: 13,
    width: 24,
  },
  inputStyle: {
    flex: 1,
    height: '100%',
    // marginTop: 13
  },
  passwordContainer: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderColor: '#edf1f7',
    borderRadius: 4,
    borderStyle: 'solid',
    borderWidth: 1,
    flexDirection: 'row',
    height: 44,
    marginTop: 20,
    // paddingBottom: 10,
    paddingLeft: 14,
    width: '100%',
  },
  viewIconeye: {
    position: 'absolute',
    top: 33,
  },
})
