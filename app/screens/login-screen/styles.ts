import { StyleSheet } from 'react-native'
import { color, typography } from '../../theme'

const styles = StyleSheet.create({
  btnFb: {
    backgroundColor: '#3B5999',
    borderRadius: 22,
    flexDirection: 'row',
    height: 44,
    marginTop: 15,
    padding: 10
  },
  buttonContainer: {
    marginTop: 50,
  },
  buttonDangky: {
    color: color.primary,
    fontFamily: typography.normal,
    fontSize: 14,
    marginLeft: 5,
    marginTop: 12,
    paddingTop: 5,
    textAlign: 'left',
  },
  buttonLoginText: {
    color: '#ffffff',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  container: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flex: 1,
    height: '100%',
    marginHorizontal: 30
  },
  content: {
    flex: 1,
    width: '100%'
  },
  flexCenter: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'center'
  },
  icArrowBack: {
    marginLeft: 11,
    marginTop: 10,
  },
  logoRed: {
    height: 75,
    marginBottom: 40,
    marginTop: 70,
    resizeMode: 'contain',
    width: 75
  },
  mainTextInput: {
    alignItems: 'center',
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
  },
  signInWithPhone: {
    alignItems: 'center',
    backgroundColor: '#BA68C8',
    borderRadius: 22,
    flexDirection: 'row',
    height: 44,
    justifyContent: 'center',
    marginTop: 10,
  },
  subTitle: {
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 14,
    marginTop: 12,
    paddingTop: 5,
    textAlign: 'center',
  },
  text: {
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginTop: 15,
    textAlign: 'right'

  },
  textBottom: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 30,
  },
  textButton: {
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 13,
    letterSpacing: 0,
    marginLeft: 8,
    marginTop: 3,
    textAlign: 'center'
  },
  textTitle: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
    marginTop: 35,
  },
  viewLogo: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 30,
    paddingRight: 30,
  },

})
export default styles
