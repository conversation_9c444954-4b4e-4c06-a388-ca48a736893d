import { Text, View, TouchableOpacity, Image, ScrollView } from 'react-native'
import React, { useContext, useState } from 'react'
import styles from './styles'
import { Password } from './PasswordTextBox'
import { observer } from 'mobx-react-lite'
import { useNavigation } from '@react-navigation/native'
import { useStores } from '@app/models'
import Icon from 'react-native-vector-icons/Ionicons'
import { SCREENS } from '@app/navigation'
import { useAuth } from '@app/use-hooks/use-auth'
import { logoMypet } from '../../assets/images'
import { useTranslation } from 'react-i18next'
import { ModalContext } from '@app/context'
import { TButton, TTextInput, ButtonBack } from '../../components'
import validate from 'validate.js'
// import { LoginManager, Profile } from 'react-native-fbsdk-next'
import { SafeAreaView } from 'react-native-safe-area-context'

export const Login = observer((props) => {
  const { t } : any = useTranslation()
  const [password, setPassword] = useState('')
  const { navigate, goBack } = useNavigation()
  const { accountStore, carStore, homeStore, profileStore } = useStores()
  const { signIn } = useAuth()
  const { showError, showSuccess } = useContext(ModalContext)
  // const { fetchUserData } = useContext(WebSocketContext)
  const [isSubmitting, setSubmitting] = useState(false)

  const goToRegisterScreen = () => navigate(SCREENS.register)

  const goToForgotPassword = () => {
    navigate(SCREENS.forgotPassword)
  }

  const handleLogin = async () => {
    setSubmitting(true)
    if (!accountStore.phoneNumber || !password) {
      showError(t('FAIL'), t('FILL_OUT_THE_FORM'))
    } else {
      setSubmitting(true)
      const rs = await accountStore.loginAccount({ phone: accountStore.phoneNumber, password })
      setSubmitting(false)
      if (rs && rs.kind === 'cannot-connect') {
        showError(t('FAIL'), t('CANNOT_CONNECT'))
        setSubmitting(false)
      } else if (rs && rs.data.error) {
        showError(t('FAIL'), t(`${rs.data.message}`))
        setSubmitting(false)
        if (t(`${rs.data.message}`) && t(`${rs.data.message}`) === 'Mật khẩu phải lớn hơn 5 ký tự') {
          showError(t('FAIL'), t('PASSWORD_MUST_BE_GREATER_THAN_5'))
          setSubmitting(false)
        } else if (t(`${rs.data.message}`) && t(`${rs.data.message}`) === 'Phone phải nhỏ hơn 12 ký tự') {
          showError(t('FAIL'), t('PHONE_MUST_BE_LESS_THAN_12'))
          setSubmitting(false)
        }
        setSubmitting(false)
      } else {
        setSubmitting(false)
        signIn() // call signIn update state
        // fetchUserData()
        // carStore.getListPet()
        homeStore.setReloadData(true)
        navigate(SCREENS.appLoading)
      }
    }
  }

  const loginFacebook = () => {
    LoginManager.logOut()
    LoginManager.logInWithPermissions(['public_profile']).then(
      function(result) {
        if (result.isCancelled) {
          __DEV__ && console.log('Login cancelled')
        } else {
          Profile.getCurrentProfile().then(
            async function(result) {
              __DEV__ && console.log('The current logged user is:', result)

              if (result?.userID) {
                const rs = await accountStore.loginAccount({ facebookId: result.userID })
                __DEV__ && console.log('rs-----login', rs)

                // trường hợp đã có tài khoản FB trong database
                if (!rs?.data?.error) {
                  await signIn()
                  await profileStore.getProfile()
                  // call signUp context
                  navigate(SCREENS.primaryStack) // redirect to home
                } else {
                  // trường hợp chưa có tài khoản FB thì sẽ chuyển sang màn đăng ký
                  const dataLogin = { fullName: result?.name || '', email: result.email || '', phone: '', picture: result.imageURL, facebookId: result.userID }
                  navigate(SCREENS.register, { data: dataLogin, registerType: 'FB' })
                }
              } else {
                // khong the dang nhap voi fb vui long thu lai sau
                __DEV__ && console.log('Login FBLoginCallback fail with error: ')
              }
            }
          )
        }
      },
      function(error) {
        __DEV__ && console.log('Login fail with error: ' + error)
      })
  }

  const validateFields = () => validate.isEmpty(accountStore.phoneNumber) || validate.isEmpty(password)

  function onChangeText(phone) {
    accountStore.setPhoneNumber(phone)
  }

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <ScrollView style={{ backgroundColor: '#fff' }}>
        <ButtonBack onPress={goBack} style={styles.icArrowBack}/>
        <View style={styles.container}>
          <View style={styles.content}>
            <Text numberOfLines={2} style={styles.textTitle}>{t('LOGIN')}</Text>
            <View style={styles.viewLogo}>
              <Image style={styles.logoRed} source={logoMypet}/>
            </View>
            <View style={styles.mainTextInput}>
              <TTextInput
                typeInput={'phone'}
                keyboardType="phone-pad"
                maxLength={12}
                autoCapitalize={'none'}
                value={accountStore.phoneNumber}
                placeholder={t('MOBILE')}
                placeholderStyle={{ textAlign: 'center' }}
                onChangeText={onChangeText}
                iconRightClick={() => { accountStore.setPhoneNumber('') }}
                iconRight={accountStore.phoneNumber?.length ? <Icon
                  name='close-circle'
                  size={24}
                  color='#c5cee0'
                /> : null }
              />
              <Password
                label={t('DANGNHAP_password')}
                onChange={e => setPassword(e)}
              />
            </View>
            <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
              <TouchableOpacity onPress={() => {
                goToForgotPassword()
              }}>
                <Text style={styles.text}>{t('FORGOTTEN_PASSWORD')}</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.buttonContainer}>
              <TButton disabled={validateFields() || isSubmitting} loading={isSubmitting} title={t('LOGIN')} onPress={handleLogin} />
            </View>
            {/* <View style={styles.buttonContainer}> */}
            {/*  <TButton buttonStyle={{ backgroundColor: '#3B5999' }} disabled={validateFields() || isSubmitting} loading={isSubmitting} title={t('DANGNHAP_loginwithfacebook')} onPress={loginfacebook} /> */}
            {/* </View> */}
            {/* <TouchableOpacity onPress={loginFacebook} style={styles.btnFb}> */}
            {/*  <View style={styles.flexCenter}> */}
            {/*    <Image style={{ width: 20, height: 19, resizeMode: 'contain' }} source={iconFacebook} /> */}
            {/*    <View> */}
            {/*      <Text style={{ */}
            {/*        ...styles.textButton, */}
            {/*        color: '#FFFFFF', */}
            {/*      }}> */}
            {/*        {t('DANGNHAP_loginwithfacebook')} */}
            {/*      </Text> */}
            {/*    </View> */}
            {/*  </View> */}
            {/* </TouchableOpacity> */}
          </View>
        </View>
        <View style={styles.textBottom}>
          <Text style={styles.subTitle}>
            {t('DANGNHAP_nothaveaccount')}
          </Text>
          <TouchableOpacity onPress={goToRegisterScreen}>
            <Text style={styles.buttonDangky}>{t('REGISTER')}</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>

  )
})
