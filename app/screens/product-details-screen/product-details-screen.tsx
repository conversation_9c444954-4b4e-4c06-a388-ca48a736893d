import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import React, { useContext, useEffect, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { ButtonBack, PlaceHolderDetails, TButton, Text, TTextInput } from '../../components'
import {

  View,
  TouchableOpacity,
  FlatList,
  Animated, Image
} from 'react-native'
import styles from './styles'
import { useTranslation } from 'react-i18next'
import { useStores } from '@app/models'
import { numberFormat } from '@app/utils/number'
import { useNavigation, useRoute } from '@react-navigation/native'
import ProductDetailHeader from './product-detail-header'
import { addProduct } from '@app/services'
import { SCREENS } from '@app/navigation'
import { ModalContext } from '@app/context'
import { ProductRender } from '../shopping-screen/product-render/product-render'
import { InputCount } from '../../services/InputCount'
import _ from 'lodash'
import { useAuth } from '@app/use-hooks/use-auth'
import Icon from 'react-native-vector-icons/Ionicons'
import StarRating from 'react-native-star-rating'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Badge, Header } from 'react-native-elements'
import { color } from '@app/theme'
import LinearGradient from 'react-native-linear-gradient'
import { palette } from '@app/theme/palette'
import { freeShipIcon, saleIcon } from '@app/assets/images'
import RenderHtml from 'react-native-render-html'

export const ProductDetailsScreen = observer(function ProductDetailsScreen(props: any) {
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()
  const { productStore, profileStore } = useStores()
  const [isShowMore, setIsShowMore] = useState(false)
  const [count, setCount] = useState(1)
  const [classifyActive, setClassifyActive] = useState({})
  const [sotyClassifyIndex] = useState({})
  const route: any = useRoute()
  const { id } = route.params
  const [images, setImages] = useState([])
  const [idSaveCache, setIdSaveCache] = useState(id)
  const { showError, showSuccess, showCustomError, showCustomSuccess } = useContext(ModalContext)
  const scrollY = new Animated.Value(0)
  const [isLoadData, setIsLoadData] = useState(true)
  const [price, setPrice] = useState(0)
  const [priceOld, setPriceOld] = useState(0)
  const [discount, setDiscount] = useState(0)
  const [indexClassify, setIndexClassify] = useState(0)
  const { updateShoppingCount } = useAuth()
  const [rateValue, setRateValue] = useState(0)
  const { shoppingCount } = useAuth()
  const [noteProduct, setNoteProduct] = useState('')

  useEffect(() => {
    productStore.clearFields()
    if (id) {
      setTimeout(() => {
        loadData().then((r) => {
        })
      }, 200)
    }
  }, [id])

  const resetClassify = () => {
    setCount(1)
    setClassifyActive({})
  }

  const loadData = async () => {
    setIsLoadData(true)
    await productStore.getProductDetails(id)
    await productStore.getProductRate(id)
    const price = productStore.product?.price
    const priceOld = productStore.product?.priceOld
    const discount = (price - priceOld) / priceOld
    setPrice(price)
    setPriceOld(priceOld)
    setDiscount(discount)
    if (productStore.product?.pictures.length > 1) {
      setImages([...productStore.product?.pictures, productStore.product?.thumbail])
    } else {
      setImages([productStore.product?.thumbail])
    }
    setRateValue(Number(productStore.totalRateValue) || 0)
    if (!productStore.product?.classify) {
      productStore.setPropertyProduct('classify', [])
    }
    setIdSaveCache(id)
    if (productStore.product?.classify?.length > 0) {
      setIdSaveCache(id + '@')
      productStore.product?.classify.forEach((e, i) => {
        sotyClassifyIndex[e._id] = i
        classifyActive[e._id] = 'none value'
      })
    }
    setIsLoadData(false)
  }

  const themVaoGioHang = async (isBuyNow = false) => {
    const productDetail = productStore
    if (profileStore.isSignedIn()) {
      let more = ''
      __DEV__ && console.log(JSON.stringify(productDetail.product?.classify))
      const validate = productStore.product?.classify.length > 0 ? (classifyActive && classifyActive['checked']) : true
      if (!validate) {
        showError(t('Có lỗi'), t('VUILONGCHONTHUOCTINH'))
        return
      } else {
        if (classifyActive && classifyActive['checked']) {
          more += classifyActive['_id'].toLowerCase().trim()
        }
        showSuccess(t('Thông báo'), t('SPDADUOCTHEMVAOGIOHANG'))
        setTimeout(() => {
          productStore.setGeneralCoupon('')
          productStore.setTotalDisCount('')
        }, 100)
        // await productStore.fetchCartData(updateShoppingCount)
        // productStore.setReCalculate()
      }
      if (productDetail.product?.weight) {
        setTimeout(async () => {
          const idProd = !_.isEmpty(classifyActive) ? (idSaveCache.indexOf('@') !== -1 ? idSaveCache + more : idSaveCache + '@' + more) : id
          await addProduct(idProd, count, productDetail.product?.storeId, classifyActive, updateShoppingCount, noteProduct)
          // await productStore.fetchCartData(updateShoppingCount)
          // productStore.setReCalculate()
          // navigate(SCREENS.cartScreen)
        }, 700)
      } else {
        showError(t('FAIL'), t('ERROR_WEIGHT'))
      }
      //
      if (isBuyNow) {
        setTimeout(() => {
          // productStore.setReCalculate()
          navigate(SCREENS.cartScreen, { updateShoppingCount: true })
        }, 1000)
      }
    } else {
      goLoginScreenRequired()
    }
  }

  const goLoginScreenRequired = () => {
    navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  const navigateCart = () => {
    if (profileStore.isSignedIn()) {
      navigate(SCREENS.cartScreen)
    } else {
      goLoginScreenRequired()
    }
  }

  const muaNgay = async () => {
    await themVaoGioHang(true)
  }

  const resetSelectProperty = () => {
    for (const item of productStore.product?.classify || []) {
      for (const item2 of item.data || []) {
        _.assign(item2, { checked: false })
      }
    }
    // __DEV__ && console.log('after-reset', productStore.product?.classify)
  }

  const renderChildClassify = (item, index, itemParent) => {
    __DEV__ && console.log('🚀 ~ file: product-details-screen.tsx ~ line 191 ~ renderChildClassify ~ item', item)
    resetSelectProperty()
    const parentId = itemParent._id
    item.checked = classifyActive && classifyActive[parentId] == item.name
    return <TouchableOpacity onPress={async () => {
      try {
        // __DEV__ && console.log('item', item)
        setIndexClassify(index)
        const regex = new RegExp(',', 'g')
        const sellPrice = item.price.replace(regex, '')
        if (item?.priceOld) {
          const priceOld = item.priceOld.replace(regex, '')
          setPriceOld(parseInt(priceOld))
        }
        // const objectActive = { ...classifyActive, ...item, id: item?._id, [parentId]: item?.name, price: item?.price, name: itemParent?.name, value: item?.name }
        const objectActive = { ...item, id: item?._id, [parentId]: item?.name, price: item?.price, name: itemParent?.name, value: item?.name, checked: true }
        // __DEV__ && console.log('objectActive', objectActive)
        // __DEV__ && console.log('objectActive2', objectActive2)
        setClassifyActive(objectActive)
        // setSellPrice: parseInt(sellPrice)
        setPrice(parseInt(sellPrice))
        productStore.setPropertyProduct('price', parseInt(sellPrice))
      } catch (e) {
        __DEV__ && console.log(e)
      }
    }} style={{
      paddingVertical: 10,
      paddingHorizontal: 20,
      marginTop: 5,
      backgroundColor: item?.checked ? '#fff' : color.primaryBackground,
      borderRadius: 4,
      borderWidth: 1,
      borderColor: item?.checked ? '#f3373a' : color.primaryBackground,
      marginRight: 8
    }}>
      <Text
        style={{
          fontSize: 13,
          color: '#333'
        }}>{item.name}</Text>
    </TouchableOpacity>
  }

  const renderClassify = ({ item, index }) => {
    const itemParent = item
    return <View style={styles.viewClassify}>
      <View>
        <Text style={styles.txtName}>{item.name}</Text>
        <FlatList
          keyExtractor={(itemParent, index) => item._id + index.toString()}
          data={item.data}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          renderItem={({ item, index }) => renderChildClassify(item, index, itemParent)}/>
      </View>
    </View>
  }

  const renderSection = ({ item, index }) => {
    let section = (<View/>)
    const percentDiscount = discount * 100
    switch (index) {
      case 0:
        section = (
          <View >
            {productStore?.product ? <ProductDetailHeader images={images} /> : null}
            <View style={styles.discountViewRow}>
              {productStore?.product?.typeShip === 0 && <Image source={freeShipIcon} style={styles.freeShipIc}/>}
              {productStore?.product?.priceOld && <Image source={saleIcon} style={styles.saleIcon}/>}
            </View>
          </View>
        )
        break
      case 1:
        // @ts-ignore
        section = (
          <View style={styles.contentChitietSp}>
            <View style={{ paddingVertical: 20, paddingLeft: 15, backgroundColor: '#fff', marginBottom: 5 }}>
              <Text style={styles.prodName}>{productStore?.product?.name}</Text>
              {/* <TouchableOpacity onPress={() => this.props.open('ChiTietCuaHang', { */}
              {/*  shopId: this.product?.storeId, */}
              {/*  name: this.product?.storeName, */}
              {/* })} style={styles.btnChitietCuahang}> */}
              {/*  <Text style={styles.topStoreName}>{this.product?.storeName}</Text> */}
              {/* </TouchableOpacity> */}

              {/* <View style={styles.viewAddress}> */}
              {/*  /!* <Image style={styles.icMap} source={IcMap} /> *!/ */}
              {/*  <Icon name={'location-outline'} size={16} color={'red'}/> */}
              {/*  <Text */}
              {/*    style={styles.address}>{productStore.product?.address || t('CHUAXACDINH')}</Text> */}
              {/* </View> */}
              <View style={{ flexDirection: 'row', paddingVertical: 5 }}>
                <Text style={styles.topPrice}>{numberFormat(price)} đ</Text>
                { priceOld > 0 ? <Text style={styles.topOldPrice}>{numberFormat(priceOld)} đ</Text> : null}
                { !isNaN(percentDiscount) && percentDiscount != Infinity && percentDiscount !== 0 ? <View style={styles.viewDiscount}>
                  <Text style={styles.discount}>{numberFormat(percentDiscount)}%</Text>
                </View> : null}
              </View>
              <View
                style={{ flexDirection: 'column', flex: 1 }}>
                {/* <Text style={styles.txtthuonghieu}>{t('Đánh giá')}</Text> */}
                <View style={styles.renderStar}>
                  <StarRating
                    fullStarColor={'#ff8900'}
                    disabled={true}
                    maxStars={5}
                    rating={rateValue}
                    emptyStarColor={'#edf1f7'}
                    emptyStar={'star'}
                    fullStar={'star'}
                    halfStar={'star-half-o'}
                    iconSet={'FontAwesome'}
                    starSize={12}
                    containerStyle={styles.startContainer}
                    starStyle={styles.customStar}
                    // selectedStar={(rating) => ratingCompleted(rating)}
                  />
                  {productStore.totalRateValue > 0 ? <Text style={styles.textPoinRate}>{productStore.totalRateValue}</Text> : null}
                  <Text style={styles.textPoinRate}> ({productStore?.commentData?.length} đánh giá)</Text>
                </View>
              </View>
            </View>
            <View style={{ flex: 1 }}>
              {renderChooseOptions()}
              {renderShippingInfo()}
              {renderProductDes()}
            </View>
            <TouchableOpacity style={styles.btnXemthem} onPress={() => {
              setIsShowMore(!isShowMore)
            }}>
              {!isShowMore ? <View style={{ flexDirection: 'row' }}>
                <Text style={styles.txtXemthem}>{t('View_more')}</Text>
                <Icon name={'chevron-down-outline'} size={18} color={'#979797'} />
              </View> : <View style={{ flexDirection: 'row' }}>
                <Text style={styles.txtXemthem}>{t('Hide')}</Text>
                <Icon name={'chevron-up-outline'} size={18} color={'#979797'} />
              </View>}
            </TouchableOpacity>
            {renderBtnReview()}
            {productStore?.productRelates?.length > 0 ? <ProductRender
              textStyle={styles.txtSpLienquan}
              data={productStore.productRelates}
              title={t('SANPHAMLIENQUAN')}
              onPress={(e) => {
                resetClassify()
                navigate(SCREENS.productDetails, { id: e._id })
              }}
            />
              : null}
          </View>
        )
        break
      // case 2 :
      //   section = (
      //     <View style={{ paddingBottom: 15 }}>
      //       {productStore.product && productStore.product?.typeProduct == 1 ? <View style={styles.fixedButton}>
      //         <TButton typeRadius={'rounded'} buttonStyle={styles.btnSubmitCT}
      //           title={t('MUANGAY')} onPress={muaNgay} />
      //         <TButton typeRadius={'rounded'} buttonStyle={styles.btnSubmitCT1}
      //           title={t('THEMVAOGIOHANG')} onPress={themVaoGioHang} />
      //       </View> : null}
      //     </View>
      //   )
      //   break
      default:
        return null
    }
    return section
  }

  const ViewRender = () => (
    <View style={styles.safeAreaView}>
      <Animated.FlatList
        onScroll={Animated.event(
          [{
            nativeEvent: {
              contentOffset: {
                y: scrollY
              }
            }
          }], { useNativeDriver: false })
        }
        // scrollEventThrottle={16}
        contentContainerStyle={{
          flexGrow: 0,
          backgroundColor: '#fff'
          // paddingTop: HEADER_EXPANDED_HEIGHT
        }}
        style={{ width: '100%' }}
        nestedScrollEnabled
        data={['1', '2', '3', '4', '5']}
        keyExtractor={(item, index) => index.toString()}
        renderItem={renderSection}
      />
      {productStore.product && productStore.product?.typeProduct == 1 ? <View style={[styles.fixedButton, { paddingBottom: useSafeAreaInsets().bottom }]}>
        <TButton typeRadius={'rounded'} buttonStyle={styles.btnSubmitCT1} titleStyle={{ fontSize: 16, fontWeight: '500', color: '#333' }}
          title={t('THEMVAOGIOHANG')} onPress={themVaoGioHang} />
        <TButton typeRadius={'rounded'} buttonStyle={styles.btnSubmitCT} titleStyle={{ fontSize: 16, fontWeight: '500' }}
          title={t('MUANGAY')} onPress={muaNgay} />
      </View> : null}
    </View>
  )

  const renderProductDes = () => {
    return (
      <View style={styles.viewDes}>
        <Text style={styles.titleVanchuyen_Des}>{t('CHITIETSANPHAM')}</Text>
        {productStore.product?.description
          ? <RenderHtml
            baseFontStyle={{ color: '#333', fontWeight: '400', fontSize: 14 }}
            containerStyle={{ height: isShowMore ? '100%' : 50, backgroundColor: '#fff', flex: 1, marginBottom: 20 }} ignoredStyles={['font-family']}
            source={{ html: productStore.product?.description }}
            ignoredTags={['script']}
            renderersProps={{
              img: {
                enableExperimentalPercentWidth: true
              }
            }}
          /> : null}
      </View>
    )
  }

  const renderBtnReview = () => {
    return (
      <View style={styles.review}>
        <Text style={styles.titleLabel}>Đánh giá sản phẩm</Text>
        <TouchableOpacity onPress={() => navigate(SCREENS.reviewProductScreen, { productId: id })}><Text style={styles.textBtnViewDetail}>Chi tiết đánh giá</Text></TouchableOpacity>
      </View>
    )
  }

  const renderChooseOptions = () => {
    return (
      <View style={{ marginBottom: 5, backgroundColor: '#fff', paddingVertical: 20 }}>
        <View style={styles.viewThuonghieu}>
          <View style={styles.viewBrand}>
            <Text style={styles.txtthuonghieu}>{t('THUONGHIEU')}</Text>
            <Text style={styles.tenThuonghieu}>{productStore?.product?.trademark}</Text>
          </View>
          <View style={styles.viewTrangthai}>
            <Text style={styles.titleTrangthai}>{t('STATUS')}</Text>
            {productStore.product ? <Text style={{
              fontSize: 13,
              color: productStore.product?.typeProductText == t('HETHANG') ? 'red' : '#333333',
              marginTop: 4
            }}>{productStore.product?.typeProductText}</Text> : null}
          </View>
        </View>
        <FlatList data={productStore?.product?.classify}
          renderItem={renderClassify}
          keyExtractor={(item, index) => index.toString() + 'classify'} />
        <View style={styles.viewSoluong}>
          <Text style={styles.count}>{t('COUNT')}</Text>
          <InputCount style={{ width: 130, height: 40, marginTop: 10 }}
            value={count + ''}
            onChangeText={(count) => {
              setCount(count)
            }} />
        </View>
        <View style={{ flexDirection: 'column', marginTop: 15, marginHorizontal: 16 }}>
          <TTextInput
            typeRadius={'rounded'}
            autoCapitalize={'none'}
            placeholder={t('Ghi chú sản phẩm, màu sắc, kích cỡ...')}
            placeholderTextColor={'#a0a0a0'}
            underlineColorAndroid="transparent"
            onChangeText={(e) => setNoteProduct(e)}
            defaultValue={productStore.product?.note || ''}
          />
        </View>
      </View>
    )
  }

  const renderShippingInfo = () => {
    return (
      <View style={styles.viewVanchuyen}>
        <Text style={styles.titleVanchuyen_Des}>{t('VANCHUYEN')}</Text>
        <Text style={styles.nameVanchuyen}>
          {productStore.product?.transport}
        </Text>
      </View>
    )
  }

  return (<SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
    <Header
      leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
      centerComponent={{ text: t('Chi tiết sản phẩm'), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
      rightComponent={<TouchableOpacity
        onPress={navigateCart}
        style={styles.iconCart}
      >
        {/* <Image source={icShoppingCart} style={{ width: 26, height: 26 }}></Image> */}
        <Icon name={'cart-outline'} size={26} color={palette.white}/>
        {/* <Badge */}
        {/*  value={shoppingCount} */}
        {/*  status="error" */}
        {/*  containerStyle={{ position: 'absolute', top: -5, right: -10 }} */}
        {/* /> */}
        {shoppingCount && shoppingCount > 0 ? <Badge value={shoppingCount > 99 ? 99 + '+' : shoppingCount} status="error" containerStyle={{ marginLeft: -12, marginTop: -7 }} /> : null}
      </TouchableOpacity>}
      containerStyle={common.headerContainer}
      statusBarProps={{ barStyle: 'light-content' }}
      ViewComponent={LinearGradient}
      linearGradientProps={linearGradientProps}
    />
    {isLoadData ? <PlaceHolderDetails/> : ViewRender()}
  </SafeAreaView>
  )
})
