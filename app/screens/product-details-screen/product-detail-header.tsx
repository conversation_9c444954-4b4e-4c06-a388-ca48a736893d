import { Dimensions, Text, View } from 'react-native'
import React, { useState } from 'react'
import { useNavigation } from '@react-navigation/native'
import { observer } from 'mobx-react-lite'

import Carousel from 'react-native-snap-carousel'
import styles from './product-detail-header.styles'
import { LazyImage } from '@app/components'
import { SCREENS } from '@app/navigation'
import { useStores } from '@app/models'
import { useAuth } from '@app/use-hooks/use-auth'

const HEAD_HEIGHT = 275
const G_WIN_WIDTH = Dimensions.get('window').width

const ProductDetailHeader: any = observer((props: any) => {
  const { images } = props
  const [index, setIndex] = useState(0)
  const { profileStore, productStore } = useStores()
  const { goBack, navigate } = useNavigation()
  const { shoppingCount } = useAuth()

  const goLoginScreenRequired = () => {
    navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  const navigateCart = () => {
    if (profileStore.isSignedIn()) {
      navigate(SCREENS.cartScreen)
    } else {
      goLoginScreenRequired()
    }
  }

  const renderPagination = () => {
    // __DEV__ && console.log('renderPagination-----')
    const total = images.length
    return (
      <View style={styles.paginationStyle}>
        <Text style={styles.paginationText}>{index + 1} / {total}</Text>
      </View>
    )
  }

  const _renderCarouselItem = ({ item }: any) => {
    return (
      <View style={styles.slide}>
        <LazyImage
          style={styles.image}
          resizeMode={'cover'}
          source={{ uri: item }}
        />
      </View>
    )
  }
  return (
    <View style={styles.container}>
      <View style={styles.containerSwiper}>
        <View>
          <View style={{ height: HEAD_HEIGHT }}>
            <Carousel
              layout={'default'}
              data={images}
              renderItem={_renderCarouselItem}
              sliderWidth={G_WIN_WIDTH}
              itemWidth={G_WIN_WIDTH}
              inactiveSlideScale={1}
              inactiveSlideOpacity={1}
              loop={true}
              autoplay={true}
              autoplayDelay={500}
              autoplayInterval={6000}
              onSnapToItem={(index) => setIndex(index)}
            />
            {renderPagination()}
          </View>
          {/* <TouchableOpacity */}
          {/*  onPress={goBack} */}
          {/*  style={styles.viewTouch}> */}
          {/*  <Icon */}
          {/*    size={20} */}
          {/*    name={'arrow-back-outline'} */}
          {/*  /> */}
          {/* </TouchableOpacity> */}
          {/* <ButtonBack onPress={goBack} style={styles.viewTouch}/> */}
          {/* <TouchableOpacity style={styles.viewTouchRight} onPress={navigateCart}> */}
          {/*  <Icon */}
          {/*    size={22} */}
          {/*    color={'#333'} */}
          {/*    name={'cart-outline'} */}
          {/*  /> */}
          {/*  <Badge */}
          {/*    value={shoppingCount} */}
          {/*    status="error" */}
          {/*    containerStyle={{ position: 'absolute', top: -5, right: -5 }} */}
          {/*  /> */}
          {/* </TouchableOpacity> */}
        </View>
      </View>
    </View>
  )
})
export default ProductDetailHeader
