import { StyleSheet } from 'react-native'
import { responsiveWidth } from 'react-native-responsive-dimensions'
import { color, typography } from '@app/theme'

const freeShipIcRatio = 55 / 14
const saleIconRatio = 38 / 14

const styles = StyleSheet.create({
  address: {
    color: '#333333',
    flex: 1,
    fontSize: 13,
    marginLeft: 10
  },
  background: {
    backgroundColor: '#fff',
    flex: 1,
  },
  btnCart: {
    padding: 0,
    position: 'relative'
  },
  btnChitietCuahang: {
    marginLeft: 15,
    marginRight: 10,
    marginTop: 5,
    padding: 5
  },
  btnSubmitCT: {
    // backgroundColor: '#FB415A',
    width: (responsiveWidth(100) - 45) / 2
  },
  btnSubmitCT1: {
    backgroundColor: color.primaryBackground,
    width: (responsiveWidth(100) - 45) / 2
  },
  btnXemthem: {
    // alignSelf: 'flex-start',
    borderColor: color.primaryBackground,
    borderTopWidth: 1,
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 15,
    // marginTop: 20,
    alignItems: 'center',
    backgroundColor: '#fff',
    borderBottomWidth: 5
  },
  contentChitietSp: {
    backgroundColor: '#f7f7f7',
    flex: 1
  },
  count: {
    color: '#999999',
    fontSize: 12
  },
  customStar: {
    marginRight: 4,
    marginTop: 3
  },
  discount: {
    color: '#fff',
    fontSize: 14,
    paddingHorizontal: 5,
    paddingVertical: 3
  },
  discountViewRow: {
    bottom: 5,
    flexDirection: 'row',
    left: 5,
    position: 'absolute'
  },
  fixedButton: {
    backgroundColor: '#fff',
    borderTopColor: '#E9E9E9',
    borderTopWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15,
  },
  freeShipIc: {
    height: 19,
    marginRight: 8,
    width: 19 * freeShipIcRatio
  },
  icArrowBack: {
    height: 24,
    resizeMode: 'contain',
    width: 24,
  },
  icCart: {
    height: 25,
    width: 25
  },
  icChat: {
    height: 30,
    marginRight: 10,
    width: 30
  },
  icMap: {
    height: 16,
    resizeMode: 'contain',
    width: 12
  },
  iconCart: {
    flexDirection: 'row',
    // marginRight: 15
  },
  image: {
    height: '100%',
  },
  imgSanPham: {
    height: '100%',
    resizeMode: 'cover',
    width: '100%'
  },
  nameVanchuyen: {
    color: '#333',
    fontSize: 13,
  },
  number: {
    color: '#C42125',
    fontSize: 11,
    textAlign: 'center'
  },
  pageNo: {
    color: '#000',
    fontSize: 13,
    position: 'absolute',
    right: 10,
    textAlign: 'center',
    top: 260
  },
  paginationStyle: {
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 8,
    bottom: 9,
    flexDirection: 'row',
    height: 20,
    justifyContent: 'center',
    position: 'absolute',
    right: 15,
    width: 45
  },
  paginationText: {
    alignItems: 'center',
    color: '#ffffff',
    fontSize: 12,
    letterSpacing: 0,
    textAlign: 'center'
  },
  price: {
    color: '#CB1016',
    fontSize: 12,
    fontWeight: 'bold',
    margin: 8,
    marginTop: -8,
    textAlign: 'left'
  },
  prodImg: {
    height: '100%',
    resizeMode: 'contain',
    width: '100%'
  },
  prodName: {
    color: '#333',
    fontSize: 16,
    lineHeight: 30,
    // paddingHorizontal: 15
  },
  renderSPLienQuan: {
    backgroundColor: '#fff',
    borderRadius: 4,
    elevation: 1,
    marginRight: 10,
    shadowColor: '#000',
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    textAlign: 'left',
    width: 140
  },
  renderStar: {
    alignItems: 'center',
    flexDirection: 'row',
    // marginBottom: 11
  },
  review: {
    alignItems: 'center',
    backgroundColor: '#fff',
    borderBottomWidth: 5,
    borderColor: color.primaryBackground,
    // borderTopWidth: 5,
    flexDirection: 'row',
    // height: 45,
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 15
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  saleIcon: {
    height: 19,
    width: 19 * saleIconRatio,
  },
  slide: {
    flex: 1,
    width: '100%',
  },
  soluongHangTrongGio: {
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    height: 20,
    justifyContent: 'center',
    position: 'absolute',
    right: -10,
    top: -10,
    width: 20,
    zIndex: 1
  },
  spLienquan: {
    backgroundColor: '#f6f6f7',
    padding: 15
  },
  startContainer: {
    // marginLeft: 30,
  },
  storeName: {
    borderRadius: 3,
    color: '#999999',
    fontSize: 11,
    margin: 8,
    marginTop: 0,
    textAlign: 'left'
  },
  tenSanpham: {
    color: '#333333',
    fontSize: 13,
    height: 40,
    margin: 8,
    textAlign: 'left'
  },
  tenThuonghieu: {
    color: '#333333',
    fontSize: 13,
    marginTop: 4
  },
  textBtnViewDetail: {
    color: color.primary,
    fontSize: 14,
    fontWeight: '400'
  },
  textPoinRate: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 5,
    marginTop: 4,
  },
  titleBackground: {
    backgroundColor: '#CB1016',
    height: 60,
    left: 0,
    top: 0,
    width: '100%',
  },
  titleLabel: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    textTransform: 'uppercase'
  },
  titleProdName: {
    color: '#fff',
    flex: 1,
    fontSize: 20,
    lineHeight: 32,
    marginLeft: 15,
    marginTop: 5
  },
  titleTrangthai: {
    color: '#999999',
    fontSize: 12
  },
  titleVanchuyen_Des: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    paddingBottom: 20,
    textTransform: 'uppercase'
  },
  topOldPrice: {
    alignSelf: 'center',
    color: '#9d9d9d',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 15,
    textDecorationLine: 'line-through'
  },
  topPrice: {
    color: color.primary,
    fontSize: 20,
    fontWeight: 'bold',
    // paddingHorizontal: 15
  },
  topStoreName: {
    color: '#333',
    fontSize: 14
  },
  txtMuangay: {
    color: '#FFFFFF',
    fontSize: 13,
    lineHeight: 18,
    textAlign: 'center'
  },
  txtName: {
    color: '#999999',
    fontSize: 11
  },
  txtSpLienquan: {
    color: '#333333',
    fontSize: 15,
    fontWeight: 'bold',
    lineHeight: 22,
    marginBottom: 10,
    marginTop: 10
  },
  txtThemHangVaoGio: {
    color: '#fff',
    fontSize: 13,
    lineHeight: 18,
    textAlign: 'center'
  },
  txtXemthem: {
    color: '#979797',
    // flex: 1,
    fontSize: 14,
    marginRight: 8
  },
  txtthuonghieu: {
    color: '#999999',
    fontSize: 12
  },
  viewAddress: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginLeft: 15,
    marginTop: 4
  },
  viewBrand: {
    flexDirection: 'column',
    flex: 1
  },
  viewClassify: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginTop: 15,
    paddingHorizontal: 15
  },
  viewDes: {
    backgroundColor: '#fff',
    flex: 1,
    paddingHorizontal: 15,
    paddingTop: 20
  },
  viewDiscount: {
    backgroundColor: color.primary,
    borderRadius: 4,
    marginLeft: 15
  },
  viewHTML: {
    color: '#333',
    fontSize: 13,
    marginBottom: 4,
  },
  viewImgSanPham: {
    backgroundColor: '#fff',
    borderRadius: 8,
    height: 140,
    padding: 0,
    width: 140
  },
  viewPager: {
    backgroundColor: '#ddd',
    height: 280,
    width: '100%'
  },
  viewPagerImg: {
    height: '100%',
    opacity: 0.4,
    position: 'absolute',
    resizeMode: 'cover',
    width: '100%',
    zIndex: 0
  },
  viewSoluong: {
    marginTop: 15,
    paddingHorizontal: 16
  },
  viewThuonghieu: {
    // alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 16
  },
  viewTopIc: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 16,
    position: 'absolute',
    zIndex: 2,
  },
  viewTrangthai: {
    flexDirection: 'column',
    flex: 1,
    paddingLeft: 30
  },
  viewVanchuyen: {
    backgroundColor: '#fff',
    marginBottom: 5,
    paddingHorizontal: 16,
    paddingVertical: 20
  },
})
export default styles
