import { Dimensions, StyleSheet } from 'react-native'
import { ifIphoneX } from 'react-native-iphone-x-helper'
import { responsiveWidth } from 'react-native-responsive-dimensions'

const tab1ItemSize = (Dimensions.get('window').width - 30) / 5
const { width } = Dimensions.get('window')
const styles = StyleSheet.create({
  // header: {
  //   alignItems: 'center',
  //   height: 320,
  //   justifyContent: 'center',
  //   // position: 'absolute',
  //   top: 0,
  //   width: '100%',
  // },
  unknownDistance: {
    color: '#d2d2d2',
    fontSize: 12,
    marginBottom: 5
  },
  label: {
    color: '#333',
    fontSize: 14,
    letterSpacing: 0,
    textAlign: 'center',
  },
  seeMoreAddress: {
    fontSize: 12,
    fontStyle: 'italic',
    paddingBottom: 15,
    paddingHorizontal: 20,
    paddingTop: 10,
    textDecorationLine: 'underline'
  },
  tab: {
    backgroundColor: '#ffffff',
    elevation: 0,
    shadowOpacity: 0,
  },
  indicator: {
    backgroundColor: 'rgba(255, 207, 216, 0.5)',
    borderRadius: 1.5,
    height: 3,
    width: 125,
  },

  // swipe
  container: {
    flexDirection: 'column',
    width: '100%',
  },
  containerSwiper: {
    flexDirection: 'row',
    height: 275,
    width: '100%',
  },
  wrapper: {
    height: '100%',
  },
  slide: {
    flex: 1,
    width: '100%',
  },
  image: {
    height: '100%',
    width,
  },
  paginationStyle: {
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 8,
    bottom: 9,
    flexDirection: 'row',
    height: 20,
    justifyContent: 'center',
    position: 'absolute',
    right: 15,
    width: 45
  },
  paginationText: {
    textAlign: 'center',
    // fontFamily: "SFProDisplay",
    fontSize: 12,
    letterSpacing: 0,
    color: '#ffffff',
    alignItems: 'center'
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  viewTouch: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 15,
    height: 30,
    justifyContent: 'center',
    left: 15,
    position: 'absolute',
    top: 30,
    width: 30,
  },
  viewTouchRight: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 15,
    height: 30,
    justifyContent: 'center',
    position: 'absolute',
    right: 15,
    top: 30,
    width: 30
  },
  viewInfo: {
    flexDirection: 'column',
    flex: 1,
    marginLeft: 15,
    marginRight: 15,
    marginTop: 12,
  },
  textTitle: {
    // fontFamily: "SFProDisplay",
    fontSize: 26,
    fontWeight: 'bold',
    lineHeight: 30,
    color: '#333',
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: {
      width: 0,
      height: 2,
    },
    textShadowRadius: 4,
    elevation: 2,
    marginBottom: 15
  },
  viewAddress: {
    // alignItems: 'flex-start',
    // flexDirection: 'row',
    marginRight: 40,
    marginTop: 15,
    paddingBottom: 10,
    width: responsiveWidth(70)
  },
  viewIconLeft: {
    alignItems: 'center',
    flexDirection: 'row',
    marginLeft: 1
  },
  viewIconLeft1: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 15,
  },
  viewIconLocation: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  textAdress: {
    color: '#000000',
    fontSize: 12,
    letterSpacing: 0,
    marginHorizontal: 5,
  },
  textPoin: {
    color: '#333',
    fontSize: 12,
    letterSpacing: 0,
    marginLeft: 5,
  },
  textPoinLocation: {
    color: '#333',
    fontSize: 12,
    letterSpacing: 0,
    marginLeft: 3,
  },
  textCountrate: {
    color: '#333',
    fontSize: 10,
    letterSpacing: 0,
    opacity: 0.58,
  },
  viewtextService: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: 15,
    marginTop: 14,
    marginBottom: 5
  },
  textService: {
    justifyContent: 'flex-start',
    textAlign: 'left',
    // fontFamily: "SFProDisplay",
    fontSize: 14,
    letterSpacing: 0,
    color: '#333',
  },
  textComment: {
    color: '#333',
    fontSize: 14,
    fontWeight: '600',
  },
  topShowall: {
    flex: 1,
    marginRight: 15,
  },
  textTop: {
    justifyContent: 'flex-end',
    textAlign: 'right',
    // fontFamily: "SFProDisplay",
    fontSize: 14,
    letterSpacing: 0,
    color: '#ff8900',
    marginLeft: 2,
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
    ...ifIphoneX({
      marginTop: 0
    }, {
      marginTop: 5
    }),
  },
  containerFlatlist: {
    flexDirection: 'column',
    marginLeft: 15,
    marginRight: 15,
  },
  viewtextTitleFlatlist: {
    flexDirection: 'row',
    flex: 1,
    marginTop: 32,
    width: responsiveWidth(85)
  },
  textTitleFlatlist: {
    justifyContent: 'flex-start',
    textAlign: 'left',
    // fontFamily: "SFProDisplay",
    fontSize: 14,
    letterSpacing: 0,
    color: '#333',
  },
  textTopFlatlist: {
    justifyContent: 'flex-end',
    textAlign: 'right',
    // fontFamily: "SFProDisplay",
    fontSize: 14,
    letterSpacing: 0,
    color: '#ff8900',
    marginLeft: 2,
    textDecorationLine: 'underline',
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',

  },
  // tabService test sau nay bo di hoac chuyenr sang file khac
  containerService: {
    flexDirection: 'row',
    marginBottom: 15,
    marginLeft: 15,
    marginRight: 15,
  },
  containerServiceChoose: {
    flexDirection: 'row',
    margin: 15
  },
  boxContainer: {
    borderBottomColor: '#e0e0e0',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 15,
    marginRight: 15,
    marginTop: 20,
    paddingBottom: 15,
  },
  viewImageService: {
    alignItems: 'center',
    borderRadius: 16,
    height: tab1ItemSize,
    justifyContent: 'center',
    marginLeft: 0,
    width: tab1ItemSize
  },
  imageChoose: {
    borderRadius: 8,
    height: 80,
    width: 80
  },
  viewService: {
    flexDirection: 'column',
    marginLeft: 10,
  },
  textTitleService: {
    color: '#333',
    fontSize: 14,
    fontWeight: '600',
  },
  textContent: {
    color: '#46474D',
    fontSize: 12,
    width: 243,
  },
  viewIcon: {
    alignItems: 'flex-end',
    flex: 1,
    justifyContent: 'flex-end',
  },
  textPriceMinMax: {
    color: '#ffa8b4',
    fontSize: 14,
    fontWeight: '600',
  },
  viewTextLabel: {
    alignItems: 'center',
    backgroundColor: '#edf1f7',
    flexDirection: 'row',
    height: 30,
    marginBottom: 18
  },
  imageService: {
    borderRadius: 8,
    height: 80,
    width: 80
  },
  textLabel: {
    color: '#333',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 15
  },
  viewPicker: {
    flex: 1,
    marginLeft: 15,
    marginRight: 15,
  },
  viewPickerWeight: {
    borderColor: '#edf1f7',
    borderRadius: 22,
    borderStyle: 'solid',
    borderWidth: 1,
    flexDirection: 'row',
    flex: 1,
    height: 44,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  viewFlatListPrice:
    {
      marginTop: 8,
    },
  viewFlatListBranch:
    {
      marginLeft: 12,
      marginTop: 8,
    },
  textInput: {
    backgroundColor: '#ffffff',
    borderColor: '#edf1f7',
    borderRadius: 22,
    borderStyle: 'solid',
    borderWidth: 1,
    height: 44,
    marginTop: '5%',
    paddingLeft: 14,
    width: 315,
  },
  viewContainerDatetime: {
    flexDirection: 'row',
    flex: 1,
    marginTop: 25,

  },
  viewBtnDatetime: {
    flexDirection: 'row',
    flex: 1,
    height: 44,
    borderRadius: 22,
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#edf1f7',
    alignItems: 'center',

  },
  viewBtntime: {
    flexDirection: 'row',
    flex: 1,
    height: 44,
    borderRadius: 22,
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#edf1f7',
    alignItems: 'center',
    marginLeft: 10,
  },
  iconPiker: {
    marginLeft: 10,
  },
  iconPikerRight: {
    marginHorizontal: 15
  },
  strPiker: {
    color: '#46474D',
    fontSize: 14,
    marginLeft: 6,
  },
  viewTextNote: {
    marginBottom: 20,
    marginLeft: 15,
    marginRight: 15
  },
  textInputNote: {
    // fontFamily: 'SanFranciscoDisplay-Regular',
    fontSize: 14,
    padding: 12,
    letterSpacing: 0,
    color: '#333333',
    minHeight: 80,
    borderRadius: 4,
    borderColor: '#EFEFEF',
    borderWidth: 1,
    marginTop: 6,
    backgroundColor: '#f7f9fc',
    textAlign: 'left',
    textAlignVertical: 'top',
  },
  viewBtnSubmit: {
    flex: 1,
    marginLeft: 15,
    marginRight: 15,
    marginTop: 15,

  },
  btnSubmit: {
    alignContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ff8ba1',
    borderRadius: 8,
    flex: 1,
    fontSize: 14,
    fontWeight: 'bold',
    height: 44,
    justifyContent: 'center',
    letterSpacing: 0,
    textAlign: 'center',
  },
  textBtnSubmit: {
    height: 20,
    // fontFamily: "SFProDisplay",
    fontSize: 14,
    fontWeight: 'bold',
    letterSpacing: 0,
    textAlign: 'center',
    color: '#ffffff',
  },
  renderItemListPrice: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 12,
    paddingLeft: 15,
    paddingRight: 15,
  },
  renderItemListBranch: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 5,
    paddingLeft: 15,
    paddingRight: 15
  },
  renderItemListClassify: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
    marginTop: 5,
    paddingLeft: 5,
    paddingRight: 5
  },
  boxAddress: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    paddingBottom: 12,
    paddingLeft: 15,
  },
  renderPhoneNumber: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingLeft: 15,
    paddingRight: 15
  },
  boxViewBranch: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    flexDirection: 'column',
    height: 125,
    justifyContent: 'space-between',
    marginBottom: 25,
    marginLeft: 10,
    marginRight: 20,
    marginTop: 25,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 1,
    shadowRadius: 10
  },
  textBranch: {
    color: '#333',
    fontSize: 14,
    fontWeight: 'bold',
  },
  textClassify: {
    marginLeft: 4,
    // fontFamily: "SFProDisplay",
    fontSize: 14,
    color: '#333',
  },
  textAddressBranch: {
    color: '#333',
    flex: 1,
    flexWrap: 'wrap',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 7,
    width: '100%',
  },
  textAddressPhone: {
    color: '#acb1c0',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 7,
    paddingBottom: 10
  },
  boxContact: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    height: 125,
    marginBottom: 20,
    marginLeft: 15,
    marginRight: 10,
    marginTop: 24,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 1,
    shadowRadius: 10,
    width: 288
  },
  couponText: {
    color: 'rgba(0, 0, 0, 0.85)',
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 15,
    textDecorationLine: 'underline'
  },
  couponTextNone: {
    color: 'rgba(0, 0, 0, 0.85)',
    fontSize: 14,
    fontStyle: 'italic',
    fontWeight: 'bold',
    marginLeft: 15,
    textDecorationLine: 'underline'
  },
  containerCoupon: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  textSelectWeight: {
    color: '#c5cee0',
    fontSize: 14,
    marginLeft: 15
  },
  textSelectWeightActive: {
    color: '#333',
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 15
  },

})
export default styles
