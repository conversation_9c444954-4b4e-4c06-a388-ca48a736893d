import React, { useEffect } from 'react'
import { observer } from 'mobx-react-lite'
import { ScrollView, View, Text } from 'react-native'
import { useStores } from '../../models'
import { useNavigation, useRoute } from '@react-navigation/native'
import styles from './styles'
import { ButtonBack } from '@app/components'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import { useTranslation } from 'react-i18next'
import LinearGradient from 'react-native-linear-gradient'
import { SafeAreaView } from 'react-native-safe-area-context'

export const SupportCenterScreen = observer(() => {
  const { supportStore } = useStores()
  const { navigate, goBack } = useNavigation()
  const route = useRoute()
  const typeSupport = route.params.typeSupport
  const title = route.params.title
  const { t } : any = useTranslation()

  function onBack() {
    goBack()
  }

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    await supportStore.getDataSupportCenter()
  }

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <Header
        // statusBarProps={{ barStyle: 'light-content' }}
        // barStyle="light-content" // or directly
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
        centerComponent={{ text: t(t(`${title}`)), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
        //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
        // </TouchableOpacity> : null}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      <ScrollView style={{ backgroundColor: '#fff' }}>
        {/* <View style={styles.renderTitle}> */}
        {/*  <ButtonBack onPress={onBack}/> */}
        {/* </View> */}
        {/* <Text style={styles.TitleText}>{title}</Text> */}
        <View style={styles.supportCenterContent}>
          {typeSupport === 'trungTamHoTro' ? <Text>{supportStore.trungTamHoTro}</Text> : typeSupport === 'dieuKhoanDichVu' ? <Text>{supportStore.dieuKhoanDichVu}</Text> : typeSupport === 'chinhSachBaoMat' ? <Text>{supportStore.chinhSachBaoMat}</Text> : typeSupport === 'gioiThieu' ? <Text>{supportStore.gioiThieu}</Text> : null }
        </View>
      </ScrollView>
    </SafeAreaView>
  )
})
