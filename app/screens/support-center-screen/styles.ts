import { StyleSheet } from 'react-native'
import { typography } from '../../theme'

const styles = StyleSheet.create({
  TitleText: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
    marginHorizontal: 15,
    marginTop: 6
  },
  icArrowBack: {
    marginBottom: 15,
    marginLeft: 15
  },
  renderTitle: {
    flexDirection: 'row',
    flex: 1,
    paddingTop: 15
  },
  safeAreaView: {
    backgroundColor: '#fff',
    height: '100%',
    marginTop: -4
  },
  supportCenterContent: {
    flex: 1,
    marginBottom: 89,
    marginHorizontal: 15,
    marginTop: 20,
  }

})
export default styles
