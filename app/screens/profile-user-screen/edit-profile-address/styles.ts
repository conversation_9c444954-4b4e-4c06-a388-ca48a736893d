import { StyleSheet } from 'react-native'
// import { ifIphoneX } from 'react-native-iphone-x-helper'
import { color, typography } from '../../../theme'
const styles = StyleSheet.create({
  boxContainerModal: {
    // ...ifIphoneX({
    //   paddingBottom: 89,
    // }, {
    //   paddingBottom: 60,
    // }),
  },
  buttonAdd: {
    alignItems: 'center',
    backgroundColor: color.primary,
    borderRadius: 4,
    color: '#ffffff',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    height: 44,
    justifyContent: 'center',
    marginHorizontal: 16,
    marginTop: 30,
    textAlign: 'center'
  },
  buttonMap: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderColor: '#edf1f7',
    borderRadius: 8,
    borderStyle: 'solid',
    borderWidth: 1,
    height: 44,
    justifyContent: 'center',
    marginLeft: 12,
    width: 48
  },
  container: {
    flex: 1
  },
  icArrowBack: {
    margin: 11,
  },
  iconLeft: {
    marginRight: 15
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  selectProvince: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginLeft: 15,
    marginTop: 26,
  },
  textButton: {
    color: '#ffffff',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center'
  },
  textInput: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderColor: '#edf1f7',
    borderRadius: 8,
    borderStyle: 'solid',
    borderWidth: 1,
    color: '#333',
    flexDirection: 'row',
    fontFamily: typography.normal,
    fontSize: 14,
    height: 44,
    justifyContent: 'space-between',
    marginLeft: 15,
    marginRight: 15,
    marginTop: 15,
    paddingLeft: 14
  },
  textMap: {
    backgroundColor: '#ffffff',
    borderColor: '#edf1f7',
    borderRadius: 8,
    borderStyle: 'solid',
    borderWidth: 1,
    height: 44,
    paddingLeft: 14,
    width: '83%'
  },
  textProvince: {
    color: '#c5cee0',
    fontFamily: typography.normal,
    fontSize: 14
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',

  },
  textTitleTotal: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
    marginLeft: 15,
    marginTop: 15,
  },
  viewContainer: {
    backgroundColor: '#fff',
    justifyContent: 'space-between',
    marginTop: 27,
    paddingBottom: 60
  },
  viewInputField: {
    marginTop: 15,
    paddingHorizontal: 16,
  },
  viewMap: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 15,
    marginRight: 15
  },
  viewProvince: {
    alignItems: 'center',
    backgroundColor: color.primaryBackground,
    borderRadius: 4,
    flexDirection: 'row',
    height: 44,
    justifyContent: 'space-between',
    marginHorizontal: 16,
    marginTop: 15,
    paddingLeft: 14
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  }
})
export default styles
