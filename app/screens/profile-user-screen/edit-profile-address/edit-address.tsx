import { SafeAreaView } from 'react-native-safe-area-context'
import React, { useState, useEffect, useRef, useContext } from 'react'
import {

  View,
  Text, TouchableOpacity, Platform, ScrollView, KeyboardAvoidingView
} from 'react-native'

import styles from './styles'

import Icon from 'react-native-vector-icons/Ionicons'

import { useNavigation } from '@react-navigation/native'

import { observer } from 'mobx-react-lite'
import { Modalize } from 'react-native-modalize'
import { countriesData } from '../../../assets/countries'
import { useStores } from '../../../models/root-store'

import { useTranslation } from 'react-i18next'
import { ButtonBack, TButton, TTextInput } from '@app/components'
import validate from 'validate.js'
import { responsiveHeight } from 'react-native-responsive-dimensions'
import { ModalContext } from '@app/context'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import { getSnapshot } from 'mobx-state-tree'
import { UpdateProfileModel } from '@app/models'
import _ from 'lodash'
import LinearGradient from 'react-native-linear-gradient'

export const RenderEditAddress: React.FC = observer((props) => {
  const { item } = props.route.params
  const modalize = useRef<Modalize>(null)
  const navigation = useNavigation()
  const goBack = () => navigation.goBack()
  const { profileStore } = useStores()
  const [countries] = useState(countriesData)
  const [province, setProvince] = useState(null)
  const [provinces, setProvinces] = useState([])
  const [district, setDistrict] = useState(null)
  const [name, setName] = useState(null)
  const [phone, setPhone] = useState(null)
  const [districts, setDistricts] = useState([])
  const [ward, setWard] = useState(null)
  const [wards, setWards] = useState([])
  const [street, setStreet] = useState('')
  const [isSubmitting, setSubmitting] = useState(false)
  const [isReClear, setReClear] = useState(false)
  const [reOpenFlag, setReopenFlag] = useState(false)
  const [selectedType, setSelectedType] = useState(-1)
  const [onchangeProvince, setOnchangeProvince] = useState(false)

  const { t } : any = useTranslation()

  const { showError, showSuccess } = useContext(ModalContext)

  useEffect(() => {
    const cities = []
    for (const city in countries) {
      cities.push({
        value: city,
      })
      setProvinces(cities)
      setProvince(item.province)
      setDistrict(item.district)
      setWard(item.ward)
      setName(item.name)
      setPhone(item.phone)
      setStreet(item.street)
    }
  }, [])

  useEffect(() => {
    if (onchangeProvince) {
      const cities = []
      for (const city in countries) {
        cities.push({
          value: city,
        })
        setProvinces(cities)
        setDistrict('')
        setDistricts([])
        setWard('')
        setWards([])
      }
    }
  }, [province])

  useEffect(() => {
    if (province) {
      setDistricts(Object.keys(countries[province]).map(x => {
        return { value: x }
      }))
    }
  }, [province])

  useEffect(() => {
    if (isReClear) {
      setDistrict('')
      setWard('')
      setReClear(false)
    }
  }, [isReClear])

  useEffect(() => {
    if (district) {
      const districts = countries[province][district]
      if (districts && districts?.length) {
        setWards(districts.map(x => {
          return { value: x }
        }))
      } else {
        setWards([])
      }
    }
  },
  [district])

  const onOpenModal = () => {
    if (selectedType === 1) {
      if (!province) return
    } if (selectedType === 2) {
      if (!district) return
    }
    modalize.current?.open()
  }
  const onCloseModal = () => {
    modalize.current?.close()
  }

  const renderHeader = () => {
    let str = ''
    if (selectedType === 0) {
      str = t('TINH/TP')
    } if (selectedType === 1) {
      str = t('CHON_quanhuyen')
    } if (selectedType === 2) {
      str = t('CHON_phuongxa')
    }
    return (<View style={styles.modal__header}>
      <ButtonBack onPress={onCloseModal} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>{str}</Text>
    </View>)
  }

  function pressItem(item, selectedType) {
    onCloseModal()
    switch (selectedType) {
      case 0:
        setProvince(item)
        setOnchangeProvince(true)
        setDistrict('')
        // setDistricts([])
        setWard('')
        // setWards([])
        break
      case 1:
        setDistrict(item)
        setWard('')
        // setWards([])
        break
      case 2:
        setWard(item)
        break
      default:
        return null
    }
  }

  useEffect(() => {
    if (selectedType >= 0) onOpenModal()
  }, [selectedType, reOpenFlag])

  const validateFields = () => validate.isEmpty(province) || validate.isEmpty(district) || validate.isEmpty(ward) || validate.isEmpty(phone) || validate.isEmpty(name) || validate.isEmpty(street)

  const renderItem = (item, index) => {
    return (
      <TouchableOpacity key={index} onPress={() => {
        pressItem(item.value, selectedType)
      }
      }>
        <Text style={styles.selectProvince}>
          {item.value}
        </Text>
      </TouchableOpacity>
    )
  }

  const renderSelect = () => {
    let data = []
    if (selectedType === 0) {
      data = provinces
    }
    if (selectedType === 1) {
      data = districts
    }
    if (selectedType === 2) {
      data = wards
    }
    return (<View style={styles.boxContainerModal}>
      {data.map((item, index) => renderItem(item, index))}
    </View>
    )
  }

  function onSelectType(type) {
    setSelectedType(type)
    setReopenFlag(!reOpenFlag)
  }

  const onUpdateProfile = async () => {
    setSubmitting(true)
    if (!province) {
      showError(t('FAIL'), 'Bạn chưa chọn Tỉnh/Thành Phố')
      return
    } else if (!district) {
      showError(t('FAIL'), 'Bạn chưa chọn Quận/Huyện')
      return
    } else if (!ward) {
      showError(t('FAIL'), 'Bạn chưa chọn Phường/Xã')
      return
    } else if (!street) {
      showError(t('FAIL'), 'Bạn chưa nhập số nhà, đường phố')
      return
    } else if (!name) {
      showError(t('FAIL'), 'Bạn chưa nhập họ và tên')
      return
    } else if (phone.length < 12) {
      showError(t('FAIL'), 'Số điện thoại phải lớn hơn 10 ký tự!')
      setSubmitting(false)
      return
    }
    const address = ((street || '') + ', ' + (ward || '') + ', ' + (district || '') + ', ' + (province || ''))
    const data = {
      _id: item._id,
      address: address,
      district: district,
      province: province,
      ward: ward,
      name: name,
      phone: phone,
      street: street,
      default: item.default
    }
    profileStore.updateAddressOfList(data)
    // const body = profileStore
    const snapShotProfile = getSnapshot(profileStore)
    const body = UpdateProfileModel.create(snapShotProfile)
    const rs = await profileStore.updateProfile(body)
    // console.log('UPDATEPROFILE', rs)
    if (rs && !rs.data.error) {
      showSuccess(t('THANHCONG'), 'Cập nhật địa chỉ thành công')
      setProvince('')
      setDistrict('')
      setName('')
      setStreet('')
      setWard('')
      setPhone('')
      profileStore.setReloadData(true)
      setTimeout(() => {
        goBack()
      }
      , 3000,
      )
    } else {
      showError(t('FAIL'), 'Cập nhật không thành công')
    }
  }

  const setPhoneNumber = (phone) => {
    if (phone.length && _.startsWith(phone, '+840')) {
      const search = '+840'
      const replaceWith = '+84'
      setPhone(phone.replace(search, replaceWith))
    } else {
      setPhone(phone)
    }
  }

  const iconLeft = <Icon name={'chevron-down-outline'} size={20} color={'#f3373a'} style={styles.iconLeft}/>

  return (<SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
    <Header
      leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
      centerComponent={{ text: t(t('Sửa địa chỉ')), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
      containerStyle={common.headerContainer}
      statusBarProps={{ barStyle: 'light-content' }}
      ViewComponent={LinearGradient}
      linearGradientProps={linearGradientProps}
    />
    <KeyboardAvoidingView
      behavior={Platform.OS == 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
        <View style={styles.viewContainer}>
          <TouchableOpacity style={styles.viewProvince} onPress={() => onSelectType(0)}>
            <Text style={{ ...styles.textProvince, color: province ? '#333' : '#BDBDBD' }}
            >{province || t('TINH/TP')}</Text>
            {iconLeft}
          </TouchableOpacity>

          <TouchableOpacity style={styles.viewProvince} onPress={ () => onSelectType(1)}
          >
            <Text style={{ ...styles.textProvince, color: district ? '#333' : '#BDBDBD' }}
            >{district || t('CHON_quanhuyen')}</Text>
            {iconLeft}
          </TouchableOpacity>

          <TouchableOpacity style={styles.viewProvince} onPress={ () => onSelectType(2)}>
            <Text style={{ ...styles.textProvince, color: ward ? '#333' : '#BDBDBD' }}
            >{ward || t('CHON_phuongxa')}</Text>
            {iconLeft}
          </TouchableOpacity>

          {/* <TextInput */}
          {/*  style={styles.textInput} */}
          {/*  placeholderTextColor="#c5cee0" */}
          {/*  placeholder={'Số nhà, Đường phố'} */}
          {/*  defaultValue={street} */}
          {/*  onChangeText={e => setStreet(e)} */}
          {/* /> */}

          {/* <TextInput */}
          {/*  style={styles.textInput} */}
          {/*  placeholderTextColor="#c5cee0" */}
          {/*  placeholder={'Họ và Tên'} */}
          {/*  defaultValue={name} */}
          {/*  onChangeText={e => setName(e)} */}
          {/* /> */}
          {/* <TextInput */}
          {/*  style={styles.textInput} */}
          {/*  placeholderTextColor="#c5cee0" */}
          {/*  placeholder={'Số điện thoại'} */}
          {/*  defaultValue={phone} */}
          {/*  onChangeText={e => setPhone(e)} */}
          {/* /> */}

          <View style={styles.viewInputField}>
            <TTextInput
              typeInput={'code'}
              typeRadius={'rounded'}
              // keyboardType="phone-pad"
              maxLength={255}
              autoCapitalize={'sentences'}
              defaultValue={street || ''}
              placeholder={t('Số nhà, Đường phố')}
              onChangeText={e => setStreet(e)}
              iconRightClick={() => setStreet('')}
              iconRight={<Icon name='close-circle-outline' size={24} color='#a0a0a0' />}
            />
          </View>
          <View style={styles.viewInputField}>
            <TTextInput
              typeInput={'code'}
              typeRadius={'rounded'}
              // keyboardType="phone-pad"
              maxLength={100}
              autoCapitalize={'sentences'}
              defaultValue={name || ''}
              placeholder={t('Họ và Tên')}
              onChangeText={e => setName(e)}
              iconRightClick={() => setName('')}
              iconRight={<Icon name='close-circle-outline' size={24} color='#a0a0a0' />}
            />
          </View>
          <View style={styles.viewInputField}>
            <TTextInput
              typeInput={'phone'}
              typeRadius={'rounded'}
              keyboardType="phone-pad"
              maxLength={12}
              autoCapitalize={'none'}
              value={phone || ''}
              placeholder={t('MOBILE')}
              placeholderStyle={{ textAlign: 'center' }}
              onChangeText={phone => setPhoneNumber(phone)}
              iconRightClick={() => setPhone('')}
              iconRight={<Icon name='close-circle-outline' size={24} color='#a0a0a0' />}
            />
          </View>

          <TButton typeRadius={'rounded'} disabled={validateFields() || isSubmitting} loading={isSubmitting} buttonStyle={styles.buttonAdd} title={t('XONG')} onPress={onUpdateProfile} />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
    <>
      <Modalize
        HeaderComponent={renderHeader}
        ref={modalize}
        modalHeight={responsiveHeight(50)}
        keyboardAvoidingBehavior={'padding'}
      >
        {renderSelect()}
      </Modalize>
    </>
  </SafeAreaView>)
})
