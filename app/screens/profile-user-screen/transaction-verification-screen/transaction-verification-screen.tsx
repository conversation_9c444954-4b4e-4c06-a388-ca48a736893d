import { SafeAreaView } from 'react-native-safe-area-context'
import React, { useState, useEffect, useRef, useContext } from 'react'
import {

  View,
  Text, TouchableOpacity, ScrollView, KeyboardAvoidingView, Platform,
} from 'react-native'

import styles from './styles'

import Icon from 'react-native-vector-icons/Ionicons'
import { useNavigation } from '@react-navigation/native'

import { observer } from 'mobx-react-lite'
import { Modalize } from 'react-native-modalize'
import { countriesData } from '../../../assets/countries'
import { UpdateProfileModel, useStores } from '@app/models'
import { useTranslation } from 'react-i18next'
import validate from 'validate.js'
import { responsiveHeight } from 'react-native-responsive-dimensions'
import { ModalContext } from '@app/context'
import { TButton, ButtonBack, TTextInput } from '@app/components'
import { color, spacing } from '@app/theme'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import { getSnapshot } from 'mobx-state-tree'
import _ from 'lodash'
import LinearGradient from 'react-native-linear-gradient'

export const TransactionVerificationScreen: React.FC = observer((props) => {
  const { t } : any = useTranslation()
  const modalize = useRef<Modalize>(null)
  const { profileStore } = useStores()
  const [countries] = useState(countriesData)
  const [province, setProvince] = useState(null)
  const [provinces, setProvinces] = useState([])
  const [district, setDistrict] = useState(null)
  const [name, setName] = useState(null)
  const [phone, setPhone] = useState(null)
  const [districts, setDistricts] = useState([])
  const [ward, setWard] = useState(null)
  const [wards, setWards] = useState([])
  const [street, setStreet] = useState('')
  const [isSubmitting, setSubmitting] = useState(false)
  const [selectedType, setSelectedType] = useState(-1)
  const [reOpenFlag, setReopenFlag] = useState(false)
  const { showError, showSuccess } = useContext(ModalContext)
  const { goBack } = useNavigation()

  useEffect(() => {
    const cities = []
    for (const city in countries) {
      cities.push({
        value: city,
      })
    }
    setProvinces(cities)
    setDistrict('')
    setDistricts([])
    setWard('')
    setWards([])
  }, [])

  useEffect(() => {
    if (province) {
      setDistricts(Object.keys(countries[province]).map(x => {
        return { value: x }
      }))
      setDistrict('')
      setWard('')
    }
  },
  [province])

  useEffect(() => {
    if (district) {
      setWards(countries[province][district].map(x => {
        return { value: x }
      }))
      setWard('')
    }
  },
  [district])

  const onOpenModal = () => {
    if (selectedType === 1) {
      if (!province) return
    } if (selectedType === 2) {
      if (!district) return
    }
    modalize.current?.open()
  }
  const onCloseModal = () => {
    modalize.current?.close()
  }
  const renderHeader = () => {
    let str = ''
    if (selectedType === 0) {
      str = t('TINH/TP')
    } if (selectedType === 1) {
      str = t('CHON_quanhuyen')
    } if (selectedType === 2) {
      str = t('CHON_phuongxa')
    }
    return (<View style={styles.modal__header}>
      <ButtonBack onPress={onCloseModal} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>{str}</Text>
    </View>)
  }

  function pressItem(item, selectedType) {
    onCloseModal()
    switch (selectedType) {
      case 0:
        setProvince(item)
        setDistrict('')
        // setDistricts([])
        setWard('')
        // setWards([])
        break
      case 1:
        setDistrict(item)
        setWard('')
        // setWards([])
        break
      case 2:
        setWard(item)
        break
      default:
        return null
    }
  }
  useEffect(() => {
    if (selectedType >= 0) onOpenModal()
  }, [selectedType, reOpenFlag])

  const validateFields = () => validate.isEmpty(province) || validate.isEmpty(district) || validate.isEmpty(ward) || validate.isEmpty(phone) || validate.isEmpty(name) || validate.isEmpty(street)

  const renderItem = (item, index) => {
    return (
      <TouchableOpacity key={index} onPress={() => {
        pressItem(item.value, selectedType)
      }
      }>
        <Text style={styles.selectProvince}>
          {item.value}
        </Text>
      </TouchableOpacity>
    )
  }
  const renderSelect = () => {
    let data = []
    if (selectedType === 0) {
      data = provinces
    }
    if (selectedType === 1) {
      data = districts
    }
    if (selectedType === 2) {
      data = wards
    }
    return (<View style={styles.boxContainerModal}>
      {data.map((item, index) => renderItem(item, index))}
    </View>
    )
  }

  function onSelectType(type) {
    setSelectedType(type)
    setReopenFlag(!reOpenFlag)
  }

  const onUpdateProfile = async () => {
    setSubmitting(true)
    if (!province) {
      showError(t('FAIL'), 'Bạn chưa chọn Tỉnh/Thành Phố')
      return
    } else if (!district) {
      showError(t('FAIL'), 'Bạn chưa chọn Quận/Huyện')
      return
    } else if (!ward) {
      showError(t('FAIL'), 'Bạn chưa chọn Phường/Xã')
      return
    } else if (!street) {
      showError(t('FAIL'), 'Bạn chưa nhập số nhà, đường phố')
      return
    } else if (!name) {
      showError(t('FAIL'), 'Bạn chưa nhập họ và tên')
      return
    } else if (phone.length < 12) {
      showError(t('FAIL'), 'Số điện thoại phải lớn hơn 10 ký tự!')
      setSubmitting(false)
      return
    }
    const address = ((street || '') + ', ' + (ward || '') + ', ' + (district || '') + ', ' + (province || ''))
    const data =
      {
        address: address,
        district: district,
        province: province,
        ward: ward,
        name: name,
        phone: phone,
        street: street,
        default: false
      }
    profileStore.setListAddress(data)
    // const body = profileStore
    const snapShotProfile = getSnapshot(profileStore)
    const body = UpdateProfileModel.create(snapShotProfile)
    const rs = await profileStore.updateProfile(body)
    if (rs && !rs.data.error) {
      setProvince('')
      setDistrict('')
      setName('')
      setStreet('')
      setWard('')
      setPhone('')
      // navigation.navigate(SCREENS.renderAddress, { refreshData: true })
      profileStore.setReloadData(true)
      goBack()
      await updateDefault()
    } else {
      showError(t('FAIL'), `${rs.data.message}`)
    }
  }

  const updateDefault = async () => {
    const findDefault = profileStore.addressList.filter(address => address.default === true)
    if (profileStore.addressList.length >= 1 && findDefault.length === 0) {
      const updateItem = { ...profileStore.addressList[0] }
      updateItem.default = true
      profileStore.updateAddressOfList(updateItem)
      __DEV__ && console.log(profileStore.addressList[0])
      const snapShotProfile = getSnapshot(profileStore)
      const body = UpdateProfileModel.create(snapShotProfile)
      const rs = await profileStore.updateProfile(body)
    } else {

    }
  }

  const setPhoneNumber = (phone) => {
    if (phone.length && _.startsWith(phone, '+840')) {
      const search = '+840'
      const replaceWith = '+84'
      setPhone(phone.replace(search, replaceWith))
    } else {
      setPhone(phone)
    }
  }

  const renderInputLabel = (title) => {
    return (
      <Text style={{
        color: '#01061F',
        fontSize: 14,
        fontWeight: '500',
        marginBottom: spacing.small
      }}>{title}</Text>
    )
  }

  const iconLeft = <Icon name={'chevron-down-outline'} size={20} color={color.primary} style={styles.iconLeft}/>
  return (<SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
    {/* <ButtonBack onPress={goBack} style={styles.icArrowBack}/> */}
    {/* <Text style={styles.textTitleTotal}>Nhập địa chỉ của bạn</Text> */}
    <Header
      // statusBarProps={{ barStyle: 'light-content' }}
      // barStyle="light-content" // or directly
      leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
      centerComponent={{ text: 'Tra soát', style: { color: '#fff', fontWeight: 'bold', fontSize: 16 } }}
      // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
      //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
      // </TouchableOpacity> : null}
      containerStyle={common.headerContainer}
      statusBarProps={{ barStyle: 'light-content' }}
      ViewComponent={LinearGradient}
      linearGradientProps={linearGradientProps}
    />
    <KeyboardAvoidingView
      behavior={Platform.OS == 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
        <View style={styles.viewContainer}>
          <View style={styles.viewInputField}>
            {renderInputLabel('Số tài khoản nhận tiền')}
            <TTextInput
              typeInput={'code'}
              typeRadius={'rounded'}
              // keyboardType="phone-pad"
              maxLength={255}
              autoCapitalize={'sentences'}
              defaultValue={street || ''}
              placeholder={t('Số nhà, Đường phố')}
              onChangeText={e => setStreet(e)}

            />
          </View>
          <View style={styles.viewInputField}>
            {renderInputLabel('Chủ tài khoản')}
            <TTextInput
              typeInput={'code'}
              typeRadius={'rounded'}
              // keyboardType="phone-pad"
              maxLength={255}
              autoCapitalize={'sentences'}
              defaultValue={street || ''}
              placeholder={'Chủ tài khoản'}
              onChangeText={e => setStreet(e)}

            />
          </View>
          <View style={styles.viewInputField}>
            {renderInputLabel('Ngân hàng')}
            <TouchableOpacity style={styles.viewProvince} onPress={() => {}}>
              <Text style={{ ...styles.textProvince, color: province ? '#333' : '#A0A0A0' }}
              >{province || 'Ngân hàng'}</Text>
              {iconLeft}
            </TouchableOpacity>
          </View>
          <View style={styles.viewInputField}>
            {renderInputLabel('Căn cước công dân/ CMT')}
            <TTextInput
              typeInput={'code'}
              typeRadius={'rounded'}
              // keyboardType="phone-pad"
              maxLength={255}
              autoCapitalize={'sentences'}
              defaultValue={street || ''}
              placeholder={'Căn cước công dân/ CMT'}
              onChangeText={e => setStreet(e)}

            />
          </View>
          <View style={styles.viewInputField}>
            {renderInputLabel('Ngày tạm ứng')}
            <TouchableOpacity style={styles.viewProvince} onPress={() => {}}>
              <Text style={{ ...styles.textProvince, color: province ? '#333' : '#A0A0A0' }}
              >{province || 'Ngày tạm ứng'}</Text>
              {iconLeft}
            </TouchableOpacity>
          </View>
          <View style={styles.viewInputField}>
            {renderInputLabel('Nội dung yêu cầu')}
            <TTextInput
              typeInput={'code'}
              typeRadius={'rounded'}
              // keyboardType="phone-pad"
              maxLength={255}
              autoCapitalize={'sentences'}
              defaultValue={street || ''}
              placeholder={t('Nội dung yêu cầu')}
              onChangeText={e => setStreet(e)}
              multiline
              style={{ textAlignVertical: 'top' }}

            />
          </View>

          <TButton typeRadius={'rounded'} loading={isSubmitting} buttonStyle={styles.buttonAdd} title={'Gửi yêu cầu tra soát'} onPress={onUpdateProfile} />

        </View>
      </ScrollView>
    </KeyboardAvoidingView>
    <>
      <Modalize
        HeaderComponent={renderHeader}
        ref={modalize}
        modalHeight={responsiveHeight(50)}
        keyboardAvoidingBehavior={'padding'}
      >
        {renderSelect()}
      </Modalize>
    </>
  </SafeAreaView>)
})
