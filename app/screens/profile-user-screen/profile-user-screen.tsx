import { SafeAreaView } from 'react-native-safe-area-context'
import {

  Text,
  View,
  TouchableOpacity,
  ScrollView, Linking
} from 'react-native'
import React, { useState, Fragment, useEffect, useRef } from 'react'
import styles from './styles'
import { useTranslation } from 'react-i18next'
import Icon from 'react-native-vector-icons/MaterialCommunityIcons'
import { useStores } from '@app/models'
import { useNavigation } from '@react-navigation/native'
import { Modalize } from 'react-native-modalize'
import { Language } from '../language-screen/language-screen'
import { observer } from 'mobx-react-lite'
import { SCREENS } from '@app/navigation'
import { useAuth } from '@app/use-hooks/use-auth'
import Share from 'react-native-share'
import { LazyImage, ButtonBack, ConfirmDialog } from '@app/components'
import DeviceInfo from 'react-native-device-info'
import { loadString } from '@app/utils/storage'
import { LogEvent } from '@app/services/loggingServices'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import { color } from '@app/theme'
import LinearGradient from 'react-native-linear-gradient'
import { Api } from '@app/services/api'
import { isAndroid } from '@app/utils/chat/deviceInfo'
import { APP_STORE_LINK, PLAY_MARKET_LINK } from '@app/constants/links'
// import { ModalContext } from '@app/context'
const api = new Api()

const RendeMenuItem = (props: any) => {
  const { navigate, goBack } = useNavigation()
  const { profileStore } = useStores()

  const navScreen = () => {
    if (profileStore.isSignedIn()) {
      navigate(props.screen, { title: props.text })
    } else {
      navigate(SCREENS.authStack, { screen: SCREENS.login })
    }
  }

  return (<TouchableOpacity
    onPress={() => props.onPress ? props.onPress() : navScreen()}
    style={{ ...styles.boxButton }}>
    <Icon name={props.icon} size={22} color={color.dim} style={styles.iconLeft} />
    <Text style={styles.textButton}>{props.text}</Text>
    <Icon size={18} color={color.primary} style={styles.iconRight} name={'chevron-right'} />
  </TouchableOpacity>)
}

export const ProfileUserScreen = observer((props) => {
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()
  const { profileStore, notificationStore, carStore, productStore } = useStores()
  // const [refreshing, setRefreshing] = useState(false)
  const modalizeRef = useRef<Modalize>(null)
  // const [photoUrl, setPhotoUrl] = useState("")
  const { signOut, userToken } = useAuth() // should be signUp
  // const { showError, showSuccess, showConfirm } = useContext(ModalContext)
  const version = DeviceInfo.getVersion()
  const [versionCodePush, setVersionCodePush] = useState('')
  const [isShowConfirmLogOut, setIsShowConfirmLogOut] = useState(false)

  useEffect(() => {
    loadData().then(r => {
    })
    __DEV__ && console.log('state =>>>>', userToken)
    // setTimeout(forceUpdate, 2000)
  }, [])

  const goLoginScreenRequired = () => {
    navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  const goToEditProfile = () => {
    if (profileStore.isSignedIn()) {
      navigate(SCREENS.editProfile, { title: t('BOOKING_HISTORY') })
    } else {
      goLoginScreenRequired()
      LogEvent('user_click_btn_register', { screen: 'profile_screen' })
    }
  }

  const loadData = async () => {
    const rs = await profileStore.getProfile()
    if (rs?.data?.error) {
      onSignOut()
    }
    const versionData = await loadString('versionCodePush')
    setVersionCodePush(versionData)
  }

  const onOpenModalEditLanguage = () => {
    modalizeRef.current?.open()
  }

  const onCloseModel = () => {
    modalizeRef.current?.close()
  }

  const onSignOut = async () => {
    try {
      await api.logoutUser()
      signOut() // gọi hàm signout global
      setTimeout(() => { resetHistory() }, 500)
    } catch (e) {
      __DEV__ && console.log(e)
    }
  }

  const resetHistory = () => {
    profileStore.clearFields()
    profileStore.reset()
    notificationStore.clearFields()
    carStore.clearFields()
    carStore.clearDataListPet()
    productStore.clearDataViewed()
    profileStore.setReloadData(true)
    profileStore.setBookingSpa([])
    profileStore.setBookingHotel([])
    profileStore.setBookingClinic([])
    profileStore.setBookingProduct([])
  }

  const navigatePetManagemant = () => {
    if (profileStore.isSignedIn()) {
      navigate(SCREENS.carManagement)
    } else {
      goLoginScreenRequired()
      LogEvent('user_click_btn_register', { screen: 'profile_screen' })
    }
  }

  const navigateToSettingProfile = () => {
    if (profileStore.isSignedIn()) {
      navigate(SCREENS.editProfile)
    } else {
      goLoginScreenRequired()
      LogEvent('user_click_btn_register', { screen: 'profile_screen' })
    }
  }

  const goLogin = async () => {
    profileStore.clearFields()
    navigate(SCREENS.authStack, { screen: SCREENS.login })
  }
  const goRegister = async () => {
    profileStore.clearFields()
    navigate(SCREENS.authStack, { screen: SCREENS.register })
  }

  const goLuckyWheel = async () => {
    navigate(SCREENS.luckyWheelStack)
  }

  const renderHeaderModalize = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onCloseModel} style={styles.viewTouchButtonTop}/>
      <Text style={styles.textTitleHeader}>{t('CHON_NGON_NGU')}</Text>
    </View>
  )

  const onInviteFriend = () => {
    const options = {
      title: '',
      social: Share.Social.SMS,
      failOnCancel: false,
      urls: ['https://maxq.vn'],
    }

    Share.open(options)
      .then((res) => {
        __DEV__ && console.log(res)
      })
      .catch((err) => {
        __DEV__ && err && console.log(err)
      })
  }

  const showAlert = () => {
    setIsShowConfirmLogOut(true)
  }

  const updateNow = () => {
    const link = isAndroid ? PLAY_MARKET_LINK : APP_STORE_LINK
    Linking.canOpenURL(link).then(
      (supported) => {
        supported && Linking.openURL(link)
      },
      (err) => console.log(err)
    )
  }

  return (
    <Fragment>
      <SafeAreaView style={{ flex: 1, backgroundColor: '#fff', marginBottom: 60, marginTop: -4 }}>
        <Header
          leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
          centerComponent={{ text: t(t('VIEWPAGEINDEX_account')), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
          containerStyle={common.headerContainer}
          statusBarProps={{ barStyle: 'light-content' }}
          ViewComponent={LinearGradient}
          linearGradientProps={linearGradientProps}
        />
        <ScrollView
          // scrollEventThrottle={20}
          // onScrollBeginDrag={onScroll}
          // onScrollEndDrag={onScroll}
          style={styles.scrollView}>
          <View style={styles.mainContainer}>
            <View style={styles.mainUser}>
              {profileStore.isSignedIn() ? <TouchableOpacity
                onPress={() => navigate(SCREENS.editProfile)}
                style={styles.touchUser}
              >
                <LazyImage
                  type='avatar'
                  resizeMode="cover"
                  style={styles.image}
                  // containerStyle={{ marginTop: 10 }}
                  source={{ uri: profileStore.avatarUser }}
                />
                <View style={styles.viewName}>
                  <Text style={styles.textName}>{profileStore.fullName}</Text>
                  <Text style={styles.textPhone}>{profileStore.phone}</Text>
                </View>
                <Icon size={18} color={color.primary} style={styles.icon} name={'chevron-right'} />
              </TouchableOpacity> : <View style={styles.profileBox}>
                <LazyImage
                  type='avatar'
                  resizeMode="cover"
                  style={styles.image}
                  source={{ uri: '' }}
                />
                <View style={styles.groupBtn}>
                  <TouchableOpacity
                    onPress={goRegister}
                    style={[styles.btnRegister, { backgroundColor: '#f3f3f3' }]}
                  >
                    <Text style={[styles.textBtn, { color: '#333' }]}>{t('REGISTER')}</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={goLogin}
                    style={[styles.btnRegister, { backgroundColor: color.primary }]}
                  >
                    <Text style={[styles.textBtn, { color: '#fff' }]}>{t('LOGIN')}</Text>
                  </TouchableOpacity>
                </View>
              </View>
              }
            </View>
            <View>
              {/* <RendeMenuItem screen={SCREENS.renderAddress} text={t('DIACHI')} icon={'home-map-marker'}/> */}
              {/* <RendeMenuItem screen={SCREENS.bookingHistoryStack} text={t('TAIKHOAN_lichsumuahang')} icon={'receipt'}/> */}
              <RendeMenuItem screen={SCREENS.notificationScreen} text={t('THONG_BAO')} icon={'bell-circle-outline'}/>
              {/* <RendeMenuItem screen={SCREENS.chatRoomScreen} text={t('CHAT_ROOM')} icon={'message-text-outline'}/> */}
              {/* <TouchableOpacity onPress={onOpenModalEditLanguage} style={styles.boxButton}> */}
              {/*  <Icon name="globe-outline" size={22} color={color.primary} style={styles.iconLeft}/> */}
              {/*  <Text style={styles.textButton}>{t('NGONNGU')}</Text> */}
              {/*  <Icon size={18} color={color.primary} style={styles.iconRight} name={'chevron-forward-outline'} /> */}
              {/* </TouchableOpacity> */}
              {/* { profileStore.isSignedIn() && <RendeMenuItem screen={SCREENS.rewardPointScreen} text={t('Điểm thưởng')} icon={'fire'}/> } */}
              {/* { profileStore.isSignedIn() && <RendeMenuItem screen={SCREENS.walletScreen} text={t('Wallet')} icon={'wallet-outline'}/> } */}
              {/* <RendeMenuItem onPress={navigateToSettingProfile} text={t('TAIKHOAN_caidat')} icon={'cog-outline'}/> */}
              {/* <RendeMenuItem onPress={onInviteFriend} text={t('MOI_BAN_BE')} icon={'account-plus-outline'}/> */}
              {/* <RendeMenuItem screen={SCREENS.supportStack} text={t('TAIKHOAN_trogiup')} icon={'lifebuoy'}/> */}
              {/* <RendeMenuItem onPress={updateNow} text={t('Cập nhật phiên bản mới')} icon={'cloud-upload-outline'}/> */}

              {/* <TouchableOpacity */}
              {/* onPress={navigatePetManagemant} */}
              {/* style={{ ...styles.boxButton }}> */}
              {/* <Icon name="briefcase-outline" size={22} color={color.primary} style={styles.iconLeft}/> */}
              {/* <Text style={styles.textButton}>{t('Quản lý xe')}</Text> */}
              {/* <Icon */}
              {/*   size={22} */}
              {/*   color={color.primary} */}
              {/*   style={styles.iconRight} */}
              {/*   name={'chevron-forward-outline'} */}
              {/* /> */}
              {/* </TouchableOpacity> */}
            </View>
            {profileStore.isSignedIn() ? <View style={styles.viewThird}>
              <RendeMenuItem onPress={showAlert} text={t('DANGXUAT')} icon={'logout'}/>
            </View> : null}
            <View style={{ backgroundColor: '#fff' }}>
              {/* <Text style={styles.textVersion}>{t('VERSION')}{version} - {versionCodePush}</Text> */}
              <Text style={styles.textVersion}>{t('VERSION')}{version}</Text>
            </View>
          </View>
        </ScrollView>
        <Modalize
          HeaderComponent={renderHeaderModalize}
          ref={modalizeRef}
          adjustToContentHeight
          keyboardAvoidingBehavior={'padding'}
        >
          <Language/>
        </Modalize>
        <ConfirmDialog confirmText={t('DONGY')} cancelText={t('CANCEL')} onClosed={() => setIsShowConfirmLogOut(false)} isVisible={isShowConfirmLogOut} message={'Bạn có chắc chắn muốn đăng xuất khỏi ứng dụng ?'} title={'Nhắc nhở'}
          onConfirm={() => {
            setIsShowConfirmLogOut(false)
            setTimeout(() => { onSignOut() }, 100)
          }
          }/>
      </SafeAreaView>
    </Fragment>
  )
})
