import { StyleSheet, Dimensions } from 'react-native'
import { color, spacing, typography } from '../../theme'

const { width, height } = Dimensions.get('window')
const styles = StyleSheet.create({
  boxButton: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    marginBottom: 1
  },
  btnLogin: {
    alignItems: 'center',
    backgroundColor: color.primary,
    borderRadius: 4,
    // elevation: 3,
    height: 44,
    justifyContent: 'center',
    margin: 10,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 1,
    // },
    // shadowOpacity: 0.22,
    // shadowRadius: 2.22,
    width: 100
  },
  btnRegister: {
    alignItems: 'center',
    // backgroundColor: '#d2d2d2',
    borderRadius: 4,
    // elevation: 5,
    height: 44,
    justifyContent: 'center',
    margin: 10,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 1,
    // },
    // shadowOpacity: 0.22,
    // shadowRadius: 2.22,
    width: 100
  },
  container: {
    flex: 1,
    justifyContent: 'center'
  },
  groupBtn: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 10
  },
  horizontal: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 10
  },
  icon: {
    alignSelf: 'center',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    // marginRight: 15,
    // marginTop: 50,

  },
  iconLeft: {
    alignSelf: 'center',
    marginHorizontal: 16
  },
  iconRight: {
    alignSelf: 'center',
    marginHorizontal: 10
  },
  image: {
    borderColor: '#f3f3f3',
    borderRadius: 100,
    borderWidth: 1,
    height: 80,
    width: 80,
  },
  imageNoUser: {
    borderRadius: 15,
    elevation: 2,
    height: 80,
    justifyContent: 'flex-start',
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10,
    width: 80
  },
  mainContainer: {
    backgroundColor: color.primaryBackground,
    flex: 1,
  },
  mainTitle: {
    backgroundColor: '#ffffff',
    paddingBottom: 16,
    paddingLeft: 15,
    paddingTop: 16
  },
  mainTitleText: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
  },
  mainUser: {
    backgroundColor: '#ffffff',
    flexDirection: 'column',
    marginBottom: 5,
    paddingTop: 20
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
  },
  profileBox: {
    alignItems: 'center'
  },
  scrollView: {
    backgroundColor: '#fff',
    flex: 1
  },
  textBtn: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  textBtnLogin: {
    color: '#fff',
  },
  textButton: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 14,
    justifyContent: 'center',
    paddingVertical: 16,
  },
  textName: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 20,
    fontWeight: '600',
    marginLeft: 15,
  },
  textPhone: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
    marginLeft: 15,
    marginTop: 6,
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  textVersion: {
    color: '#a0a0a0',
    fontFamily: typography.normal,
    fontSize: 12,
    padding: 20,
    textAlign: 'center'
  },

  touchUser: {
    flexDirection: 'row',
    marginLeft: 16,
    marginRight: 10,
    paddingBottom: 10
  },
  viewName: {
    flexDirection: 'column',
    flex: 1,
    justifyContent: 'flex-start',
    alignSelf: 'center'
    // marginTop: 29,
  },
  viewSecond: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    flexDirection: 'column',
    marginHorizontal: spacing.small,
    marginTop: 20,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10
  },
  viewThird: {
    backgroundColor: '#ffffff',
    // borderRadius: 8,
    // elevation: 2,
    flexDirection: 'column',
    marginTop: 4,
    marginBottom: 5
    // marginHorizontal: 16,
    // marginTop: 20,
    // shadowColor: 'rgba(85, 85, 85, 0.1)',
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 1,
    // shadowRadius: 10
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
})
export default styles
