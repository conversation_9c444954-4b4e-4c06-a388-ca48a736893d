import { StyleSheet, Dimensions } from 'react-native'
import { responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import { color, spacing } from '@app/theme'
const tab1ItemSize = (Dimensions.get('window').width - 30) / 5
const { height } = Dimensions.get('window')
const styles = StyleSheet.create({
  boxAddress: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    paddingBottom: 12,
    paddingLeft: 15,
  },
  boxChoose: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 12,
  },
  boxContact: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    height: 125,
    marginBottom: 20,
    marginLeft: 15,
    marginRight: 10,
    marginTop: 24,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10,
    width: 288
  },
  boxViewAddress: {
    backgroundColor: color.primaryBackground,
    // flex: 1,
    marginBottom: 1,
  },
  boxViewBTN: {
    flexDirection: 'row',
    justifyContent: 'flex-end'
  },
  boxViewPhone: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  buttonAdd: {
    alignItems: 'center',
    borderColor: color.primary,
    borderRadius: 4,
    borderStyle: 'dashed',
    borderWidth: 1,
    height: 50,
    justifyContent: 'center',
    marginHorizontal: 16
  },
  chooseAddress: {
    color: '#f3373a',
    fontSize: 12,
    marginLeft: 5,
  },
  container: {
    flex: 1,
    height: responsiveHeight(100),
    marginBottom: 100
  },
  icArrowBack: {
    marginVertical: 15,
    paddingRight: 20
  },
  icLocation: {
    marginTop: 2
  },
  placeholder: {
    borderRadius: 4,
    marginHorizontal: 15,
    marginVertical: 6,
  },
  renderItemListAddress: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 10,
    paddingHorizontal: 16
  },
  renderItemListPhone: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 1,
    paddingBottom: 10,
    paddingHorizontal: 16
  },
  safeAreaView: {
    // backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  textAddressBranch: {
    color: '#333',
    fontSize: 14,
    marginLeft: 9,
    width: '65%',
  },
  textAddressPhone: {
    color: '#333',
    fontSize: 14,
    marginLeft: 10,
    marginTop: 3
  },
  textButton: {
    color: color.primary,
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',

  },
  textName: {
    color: '#333',
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 12,
    maxWidth: responsiveWidth(50)
  },
  textTitleTotal: {
    color: '#333',
    fontSize: 26,
    fontWeight: 'bold',
    marginHorizontal: spacing.small
  },
  unChooseAddress: {
    color: '#b0aeae',
    fontSize: 12,
    marginLeft: 5,
    textDecorationLine: 'underline'
  },
  viewFlatlist: {
    // height: responsiveHeight(100) - 220,
    // justifyContent: 'space-between',
    // marginBottom: 90
    // backgroundColor: color.primaryBackground,
    // paddingTop: 5,
    flex: 1,
  }
})
export default styles
