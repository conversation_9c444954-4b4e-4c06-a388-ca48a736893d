import { StyleSheet, Dimensions } from 'react-native'
import { ifIphoneX } from 'react-native-iphone-x-helper'
import { spacing, typography } from '../../theme'
const { width, height } = Dimensions.get('window')
const styles = StyleSheet.create({
  allIcon: {
    marginTop: 3
  },
  badgeCLDV: {
    backgroundColor: '#C8E6C9',
    borderRadius: 12.5,
    height: 25,
    justifyContent: 'center',
    minWidth: 90
  },
  badgeCXN: {
    backgroundColor: '#FFF176',
    borderRadius: 12.5,
    height: 25,
    justifyContent: 'center',
    minWidth: 90
  },
  badgeCancel: {
    borderRadius: 12.5,
    height: 25,
    justifyContent: 'center',
    minWidth: 90
  },
  badgeDone: {
    backgroundColor: '#00e096',
    borderRadius: 12.5,
    height: 25,
    justifyContent: 'center',
    minWidth: 90
  },
  boxAddress: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    paddingBottom: 12,
    paddingLeft: spacing.small,
  },
  boxCheckInTime: {
    flex: 1,
    flexDirection: 'column'
  },
  boxContact: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    height: 125,
    marginBottom: 20,
    marginLeft: 15,
    marginRight: 10,
    marginTop: 24,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10,
    width: 288
  },
  boxService: {
    backgroundColor: '#fff',
    borderBottomColor: '#f6f6f7',
    flexDirection: 'row',
    marginHorizontal: 15,
    marginVertical: 16
  },
  boxViewAddress: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    marginHorizontal: spacing.small,
    marginVertical: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.18,
    shadowRadius: 3.00,
  },
  boxViewPhone: {
    flexDirection: 'row'
  },
  codeOrder: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.small,
    paddingTop: 15
  },
  container: {
    borderBottomColor: '#e3e3e3',
    borderBottomWidth: 0.5
  },
  containerServiceChoose: {
    flexDirection: 'row',
    marginLeft: 10,
    marginTop: 15
  },
  contentDateTime: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginLeft: 15,
  },
  contentText: {
    alignItems: 'flex-start',
    flex: 1,
    position: 'relative',
  },
  date: {
    alignSelf: 'center',
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 10
  },
  dateTime: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingRight: 15
  },
  icArrowBack: {
    // marginLeft: 10,
    paddingRight: 20
  },
  icStatus: {
    alignSelf: 'center',
    marginLeft: 10
  },
  imageChoose: {
    borderRadius: 8,
    height: 50,
    width: 50
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
    ...ifIphoneX({
      marginTop: 0
    }, {
      marginTop: 5
    }),
  },
  noteContent: {
    backgroundColor: '#f7f9fc',
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginHorizontal: spacing.small,
    marginVertical: 10,
    paddingHorizontal: 10,
    paddingVertical: 10
  },
  orderNo: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: -100
  },
  paymentMethod: {
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 12,
  },
  rSp: {
    alignItems: 'flex-start',
    backgroundColor: '#fff',
    borderBottomColor: '#E9E9E9',
    borderBottomWidth: 0.5,
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 16,
  },
  rSpView: {
    alignItems: 'center',
    backgroundColor: '#f3f3f3',
    height: 82,
    justifyContent: 'center',
    padding: 2,
    width: 82
  },
  rSpView1: {
    flex: 1,
    paddingLeft: 20
  },
  rSpView1Input: {
    color: '#333',
    fontSize: 13,
  },
  rSpView1Text: {
    color: '#333333',
    fontSize: 14,
  },
  rSpView1Text1: {
    color: '#333',
    fontSize: 13,
    marginTop: 10
  },
  rSpView1Text2: {
    color: '#999999',
    fontSize: 11,
    marginTop: 2
  },
  rSpView2: {
    alignItems: 'center',
    justifyContent: 'center'
  },
  rSpViewImage: {
    height: 80,
    resizeMode: 'contain',
    width: 80,
  },
  rdkTopImage: {
    borderRadius: 8,
    height: 50,
    resizeMode: 'cover',
    width: 50
  },
  rdkTopText: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0,
    paddingBottom: 10
  },
  rdkTopText1: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0
  },
  renderItemListAddress: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 8,
    paddingLeft: spacing.small,
    paddingRight: spacing.small,
  },
  renderItemListPhone: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
    paddingLeft: 13,
    paddingRight: 13,
  },
  serviceContent: {
    flex: 1,
    paddingHorizontal: 10
  },
  starRate: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
    paddingBottom: 10,
    paddingHorizontal: 10,
    width: '100%'
  },
  textAddress: {
    // padding: 8
  },
  textAddressBranch: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    marginHorizontal: 7,
    width: '90%'
  },
  textAddressPhone: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 13,
    marginLeft: 7
  },
  textContent: {
    color: '#ff8ba1',
    fontFamily: typography.normal,
    fontSize: 13,
    fontWeight: '600'
  },
  textLabel: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0,
    marginLeft: spacing.small,
  },
  textName: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    marginTop: 5,
  },
  textPaymentMethod: {
    flex: 1,
    marginHorizontal: spacing.small,
    marginVertical: 10
  },
  textPrice_Count: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0,
    marginHorizontal: spacing.small,
    marginVertical: 10,
  },
  textPrice_Shipping: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginHorizontal: spacing.small,
    marginVertical: 10
  },
  textStatus: {
    color: 'black',
    fontFamily: typography.normal,
    fontSize: 12,
    paddingHorizontal: 10,
    textAlign: 'center',
  },
  textTime: {
    color: '#333',
    fontSize: 12,
    marginLeft: 15,
    paddingBottom: 10,
    paddingHorizontal: 16
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',

  },
  textTitleService: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
  },
  title: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
  },
  totalMoney: {
    color: '#333',
    fontWeight: '600',
    letterSpacing: 0,
    marginHorizontal: spacing.small,
    marginVertical: 10
  },
  totalMoneyContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  viewBTTop: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  viewBtn: {
    margin: 15,
    marginTop: 30
  },
  viewBtnItem: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 15
  },
  viewBtnTop: {
    color: '#f3373a',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    marginRight: spacing.small
  },
  viewContainer: {
    marginTop: 15,
    // paddingBottom: 89
  },
  viewImageService: {
    alignItems: 'center',
    borderRadius: 16,
    justifyContent: 'center',
    marginLeft: 0,
  },
  viewService: {
    flexDirection: 'column',
    flex: 1,
    marginLeft: spacing.small
  },
  viewStatus: {
    backgroundColor: '#00e096',
    borderRadius: 12.5,
    height: 25,
    justifyContent: 'center',
    minWidth: 90,
  },
  viewTextLabel: {
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    flexDirection: 'row',
    height: 30
  },
  viewTextLabelFooter: {
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    flexDirection: 'row',
    height: 30,
  },
  viewTitle: {
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.small,
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
})
export default styles
