import React, { useEffect } from 'react'
import { observer } from 'mobx-react-lite'
import { View, Text, TouchableOpacity, FlatList } from 'react-native'
import Icon from 'react-native-vector-icons/Ionicons'
import { ButtonBack } from '@app/components'
import { useNavigation, useRoute } from '@react-navigation/native'
import styles from './styles'
import { useStores } from '@app/models'
import moment from 'moment'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import { useTranslation } from 'react-i18next'
import LinearGradient from 'react-native-linear-gradient'
import { SafeAreaView } from 'react-native-safe-area-context'
// import Clipboard from '@react-native-clipboard/clipboard'

export const ShippingInformationScreen = observer(function ShippingInformationScreen() {
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()
  const route: any = useRoute()
  const { productDetail } = route?.params
  const { productStore } = useStores()

  useEffect(() => {
    loadData()
  }, [])

  const loadData = () => {
    productStore.getShippingInfo(productDetail.orderId)
  }

  // const renderBill = (item) => {
  //   return (
  //     <View style={styles.rSp}>
  //       <View style={styles.rSpView}>
  //         <LazyImage source={{ uri: item.thumbnail }}
  //           style={styles.rSpViewImage} />
  //       </View>
  //       <View style={styles.rSpView1}>
  //         <Text style={styles.rSpView1Text}>{productDetail.shippingService}</Text>
  //         {productStore?.shippingInFo?.length > 0 ? <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}><Text style={styles.rSpView1Text2}>Mã vận đơn: <Text style={styles.rSpView1Input}>{productStore?.shippingInFo[0]?.shippingCode}</Text></Text>
  //           <TouchableOpacity onPress={() => Clipboard.setString(`${productStore?.shippingInFo[0]?.shippingCode}`)}>
  //             <View>
  //               <Text style={{ color: 'red', fontSize: 14 }}>Sao chép</Text>
  //             </View>
  //           </TouchableOpacity></View> : null}
  //       </View>
  //     </View>
  //   )
  // }

  // const renderBillView = () => {
  //   return (<View>{productDetail.products.map((item) => renderBill(item))}</View>
  //   )
  // }

  const renderItem = ({ item, index }) => {
    return (
      <TouchableOpacity onPress={() => {
        // setSelect(item._id)
        // props.onPress(item.id)
      }}
      style={styles.container}
      >
        <View style={{ flexDirection: 'row', paddingHorizontal: 16, paddingVertical: 10 }}>
          {index === 0 ? <Icon style={{ marginTop: 2, marginRight: 4 }} color={'#4DB6AC'} name={'ellipse'} size={12} ></Icon> : <Icon style={{ marginTop: 2, marginRight: 4 }} color={'#B0BEC5'} name={'ellipse-outline'} size={12} ></Icon>}
          <Text style={[styles.textAddress, { color: index === 0 ? '#4DB6AC' : '#333' }]}>{item.statusText}</Text>
        </View>
        <Text style={[styles.textTime, { color: index === 0 ? '#4DB6AC' : '#333' }]}>{moment(item.createAt).format('HH:mm   DD/MM/YYYY')}</Text>
      </TouchableOpacity>
    )
  }

  return (
    <SafeAreaView style={{ backgroundColor: '#fff', flex: 1 }}>
      <Header
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
        centerComponent={{ text: t('Thông tin vận chuyển'), style: { color: '#fff', fontWeight: 'bold', fontSize: 16 } }}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      {/* {renderBillView()} */}
      {/* {renderTimeline()} */}
      <View>
        <FlatList
          horizontal={false}
          keyExtractor={(item, index) => item._id}
          data={productStore.shippingInFo}
          extraData={productStore.shippingInFo}
          renderItem={renderItem}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </SafeAreaView>
  )
})
