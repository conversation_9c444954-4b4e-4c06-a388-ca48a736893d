
import {
  Text,
  View,
  StyleSheet, useWindowDimensions, Linking, Dimensions
} from 'react-native'
import React, { useEffect, useState } from 'react'
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import { observer } from 'mobx-react-lite'
import { useNavigation, useRoute } from '@react-navigation/native'
import { ButtonBack, PlaceHolder, TButton } from '@app/components'
import { typography } from '@app/theme'
import { useTranslation } from 'react-i18next'
import { useStores } from '@app/models'
import RenderHtml from 'react-native-render-html'
import validate from 'validate.js'
import { responsiveWidth } from 'react-native-responsive-dimensions'
import FastImage from 'react-native-fast-image'
import { common, linearGradientProps } from '@app/theme/styles/common'
import LinearGradient from 'react-native-linear-gradient'
import { Header } from 'react-native-elements'
import Animated, { useSharedValue, useAnimatedScroll<PERSON><PERSON><PERSON>, useAnimatedStyle, withTiming } from 'react-native-reanimated'
import { SCREENS } from '@app/navigation'
import { tryParseJSON } from '@app/utils/json'

const renderersProps = {
  img: {
    enableExperimentalPercentWidth: true
  }
}

const { width, height } = Dimensions.get('window')
const imgHeight = 222 * (width / 375)

const HEADER_MAX_HEIGHT = 240
const HEADER_MIN_HEIGHT = 84

export const BlogDetail = observer((props: any) => {
  const route: any = useRoute()
  const { id } = route?.params
  const { postsStore } = useStores()
  const [news, setNews] = useState(null)
  const { navigate, goBack } = useNavigation()
  const { t } : any = useTranslation()
  const { width } = useWindowDimensions()

  const translationY = useSharedValue(0)
  const opacity = useSharedValue(0)
  const opacityButtonBack = useSharedValue(1)

  const scrollHandler = useAnimatedScrollHandler(
    {
      onScroll: (e) => {
        translationY.value = e.contentOffset.y * -1
      },
      onEndDrag: (e) => {
        // __DEV__ && console.log(e.contentOffset.y)
        if (e.contentOffset.y <= 0) {
          opacity.value = 0
          opacityButtonBack.value = 1
        } else {
          opacity.value = 1
          opacityButtonBack.value = 0
        }
      },
    })

  const stylez = useAnimatedStyle(() => {
    return {
      // transform: [
      //   {
      //     translateY: withTiming(translationY.value, {
      //       duration: 300
      //     }),
      //   },
      // ],
      opacity: withTiming(opacity.value, {
        duration: 300,
      })
    }
  })

  const stylesAnimateButtonBack = useAnimatedStyle(() => {
    return {
      opacity: withTiming(opacityButtonBack.value, {
        duration: 300,
      })
    }
  })

  useEffect(() => {
    loadData()
  }, [])
  const loadData = async () => {
    const rs = await postsStore.getPostDetail(id)
    if (rs.kind === 'ok' && rs?.data?.data) {
      const data = rs.data.data
      setNews(data)
    }
  }

  const onPressButton = () => {
    const item = news.attributes
    if (item?.screen_open) {
      const params = item?.screen_param || ''
      const trueParams = params !== '#'
      // kiểu tra nếu là LINK thì open , ngược lại thì open screen
      const jsonParams = tryParseJSON(params)
      switch (item?.screen_open) {
        case 'BHTNDSCAR':
          if (trueParams) {
            navigate(SCREENS.baoHiemTNDSB1, { prodId: params })
          }
          break
        case 'BHVCCAR':
          if (trueParams) {
            navigate(SCREENS.baoHiemVCXB1, { prodId: params })
          }
          break
        case 'BHTNDSBIKE':
          if (trueParams) {
            navigate(SCREENS.baoHiemTNDSXeMayB1, { prodId: params })
          }
          break
        case 'POPUP_SERVICE_OF_BRAND':
          if (params && item?.screen_param_spaId) {
            navigate(SCREENS.serviceDetail, { id: params, spaId: item?.screen_param_spaId })
          }
          break
        default:
          if (item.screen_open === 'LINK' && params) {
            const isValidURL = validate({ website: params }, { website: { url: true } })
            if (isValidURL === undefined) {
              Linking.openURL(params)
            }
          } else {
            if (params && params) {
              navigate(item.screen_open, { id: params })
            } else {
              navigate(item.screen_open)
            }
          }
          break
      }
    }
  }

  return (
    <SafeAreaView style={styles.container} edges={['right', 'left']}>
      <Animated.View style={[{ position: 'absolute', top: 0, width: '100%', zIndex: 9999 }, stylez]} >
        <Header
          // statusBarProps={{ barStyle: 'light-content' }}
          // barStyle="light-content" // or directly
          leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
          centerComponent={{ text: news?.attributes?.title, style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
          // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
          //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
          // </TouchableOpacity> : null}
          containerStyle={[common.headerContainer]}
          statusBarProps={{ barStyle: 'light-content' }}
          ViewComponent={LinearGradient}
          linearGradientProps={linearGradientProps}
        />
      </Animated.View>
      <Animated.ScrollView
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        onScroll={scrollHandler}
        scrollEventThrottle={16}
      >
        {!news ? <PlaceHolder/>
          : <View>
            <Animated.View style={{ marginTop: 0, height: imgHeight + useSafeAreaInsets().top }}>
              <FastImage
                resizeMode={'cover'}
                // style={styles.newsPicture}
                style={{ width: '100%', height: imgHeight + useSafeAreaInsets().top }}
                source={{ uri: `${news.attributes.image.data.attributes.url}` }} />
              <Animated.View style={[{ position: 'absolute', top: useSafeAreaInsets().top + 16, left: 8, zIndex: 9999 }, stylesAnimateButtonBack]}>
                <ButtonBack style={{ color: '#fff' }} onPress={goBack} />
              </Animated.View>
              <Text style={styles.newsTitle}>{news?.attributes?.title}</Text>
            </Animated.View>
            <Text style={styles.newsTitle}>{news?.attributes?.title}</Text>
            <View style={{ flexGrow: 0, backgroundColor: '#fff', paddingBottom: useSafeAreaInsets().bottom, paddingHorizontal: 16 }}>
              <RenderHtml
                contentWidth={width}
                source={{ html: news?.attributes?.description }}
                renderersProps={renderersProps}
                ignoredTags={['script']}
                ignoredStyles={['font-family']}
              />
            </View>
          </View>
        }
      </Animated.ScrollView>
      { news?.attributes?.screen_button_text && news?.attributes?.screen_open ? <View style={styles.fixedButton}>
        <TButton typeRadius={'rounded'} buttonStyle={{ width: responsiveWidth(90) }} title={news?.attributes?.screen_button_text} onPress={onPressButton} />
      </View> : null }
    </SafeAreaView>
  )
}
)

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    height: '100%',
    marginTop: -4
  },
  des: {
    backgroundColor: '#fff',
    color: '#333',
    fontSize: 12,
    padding: 8,
  },
  fixedButton: {
    backgroundColor: '#fff',
    borderTopColor: '#E9E9E9',
    borderTopWidth: 1,
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 15,
    position: 'absolute',
    width: '100%'
  },
  icArrowBack: {
    paddingRight: 20
  },
  newsPicture: {
    height: 230,
    width: '100%'
  },
  newsTitle: {
    backgroundColor: '#fff',
    color: '#333',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    marginTop: 8,
    paddingHorizontal: 16,
  },
  title: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 17,
    fontWeight: 'bold',
    marginLeft: -15
  },
  viewBTTop: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
})
