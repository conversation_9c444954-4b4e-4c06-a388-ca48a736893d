import {
  StyleSheet,
  FlatList,
  Text,
  TouchableOpacity,
  View
} from 'react-native'
import React, { useEffect, useState } from 'react'

import { useTranslation } from 'react-i18next'
import { observer } from 'mobx-react-lite'
import { useNavigation } from '@react-navigation/native'
import { color, spacing, typography } from '../../theme'
import { ButtonBack, EmptyData, PlaceHolder } from '../../components'
import { useStores } from '@app/models'
import { SCREENS } from '@app/navigation'

import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import LinearGradient from 'react-native-linear-gradient'
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import { DEFAULT_API_CONFIG } from '@app/services/api/api-config'
import FastImage from 'react-native-fast-image'
import { noImage } from '@app/assets/images'

export const BlogScreen = observer((props: any) => {
  const { t } : any = useTranslation()
  const { postsStore } = useStores()
  const [isFetched, setIsFetched] = useState(false)
  const [page, setPage] = useState(1)
  const [categoriesID, setCategoriesID] = useState(0)
  const [cateText, setCateText] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const [loadMore, setLoadMore] = useState(false) // mark scrollEnd to load more

  const { navigate, goBack } = useNavigation()

  useEffect(() => {
    loadData().then(r => {})
  }, [])

  useEffect(() => {
    loadDataFilter()
  }, [cateText])

  const loadDataFilter = async () => {
    setIsFetched(true)
    await postsStore.getPosts(cateText)
    setIsFetched(false)
  }

  /**
     * call Store
     */
  const loadData = async () => {
    // const isLoadMore = page > 1
    // if (!isLoadMore) {
    //   setIsFetched(true)
    // }
    // await postsStore.getCategoriesPosts()
    // if (categoriesID !== '') {
    //   await postsStore.getPostsByCategories(categoriesID, page, isLoadMore)
    // } else {
    //   await postsStore.getPosts()
    // }
    // // call event store here
    // setLoadMore(false)
    // setRefreshing(false)
    // setIsFetched(false)
    // await postsStore.getPosts()
    await postsStore.getPostCategories()
  }

  const refreshData = async () => {
    setIsFetched(true)
    if (cateText) {
      await postsStore.getPosts(cateText)
    } else {
      await postsStore.getPosts()
    }
    await postsStore.getPostCategories()
    setLoadMore(false)
    setRefreshing(false)
    setIsFetched(false)
  }
  /**
     * onRefresh
     */
  const onRefresh = () => {
    refreshData().then(r => {
    })
  }
  /**
     * onLoadMore Data
     */

  // const handleLoadMore = () => {
  //   // if (!loadMore) return
  //   // khi scroll dừng lại sẽ gọi vào hàm này
  //   __DEV__ && console.log('onMomentumScrollEnd')
  //   // call total page here
  //   const totalPage = postsStore.totalPage
  //   if (page < totalPage) {
  //     setPage(page + 1)
  //   }
  //   if (page === totalPage) {
  //     __DEV__ && console.log('No more data...')
  //     setLoadMore(false)
  //   }
  // }

  const builderPathImage = (item) => {
    __DEV__ && console.log(item)
    if (item?.attributes?.image?.data?.attributes.url.indexOf('http') != -1) {
      return `${item?.attributes?.image?.data?.attributes.url}`
    }
    return `${DEFAULT_API_CONFIG.url2}${item?.attributes?.image?.data?.attributes.url}`
  }

  /**
     * render Footer UI
     */
  const renderTinTuc = ({ item, index }) => {
    return <TouchableOpacity
      onPress={() => navigate(SCREENS.blogDetailScreen, { id: item.id })}
      style={styles.item}>
      <TouchableOpacity
        onPress={() => navigate(SCREENS.blogDetailScreen, { id: item.id })}
      >
        <FastImage
          style={styles.image}
          source={item?.attributes?.image?.data?.attributes.url ? { uri: builderPathImage(item) } : noImage} />
      </TouchableOpacity>
      <View style={{ flex: 1 }}>
        <TouchableOpacity
          onPress={() => navigate(SCREENS.blogDetailScreen, { id: item.id })}
        >
          <Text numberOfLines={3} style={{ color: '#333', fontSize: 15 }}>{item?.attributes.title}</Text>
        </TouchableOpacity>
        <TouchableOpacity>
          <Text numberOfLines={3} style={styles.textDes}>{item?.attributes.short_description}</Text>
        </TouchableOpacity>
      </View>
      {/* <TouchableOpacity style={styles.btnRight} */}
      {/*  onPress={() => navigate(SCREENS.blogDetailScreen, { id: item.id })} */}
      {/* > */}
      {/*  <Text style={styles.textViewDetail}> {t('Xem chi tiết')}</Text> */}
      {/* </TouchableOpacity> */}
    </TouchableOpacity>
  }

  // const renderFooter = () => {
  //   const Spinner = require('react-native-spinkit')
  //   return loadMore === true ? (
  //     <View
  //       style={{
  //         marginBottom: 20,
  //         alignItems: 'center',
  //       }}
  //     >
  //       <Spinner isVisible={true} size={40} type='ThreeBounce' color={color.primary}/>
  //     </View>
  //   ) : null
  // }

  const renderCategories = ({ item, index }) => {
    return <TouchableOpacity onPress={() => {
      setCategoriesID(item.id)
      if (item.id === 0) {
        setCateText('')
      } else {
        setCateText(item.attributes.name)
      }

      setPage(1)
      // fetchLastestPost()
    }} style={[styles.rdDanhmuc, categoriesID === item.id ? styles.cateSelected : { }]}>
      <Text style={[styles.rdDanhmucText, { fontWeight: categoriesID === item.id ? 'bold' : 'normal' }]}>{item.attributes.name}</Text>
    </TouchableOpacity>
  }

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <Header
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack} />}
        centerComponent={{ text: t('TIN_TUC'), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      <View style={styles.viewContainer}>
        {isFetched ? <View style={styles.placeHolder}>
          <PlaceHolder/>
        </View> : <View style={styles.postsList}>
          <FlatList
            horizontal={true}
            // ItemSeparatorComponent={renderSeparator}
            nestedScrollEnabled={true}
            data={postsStore.dataPostCategories}
            keyExtractor={(item, index) => index + 'category'}
            contentContainerStyle={{ marginLeft: 16 }}
            showsHorizontalScrollIndicator={false}
            onEndReachedThreshold={0.1}
            renderItem={renderCategories}/>
          <FlatList
            showsVerticalScrollIndicator={false}
            data={postsStore.dataPosts}
            horizontal={false}
            scrollEventThrottle={1}
            initialNumToRender={10}
            refreshing={refreshing}
            onRefresh={onRefresh}
            keyExtractor={(item, index) => index + 'news'}
            renderItem={renderTinTuc}
            extraData={postsStore.dataPosts}
            onScrollBeginDrag={e => {
              // onScroll(e)
              __DEV__ && console.log('onScrollBeginDrag')
              setLoadMore(true)
            }}
            contentContainerStyle={{ paddingBottom: useSafeAreaInsets().bottom + 60 }}
            // onScrollEndDrag={onScroll}
            // contentContainerStyle={{ paddingBottom: 120 }}
            // onMomentumScrollEnd={handleLoadMore}
            // ListFooterComponent={renderFooter}
          />
          {postsStore.dataPosts && postsStore.dataPosts.length ? null : <EmptyData title={t('Không tìm thấy')} message={t('Không có dữ liệu để hiển thị')}/>}
        </View>
        }
      </View>
    </SafeAreaView>
  )
}
)

const styles = StyleSheet.create({
  cateSelected: {
    borderBottomColor: color.primary,
    borderBottomWidth: 3
  },
  image: {
    height: 120,
    marginRight: 10,
    width: 120
  },
  item: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    flex: 1,
    marginBottom: 1,
    paddingHorizontal: 16,
    paddingVertical: 15
  },
  placeHolder: {
    flex: 1,
    marginTop: 20
  },
  postsList: {
    // flex: 1,
    // paddingBottom: 50
  },
  rdDanhmuc: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    marginRight: spacing.large,
    marginVertical: 8
  },
  rdDanhmucText: {
    fontFamily: typography.normal,
    fontSize: 15,
    paddingVertical: 8,
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  textDes: {
    color: '#979797',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 15,
    marginVertical: 5,
    // paddingRight: 40
  },

  textViewDetail: {
    color: '#fff',
    fontFamily: typography.normal,
    fontSize: 12,
    textAlign: 'center',
  },
  topbutton: {
    justifyContent: 'center',
    marginBottom: 8,
    marginTop: 8,
    width: '100%'
  },
  viewContainer: {
    // backgroundColor: '#f6f6f7',
    flex: 1,
    // height: responsiveHeight(100),
    // paddingBottom: 140,
  },
})
