import { SafeAreaView } from 'react-native-safe-area-context'
import {

  Text,
  View, TextInput,
} from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import styles from './styles'
import { observer } from 'mobx-react-lite'
import { useStores } from '@app/models'
import { useNavigation } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import { TButton, ButtonBack } from '@app/components'
import { Password } from '../register-screen/PasswordTextBox'
import { SCREENS } from '@app/navigation'
import { useAuth } from '@app/use-hooks/use-auth'
import { ModalContext } from '@app/context'
import validate from 'validate.js'

export const CreatePasswordNewAccount = observer((props: any) => {
  const { t } : any = useTranslation()
  const { accountStore, profileStore } = useStores()
  const { navigate, goBack } = useNavigation()
  const [password, setPassword] = useState('')
  const [rePassword, setRePassword] = useState('')
  const [fullName, setFullName] = useState('')
  const [isSubmitting, setSubmitting] = useState(false)
  const { signUp } = useAuth()
  const { showError, showSuccess } = useContext(ModalContext)
  const { data, registerType, typeConfirm } = props?.route?.params || {}

  const validateFields = () => validate.isEmpty(password) || validate.isEmpty(rePassword) || validate.isEmpty(fullName)

  // trường hợp đăng nhập, đăng ký qua facebook thì sẽ có name cần default ở ô input
  useEffect(() => {
    if (props?.route?.params?.data?.fullName) {
      setFullName(data.fullName)
    }
  }, [props?.route?.params])

  const hanldeCreatePassword = async () => {
    setSubmitting(true)
    if (!password) {
      showError(t('FAIL'), t('VUI_LONG_NHAP_MAT_KHAU'))
      setSubmitting(false)
    } else if (!rePassword) {
      showError(t('FAIL'), t('VUI_LONG_NHAP_LAI_MAT_KHAU'))
      setSubmitting(false)
    } else if (password === rePassword) {
      const rs = await accountStore.registerAccount({ ...data, phone: accountStore.phoneNumber, password, fullName })
      // const rs = await accountStore.registerAccount(accountStore.phoneNumber, password, fullName)
      setSubmitting(false)
      if (rs.kind === 'ok' && rs.data && rs.data.token) {
        await signUp()
        showSuccess(t('THANHCONG'), `${t('register_success')}`)
        await accountStore.loginAccount({ phone: accountStore.phoneNumber, password })
        await profileStore.getProfile()
        // call signUp context
        navigate(SCREENS.primaryStack) // redirect to home
        // showMsg('Đăng ký thành công. Bạn sẽ được chuyển tới trang chủ', () => {
        // })
        // setTimeout(() => {
        //   navigate(SCREENS.primaryStack)
        // }, 3000)
        setSubmitting(false)
      } else {
        showError(t('FAIL'), t('CANNOT_CREATE_PASSWORD'))
        setSubmitting(false)
      }
    } else {
      showError(t('FAIL'), t('MAT_KHAU_KHONG_KHOP'))
      setSubmitting(false)
    }
  }

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <ButtonBack onPress={goBack} style={styles.icArrowBack}/>
      <View style={styles.container}>
        <View style={styles.content}>
          <View>
            <Text numberOfLines={2} style={styles.textTitle}>{t('Create_Account')}</Text>
          </View>
          <View style={{ flexDirection: 'row', justifyContent: 'flex-start' }}>
            <Text style={styles.text}>{t('Enter_New_Password_And_Confirm')}</Text>
          </View>

          <View style={styles.mainTextInput}>
            <TextInput
              maxLength={30}
              autoCapitalize={'none'}
              style={styles.textInput}
              placeholder={t('FULL_NAME')}
              placeholderStyle={{ textAlign: 'center' }}
              value={fullName}
              onChangeText={e => setFullName(e)}
            />
            <Password
              label={t('NEWPASSWORD')}
              onChange={(e) => {
                setPassword(e)
              }}
            />
            <Password
              label={t('NHAP_LAI_MK_MOI')}
              onChange={(e) => {
                setRePassword(e)
              }}
            />
          </View>
          <View style={styles.buttonContainer}>
            <TButton typeRadius={'rounded'} disabled={validateFields() || isSubmitting} loading={isSubmitting} buttonStyle={styles.buttonLogin}
              title={t('XONG')} onPress={hanldeCreatePassword}/>
            {/* <TouchableOpacity */}
            {/*  onPress={() => hanldeCreatePassword()} */}
            {/*  style={styles.buttonLogin}> */}
            {/*  <Text style={styles.buttonLoginText}>{t('XONG')}</Text> */}
            {/* </TouchableOpacity> */}
          </View>
        </View>
      </View>
    </SafeAreaView>
  )
})
