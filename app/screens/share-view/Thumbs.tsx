import React from 'react'
import {
  FlatList, Image, View, StyleSheet, TouchableOpacity
} from 'react-native'
import { RectButton } from 'react-native-gesture-handler'

import { THUMBS_HEIGHT } from './constants'
import { allowPreview } from './utils'
import { themes } from '@app/constants/colors'
import Icon from 'react-native-vector-icons/Ionicons'

const THUMB_SIZE = 64

const styles = StyleSheet.create({
  dangerIcon: {
    bottom: 0,
    position: 'absolute',
    right: 16
  },
  item: {
    paddingTop: 8
  },
  list: {
    height: THUMBS_HEIGHT,
    paddingHorizontal: 8
  },
  removeButton: {
    alignItems: 'center',
    borderRadius: 14,
    borderWidth: 2,
    height: 28,
    justifyContent: 'center',
    position: 'absolute',
    right: 6,
    width: 28
  },
  removeView: {
    alignItems: 'center',
    borderRadius: 14,
    borderWidth: 2,
    height: 28,
    justifyContent: 'center',
    width: 28
  },
  thumb: {
    alignItems: 'center',
    borderRadius: 2,
    borderWidth: 1,
    height: THUMB_SIZE,
    justifyContent: 'center',
    marginRight: 16,
    overflow: 'hidden',
    width: THUMB_SIZE
  },
  videoThumbIcon: {
    bottom: 0,
    left: 0,
    position: 'absolute'
  }
})

// const ThumbButton = isIOS ? TouchableOpacity : TouchableNativeFeedback

const ThumbContent = React.memo(({ item, theme, isShareExtension }: any) => {
  const type = item?.mime

  if (type?.match(/image/)) {
    // Disallow preview of images too big in order to prevent memory issues on iOS share extension
    if (allowPreview(isShareExtension, item?.size)) {
      return (
        <Image
          source={{ uri: item.path }}
          style={[styles.thumb, { borderColor: themes[theme].borderColor }]}
        />
      )
    } else {
      return (
        <View style={[styles.thumb, { borderColor: themes[theme].borderColor }]}>
          <Icon
            name='image'
            size={30}
            color={themes[theme].tintColor}
          />
        </View>
      )
    }
  }

  if (type?.match(/video/)) {
    const { uri } = item
    return (
      <>
        <Image source={{ uri }} style={styles.thumb} />
        <Icon
          name='camera-filled'
          size={20}
          color={themes[theme].buttonText}
          style={styles.videoThumbIcon}
        />
      </>
    )
  }

  // Multiple files upload of files different than image/video is not implemented, so there's no thumb
  return null
})

const Thumb = ({
  item, theme, isShareExtension, onPress, onRemove
}) => (
  <TouchableOpacity style={styles.item} onPress={() => onPress(item)} activeOpacity={0.7}>
    <>
      <ThumbContent
        item={item}
        theme={theme}
        isShareExtension={isShareExtension}
      />
      <RectButton
        // hitSlop={BUTTON_HIT_SLOP}
        style={[styles.removeButton, { backgroundColor: themes[theme].bodyText, borderColor: themes[theme].auxiliaryBackground }]}
        activeOpacity={1}
        rippleColor={themes[theme].bannerBackground}
        onPress={() => onRemove(item)}
      >
        <View style={[styles.removeView, { borderColor: themes[theme].auxiliaryBackground }]}>
          <Icon
            name='close'
            color={themes[theme].backgroundColor}
            size={14}
          />
        </View>
      </RectButton>
      {!item?.canUpload ? (
        <Icon
          name='warning'
          size={20}
          color={themes[theme].dangerColor}
          style={styles.dangerIcon}
        />
      ) : null}
    </>
  </TouchableOpacity>
)

const Thumbs = React.memo(({ attachments, theme, isShareExtension, onPress, onRemove }: any) => {
  if (attachments?.length > 0) {
    return (
      <FlatList
        showsVerticalScrollIndicator={false}
        horizontal
        data={attachments}
        keyExtractor={item => item.path}
        renderItem={({ item }) => (
          <Thumb
            item={item}
            theme={theme}
            isShareExtension={isShareExtension}
            onPress={() => onPress(item)}
            onRemove={() => onRemove(item)}
          />
        )}
        style={[styles.list, { backgroundColor: themes[theme].messageboxBackground }]}
      />
    )
  }
  return null
})

export default Thumbs
