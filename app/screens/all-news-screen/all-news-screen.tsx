import React from 'react'
import { observer } from 'mobx-react-lite'
import { View, Text, FlatList, TouchableOpacity } from 'react-native'
import { useNavigation, useRoute } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import { ButtonBack, StoreListItem } from '@app/components'
import styles from '@app/screens/top-branch-screen/styles'
import { SCREENS } from '@app/navigation'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import LinearGradient from 'react-native-linear-gradient'
import { SafeAreaView } from 'react-native-safe-area-context'
import FastImage from 'react-native-fast-image'
export const AllNewsScreen = observer(function AllNewsScreen() {
  const { navigate, goBack } = useNavigation()
  const { t } : any = useTranslation()
  const route: any = useRoute()
  const { data, title } = route?.params

  const renderList = ({ item }) => (
    <StoreListItem type={'news'} item={item}
      onPress={() => navigate(SCREENS.newsDetailScreen, { id: item._id })}/>
  )

  const RenderNews = ({ item }) => {
    return (
      <TouchableOpacity
        onPress={() => navigate(SCREENS.blogDetailScreen, { id: item._id })}
        style={styles.item}>
        <FastImage
          style={styles.image}
          source={{ uri: item.picture }} />
        <View style={{ flex: 1 }}>
          <Text numberOfLines={3} style={{ color: '#333', fontSize: 15 }}>{item?.name}</Text>
          <Text numberOfLines={3} style={styles.textDes}>{item?.short_description}</Text>
        </View>
        {/* <TouchableOpacity style={styles.btnRight} */}
        {/*  onPress={() => navigate(SCREENS.newsDetailScreen, { id: item._id })} */}
        {/* > */}
        {/*  <Text style={styles.textViewDetail}> {t('Xem chi tiết')}</Text> */}
        {/* </TouchableOpacity> */}
      </TouchableOpacity>
    )
  }

  return (
    <SafeAreaView style={{ flex: 1, marginTop: -4 }}>
      <View style={styles.containerItem}>
        <Header
          // statusBarProps={{ barStyle: 'light-content' }}
          // barStyle="light-content" // or directly
          leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
          centerComponent={{ text: t(`${title}`), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
          // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
          //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
          // </TouchableOpacity> : null}
          containerStyle={common.headerContainer}
          statusBarProps={{ barStyle: 'light-content' }}
          ViewComponent={LinearGradient}
          linearGradientProps={linearGradientProps}
        />
        <View style={{ marginTop: 1, flex: 1 }}>
          <FlatList
            data={data}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            renderItem={RenderNews}
            extraData={data}
            keyExtractor={item => item._id}/>
        </View>
      </View>
    </SafeAreaView>
  )
})
