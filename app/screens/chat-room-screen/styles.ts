import { StyleSheet, Dimensions } from 'react-native'
import { responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import { color, spacing, typography } from '@app/theme'
import { ifIphoneX } from 'react-native-iphone-x-helper'

const tab1ItemSize = (Dimensions.get('window').width - 30) / 5
const { height } = Dimensions.get('window')
const styles = StyleSheet.create({
  admin: {
    marginTop: 10
  },
  boxviewImage: {
    width: 50
  },
  btnRadioGroup: {
    alignItems: 'flex-start',
    borderColor: color.primary,
    color: color.primary,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginLeft: -10,
    paddingTop: 15
  },
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 14,
    marginVertical: 11,
    paddingBottom: 15
  },
  containerComment: {
    flex: 1,
    marginLeft: 8,
    // flexDirection: "column"
  },
  crossCenter: {
    alignItems: 'center',
  },
  customStar: {
    marginRight: 4,
    marginTop: 3
  },
  dot: {
    backgroundColor: '#6dd400',
    borderRadius: 8,
    bottom: 10,
    elevation: 2,
    height: 8,
    position: 'absolute',
    right: 6,
    shadowColor: 'rgba(0, 0, 0, 0.15)',
    shadowOffset: {
      width: 0,
      height: 0
    },
    shadowOpacity: 1,
    shadowRadius: 5,
    width: 8
  },
  icArrowBack: {
    marginLeft: 3,
    paddingBottom: 20,
    paddingRight: 20
  },
  imageUser: {
    borderColor: '#f2f2f2',
    borderRadius: 60,
    borderWidth: 1,
    height: 60,
    width: 60
  },
  mainSpaceBetween: {
    justifyContent: 'space-between',
  },
  mainStart: {
    justifyContent: 'flex-start',
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
  },
  row: {
    flexDirection: 'row',
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
    // height: responsiveHeight(100)
  },
  textContent: {
    color: '#8f9bb3',
    fontFamily: typography.normal,
    fontSize: 12,
    letterSpacing: 0
  },
  textTime: {
    color: '#c5cee0',
    fontFamily: typography.normal,
    fontSize: 12
  },
  textTitle: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
  },
  textTitleDetailNoti:
    {
      // textAlign: 'center',
      // alignItems: 'center',
      flex: 1,
      fontFamily: typography.normal,
      fontSize: 14,
      letterSpacing: 0,
      marginTop: 10
    },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',

  },
  textTitleTotal: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: spacing.small,
  },
  viewDetailNoti: {
    marginBottom: 30,
    marginLeft: 15,
    marginRight: 15,
    marginTop: 10,
    ...ifIphoneX({
      paddingBottom: 120,
    }, {
      paddingBottom: 90,
    }),
  },
  viewFlatlist: {
    flex: 1,
    height: responsiveHeight(100),
    // marginTop: 15,
    // backgroundColor: 'red',
    // ...ifIphoneX({
    //   paddingBottom: 89,
    // }, {
    //   paddingBottom: 60,
    // }),
    width: responsiveWidth(100)
  },
  viewIcon: {
    alignItems: 'flex-end',
    flex: 1,
    justifyContent: 'flex-end',
  },
  viewImage: {
    alignItems: 'center',
    flex: 1,
    height: 44,
    width: 44,
  },
  viewService: {
    flexDirection: 'column',
    marginTop: 17
  },
  viewStar: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between'
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
})
export default styles
