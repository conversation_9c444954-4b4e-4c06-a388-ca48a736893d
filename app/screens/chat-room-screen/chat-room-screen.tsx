import React, { useState, useEffect } from 'react'
import {
  View,
  Text, FlatList, TouchableOpacity
} from 'react-native'
import styles from './styles'
import { useNavigation } from '@react-navigation/native'
import { useStores } from '@app/models'
import moment from 'moment'
import { PlaceHolder, EmptyData, ButtonBack } from '@app/components'
import { observer } from 'mobx-react-lite'
import { SafeAreaView } from 'react-native-safe-area-context'

import { color, typography } from '@app/theme'
import { SCREENS } from '@app/navigation'
import { useTranslation } from 'react-i18next'

import _ from 'lodash'
import { common, linearGradientProps } from '@app/theme/styles/common'
import { Badge, Header } from 'react-native-elements'
import firestore from '@react-native-firebase/firestore'
import FastImage from 'react-native-fast-image'
import LinearGradient from 'react-native-linear-gradient'
import { Api } from '@app/services/api'
import { noAvatar } from '@app/assets/images'

export const ChatRoomScreen: React.FC = observer((props) => {
  const { t } : any = useTranslation()
  const { profileStore } = useStores()
  const { navigate, goBack } = useNavigation()
  const [refreshing, setRefreshing] = useState(false)
  const [loadMore, setLoadMore] = useState(false)
  const [isFetched, setIsFetched] = useState(true) // event view placeholder
  const { notificationStore } = useStores()
  const [listRoom, setListRoom] = useState([])
  const chatRoomsRef = firestore().collection('chatRooms')
  const api = new Api()

  /**
   * Fetch threads from Firestore
   */
  useEffect(() => {
    const unsubscribe = profileStore?._id ? chatRoomsRef
      // .where('members', 'array-contains-any', [44])
      .where('members', 'array-contains', profileStore._id)
      // .orderBy('latestMessage.createdAt', 'desc')
      .onSnapshot(querySnapshot => {
        const threads: any = querySnapshot.docs.map(documentSnapshot => {
          if (documentSnapshot.data() && documentSnapshot.data().members && documentSnapshot.data().members.length && documentSnapshot.data().members.includes(profileStore._id)) {
            return {
              _id: documentSnapshot.id,
              // give defaults
              name: '',
              latestMessage: {
                text: ''
              },
              ...documentSnapshot.data()
            }
          }
          return null
        })

        const array = _.orderBy(threads.filter(x => x !== null && x?.latestMessage?.text?.length > 0), item => item.latestMessage.createdAt, ['desc'])
        setListRoom(array)
        setIsFetched(false)

        // if (loading) {
        //   setLoading(false);
        // }
      }) : () => {}

    /**
     * unsubscribe listener
     */
    return () => unsubscribe()
  }, [])

  const onChat = (item) => {
    __DEV__ && console.log('onChat')

    if (item.fromId != profileStore._id) {
      const roomName = item.fromName
      navigate(SCREENS.chatDetailScreen, { toUserId: item.toUserId, roomName: roomName, rId: item._id })
    }

    if (item.fromId == profileStore._id) {
      const roomName = item.name
      navigate(SCREENS.chatDetailScreen, { toUserId: item.toUserId, roomName: roomName, rId: item._id })
    }
  }

  const renderFooter = () => {
    const Spinner = require('react-native-spinkit')
    return loadMore === true
      ? (
        <View
          style={{
            marginTop: 10,
            alignItems: 'center',
          }}
        >
          <Spinner isVisible={true} size={40} type='ThreeBounce' color={color.primary}/>
        </View>
      )
      : null
  }

  const renderItem = ({ item, index }) => {
    __DEV__ && console.warn(item)

    const displayUserId = item.fromId == profileStore._id ? item.toUserId : item.fromId
    const displayName = item['displayName_' + displayUserId]
    const avatarDisplay = item['avatar_' + displayUserId]
    const count = item['unReadCount_' + profileStore._id]
    return (
      <>
        <View style={{ paddingHorizontal: 15, borderBottomColor: '#f2f2f2', borderBottomWidth: 1 }}>
          <View style={styles.container}>
            <TouchableOpacity style={styles.boxviewImage} onPress={() => { onChat(item) } }>
              <View style={styles.viewImage}>
                <FastImage style={styles.imageUser} resizeMode='cover' source={ avatarDisplay ? { uri: `https://storage.googleapis.com/car-city-20204.appspot.com/${avatarDisplay}` } : noAvatar}/>
              </View>
              {item.watched === 0 ? <View style={styles.dot}/> : null}
            </TouchableOpacity>
            <View style={styles.containerComment}>
              <TouchableOpacity onPress={() => {
                // if (item.watched === 0) {
                //   daXemThongBao(item, index)
                // }
                onChat(item)
              }}>
                <View style={ [styles.row, styles.mainSpaceBetween] }>
                  <Text style={styles.textTitle}>{ displayName }</Text>
                  {/* <Text style={styles.textTitle}>{ item.fromId != profileStore._id ? item.fromName : profileStore.fullName }</Text> */}
                  <Text style={[styles.textTime, { marginRight: count > 0 ? 30 : 0 }]}>{moment(item.latestMessage.createdAt).locale('vi').fromNow()}</Text>
                </View>
                <View>
                  <Text style={{
                    color: '#c5cee0',
                    fontFamily: typography.normal,
                    fontSize: 12,
                    marginLeft: 3
                  }}>{item.latestMessage.text}</Text>
                </View>
              </TouchableOpacity>
              {count && count > 0 ? <Badge containerStyle={{ position: 'absolute', right: 0, top: 0 }} value={count} status="error" /> : null}
            </View>
          </View>
        </View>
      </>
    )
  }

  return (<>
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'left']}>
      <Header
        statusBarProps={{ barStyle: 'light-content' }}
        backgroundColor={'transparent'}
        barStyle="light-content" // or directly
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
        centerComponent={{ text: t('CHAT_ROOM'), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
        containerStyle={common.headerContainer}
      />
      <View style={{ flex: 1 }}>
        {/* <ButtonBack onPress={goBack} style={styles.icArrowBack}/> */}
        {/* <Text style={styles.textTitleTotal}>Tất cả thông báo</Text> */}
        { !profileStore.isSignedIn() ? <EmptyData title={'Bạn chưa là thành viên'} message={'Vui lòng đăng nhập tại menu Cá nhân'}/> : null }
        {isFetched && profileStore.isSignedIn() ? <PlaceHolder/> : <View style={styles.viewFlatlist}>
          {!listRoom || !listRoom.length ? <EmptyData title={'Không có tin nhắn nào'} message={'Tin nhắn sẽ được hiển thị ở đây nếu có'}/>
            : <>
              <FlatList
                data={listRoom}
                initialNumToRender={10}
                refreshing={refreshing}
                // onRefresh={onRefresh}
                keyExtractor={item => item._id}
                renderItem={renderItem}
                extraData={notificationStore.notifications}
                onScrollBeginDrag={e => {
                  __DEV__ && console.log('onScrollBeginDrag')
                  setLoadMore(true)
                  setTimeout(() => {
                    setLoadMore(false)
                  }, 1000)
                }}
                // onMomentumScrollEnd={handleLoadMore}
                ListFooterComponent={renderFooter}
              />
            </>}
        </View>
        }
      </View>
    </SafeAreaView>
  </>)
})
