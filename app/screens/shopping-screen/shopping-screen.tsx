import React, { useEffect, useState } from 'react'
import { observer } from 'mobx-react-lite'
import styles from './styles'

import {
  TouchableOpacity, View, Dimensions, Text, Platform, Image, Linking, RefreshControl
} from 'react-native'
import { icShoppingCart } from '@app/assets/images'
import { useTranslation } from 'react-i18next'
import { useStores } from '@app/models'
import { SCREENS } from '@app/navigation'
import { useNavigation } from '@react-navigation/native'
import { CategoriesRender } from './render-categories/categories-render'
import { ProductRender } from './product-render/product-render'
import { LazyImage, useLoading } from '@app/components'
import { Badge, Header } from 'react-native-elements'
import { useAuth } from '@app/use-hooks/use-auth'
import Carousel from 'react-native-snap-carousel'
import { TabBar } from 'react-native-tab-view'
import Icon from 'react-native-vector-icons/Ionicons'
import {
  TabCategoryProductScreen
} from '@app/screens'
import BannerShopping from '@app/screens/home-screen/banner-shopping'
import { LogEvent } from '@app/services/loggingServices'
import { Api } from '@app/services/api'
import { color } from '@app/theme'
import validate from 'validate.js'
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import { PickerSelect } from '@app/components/picker-select/picker-select'
import { loadString, remove, saveString } from '@app/utils/storage'
import { useForceUpdate } from '@app/use-hooks/useForceUpdate'
import ScrollingButtonMenu from 'react-native-scroll-menu'
import Animated, {
  useSharedValue,
  useAnimatedScrollHandler,
  useAnimatedStyle, interpolateColor,
} from 'react-native-reanimated'

const initialLayout = { width: Dimensions.get('window').width }
const HEADER_EXPANDED_HEIGHT = 0
const G_WIN_WIDTH = Dimensions.get('window').width
const api = new Api()

export const ShoppingScreen = observer(function ShoppingScreen() {
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()
  const { productStore, profileStore, searchStore, notificationStore, homeStore, renderFlatListStore } = useStores()
  const [isLoadData, setIsLoadData] = useState(true)
  const { shoppingCount } = useAuth()
  const [index, setIndex] = React.useState(0)
  const { show, hide } = useLoading()
  const [province, setProvince] = useState(null)
  const [provinces, setProvinces] = useState([])
  const [isPickerSelectVisible, setIsPickerSelectVisible] = useState(false)
  const [refreshing, setRefreshing] = useState(false)
  const forceUpdate = useForceUpdate()
  // const [page, setPage] = useState(1)
  const [isFetched, setIsFetched] = useState(true) // event view placeholder

  const [routes] = React.useState([
    { id: 0, name: t('Ô tô') },
    { id: 1, name: t('Xe máy') },
    { id: 2, name: t('Phương tiện khác') },
  ])

  // useEffect(() => {
  //   loadMoreData()
  // }, [page])

  useEffect(() => {
    setIsFetched(true)
    loadData().then(r => {
      setIsFetched(false)
      forceUpdate()
    })
  }, [])

  const onRefresh = async () => {
    setRefreshing(true)
    await loadData()
    setTimeout(() => {
      setRefreshing(false)
    }, 100)
    // show()
    // setTimeout(() => {
    //   hide()
    // }, 300)
  }

  // const onRefresh = () => {
  //   if (refreshing) {
  //     loadData()
  //   }
  // }

  useEffect(() => {
    let isLoad = true
    if (isLoad) {
      if (province) {
        onChangeProvince(province).then(r => {
          isLoad = false
        })
      }
    }
    return () => { isLoad = false }
  }, [province])

  const onChangeProvince = async (value) => {
    try {
      LogEvent('user_select_province', value)
      const province = await loadString('provinceSelected')
      if (province !== value) {
        renderFlatListStore.clearData()
        await saveString('provinceSelected', value)
        homeStore.setProvince(value)
        await getApiDataHome()
      } else {
        // get all province
        loadData().then(r => { })
      }
    } catch (error) {
      // Error saving data
    }
  }

  const navigateDetails = async (item) => {
    const send = {
      field: 'productIds',
      fieldId: item._id
    }
    const api = new Api()
    await api.sendProductViewed(send)
    navigate(SCREENS.productDetails, { id: item._id })
  }

  const navigateCategory = (item) => {
    navigate(SCREENS.categoryScreen, { categoryId: item._id, title: item.name })
  }

  const goLoginScreenRequired = () => {
    navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  const navigateCart = () => {
    if (profileStore.isSignedIn()) {
      navigate(SCREENS.cartScreen)
    } else {
      goLoginScreenRequired()
    }
  }

  const loadData = async () => {
    setIsLoadData(true)
    await getApiData()
    setProvinces(homeStore.provinces)
    setIsLoadData(false)
    forceUpdate()
  }

  // const loadMoreData = async () => {
  //   const isLoadMore = page > 1
  //   if (isLoadMore) {
  //     await productStore.getProductByCategory(0, page, isLoadMore)
  //     await productStore.getProductByCategory(1, page, isLoadMore)
  //     await productStore.getProductByCategory(2, page, isLoadMore)
  //   }
  // }

  const getApiData = async () => {
    return Promise.all([
      homeStore.getAppConfig(),
      productStore.getBannerShoppingTop(),
      productStore.getBannerShopping(),
      productStore.getViewedProducts(),
      await productStore.getProducts(),
      await homeStore.getProvinces()
    ])
  }

  const getApiDataHome = async () => {
    await remove('districtSelected')
    return Promise.all([
      homeStore.getBanners(),
      // homeStore.getPromotion(), // tạm ẩn vì lỗi api
      homeStore.getPromotions(),
      // homeStore.getHomeCategories(),
      renderFlatListStore.getService(1, 1, ''),
      renderFlatListStore.getService(2, 1, ''),
      renderFlatListStore.getService(3, 1, ''),
      homeStore.getNewsForHome(),
      homeStore.getTopBranch(),
      // homeStore.getBannerHome2(),
    ])
  }

  const goBackHome = () => {
    navigate(SCREENS.homeStack)
  }

  const goChatRoom = () => {
    if (profileStore.isSignedIn()) {
      navigate(SCREENS.chatRoomScreen)
    } else {
      goLoginScreenRequired()
      LogEvent('user_click_btn_register', { screen: 'profile_screen' })
    }
  }

  // const handleLoadMore = () => {
  //   // if (!loadMore) return
  //   // khi scroll dừng lại sẽ gọi vào hàm này
  //   __DEV__ && console.log('onMomentumScrollEnd')
  //   const totalPage = productStore.totalPage
  //   if (page < totalPage) {
  //     setPage(page + 1)
  //   }
  //   if (page === totalPage) {
  //     __DEV__ && console.log('No more data...')
  //     setLoadMore(false)
  //   }
  // }

  const onPressImage = (item, navigate) => {
    if (item?.screen) {
      const params = item?.params || ''
      const trueParams = params !== '#'
      const isValidURL = validate({ website: params }, { website: { url: true } })
      switch (item.screen) {
        case 'PROMOTION_DETAIL_SCREEN':
          if (trueParams) {
            navigate(item.screen, { id: params })
          }
          break
        case 'SHOPPING_STACK':
          navigate(item.screen)
          break
        case 'PRODUCT_DETAILS':
          if (trueParams) {
            navigate(item.screen, { id: params })
          }
          break
        case 'BLOG_DETAIL_SCREEN':
          if (trueParams) {
            navigate(item.screen, { id: params })
          }
          break
        case 'SERVICE_DETAIL':
          if (trueParams) {
            navigate(item.screen, { id: params })
          }
          break
        case 'POPUP_SERVICE_OF_BRAND':
          if (params && item?.screen_param_spaId) {
            navigate(SCREENS.serviceDetail, { id: params, spaId: item?.screen_param_spaId })
          }
          break
        case 'BLOG_SCREEN':
          if (trueParams) {
            navigate(item.screen)
          }
          break
        case 'BHTNDSCAR':
          if (trueParams) {
            navigate(SCREENS.baoHiemTNDSB1, { prodId: params })
          }
          break
        case 'BHVCCAR':
          if (trueParams) {
            navigate(SCREENS.baoHiemVCXB1, { prodId: params })
          }
          break
        case 'BHTNDSBIKE':
          if (trueParams) {
            navigate(SCREENS.baoHiemTNDSXeMayB1, { prodId: params })
          }
          break
        case 'SCREEN_NAME':
          // eslint-disable-next-line no-case-declarations
          const data = JSON.parse(params || '{}')
          console.log('********************************', data)
          navigate(data?.screen || '', { ...data?.params })
          break
        default:
          if (trueParams && isValidURL === undefined) {
            Linking.openURL(params)
          }
          break
      }
    }
  }

  const renderButtonCart = () => {
    return (
      <TouchableOpacity
        onPress={navigateCart}
        style={{ flexDirection: 'row' }}
      >
        {/* <Icon size={25} color={'#333'} name={'cart-outline'} /> */}
        <Image source={icShoppingCart} style={{ width: 26, height: 26 }}></Image>
        {/* {shoppingCount && shoppingCount > 0 ? <Badge */}
        {/*  value={shoppingCount} */}
        {/*  status="error" */}
        {/*  containerStyle={{ position: 'absolute', top: -8, right: -8 }} */}
        {/* /> : null} */}
        {/* {shoppingCount && shoppingCount > 0 ? <View style={[styles.badge, { top: -9, right: -6 }]}> */}
        {/*  <Text style={{ color: '#fff', textAlign: 'center', fontSize: 12 }}>{shoppingCount}</Text> */}
        {/* </View> : null} */}
        {shoppingCount && shoppingCount > 0 ? <Badge value={shoppingCount > 99 ? 99 + '+' : shoppingCount} status="error" containerStyle={{ marginLeft: -12, marginTop: -7 }} /> : null}
      </TouchableOpacity>
    )
  }

  const renderCarousel = () => {
    return productStore.bannerShopping.length
      ? (<View style={{ height: 140, backgroundColor: '#fff', marginBottom: 8 }}>
        <Carousel
          layout={'default'}
          data={productStore.bannerShopping}
          // onCurrentImagePressed={index => onPressImage(index, homeStore.banners, navigate) }
          renderItem={renderCarouselItem}
          sliderWidth={G_WIN_WIDTH}
          itemWidth={G_WIN_WIDTH}
          inactiveSlideScale={1}
          inactiveSlideOpacity={1}
          loop={true}
          autoplay={true}
          autoplayDelay={500}
          autoplayInterval={4000}
          removeClippedSubviews={false}
          // onSnapToItem={(index) => setIndex(index)}
        />
        {/* {renderPagination()} */}
      </View>) : null
  }

  // const renderButtonSearch = () => {
  //   return (
  //
  //     <TouchableOpacity style={styles.viewTouchRightChatIcon}
  //       onPress={goChatRoom}
  //     >
  //       <Icon
  //         size={20}
  //         color={'#fff'}
  //         name={'chatbubble-ellipses-outline'}
  //       />
  //     </TouchableOpacity>
  //   )
  // }

  const renderCarouselItem = ({ item }: any) => {
    return (
      <TouchableOpacity style={styles.slide}
        onPress={() => onPressImage(item, navigate) }
      >
        <LazyImage
          style={styles.image}
          resizeMode={'cover'}
          source={{ uri: item.picture }}
        />
      </TouchableOpacity>
    )
  }

  // const renderTabBar = (props) => (
  //   <TabBar
  //     {...props}
  //     indicatorStyle={{ backgroundColor: color.primary }}
  //     style={styles.tabBar}
  //     renderLabel={({ route, focused, color }) => (
  //       <Text style={[styles.tabBarText, { color: focused ? '#2e2e2e' : '#848484' }]}>
  //         {route.title}
  //       </Text>
  //     )}
  //   />
  // )

  const renderTabBar = (props: any) => {
    return <TabBar
      {...props}
      inactiveColor={'#333'}
      activeColor={'#333'}
      indicatorStyle={{ backgroundColor: color.primary }}
      tabStyle={{ minHeight: 30 }}
      labelStyle={{
        textTransform: 'capitalize'
      }}
      style={{
        backgroundColor: '#fff',
        paddingTop: 0
      }}
    />
  }

  const renderScene = () => {
    __DEV__ && console.log('render Tab by Index')
    switch (index) {
      case 0:
        // return <TabCategoryProductScreen loadMore={loadMore} setLoadMore={setLoadMore} product={productStore.productDog} handleLoadMore={handleLoadMore()}/>
        return <TabCategoryProductScreen index={0} type={0}/>
      case 1:
        return <TabCategoryProductScreen index={1} type={1}/>
      case 2:
        return <TabCategoryProductScreen index={2} type={2}/>
      default:
        return null
    }
  }

  const backgroundColor = useSharedValue('#00000000')

  const scrollHandler = useAnimatedScrollHandler((e) => {
    // Tính toán màu nền dựa trên vị trí cuộn`
    backgroundColor.value = interpolateColor(
      e.contentOffset.y,
      [0, 100], // Đoạn cuộn từ 0 đến 100px
      ['#00000000', '#e10814'] // Từ màu trong suốt sang đỏ
    )
  })

  const headerStyle = useAnimatedStyle(() => {
    return {
      opacity: 1,
      backgroundColor: backgroundColor.value,
    }
  })

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'left']}>
      <Animated.View style={[{ position: 'absolute', top: 0, width: '100%', zIndex: 9999 }, headerStyle]} >
        <Header
          leftComponent={<View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 5 }}>
            <TouchableOpacity
              onPress={() => setIsPickerSelectVisible(true)}
              style={styles.viewAddress}>
              <Icon name={'location-outline'} size={16} color='#fff'/>
              {/* <Image style={styles.icMap} source={IcMap}/> */}
              <Text numberOfLines={1} style={[styles.address, { color: '#fff' }]}>{homeStore.province || t('CHOOSE_LOCATION')}</Text>
              <Icon size={14} style={{ marginLeft: 10, marginRight: 10, color: '#fff' }} name="chevron-down-outline"/>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => {
              navigate(SCREENS.search)
              searchStore.setTypeSearch(0)
              navigate(SCREENS.search, { filterType: 0, screenType: 'shop' })
            }}>
              <View style={styles.inputStyle}>
                {/* <Image style={styles.icSearch} source={icSearch}/> */}
                <Icon size={14} style={{ color: '#fff', marginRight: 4, marginLeft: 8 }} name="search"/>
                <Text style={styles.input}>{t('Tìm kiếm...')}</Text>
              </View>
            </TouchableOpacity>
          </View>}
          containerStyle={[{
            backgroundColor: 'transparent',
            borderBottomWidth: 0,
          }]}
          rightComponent={<View style={styles.viewIcRight}>
            <TouchableOpacity
              style={{ flexDirection: 'row' }}
              onPress={() => navigate(SCREENS.notificationScreen)}>
              <Icon size={22} color='#fff' name={'notifications-outline'} />
              {notificationStore?.notSeen ? <Badge value={notificationStore?.notSeen > 99 ? 99 + '+' : notificationStore?.notSeen} status="error" containerStyle={{ marginLeft: -12, marginTop: -8 }} /> : null}
            </TouchableOpacity>
            <TouchableOpacity
              onPress={navigateCart}
              style={{ marginLeft: 10, flexDirection: 'row' }}
            >
              <Icon size={25} color='#fff' name={'cart-outline'}/>
              {shoppingCount && shoppingCount > 0 ? <Badge value={shoppingCount > 99 ? 99 + '+' : shoppingCount} status="error" containerStyle={{ marginLeft: -12, marginTop: -7 }} /> : null}
            </TouchableOpacity>
            {/* {renderBtnChat()} */}
          </View>}
          // backgroundImage={{ uri: homeStore.appConfig?.bg_home_header?.data?.attributes.url }}
          statusBarProps={{ barStyle: 'light-content', backgroundColor: 'transparent' }}
        />
      </Animated.View>
      <Animated.ScrollView style={[styles.rdView, { marginBottom: useSafeAreaInsets().bottom }]}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
          />
        }
      >
        {Platform.OS === 'ios' ? <View /> : null}
        {/* {Header()} */}
        <View style={styles.rdView}>
          <View style={styles.renderTopSection1}>
            {/* {renderHeader()} */}
            <View style={styles.rdView}>
              <View style={{ backgroundColor: 'transparent', marginTop: 0 }}>
                <BannerShopping images={productStore.images} banners={productStore.bannerShoppingTop}/>
              </View>
              <View style={{ backgroundColor: '#fff' }}>
                <CategoriesRender data={[...productStore.categoriesCho, ...productStore.categoriesMeo]} onPress={(item) => { navigateCategory(item) }}/>
              </View>
              <ProductRender data={productStore?.productSells} onPress={(item) => { navigateDetails(item) }} title={t('SELLINGPRODUCT')}/>
              <ProductRender data={productStore?.viewed} onPress={(item) => { navigateDetails(item) }} title={t('Xem gần nhất')}/>
              {renderCarousel()}
            </View>
          </View>
          <ScrollingButtonMenu
            items={routes}
            buttonStyle={{ backgroundColor: 'white', borderRadius: 3, borderWidth: 1 }}
            activeButtonStyle={{ borderBottomColor: color.primary, borderBottomWidth: 2 }}
            onPress={(e) => {
              __DEV__ && console.log('menu selected ', e)
              setIndex(e.id)
            }}
            upperCase={false}
            activeBackgroundColor={'#fff'}
            activeColor={'#333'}
            containerStyle={{ backgroundColor: '#fff', margin: 0, padding: 0 }}
            scrollStyle={{ marginLeft: -5 }}
            selected={index}
          />
          <View style={{ backgroundColor: '#fff', flex: 1, paddingTop: 10, paddingBottom: 15 }}>
            {renderScene()}
          </View>
          {/* <TabView */}
          {/*  lazy={true} */}
          {/*  renderTabBar={renderTabBar} */}
          {/*  // makeHeaderHeight={calculateHeaderH} */}
          {/*  // makeHeaderHeight={calculateHeaderHeight} */}
          {/*  // renderScrollHeader={renderContainer} */}
          {/*  navigationState={{ index, routes }} */}
          {/*  renderScene={renderScene} */}
          {/*  onIndexChange={setIndex} */}
          {/*  // style={{ flex: 1 }} */}
          {/*  initialLayout={{ width: responsiveWidth(100) }} */}
          {/* /> */}
        </View>
      </Animated.ScrollView>

      <PickerSelect data={provinces} title={t('CHOOSE_PROVINCE')} isVisible={isPickerSelectVisible} defaultValue={homeStore.province} onSelect={(e) => {
        setIsPickerSelectVisible(!isPickerSelectVisible)
        setTimeout(() => {
          setProvince(e.label)
          homeStore.setProvince(e.label)
        }, 300)
      }}

      callBackVisible={() => {
        setIsPickerSelectVisible(!isPickerSelectVisible)
      }}
      goBack={() => {
        setIsPickerSelectVisible(!isPickerSelectVisible)
      }}
      />
    </SafeAreaView>
  )
})
