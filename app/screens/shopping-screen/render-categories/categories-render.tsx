import { observer } from 'mobx-react-lite'
import { FlatList, Text, TouchableOpacity, View, ScrollView } from 'react-native'
import styles from '../styles'
import React from 'react'
import { spacing } from '@app/theme'
import { LazyImage } from '@app/components'
import { useTranslation } from 'react-i18next'

export const CategoriesRender = observer(function CategoriesRender(props:any) {
  const { t } : any = useTranslation()

  const renderItemDanhMuc1 = ({ item, index }) => {
    return <View>
      {item?.status === 1 ? <TouchableOpacity
        onPress={ () => { props.onPress(item) }}
        style={styles.ridmTop}>
        <LazyImage style={styles.imageCat} source={ { uri: item.picture } } resizeMode="cover"/>
        <Text numberOfLines={2} style={styles.ridmTopText}>{item.name}</Text>
      </TouchableOpacity> : null}
    </View>
  }
  return (
    <View>
      {props?.data?.length > 0 ? <View>
        <Text style={styles.titleCat}>{t('Danh_muc')}</Text>
        <ScrollView style={styles.rdView1}
          horizontal
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
        >
          <FlatList
            style={{ marginLeft: spacing.small }}
            scrollEnabled={false}
            contentContainerStyle={{ backgroundColor: '#fff' }}
            showsHorizontalScrollIndicator={false}
            // horizontal={true}
            data={props.data}
            numColumns={Math.ceil(props?.data?.length / 2)}
            keyExtractor={(e, i) => i + 'l1'}
            renderItem={renderItemDanhMuc1} />
        </ScrollView>
      </View> : null}
    </View>
  )
})
