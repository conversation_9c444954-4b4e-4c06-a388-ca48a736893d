import { StyleSheet } from 'react-native'

const styles = StyleSheet.create({
  ads: {
    alignSelf: 'center',
    height: 200,
    resizeMode: 'stretch',
    width: '100%',
  },
  rdView: {
    backgroundColor: '#f6f6f7',
    flex: 1
  },
  rdView1: {
    alignItems: 'center',
    backgroundColor: '#fff',
    borderBottomColor: '#f6f6f7',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    paddingLeft: 15
  },
  rdView1Image: {
    height: 30,
    resizeMode: 'contain',
    width: 30
  },

  rdView2: {
    marginBottom: 20,
    paddingHorizontal: 15
  },
  rdView2Text: {
    color: '#CB1016',
    fontSize: 15,
    fontWeight: 'bold',
    lineHeight: 22,
    marginBottom: 10,
    marginTop: 20,
    textTransform: 'uppercase'
  },
  ridmTop: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: 18,
    paddingLeft: 10,
    paddingRight: 10,
    paddingTop: 18
  },
  ridmTopText: {
    color: '#333333',
    fontSize: 14,
    fontWeight: 'bold',
  },
  rspTop: {
    backgroundColor: '#fff',
    borderRadius: 4,
    elevation: 1,
    marginRight: 10,
    shadowColor: '#000',
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    textAlign: 'left',
    width: 140
  },
  rspTopView: {
    backgroundColor: '#fff',
    borderRadius: 8,
    height: 140,
    padding: 0,
    width: 140,
  },
  rspTopViewImage: {
    height: '100%',
    resizeMode: 'cover',
    width: '100%'
  },
  rspTopViewText: {
    color: '#333333',
    fontSize: 13,
    height: 40,
    margin: 8,
    textAlign: 'left'
  },
  rspTopViewTextPrice: {
    color: '#CB1016',
    fontSize: 12,
    fontWeight: 'bold',
    margin: 8,
    marginTop: -8,
    textAlign: 'left'
  },
  rspTopViewTextSeller: {
    borderRadius: 3,
    color: '#999999',
    fontSize: 11,
    margin: 8,
    marginTop: 0,
    textAlign: 'left'
  }
})
export default styles
