import { StyleSheet } from 'react-native'
import { spacing, typography } from '@app/theme'
import { ifIphoneX } from 'react-native-iphone-x-helper'
import { responsiveWidth } from 'react-native-responsive-dimensions'
import { palette } from '@app/theme/palette'

const styles = StyleSheet.create({
  ads: {
    alignSelf: 'center',
    height: 200,
    resizeMode: 'stretch',
    width: '100%',
  },
  viewTouchRightChatIcon: {
    alignSelf: 'center',
    position: 'absolute',
    right: 10
  },
  badge: {
    alignItems: 'center',
    backgroundColor: 'red',
    borderColor: '#fff',
    borderRadius: 20,
    borderWidth: 1,
    height: 20,
    justifyContent: 'center',
    position: 'absolute',
    right: -8,
    top: -10,
    width: 20
  },
  rdView2Text: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 22,
    marginVertical: 10,
  },
  imageCat: {
    borderRadius: 15,
    height: 45,
    width: 45
  },
  container: {
    // backgroundColor: '#fff',
    // height: responsiveHeight(100) + 200,
    // ...Platform.select({
    //   ios: {
    //     paddingBottom: 100
    //   },
    //   android: {
    //     paddingBottom: Platform.OS !== 'ios' && ExtraDimensions.isSoftMenuBarEnabled() ? 48 : 0 // TODO: android
    //   }
    // })
  },
  icArrowBack: {
    paddingHorizontal: 10
  },
  safeAreaView: {
    flex: 1,
    marginTop: -4
  },
  rdView1Image: {
    height: 30,
    resizeMode: 'contain',
    width: 30
  },
  // rdView2: {
  //   marginBottom: 20,
  //   paddingHorizontal: 15
  // },
  titleCat: {
    color: '#333',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 16,
    marginVertical: 10
  },

  ridmTop: {
    alignItems: 'center',
    backgroundColor: '#fff',
    justifyContent: 'center',
    marginRight: 5,
    paddingVertical: 8
  },

  ridmTopText: {
    color: '#333',
    flex: 1,
    fontSize: 13,
    marginRight: 8,
    paddingVertical: 4,
    textAlign: 'center',
    width: 90
  },
  rspTop: {
    backgroundColor: '#fff',
    borderRadius: 4,
    elevation: 1,
    marginRight: 10,
    shadowColor: '#000',
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    textAlign: 'left',
    width: 140
  },
  rspTopView: {
    backgroundColor: '#fff',
    borderRadius: 8,
    height: 140,
    padding: 0,
    width: 140,
  },
  rspTopViewImage: {
    height: '100%',
    resizeMode: 'cover',
    width: '100%'
  },
  rspTopViewText: {
    color: '#333333',
    fontSize: 13,
    height: 40,
    margin: 8,
    textAlign: 'left'
  },
  rspTopViewTextPrice: {
    color: '#CB1016',
    fontSize: 12,
    fontWeight: 'bold',
    margin: 8,
    marginTop: -8,
    textAlign: 'left'
  },
  rspTopViewTextSeller: {
    borderRadius: 3,
    color: '#999999',
    fontSize: 11,
    margin: 8,
    marginTop: 0,
    textAlign: 'left'
  },
  sAvView0Text: {
    color: '#333',
    flex: 1,
    fontSize: 14
  },
  scrollView: {
    marginBottom: 89,
    width: '100%'
  },
  ImageComponentStyle1: {
    borderRadius: 8,
    // marginTop: 15,
  },
  ImageComponentStyle2: {
    borderRadius: 8,
  },
  address: {
    fontFamily: typography.normal,
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4
  },
  slide: {
    backgroundColor: '#fff',
    // paddingHorizontal: 4,
    width: '100%'
  },
  image: {
    // borderRadius: 4,
    height: '100%',
    width: '100%',
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  boxContainerModal: {
    marginLeft: 15,
    marginRight: 15,
    marginTop: 10,
    ...ifIphoneX({
      paddingBottom: 89,
    }, {
      paddingBottom: 60,
    }),
  },
  sectionCategory: {
    backgroundColor: '#fff'
  },
  tabBar: {
    // Remove border top on both android & ios
    backgroundColor: '#fff',
    borderTopColor: 'transparent',
    borderTopWidth: 0,
    elevation: 0,
    shadowColor: '#5bc4ff',
    shadowOpacity: 0,
    shadowRadius: 0,
  },
  tabBarText: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
  },

  selectProvince: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    marginTop: 10,
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
  },
  bigText: {
    alignItems: 'center',
    color: '#fff',
    flexDirection: 'row',
    fontFamily: typography.normal,
    fontSize: 30,
    marginLeft: 25
  },
  dotStyle1: {
    borderRadius: 5,
    height: 6,
    marginHorizontal: -10,
    margin: 0,
    padding: 0,
    width: 6
  },
  icSearch: {
    height: 24,
    margin: 6,
    marginLeft: 15,
    resizeMode: 'contain',
    width: 24
  },
  imageStyleIMGbackgroud: {
    borderRadius: 4
  },
  input: {
    color: '#fff',
    flex: 1,
    fontSize: 12
  },
  inputStyle: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 25,
    // elevation: 1,
    flexDirection: 'row',
    height: 36,
    justifyContent: 'center',
    marginHorizontal: spacing.small,
    // shadowColor: '#F4F4F4',
    // shadowOffset: {
    //   width: 0,
    //   height: 2
    // },
    // shadowOpacity: 1,
    // shadowRadius: 5,
    width: 150
  },
  paginationBoxStyle1: {
    alignItems: 'center',
    alignSelf: 'center',
    bottom: 6,
    justifyContent: 'center',
    paddingVertical: 0,
    position: 'absolute',
  },
  placeholderContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
    elevation: 1,
    marginBottom: 20,
    marginTop: 10,
    paddingBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 3, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    width: '100%',
  },
  placeholderDanhmuc: {
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
    justifyContent: 'center',
    minHeight: 80,
    paddingLeft: 20,
    padding: 3,
    width: 30,
  },
  rdDanhmuc: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    height: 197,
    position: 'relative',
    shadowColor: '#e6e8ef',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 1,
    shadowRadius: 10,
    width: 138
  },
  rdDanhmucImage: {
    borderRadius: 8,
    height: 100,
    width: 138,
  },
  rdDanhmucText: {
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 11,
    height: 40,
    marginTop: 9,
    textAlign: 'center'
  },
  rdSeparator: {
    width: 10,
  },
  rdTTtop: {
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
    elevation: 1,
    marginBottom: 20,
    paddingBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 3, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    width: '100%'
  },
  rdTTtopImage: {
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    height: 160,
    resizeMode: 'cover',
    width: '100%'
  },
  rdTTtopText: {
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold',
    paddingLeft: 15,
    paddingRight: 15,
    paddingTop: 15
  },
  rdTTtopText1: {
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 13,
    paddingLeft: 15,
    paddingRight: 15,
    paddingTop: 15,
  },
  rdTTtopText2: {
    color: '#999999',
    fontFamily: typography.normal,
    fontSize: 11,
    paddingHorizontal: 8,
    paddingTop: 15
  },
  rdView: {
    backgroundColor: '#eee',
    flex: 1
    // paddingBottom: 60,
    // marginTop: 0
  },
  rdView1: {
    borderRadius: 4,
    paddingHorizontal: 8,
  },
  rdView1Text: {
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 15,
    fontWeight: 'bold',
    marginLeft: 5,
    marginTop: 20
  },
  rdView2: {
    flex: 1,
    marginTop: 10,
    padding: 0
  },
  rdView2Top: {
    flex: 1,
  },
  rdView2Top1: {
    flex: 1,
    marginBottom: 10,
    marginTop: 10
  },
  rdView2Top2: {
    flex: 1,
    width: '100%'
  },
  rdView3: {
    paddingLeft: 20,
    paddingRight: 20
  },
  rdView3Text: {
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 15,
    fontWeight: 'bold',
    marginTop: 20
  },
  rdView4: {
    paddingLeft: 20,
    paddingRight: 20
  },
  rdView4Image: {
    alignSelf: 'center',
    height: 200,
    resizeMode: 'stretch',
    width: '100%'
  },
  rdView4Text: {
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 15,
    fontWeight: 'bold',
    marginTop: 20,
  },
  rdView4Text1: {
    color: '#333333',
    fontFamily: typography.normal,
    fontSize: 15,
    fontWeight: 'bold',
    marginTop: 20,
    textAlign: 'right'
  },
  rdView4TextContainerStyle: {
    flex: 1,
    marginTop: 20
  },
  renderTopSection: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 10,
    width: responsiveWidth(100),
    zIndex: 9999
  },
  renderTopSection1: {
    // backgroundColor: '#eee',
    // left: 0,
    // position: 'absolute',
    // top: 0,
    width: responsiveWidth(100),
    zIndex: 111,
  },
  viewIcRight: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'flex-end'
  },
  searchInput: {
    flex: 1,
    flexDirection: 'row'
  },
  smallText: {
    alignItems: 'center',
    color: '#fff',
    flexDirection: 'row',
    fontFamily: typography.normal,
    fontSize: 20,
    marginLeft: 25
  },
  txtGiamgia: {
    backgroundColor: 'red',
    borderRadius: 8,
    bottom: 100,
    color: '#fff',
    fontFamily: typography.normal,
    fontSize: 14,
    left: 10,
    position: 'absolute'
  },
  txtTop: {
    marginBottom: 10,
    marginLeft: 16,
    marginTop: 5
  },
  viewAddress: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10,
    height: 36,
    // width: 120,
    borderWidth: 0,
    // borderColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 30,
    paddingHorizontal: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  textCountBr: {
    color: palette.lightGrey,
    fontSize: 12,
    paddingBottom: 5,
    paddingTop: 5,
  },
  viewProvince: {
    borderBottomWidth: 1,
    borderColor: palette.lightGrey
  },
  // TOUR
  activeSwitchContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    paddingHorizontal: 40,
  },
  button: {
    backgroundColor: '#2980b9',
    margin: 2,
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  buttonText: {
    color: 'white',
    fontSize: 15,
  },
  middleView: {
    // alignItems: 'center',
    // flex: 1,
  },
  profilePhoto: {
    borderRadius: 70,
    height: 140,
    marginVertical: 20,
    width: 140,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15,
    width: '100%',
  },
  title: {
    fontSize: 24,
    textAlign: 'center',
  },
})
export default styles
