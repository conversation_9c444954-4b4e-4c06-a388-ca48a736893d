import { StyleSheet } from 'react-native'
import { color, typography } from '@app/theme'

const freeShipIcRatio = 55 / 14
const saleIconRatio = 38 / 14

const styles = StyleSheet.create({
  ads: {
    alignSelf: 'center',
    height: 200,
    resizeMode: 'stretch',
    width: '100%',
  },
  rdView: {
    backgroundColor: '#f6f6f7',
    flex: 1
  },
  customStar: {
    marginRight: 2,
    marginTop: 3
  },
  renderStar: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  textAddress: {
    color: '#9d9d9d',
    fontSize: 12,
    marginTop: 5
  },
  textRate: {
    color: '#9d9d9d',
    fontFamily: typography.normal,
    fontSize: 12,
    marginVertical: 5
  },
  sectionRate: {
    // flexDirection: 'row',
    // justifyContent: 'space-between',
    marginHorizontal: 8
  },
  priceSale: {
    color: '#9D9D9D',
    fontSize: 12,
    fontWeight: '400',
    marginBottom: 5,
    marginLeft: 8,
    textAlign: 'left',
    textDecorationLine: 'line-through'
  },
  rdView1: {
    alignItems: 'center',
    backgroundColor: '#fff',
    borderBottomColor: '#f6f6f7',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    paddingLeft: 15
  },
  textBtnAdd: {
    alignItems: 'center',
    borderColor: color.primary,
    borderRadius: 3,
    borderWidth: 1,
    color: color.primary,
    justifyContent: 'center',
    marginBottom: 10,
    marginHorizontal: 6,
    padding: 8,
    textAlign: 'center'
  },
  rdView1Image: {
    height: 30,
    resizeMode: 'contain',
    width: 30
  },
  rdView2: {
    backgroundColor: '#fff',
    marginBottom: 8,
    paddingHorizontal: 16
  },

  rdView2Text: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 22,
    marginVertical: 10,
  },
  ridmTop: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: 18,
    paddingLeft: 10,
    paddingRight: 10,
    paddingTop: 18
  },
  ridmTopText: {
    color: '#333333',
    fontSize: 14,
    fontWeight: 'bold',
  },

  // 19/3
  rspTop: {
    backgroundColor: '#f7f7f7',
    borderRadius: 5,
    height: 320,
    marginLeft: 1,
    marginRight: 8,
    marginVertical: 5,
    width: 150
  },
  rspTopView: {
    backgroundColor: '#F7F7F7',
    borderRadius: 4,
    height: 140,
    padding: 2,
    width: '100%',
  },
  rspTopViewImage: {
    // borderRadius: 4,
    height: '100%',
    resizeMode: 'cover',
    width: '100%',
    // padding: 4
  },
  rspTopViewText: {
    color: '#333',
    fontSize: 14,
    fontWeight: '400',
    height: 35,
    marginLeft: 8,
    marginVertical: 5,
    textAlign: 'left',
    width: '90%'
  },
  rspTopViewTextPrice: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 5,
    marginLeft: 8,
    textAlign: 'left'
  },
  rspTopViewTextSeller: {
    borderRadius: 3,
    color: '#999999',
    fontSize: 11,
    marginLeft: 5,
    marginVertical: 3,
    textAlign: 'left'
  },
  viewIcon: {
    bottom: 3,
    position: 'absolute',
    right: 3
  },
  freeShipIc: {
    height: 18,
    marginRight: 12,
    width: 18 * freeShipIcRatio
  },
  saleIcon: {
    height: 18,
    width: 18 * saleIconRatio
  },
  textSaleOff: {
    color: color.primary,
    fontFamily: typography.normal,
    fontSize: 10,
    marginLeft: 8
  },
  textOldPrice: {
    color: '#9D9D9D',
    fontFamily: typography.normal,
    fontSize: 10,
    textDecorationLine: 'line-through'
  },
  discountViewRow: {
    bottom: 8,
    flexDirection: 'row',
    left: 8,
    position: 'absolute'
  }
})
export default styles
