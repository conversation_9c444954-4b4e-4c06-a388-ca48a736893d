import { observer } from 'mobx-react-lite'
import styles from './styles'
import { FlatList, Image, Text, TouchableOpacity, View } from 'react-native'
import React, { useState } from 'react'
import { LazyImage } from '@app/components'
import StarRating from 'react-native-star-rating'
import { freeShipIcon, saleIcon } from '@app/assets/images'
import { numberFormat } from '@app/utils'

export const ProductRender = observer(function ProductRender(props:any) {
  const [rateValue, setRateValue] = useState(0)

  // TODO: old version

  // const renderSanPham = ({ item }) => {
  //   return <View style={{ flex: 1 }}>
  //     <TouchableOpacity
  //       onPress={() => { props.onPress(item) }}
  //       style={styles.rspTop}>
  //       <View style={styles.rspTopView}>
  //         {item.thumbail !== '' ? <LazyImage source={{ uri: item.thumbail }} style={styles.rspTopViewImage} /> : null}
  //       </View>
  //       <View>
  //         <Text numberOfLines={1}
  //           ellipsizeMode="tail"
  //           style={styles.rspTopViewText}>{item.name}</Text>
  //         <Text
  //           style={styles.rspTopViewTextPrice}>{numberFormat(item.price)} đ</Text>
  //         {/* <Text numberOfLines={1} ellipsizeMode="middle" style={styles.rspTopViewTextSeller}>{item.storeName}</Text> */}
  //         <View style={styles.viewIcon}>
  //           <Icon name={'add-circle'} size={18} color={color.primary}/>
  //         </View>
  //       </View>
  //     </TouchableOpacity>
  //   </View>
  // }

  // TODO: new version

  const renderSanPham = ({ item }) => {
    const discount = (item.price - item.priceOld) / item.priceOld
    return <View style={{ flex: 1 }}>
      <View
        style={styles.rspTop}>
        <TouchableOpacity onPress={() => { props.onPress(item) }} style={styles.rspTopView}>
          {item.thumbail !== '' ? <LazyImage source={{ uri: item.thumbail }} style={styles.rspTopViewImage} /> : null}
          <View style={styles.discountViewRow}>
            {item.typeShip === 0 && <Image source={freeShipIcon} style={styles.freeShipIc}></Image>}
            {item?.priceOld && <Image source={saleIcon} style={styles.saleIcon}></Image>}
          </View>
        </TouchableOpacity>
        <View style={{ flex: 1 }}>
          <TouchableOpacity onPress={() => { props.onPress(item) }} >
            <Text numberOfLines={2} ellipsizeMode="tail" style={styles.rspTopViewText}>{item.name}</Text>
          </TouchableOpacity>
          <View>
            <Text style={styles.rspTopViewTextPrice}>{numberFormat(item.price)} đ</Text>
            {item?.priceOld && item.priceOld > 0 && <View style={{ flexDirection: 'row', marginLeft: 8 }}>
              <Text style={styles.textOldPrice}>{numberFormat(item.priceOld)} đ</Text>
              <Text style={styles.textSaleOff}>{numberFormat(discount * 100)}%</Text>
            </View>}
          </View>
          <View style={styles.sectionRate}>
            <Text numberOfLines={1} style={styles.textAddress}>{item.storeName}</Text>
            {rateValue > 0 ? <View style={styles.renderStar}>
              <StarRating
                fullStarColor={'#FFC107'}
                disabled={true}
                maxStars={5}
                rating={rateValue}
                emptyStarColor={'#edf1f7'}
                emptyStar={'star'}
                fullStar={'star'}
                halfStar={'star-half-o'}
                iconSet={'FontAwesome'}
                starSize={10}
                // containerStyle={styles.startContainer}
                starStyle={styles.customStar}
                // selectedStar={(rating) => ratingCompleted(rating)}
              />
            </View> : <Text style={styles.textRate}>Chưa có đánh giá</Text> }
          </View>
        </View>
        <TouchableOpacity onPress={() => { props.onPress(item) }} style={{ justifyContent: 'center' }}>
          {/* <Text style={styles.textBtnAdd}>Thêm vào giỏ</Text> */}
          <Text style={styles.textBtnAdd}>Mua ngay</Text>
        </TouchableOpacity>
      </View>
    </View>
  }

  return (
    <View>
      {props?.data?.length ? <View style={styles.rdView2}>
        <Text style={props.textStyle || styles.rdView2Text}>{props.title}</Text>
        <FlatList
          data={props.data}
          horizontal={true}
          keyExtractor={(item, index) => item._id}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          renderItem={renderSanPham} />
      </View> : null}
    </View>
  )
})
