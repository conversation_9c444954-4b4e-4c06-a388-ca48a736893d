import { SafeAreaView } from 'react-native-safe-area-context'
import {
  Alert,
  View
} from 'react-native'
import React, { useContext, useState } from 'react'

import { WebView } from 'react-native-webview'
import { observer } from 'mobx-react-lite'
import styles from './payment-screen.styles'
import { useNavigation, StackActions } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import { ModalContext } from '@app/context'
import { PaymentApi } from '@app/services/api/payment.api'
import { useStores } from '@app/models'
import { SCREENS } from '@app/navigation'
import { ButtonBack } from '@app/components'
import { Header } from 'react-native-elements'
import LinearGradient from 'react-native-linear-gradient'
import { common, linearGradientProps } from '@app/theme/styles/common'

export const PaymentScreen = observer(props => {
  const { vnpUrl, orderId, typeBooking, paymentId, title, callBackData } = props.route.params
  const [isReturn, setIsReturn] = useState(false)
  const [uri, setUri] = useState(vnpUrl)
  const { showError, showSuccess } = useContext(ModalContext)
  const { bookingStore, productStore, insuranceStore } = useStores()
  const { navigate, goBack, } = useNavigation()
  const navigation = useNavigation()
  const { t } : any = useTranslation()
  /**
   * get query from url webview
   * @param qs
   */
  const getQueryParams: any = (qs: string) => {
    // Loại bỏ phần trước dấu '?'
    qs = qs.substring(qs.indexOf('?') + 1)

    // Chuyển đổi dấu '+' thành khoảng trắng
    qs = qs.split('+').join(' ')

    const params: { [key: string]: string } = {}
    let tokens
    const re = /[?&]?([^=]+)=([^&]*)/g

    // eslint-disable-next-line no-cond-assign
    while ((tokens = re.exec(qs))) {
      params[decodeURIComponent(tokens[1])] = decodeURIComponent(tokens[2])
    }

    return params
  }

  const onGoBack = () => {
    if (typeBooking == 6) {
      // type thanh toán bảo hiểm
      // fix bug lưu 2 lần cancel vì ở web change status set flag 1 lần rồi
      // insuranceStore.setPaymentStatus(false)
      // TODO: nếu muốn tạo bản ghi thanh toán thất bại thì set cancel = true
      // insuranceStore.setPaymentCancel(true)
      goBack()
    } else {
      Alert.alert(
        t('THANHTOAN_alert_thongbao'),
        t('TITLE_CANCEL_ODER'),
        [{ text: t('CANCEL') }, {
          text: t('DONGY'),
          onPress: () => {
            const paymentApi = new PaymentApi()
            // type thanh toán sản phẩm
            if (typeBooking === 0) {
              productStore.setBookingStatus(false)
              paymentApi.deleteOrderById(paymentId).then(r => {})
            } else {
              // type các dịch vụ booking khác
              bookingStore.setBookingStatus(false)
              // paymentApi.deleteOrderBookServiceById(orderId, typeBooking).then(r => {})
            }
            goBack()
          },
        }], { cancelable: true },
      )
      productStore.setReloadData(true)
    }
  }

  /**
   * đến màn hình chi tiết theo loại booking
   * @param orderId
   */
  const goBookingDetail = (orderId) => {
    if (Number(typeBooking) === 0) {
      navigation.dispatch(
        StackActions.replace(SCREENS.bookingHistoryScreen, { bookingType: 0, title: t('BOOKING_HISTORY') })
      )
      showSuccess(t('THANHCONG'), t('BOOKING_PRODUCT_SUCCESS'))
    } else if (Number(typeBooking) === 6) {
      // thanh toán bảo hiểm thành công, bắn event listener để lưu db
      insuranceStore.setPaymentStatus(true)
      insuranceStore.setPaymentId(orderId)
      __DEV__ && console.log('setPaymentId', orderId)
      insuranceStore.setDataCreateOrder(callBackData)
      // navigation.dispatch(StackActions.replace(SCREENS.insurance))
      navigation.dispatch(StackActions.popToTop())
      showSuccess(t('THANHCONG'), t('Thanh toán bảo hiểm thành công'))
    } else {
      navigation.dispatch(
        StackActions.replace(SCREENS.bookingHistoryDetail, { orderId: orderId })
      )
      showSuccess(t('THANHCONG'), t('BOOKING_SPA_OK'))
      bookingStore.setBookingStatus(true)
    }
  }

  const _onNavigationStateChange = webViewState => {
    __DEV__ && console.log('webViewState', webViewState)
    if (isReturn) return
    if (webViewState.url.includes('/vnpay_return?') || webViewState.url.includes('/VnPayReturn?')) {
      const resultParams: any = getQueryParams(webViewState.url)
      setIsReturn(true)
      // eslint-disable-next-line eqeqeq
      __DEV__ && console.log('resultParams.vnp_ResponseCode =>>', resultParams)
      if (resultParams.vnp_ResponseCode === '00') {
        setTimeout(() => { goBookingDetail(resultParams.vnp_TxnRef) }, 3000)
      } else {
        if (typeBooking == 6) {
          insuranceStore.setDataCreateOrder(callBackData)
          // thanh toán bảo hiểm thất bại
          insuranceStore.setPaymentStatus(false)
          // TODO: nếu muốn tạo bản ghi thanh toán thất bại thì set cancel = true
          insuranceStore.setPaymentCancel(true)
        } else {
          // Mua thất bại: xoá lệnh mua ở server
          bookingStore.setBookingStatus(false)
          // const paymentApi = new PaymentApi()
          // paymentApi.deleteOrderBookServiceById(orderId, typeBooking).then(r => {})
        }
      }
    }
  }

  return (
    <SafeAreaView style={styles.sAv} edges={['right', 'left']}>
      {/* <ButtonBack onPress={onGoBack}/> */}
      <Header
        statusBarProps={{ barStyle: 'light-content' }}
        backgroundColor={'transparent'}
        barStyle="light-content" // or directly
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={onGoBack}/>}
        centerComponent={{ text: title || 'Thanh toán', style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
        containerStyle={common.headerContainer}
      />
      <View style={styles.sAvView}>
        <WebView source={{ uri: uri }}
          // onLoad={this.hideLoading.bind(this)}
          onNavigationStateChange={_onNavigationStateChange}
          javaScriptEnabled={true} />
      </View>
    </SafeAreaView>
  )
})
