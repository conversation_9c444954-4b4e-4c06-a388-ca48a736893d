import { StyleSheet, Dimensions } from 'react-native'

const tab1ItemSize = (Dimensions.get('window').width - 30) / 5
const { height } = Dimensions.get('window')
const styles = StyleSheet.create({
  flatList: {
    // paddingHorizontal: 8,
  },
  image: {
    borderRadius: 10,
  },
  item: {
    paddingHorizontal: 16
  },
  renderItem: {
    // marginHorizontal: 8,
    paddingTop: 30
  },
  textItem: {
    color: '#333',
    fontSize: 18,
    fontWeight: '600',
    paddingVertical: 15,
    textAlign: 'center'
  }
})
export default styles
