import React, { useEffect, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { View, Text, TouchableOpacity, ScrollView, Linking } from 'react-native'
import styles from './styles'
import { responsiveWidth } from 'react-native-responsive-dimensions'
import { ButtonBack, EmptyData, LazyImage } from '@app/components'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import { useTranslation } from 'react-i18next'
import { useNavigation } from '@react-navigation/native'
import { Api } from '@app/services/api'
import LinearGradient from 'react-native-linear-gradient'

const responsiveImage = (width, height) => {
  const screenWidth = responsiveWidth(100) - 32
  return {
    width: Math.floor(screenWidth),
    height: Math.floor(height * (screenWidth / width))
  }
}

export const CommunityScreen = observer(function CommunityScreen() {
  const { t } : any = useTranslation()
  const { goBack } = useNavigation()
  const [listFanPage, setListFanPage] = useState([])

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    const api = new Api()
    const rs = await api.getListFanPage()
    if (rs?.data?.data?.banners) {
      const data = rs?.data?.data?.banners
      setListFanPage(data)
    }
  }

  const renderList = () => {
    return (<View key={'index'}>
      {listFanPage ? <View style={styles.renderItem}>
        {listFanPage.map((item) => renderItem(item))}
      </View> : null}
    </View>)
  }

  const renderItem = (item) => {
    return (<TouchableOpacity
      style={styles.item}
      onPress={() => {
        Linking.openURL(item.params)
      }
      }
    >
      <LazyImage
        source={{ uri: item.thumbail }}
        style={{ ...styles.image, ...responsiveImage(650, 250) }}
      ></LazyImage>
      <Text style={styles.textItem}>{item.name}</Text>
    </TouchableOpacity>)
  }

  return (
    <View style={{ flex: 1 }}>
      <Header
        // statusBarProps={{ barStyle: 'light-content' }}
        // barStyle="light-content" // or directly
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
        centerComponent={{ text: t(t('mypet_community')), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
        //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
        // </TouchableOpacity> : null}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      <ScrollView style={{ backgroundColor: '#fff' }}>
        {!listFanPage ? <EmptyData title={t('Không có dữ liệu')} message={t('Tính năng này đang phát triển')}/> : renderList()}
      </ScrollView>
    </View>
  )
})
