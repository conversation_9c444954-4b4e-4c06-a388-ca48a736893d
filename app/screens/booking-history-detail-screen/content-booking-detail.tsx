
import { ButtonBack, LazyImage, TButton } from '@app/components'
import { formatMoney } from '@app/utils/string'
import { useNavigation } from '@react-navigation/native'
import moment from 'moment'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { View, Text, FlatList, TouchableOpacity, ScrollView } from 'react-native'
import { <PERSON><PERSON>, Header } from 'react-native-elements'
import Icon from 'react-native-vector-icons/Ionicons'
import styles from './styles-booking-history-detail'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { color } from '@app/theme'
import LinearGradient from 'react-native-linear-gradient'
import { BookingType } from '@app/constants/bookingType'

export interface ContentBookingDetailProps {
  serviceDetail: any,
  bookingData: any,
  onOpenCancelModal?: any,
  onOpenReviewModal?:any
  bookingType?:any
}

const ContentBookingDetail = (props: ContentBookingDetailProps) => {
  const { t } : any = useTranslation()
  const { goBack } = useNavigation()
  const { serviceDetail, bookingData } = props
  const [isSubmitting, setSubmitting] = useState(false)

  useEffect(() => {
    __DEV__ && console.log('*************', props)
  }, [props])

  const renderCart = ({ item, index }) => {
    return (
      <View style={{ backgroundColor: '#fff', marginBottom: 1 }}>
        <View style={styles.boxViewAddress}>
          <View style={styles.containerServiceChoose}>
            <View style={styles.viewImageService}>
              <LazyImage style={styles.imageChoose} source={{ uri: item.image }} resizeMode="cover"/>
            </View>
            <View style={styles.viewService}>
              <TouchableOpacity style={styles.starRate}>
                <Text numberOfLines={1} style={styles.textTitleService}>{item.serviceName || item.name}</Text>
              </TouchableOpacity>
              <View style={styles.starRate}>
                <Text style={styles.textContent}>{formatMoney(item.price)} đ</Text>
              </View>
              <View style={styles.starRate}>
                <Text style={styles.paymentMethod}>{t('THOIGIANDEN')}: </Text>
                <Text style={styles.date}>{moment(item.timeCheckIn).format('HH:mm   DD/MM/YYYY')}</Text>
              </View>
              {item.timeCheckOut > 0 ? <View style={styles.starRate}>
                <Text style={styles.paymentMethod}>{t('CHECKOUTTIME')}: </Text>
                <Text style={styles.date}>{moment(item.timeCheckOut).format('HH:mm   DD/MM/YYYY')}</Text>
              </View> : null}
            </View>
          </View>
        </View>
      </View>
    )
  }

  const renderTop = () => {
    return (
      <View>
        <View style={styles.viewSection}>
          <View style={styles.codeOrder}>
            <Text>{t('Mã dịch vụ')}: </Text>
            <Text style={styles.orderNo}>#{bookingData && bookingData.orderId ? bookingData.orderId : null}</Text>
            {/* {bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}> */}
            {/*  <Text style={styles.viewBtnTop}>Hủy đơn</Text> */}
            {/* </TouchableOpacity> : null} */}
          </View>
          <View style={styles.viewTitle}>
            <Text>{t('status')}: </Text>
            {bookingData && bookingData.status === 0 ? <Badge
              badgeStyle={styles.badgeCXN}
              value={<Text style={styles.textStatus}>{t('CHOXACNHAN')}</Text>}
            /> : bookingData && bookingData.status === 1 ? <Badge
              badgeStyle={styles.badgeCLDV}
              value={<Text style={styles.textStatus}>{t('CHOLAMDV')}</Text>}
            /> : bookingData && bookingData.status === 2 ? <Badge
              status="error"
              badgeStyle={styles.badgeCancel}
              value={<Text style={styles.textStatus}>{t('CANCELED')}</Text>}
            /> : bookingData && bookingData.status === 3 ? <Badge
              badgeStyle={styles.badgeDone}
              value={<Text style={styles.textStatus}>{t('DONE')}</Text>}
            /> : null }
          </View>
          {bookingData && bookingData?.status === 0 ? <TouchableOpacity style={styles.btnCancelOrder} onPress={() => props.onOpenCancelModal()}>
            <Text style={styles.viewBtnTop}>Hủy đơn</Text>
          </TouchableOpacity> : <View></View>}
        </View>
        <View style={styles.viewSection}>
          <Text style={styles.titleLabel}>{t('BRANCH')} </Text>
          <TouchableOpacity style={styles.renderItemListAddress}>
            <Text style={styles.textName}>{bookingData && bookingData.branchName ? bookingData.branchName : null}</Text>
          </TouchableOpacity>
          <View style={styles.boxAddress}>
            <Icon style={styles.icLocation} name={'location-outline'} size={22} color={color.primary}/>
            <Text numberOfLines={2} style={styles.textAddressBranch}>{bookingData && bookingData.branchAddress ? bookingData.branchAddress : null}</Text>
          </View>
          <View style={styles.renderItemListPhone}>
            <View style={styles.boxViewPhone}>
              <Icon name={'call-outline'} size={22} color={color.primary}/>
              <Text style={styles.textAddressPhone}>{bookingData && bookingData.branchPhone ? bookingData.branchPhone : null}</Text>
            </View>
          </View>
        </View>
      </View>
    )
  }

  const renderHeaderList: any = () => {
    return (
      <View style={styles.listHeader}>
        {serviceDetail
          ? <View style={styles.viewTextLabel}>
            <Text style={styles.titleLabel}>{t('CHITIETLICHHEN_dichvudachon') }</Text>
          </View> : null}
      </View>
    )
  }

  const renderFooter = () => {
    return (
      <View style={{ marginTop: 4 }}>
        <View style={styles.viewSection}>
          <View style={styles.viewTextLabelFooter}>
            <Text style={styles.titleLabel}>{t('NOTE')}</Text>
          </View>
          <Text style={styles.noteContent}>{bookingData?.note ? bookingData?.note : null}</Text>
        </View>
        { props?.bookingType != BookingType.SHOWROOM && <>
          <View style={styles.viewSection}>
            <View style={styles.viewTextLabelFooter}>
              <Text style={styles.titleLabel}>{t('PHUONGTHUC_THANHTOAN')}</Text>
            </View>
            <View style={{ flex: 1, marginVertical: 10 }}>
              {bookingData?.paymentMethod === 1 ? <View style={{ flexDirection: 'row' }}>
                <Text style={{ color: '#333' }}>{t('PHUONGTHUCTT_online')}: </Text>
                <Text style={{ color: '#333333' }}>{bookingData?.bankCode ? bookingData?.bankCode : null}</Text>
              </View> : bookingData?.paymentMethod === 0 ? <Text style={{ color: '#333' }}>{t('PHUONGTHUCTT_taiquay')}</Text> : null }
            </View>
          </View>
          <View style={styles.viewSection}>
            <View style={styles.viewTextLabelFooter}>
              <Text style={styles.titleLabel}>{t('PAYMENT_STATUS')}</Text>
            </View>
            <View style={{ flex: 1, marginVertical: 10 }}>
              {bookingData?.paymentMethod === 1 && bookingData?.isPayOnline === 1 ? <View
                style={{ flexDirection: 'row' }}><Text style={{ color: 'green' }}>{t('PAID_SUCCESSFULLY')}</Text></View> : bookingData?.paymentMethod === 1 && bookingData?.isPayOnline === 0 ? <View
                  style={{ flexDirection: 'row' }}><Text style={{ color: 'red' }}>{t('UNPAID')}</Text></View> : bookingData?.paymentMethod === 0 ? <View
                    style={{ flexDirection: 'row' }}><Text style={{ color: 'orange' }}>{t('PAID_AT_STORE')}</Text></View> : null}
            </View>
          </View>
          <View style={styles.viewSection}>
            <View style={styles.viewTextLabel}>
              <Text style={styles.titleLabel}>{t('TOTAL_MONEY')}</Text>
            </View>
            <View style={styles.totalMoneyContent}>
              <Text style={styles.subLabel}>{t('CHITIETLICHHEN_giadichvu')}</Text>
              <Text style={styles.textPrice_Count}>{formatMoney(bookingData?.price)} đ</Text>
            </View>
            <View style={styles.totalMoneyContent}>
              <Text style={styles.subLabel}>{t('CHIPHI_VANCHUYEN')}</Text>
              <Text style={styles.textPrice_Count}>0 đ</Text>
            </View>
            <View style={styles.totalMoneyContent}>
              <Text style={styles.subLabel}>{t('TOTAL_MONEY')}</Text>
              <Text style={styles.textPrice_Count}>{formatMoney(bookingData?.price)} đ</Text>
            </View>
            { bookingData && bookingData.status === 3 ? <View style={styles.viewBtn} >
              <TButton typeRadius={'rounded'} disabled={ isSubmitting} loading={isSubmitting} title={t('REVIEW')} onPress={() => props.onOpenReviewModal()} />
            </View> : null}
          </View>
        </>}
      </View>
    )
  }
  return (
    <View style={styles.container}>
      <Header
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
        centerComponent={{ text: t(t('ORDER')), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      <ScrollView showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
        {renderTop()}
        <FlatList
          data={serviceDetail}
          renderItem={renderCart}
          keyExtractor={(item, index) => item._id + index}
          ListHeaderComponent={renderHeaderList}
          ListFooterComponent={renderFooter}
        />
      </ScrollView>
    </View>
  )
}

export default ContentBookingDetail
