
import { ButtonBack, LazyImage, TButton } from '@app/components'
import { formatMoney } from '@app/utils/string'
import { useNavigation } from '@react-navigation/native'
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { View, Text, FlatList, TouchableOpacity, Image, ScrollView } from 'react-native'
import { Bad<PERSON>, Header } from 'react-native-elements'
import Icon from 'react-native-vector-icons/Ionicons'

import styles from './styles-booking-history-detail'
import { color, spacing } from '@app/theme'
import { SCREENS } from '@app/navigation'
import { iconTruckOutline } from '@app/assets/images'
import common, { linearGradientProps } from '@app/theme/styles/common'
import LinearGradient from 'react-native-linear-gradient'

export interface ContentProductDetailProps {
  productDetail: any,
  bookingData: any,
  onOpenCancelModal?: any,
  onOpenReviewModal?:any
}

function ContentProductDetail(props: ContentProductDetailProps) {
  const { t } : any = useTranslation()
  const { goBack, navigate } = useNavigation()
  const { productDetail, bookingData } = props
  const [isSubmitting, setSubmitting] = useState(false)
  const totalPrice = parseInt(bookingData.transportFee) + parseInt(bookingData.totalPriceShop)
  const totalWeight = Number(bookingData.totalWeight / 1000)
  // const shippingInfo = bookingData.shippingInfo[0].statusText

  const showPrice = (item) => {
    if (item.classifyActive && Object.keys(item.classifyActive).length) {
      const regex = new RegExp(',', 'g')
      const p = item.classifyActive[0].price.replace(regex, '')
      // giá con
      return <Text style={styles.textContent}>{formatMoney(parseInt(p))} đ</Text>
    } else {
      // fixed price
      return <Text style={styles.textContent}>{formatMoney(item?.price)} đ</Text>
    }
  }

  const renderProductItem = ({ item, index }) => {
    return <View style={{ marginBottom: 1 }}>
      <View style={styles.rSp}>
        <View style={styles.rSpView}>
          <LazyImage source={{ uri: item.thumbnail }}
            style={styles.rSpViewImage} />
        </View>
        <View style={styles.rSpView1}>
          <Text style={styles.rSpView1Text}>{item.productName} </Text>

          {showPrice(item)}

          {item.classifyActive.map((e, classifyActive) => {
            return <Text key={classifyActive} style={styles.rSpView1Text2}>{e.name}: <Text style={styles.rSpView1Input}>{e.value}</Text></Text>
          })}

          <View style={{ flexDirection: 'row' }}>
            <Text style={styles.rSpView1Text2}>{t('COUNT')}: </Text>
            <Text style={styles.rSpView1Input}>{item.count}</Text>
          </View>
          <View style={{ flexDirection: 'row' }}>
            <Text style={styles.rSpView1Text2}>{t('Ghi chú sản phẩm')}: </Text>
            <Text style={styles.rSpView1Input}>{item?.noteProduct}</Text>
          </View>
        </View>
      </View>
    </View>
  }

  const renderFooter = () => {
    return (
      <View style={{ marginTop: 4 }}>
        <View style={styles.viewSection}>
          <View style={styles.viewTextLabel}>
            <Text style={styles.titleLabel}>{t('NOTE')}</Text>
          </View>
          <Text style={styles.noteContent}>{bookingData && bookingData.note ? bookingData.note : 'Không có ghi chú'}</Text>
        </View>
        <View style={styles.viewSection}>
          <View style={styles.viewTextLabel}>
            <Text style={styles.titleLabel}>{t('PHUONGTHUC_THANHTOAN')}</Text>
          </View>
          <View style={styles.textPaymentMethod}>
            {bookingData && bookingData.paymentMethod === 1 && bookingData.bankCode ? <View style={{ flexDirection: 'row' }}><Text style={{ color: '#333' }}>{t('PHUONGTHUCTT_online')}: </Text><Text style={{ color: '#333' }}>{bookingData.bankCode}</Text></View> : <Text style={{ color: '#333' }}>{bookingData && bookingData.paymentMethod === 0 ? <Text >{t('Payment_on_delivery')}</Text> : null}</Text>}
          </View>
        </View>
        <View style={styles.viewSection}>
          <Text style={styles.titleLabel}>{t('PAYMENT_STATUS')}</Text>
          <View style={{ flex: 1 }}>
            {bookingData && bookingData.paymentMethod === 1 && bookingData.isPayOnline === 1 ? <View
              style={{ flexDirection: 'row' }}><Text
                style={{ color: 'green', paddingVertical: 10 }}>{t('PAID_SUCCESSFULLY')}</Text></View> : bookingData && bookingData.paymentMethod === 1 && bookingData.isPayOnline === 0 ? <View
              style={{ flexDirection: 'row' }}><Text style={{ color: 'red', paddingVertical: 10 }}>{t('UNPAID')}</Text></View> : bookingData && bookingData.paymentMethod === 0 ? <View
              style={{ flexDirection: 'row' }}><Text
                style={{ color: 'orange', paddingVertical: 10 }}>{t('Payment_on_delivery')}</Text></View> : null}
          </View>
        </View>
        <View style={styles.viewSection}>
          <View style={styles.viewTextLabel}>
            <Text style={styles.titleLabel}>{t('TOTAL_MONEY')}</Text>
          </View>
          <View style={styles.totalMoneyContent}>
            <Text style={styles.subLabel}>{t('Product_price')}</Text>
            <Text style={styles.textPrice_Count}>{formatMoney(bookingData.totalPriceShop)} đ</Text>
          </View>
          <View style={styles.totalMoneyContent}>
            <Text style={styles.subLabel}>{t('Khối lượng vận chuyển')}</Text>
            <Text style={styles.textPrice_Count}>{totalWeight} kg</Text>
          </View>
          <View style={styles.totalMoneyContent}>
            <Text style={styles.subLabel}>{t('CHIPHI_VANCHUYEN')}</Text>
            <Text style={styles.textPrice_Count}>{formatMoney(bookingData.transportFee)} đ</Text>
          </View>
          <View style={styles.totalMoneyContent}>
            <Text style={styles.subLabel}>{t('TOTAL_MONEY')}</Text>
            <Text style={styles.textPrice_Count}>{formatMoney(totalPrice)} đ</Text>
          </View>
          { bookingData && bookingData.status === 3 ? <View style={styles.viewBtn} >
            <TButton typeRadius={'rounded'} disabled={ isSubmitting} loading={isSubmitting} title={t('REVIEW')} onPress={() => props.onOpenReviewModal()} />
          </View> : null}
        </View>
      </View>
    )
  }

  const renderTop = () => {
    return (
      <View>
        <View style={styles.viewSection}>
          <View style={styles.codeOrder}>
            <Text>{t('code')}: </Text>
            <Text style={styles.orderNo}>#{bookingData?.orderId ? bookingData.orderId : null}</Text>
          </View>
          <View style={styles.viewTitle}>
            <Text>{t('status')}: </Text>
            {bookingData && bookingData.statusText === 'Chờ xác nhận' ? <Badge
              badgeStyle={styles.badgeCXN}
              value={<Text style={styles.textStatus}>{t('CHOXACNHAN')}</Text>}
            /> : bookingData && bookingData.statusText === 'Chờ giao hàng' ? <Badge
              badgeStyle={styles.badgeCLDV}
              value={<Text style={styles.textStatus}>{t('wait_for_shipping')}</Text>}
            /> : bookingData && bookingData.statusText === 'Đang giao' ? <Badge
              badgeStyle={styles.badgeCLDV}
              value={<Text style={styles.textStatus}>{t('delivering')}</Text>}
            /> : bookingData && bookingData.statusText === 'Hoàn thành' ? <Badge
              badgeStyle={styles.badgeDone}
              value={<Text style={styles.textStatus}>{t('DONE')}</Text>}
            /> : bookingData && bookingData.statusText === 'Trả hàng' ? <Badge
              badgeStyle={styles.badgeCXN}
              value={<Text style={styles.textStatus}>{t('returns')}</Text>}
            /> : bookingData && bookingData.statusText === 'Đã hủy' ? <Badge
              badgeStyle={styles.badgeCancel}
              status="error"
              value={<Text style={styles.textStatus}>{t('CANCELED')}</Text>}
            /> : null }
            {/* {bookingData && bookingData.statusText ? <Badge
                badgeStyle={styles.badgeDone}
                value={<Text style={styles.textStatus}>{bookingData.statusText}</Text>}/> : null } */}
          </View>
          {bookingData && bookingData.status === 0 ? <TouchableOpacity style={styles.btnCancelOrder} onPress={() => props.onOpenCancelModal()}>
            <Text style={styles.viewBtnTop}>Hủy đơn</Text>
          </TouchableOpacity> : <View></View>}
        </View>
        {bookingData?.shippingInfo && bookingData?.shippingInfo.length > 0 ? <TouchableOpacity
          style={styles.btnShippingInfo}
          onPress={(e) => {
            navigate(SCREENS.shippingInformationScreen, { productDetail: productDetail })
          }}
        >
          <View style={{ flexDirection: 'row' }}>
            <Image style={{ width: 30, height: 30 }} source={iconTruckOutline}></Image>
            <Text style={{ flex: 1, color: '#4DB6AC', alignSelf: 'center', marginLeft: spacing.small }}>{bookingData?.shippingInfo[0].statusText}</Text>
            <Icon name="chevron-forward-outline" size={18} style={{ alignSelf: 'center' }} color={'#4DB6AC'}></Icon>
          </View>
        </TouchableOpacity> : null}
        <View style={styles.viewSection}>
          <View style={styles.viewTextLabel}>
            <Text style={styles.titleLabel}>{t('shipping_address')}</Text>
          </View>
          <View>
            <TouchableOpacity>
              <View style={styles.renderItemListAddress}>
                {/* <Text style={styles.textName}>{bookingData?.phone ? bookingData.branchName : null}</Text> */}
                <Text style={styles.textName}>{bookingData.userBuyFullName}</Text>
              </View>
            </TouchableOpacity>
            <View style={styles.boxAddress}>
              <Icon name={'location-outline'} size={22} color={color.primary}/>
              <Text numberOfLines={3} style={styles.textAddressBranch}>{bookingData?.address ? bookingData.location : null}</Text>
            </View>
            <View style={styles.renderItemListPhone}>
              <View style={styles.boxViewPhone}>
                <Icon name={'call-outline'} size={22} color={color.primary}/>
                <Text style={styles.textAddressPhone}>{bookingData?.phone ? bookingData.phone : null}</Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    )
  }

  const renderHeaderList: any = () => {
    return (
      <View style={styles.listHeader}>
        {productDetail
          ? <View style={styles.viewTextLabel}>
            <Text style={styles.titleLabel}>{t('all_product') }</Text>
          </View> : null}
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <Header
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
        centerComponent={{ text: t('ORDER'), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      <ScrollView showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
        {renderTop()}
        <FlatList
          data={productDetail.products}
          renderItem={renderProductItem}
          keyExtractor={(item, index) => item._id + index}
          ListHeaderComponent={renderHeaderList}
          ListFooterComponent={renderFooter}
        />
      </ScrollView>
    </View>
  )
}

export default ContentProductDetail
