import { StyleSheet, Dimensions } from 'react-native'
import { ifIphoneX } from 'react-native-iphone-x-helper'
import { color, spacing, typography } from '../../theme'
const { width, height } = Dimensions.get('window')
const styles = StyleSheet.create({
  allIcon: {
    marginTop: 3
  },
  badgeCLDV: {
    backgroundColor: '#C8E6C9',
    borderRadius: 12.5,
    height: 25,
    justifyContent: 'center',
    minWidth: 90
  },
  badgeCXN: {
    backgroundColor: '#FFF176',
    borderRadius: 12.5,
    height: 25,
    justifyContent: 'center',
    minWidth: 90
  },
  badgeCancel: {
    borderRadius: 12.5,
    height: 25,
    justifyContent: 'center',
    minWidth: 90
  },
  badgeDone: {
    backgroundColor: '#00e096',
    borderRadius: 12.5,
    height: 25,
    justifyContent: 'center',
    minWidth: 90
  },
  boxAddress: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    paddingBottom: 12,
    // paddingLeft: 11,
  },
  boxCheckInTime: {
    flex: 1,
    flexDirection: 'column'
  },
  boxContact: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    height: 125,
    marginBottom: 20,
    marginLeft: 15,
    marginRight: 10,
    marginTop: 24,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10,
    width: 288
  },
  boxService: {
    backgroundColor: '#fff',
    borderBottomColor: '#f6f6f7',
    flexDirection: 'row',
    marginHorizontal: 15,
    marginVertical: 16
  },
  boxViewAddress: {
    backgroundColor: '#ffffff',
    paddingBottom: 20,
    paddingHorizontal: 16
  },
  boxViewPhone: {
    flexDirection: 'row'
  },
  btnCancelOrder: {
    backgroundColor: color.primary,
    borderRadius: 4,
    flex: 1,
    width: 100,
  },
  btnShippingInfo: {
    borderBottomWidth: 0.3,
    borderColor: '#f2f2f2',
    borderTopWidth: 0.3,
    marginBottom: 8,
    marginHorizontal: 8,
  },
  codeOrder: {
    flexDirection: 'row',
    paddingTop: 15
  },
  container: {
    backgroundColor: color.primaryBackground,
    flex: 1
  },
  containerServiceChoose: {
    flexDirection: 'row',
    marginTop: 15
  },
  contentDateTime: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginLeft: 15,
  },
  contentText: {
    alignItems: 'flex-start',
    flex: 1,
    position: 'relative',
  },
  date: {
    alignSelf: 'center',
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    marginLeft: 10
  },
  dateTime: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingRight: 15
  },
  icArrowBack: {
    // marginLeft: 10,
    paddingRight: 20
  },
  icLocation: {
    marginTop: 2
  },
  icStatus: {
    alignSelf: 'center',
    marginLeft: 10
  },
  imageChoose: {
    // borderRadius: 4,
    height: 80,
    width: 80
  },
  listHeader: {
    backgroundColor: '#fff',
    paddingHorizontal: 16
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
    ...ifIphoneX({
      marginTop: 0
    }, {
      marginTop: 5
    }),
  },
  noteContent: {
    // backgroundColor: '#f7f9fc',
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    paddingVertical: 10
  },
  orderNo: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold',
  },
  paymentMethod: {
    color: '#979797',
    fontFamily: typography.normal,
    fontSize: 14,
  },
  rSp: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    paddingBottom: 20,
    paddingHorizontal: 16,
    paddingTop: 15
  },
  rSpView: {
    alignItems: 'center',
    backgroundColor: '#f3f3f3',
    height: 82,
    justifyContent: 'center',
    padding: 2,
    width: 82
  },
  rSpView1: {
    flex: 1,
    paddingLeft: 20
  },
  rSpView1Input: {
    color: '#333',
    fontSize: 13,
    marginTop: 10
  },
  rSpView1Text: {
    color: '#333333',
    fontSize: 14,
    marginBottom: 10
  },
  rSpView1Text1: {
    color: '#333',
    fontSize: 13,
    marginTop: 10
  },
  rSpView1Text2: {
    color: '#999999',
    fontSize: 11,
    marginTop: 12,
  },
  rSpView2: {
    alignItems: 'center',
    justifyContent: 'center'
  },
  rSpViewImage: {
    height: 80,
    resizeMode: 'contain',
    width: 80,
  },
  rdkTopImage: {
    borderRadius: 8,
    height: 50,
    resizeMode: 'cover',
    width: 50
  },
  rdkTopText: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0,
    paddingBottom: 10
  },
  rdkTopText1: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0
  },
  renderItemListAddress: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10
  },
  renderItemListPhone: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  serviceContent: {
    flex: 1,
    paddingHorizontal: 10
  },
  starRate: {
    alignItems: 'center',
    flexDirection: 'row',
    // justifyContent: 'space-between',
    // marginTop: 10,
    paddingBottom: 15,
    flex: 1
    // paddingHorizontal: 10,
  },
  subLabel: {
    color: '#979797',
    fontFamily: typography.normal,
    fontSize: 14,
    marginVertical: 10
  },
  textAddressBranch: {
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 14,
    marginHorizontal: 7
  },
  textAddressPhone: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 13,
    marginLeft: 7
  },
  textContent: {
    color: '#f3373a',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600'
  },
  textContentOldPrice: {
    color: '#979797',
    fontFamily: typography.normal,
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 10,
    textDecorationLine: 'line-through'
  },
  textLabel: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0,
    marginLeft: spacing.small,
  },
  textName: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    marginTop: 10
  },
  textPaymentMethod: {
    flex: 1,
    marginVertical: 10
  },
  textPrice_Count: {
    color: color.primary,
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    marginVertical: 10,
  },
  textStatus: {
    color: 'black',
    fontFamily: typography.normal,
    fontSize: 12,
    paddingHorizontal: 10,
    textAlign: 'center',
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  textTitleService: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
  },
  timeCheckin: {
    color: '#979797',
    fontFamily: typography.normal,
    fontSize: 14,
  },
  title: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
  },
  titleLabel: {
    color: '#979797',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '500',
    paddingBottom: 10,
    // paddingHorizontal: 15,
    paddingVertical: 20,
    textTransform: 'uppercase'
  },
  totalMoney: {
    color: '#333',
    fontWeight: '600',
    letterSpacing: 0,
    marginHorizontal: spacing.small,
    marginVertical: 10
  },
  totalMoneyContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  viewBTTop: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  viewBtn: {
    marginTop: 30,
    marginVertical: 15
  },
  viewBtnItem: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 15
  },
  viewBtnTop: {
    color: '#fff',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    paddingHorizontal: 12,
    paddingVertical: 8,
    textAlign: 'center'
  },
  viewContainer: {
    flex: 1,
    // marginTop: 15,
    // paddingBottom: 70
  },
  viewImageService: {
    alignItems: 'center',
    backgroundColor: color.primaryBackground,
    // borderRadius: 4,
    height: 82,
    justifyContent: 'center',
    padding: 2,
    width: 82
  },
  viewSection: {
    backgroundColor: '#fff',
    marginBottom: 5,
    paddingBottom: 10,
    paddingHorizontal: 16,
  },
  viewService: {
    flexDirection: 'column',
    flex: 1,
    marginLeft: 10,
    justifyContent: 'space-between'
  },
  viewStatus: {
    backgroundColor: '#00e096',
    borderRadius: 12.5,
    height: 25,
    justifyContent: 'center',
    minWidth: 90,
  },
  viewTextLabel: {
    alignItems: 'center',
    // backgroundColor: '#F5F5F5',
    flexDirection: 'row',
    // height: 30
  },
  viewTextLabelFooter: {
    alignItems: 'center',
    // backgroundColor: '#F5F5F5',
    flexDirection: 'row',
    // height: 30,
  },
  viewTitle: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacing.small,
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
})
export default styles
