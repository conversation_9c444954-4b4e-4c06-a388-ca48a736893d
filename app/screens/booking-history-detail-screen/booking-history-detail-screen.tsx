import React, { useContext, useEffect, useRef, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { View } from 'react-native'
import { useNavigation, useRoute } from '@react-navigation/native'
import styles from './styles-booking-history-detail'
import { useTranslation } from 'react-i18next'
import { HistoryModel, OrderProductStoreModel, useStores } from '@app/models'
import { Api } from '../../services/api'
import { Modalize } from 'react-native-modalize'
import Comment from '../service-detail-screen/tabrender/Comment/Comment'
import { ModalContext } from '@app/context'
import { CancelOrderScreen } from '@app/screens'
import { responsiveHeight } from 'react-native-responsive-dimensions'
import { ButtonBack, Text } from '@app/components'
import ContentProductDetail from './content-product-detail'
import ContentBookingDetail from './content-booking-detail'
// import common from '@app/theme/styles/common'
// import { Header } from 'react-native-elements'
import CommentProduct from '@app/screens/service-detail-screen/tabrender/Comment/CommentProduct'
import { SCREENS } from '@app/navigation'
import { LogEvent } from '@app/services/loggingServices'
import { SafeAreaView } from 'react-native-safe-area-context'
import { BookingType } from '@app/constants/bookingType'

export const BookingHistoryDetailScreen = observer((props: any) => {
  const modalizeRef = useRef<Modalize>(null)
  const { t } : any = useTranslation()
  const { goBack, navigate } = useNavigation()
  const route : any = useRoute()
  const { orderId, bookingType } = route.params || {}
  const [bookingData, setBookingData] = useState(HistoryModel.create())
  const [bookingProduct, setBookingProduct] = useState(OrderProductStoreModel.create())
  const [serviceDetail, setServiceDetail] = useState(null)
  const [productDetail, setProductDetail] = useState([])
  const { profileStore, serviceStore, productStore, commentStore } = useStores()
  const { showError } = useContext(ModalContext)
  const modalizeCancelOrder = useRef<Modalize>(null)
  const modalCommentProduct = useRef<Modalize>(null)

  useEffect(() => {
    if (!orderId) {
      goBack()
    }
    loadData().then(r => {})
  }, [])

  const loadData = async () => {
    const api = new Api()
    if (Number(bookingType) === BookingType.SHOP) {
      const rs = await api.getBookingProductDetail(orderId)
      if (rs && rs?.data?.data?.detail) {
        setBookingProduct(OrderProductStoreModel.create(rs.data.data.detail))
        setProductDetail(rs.data.data.detail)
        productStore.setProductId(rs.data.data.detail.products[0].productId)
      }
      // await commentStore.getCommentProductById(productStore.productId)
    }
    if (Number(bookingType) === BookingType.CLINIC) {
      const rs = await api.getBookingClinicDetail(orderId)
      if (rs && rs.data) {
        setBookingData(HistoryModel.create(rs.data.data.detail))
        if (rs.data.data.detail.items) {
          setServiceDetail(rs.data.data.detail.items)
        } else {
          const data = [JSON.parse(rs.data.data.detail.serviceDetail)]
          setServiceDetail(data)
        }
        // await serviceStore.getLastestComments(rs.data.data.detail.storeId, profileStore._id)
      }
    }
    if (Number(bookingType) === BookingType.PARKING) {
      const rs = await api.getBookingHotelDetail(orderId)
      if (rs && rs.data) {
        setBookingData(HistoryModel.create(rs.data.data.detail))
        if (rs.data.data.detail.items) {
          setServiceDetail(rs.data.data.detail.items)
        } else {
          const data = [JSON.parse(rs.data.data.detail.serviceDetail)]
          setServiceDetail(data)
        }
        // await serviceStore.getLastestComments(rs.data.data.detail.storeId, profileStore._id)
      }
    }
    if (Number(bookingType) === BookingType.SPA) {
      const rs = await api.getBookingSpaDetail(orderId)
      __DEV__ && console.log('LOAD DATA BOOKING DETAIL', rs)
      if (rs && rs.data) {
        setBookingData(HistoryModel.create(rs.data.data.detail || rs.data.data.detail.serviceDetail))
        if (rs.data.data.detail.items) {
          setServiceDetail(rs.data.data.detail.items)
        } else {
          const data = [JSON.parse(rs.data.data.detail.serviceDetail)]
          setServiceDetail(data)
        }
        // await serviceStore.getLastestComments(rs.data.data.detail.storeId, profileStore._id)
      }
    }
    if (Number(bookingType) === BookingType.SHOWROOM) {
      const rs = await api.getBookingShowRoomDetail(orderId)
      __DEV__ && console.log('LOAD DATA BOOKING DETAIL', rs)
      if (rs && rs.data) {
        setBookingData(HistoryModel.create(rs.data.data.detail || rs.data.data.detail.serviceDetail))
        if (rs.data.data.detail.items) {
          setServiceDetail(rs.data.data.detail.items)
        } else {
          const data = [JSON.parse(rs.data.data.detail.serviceDetail)]
          setServiceDetail(data)
        }
        // await serviceStore.getLastestComments(rs.data.data.detail.storeId, profileStore._id)
      }
    }
  }

  const onOpen = () => {
    if (profileStore.isSignedIn()) {
      modalizeRef.current?.open()
    } else {
      goLoginScreenRequired()
      LogEvent('user_click_btn_login', { screen: 'profile_screen' })
    }
  }

  const onCloseModal = () => {
    modalizeRef.current?.close()
    serviceStore.getLastestComments(bookingData.storeId, profileStore._id)
  }

  const goLoginScreenRequired = () => {
    navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  const onOpenModalProduct = () => {
    if (profileStore.isSignedIn()) {
      modalCommentProduct.current?.open()
    } else {
      goLoginScreenRequired()
      LogEvent('user_click_btn_login', { screen: 'profile_screen' })
    }
  }

  const onCloseModalCommentProduct = () => {
    modalCommentProduct.current?.close()
    commentStore.getCommentProductById(productStore.productId)
  }

  const onCloseModalCancelOrder = () => {
    modalizeCancelOrder.current?.close()
  }

  const onOpenModalCancelOrder = () => {
    modalizeCancelOrder.current?.open()
  }
  const renderHeaderCancelOrder: any = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onCloseModalCancelOrder} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>{t('request_to_cancel_the_order')}</Text>
    </View>
  )

  const renderHeaderModalize: any = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onCloseModal} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>{t('REVIEW')}</Text>
    </View>
  )

  const renderHeaderModalCommentProduct: any = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onCloseModalCommentProduct} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>{t('REVIEW')}</Text>
      <View style={{ width: 20 }}></View>
    </View>
  )

  const renderContent = () => {
    if (Number(bookingType) === 0) {
      return <ContentProductDetail productDetail={productDetail} bookingData={ bookingProduct } onOpenCancelModal={onOpenModalCancelOrder} onOpenReviewModal={onOpenModalProduct}/>
    } else {
      return <ContentBookingDetail serviceDetail={serviceDetail} bookingData={bookingData} onOpenCancelModal={onOpenModalCancelOrder} onOpenReviewModal={onOpen} bookingType={bookingType}/>
    }
  }

  return (<SafeAreaView style={{ backgroundColor: '#fff', flex: 1, marginTop: -4 }} edges={['right', 'top', 'left']}>
    <View style={styles.viewContainer}>
      {renderContent()}
    </View>
    <Modalize
      HeaderComponent={renderHeaderModalize}
      ref={modalizeRef}
      adjustToContentHeight
      keyboardAvoidingBehavior={'padding'}
    >
      <Comment closeModal={onCloseModal} storeId={bookingData && bookingData.storeId} onRefresh={() => {}} />
    </Modalize>

    <Modalize
      HeaderComponent={renderHeaderModalCommentProduct}
      ref={modalCommentProduct}
      adjustToContentHeight
      keyboardAvoidingBehavior={'padding'}
    >
      <CommentProduct closeModal={onCloseModalCommentProduct} storeId={bookingData && bookingData.storeId} onRefresh={() => {}} />
    </Modalize>
    <Modalize
      modalHeight={responsiveHeight(85)}
      HeaderComponent={renderHeaderCancelOrder}
      ref={modalizeCancelOrder}
      keyboardAvoidingBehavior={'padding'}
    >
      <CancelOrderScreen bookingType={bookingType} bookingData={bookingType !== 0 ? bookingData : bookingProduct} onCloseModalCancelOrder={onCloseModalCancelOrder} />
    </Modalize>
  </SafeAreaView>
  )
})
