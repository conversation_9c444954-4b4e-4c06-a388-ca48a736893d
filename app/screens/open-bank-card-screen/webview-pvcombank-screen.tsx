import { SafeAreaView } from 'react-native-safe-area-context'
import React, { useEffect, useState } from 'react'
import {
  View,
  Text,
  Dimensions,
  StatusBar

} from 'react-native'

import styles from './styles'
import { useTranslation, withTranslation } from 'react-i18next'
import { useNavigation } from '@react-navigation/native'
import { observer } from 'mobx-react-lite'
import moment from 'moment'
import 'moment/locale/vi'
import { Api } from '@app/services/api'
import { ButtonBack } from '@app/components'
import { getStatusBarHeight } from 'react-native-iphone-x-helper'
import { WebView } from 'react-native-webview'
import { getRemoveConfig } from '@app/utils/storage'

const api = new Api()

moment.locale('vi')
const { width } = Dimensions.get('window')

export const WebviewPvComBankScreen = observer((props:any) => {
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()
  const [url, setUrl] = useState('')

  useEffect(() => {
    getRemoveConfig().then(e => {
      setUrl(e.url_pvcombank)
    })
    return () => {

    }
  }, [])

  const renderHeader = () => {
    return (<View style={styles.top}>
      <View style={styles.header}>
        <ButtonBack style={{ color: '#333', position: 'absolute', left: 16, top: getStatusBarHeight() }} onPress={goBack}/>
        <Text style={styles.headerTitle}>Mở tài khoản</Text>
        <View/>
      </View>
    </View>
    )
  }

  return (
    <SafeAreaView style={{ flex: 1, marginTop: -4 }} edges={['right', 'top', 'left']}>
      <StatusBar backgroundColor='transparent' barStyle={'dark-content'} />
      {renderHeader()}
      { url && url !== '' ? <WebView source={{ uri: url }}
        // onLoad={this.hideLoading.bind(this)}
        //        onNavigationStateChange={_onNavigationStateChange}
        javaScriptEnabled={true} /> : null}

    </SafeAreaView>
  )
})

export default withTranslation()(WebviewPvComBankScreen)
