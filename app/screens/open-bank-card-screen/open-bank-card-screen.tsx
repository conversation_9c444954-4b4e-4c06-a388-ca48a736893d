import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import React, { useContext, useEffect, useState } from 'react'
import { Dimensions, ScrollView, View } from 'react-native'

import styles from './styles'
import { useTranslation, withTranslation } from 'react-i18next'
import { useNavigation } from '@react-navigation/native'
import { observer } from 'mobx-react-lite'
import { useStores } from '@app/models'
import moment from 'moment'
import 'moment/locale/vi'
import { SCREENS } from '@app/navigation'

import { useAbortableEffect } from '@app/use-hooks'
import { useAuth } from '@app/use-hooks/use-auth'
import { Api } from '@app/services/api'
import { ButtonBack, TButton } from '@app/components'
import RenderHtml from 'react-native-render-html'
import { responsiveWidth } from 'react-native-responsive-dimensions'

import { ModalContext } from '@app/components/modal-success'
import LinearGradient from 'react-native-linear-gradient'
import { common, linearGradientProps } from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import { isAndroid } from '@app/utils/chat/deviceInfo'
import { getRemoveConfig } from '@app/utils/storage'

const api = new Api()

moment.locale('vi')
const { width } = Dimensions.get('window')

export const OpenCardScreen = observer((props:any) => {
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()

  const [isFetched, setIsFetched] = useState(true)
  const navigation = useNavigation()
  const { serviceStore, profileStore, notificationStore } = useStores()
  const { showSuccess, showError } = useContext(ModalContext)
  const { signOut } = useAuth() // should be signUp
  const [appId, setAppId] = useState('')
  const [appCode, setAppCode] = useState('')
  const [isProd, setIsProd] = useState(false)

  const [content, setContent] = useState<any>('')

  useEffect(() => {
    return () => {

    }
  }, [])

  const getApiData = async () => {
    return Promise.all([
      getApiConfig(),
      getRemoteConfig()
    ])
  }

  const getRemoteConfig = async () => {
    try {
      const config = await getRemoveConfig() // Assuming getRemoveConfig() is defined elsewhere
      setAppCode(config.pvcbAppCode)
      setAppId(config.pvcbAppId)
      setIsProd(config.pvcbIsProd)
    } catch (error) {
      console.error('Failed to fetch remote config:', error)
    }
  }
  const getApiConfig = async () => {
    const rs = await api.getAppConfig()
    __DEV__ && console.log('🚀 ~ file: open-card-screen.tsx ~ line 55 ~ getApiConfig ~ rs', rs)
    if (rs && rs?.data?.data.attributes) {
      setContent(rs?.data?.data?.attributes?.page_content_open_bank_card)
    }
  }

  const loadData = async () => {
    setIsFetched(true)
    getApiData().then(data => {
      setIsFetched(false)
    }).catch(err => {
      __DEV__ && console.log(err)
    })
  }

  const goLoginScreenRequired = () => {
    navigation.navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  useAbortableEffect(() => {
    loadData().then(r => {})
  }, [])

  const onAccept = () => {
    if (profileStore.isSignedIn()) {
      /**
       * Configuration for the client.
       * isPro: Production environment, default value is *true*.
       * appId: Partner identification code.
       * appCode: Partner identification code.
       * keyTracking: Partner identification code.
       * appleAppID: App Store is "id95728xxxx", appleAppID value is
       *95728xxxx*.
       */

      // TODO: tích hợp apk
      // navigate(SCREENS.webViewPvComBankScreen)
    } else {
      goLoginScreenRequired()
    }
  }

  return (
    <SafeAreaView style={{ flex: 1, marginTop: -4 }} edges={['right', 'left']}>
      {/* <StatusBar backgroundColor='transparent' barStyle={'dark-content'} /> */}
      {/* {renderHeader()} */}
      <Header
        // statusBarProps={{ barStyle: 'light-content' }}
        // barStyle="light-content" // or directly
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
        centerComponent={{ text: t('Chương trình mở tài khoản'), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
        //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
        // </TouchableOpacity> : null}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={{ marginHorizontal: 16, marginTop: 16 }}>
        {content ? <RenderHtml
          contentWidth={width}
          source={{ html: content }}
          ignoredTags={['script']}
          ignoredStyles={['font-family']}
          renderersProps={{
            img: {
              enableExperimentalPercentWidth: true
            }
          }}
        /> : null}
      </ScrollView>
      <View style={[styles.fixedButton, { paddingBottom: useSafeAreaInsets().bottom + (isAndroid ? 15 : 0) }]}>
        <TButton typeRadius={'rounded'} buttonStyle={{ width: responsiveWidth(100) - 32 }} titleStyle={{ fontSize: 16, fontWeight: '500', textTransform: 'uppercase' }}
          title={t('Đồng ý mở tài khoản')} onPress={onAccept} />
      </View>

    </SafeAreaView>
  )
})

export default withTranslation()(OpenCardScreen)
