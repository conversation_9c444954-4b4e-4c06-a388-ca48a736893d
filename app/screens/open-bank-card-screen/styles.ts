import { StyleSheet } from 'react-native'
import { getStatusBarHeight } from 'react-native-iphone-x-helper'

const styles = StyleSheet.create({

  fixedButton: {
    backgroundColor: '#fff',
    borderTopColor: '#E9E9E9',
    borderTopWidth: 1,
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 15,
    position: 'absolute',
    width: '100%'
  },
  header: {
    alignItems: 'center',
    borderBottomWidth: 1,
    borderColor: '#F3F3F3',
    flexDirection: 'row',
    justifyContent: 'center',
    paddingBottom: 20,
    paddingHorizontal: 16,
    paddingTop: 30
  },

  headerTitle: {
    color: '#333',
    fontSize: 16,
    fontWeight: '600'
  },

  label: {
    color: '#2D384C',
    marginVertical: 2
  },

  top: {
    marginTop: -(getStatusBarHeight()),
  },
  txtBtn: {
    color: '#3F3F3F',
    marginTop: 5
  }
})
export default styles
