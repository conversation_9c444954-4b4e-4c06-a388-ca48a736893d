import { Dimensions, StyleSheet } from 'react-native'
import { color, typography } from '@app/theme'
import { ifIphoneX } from 'react-native-iphone-x-helper'

const freeShipIconRatio = 55 / 14
const saleIconRatio = 38 / 14

const styles = StyleSheet.create({
  address: {
    fontFamily: typography.normal,
    marginLeft: 4,
    paddingHorizontal: 4
  },
  background: {
    backgroundColor: '#fff',
    flex: 1,
  },
  btnChooseDistrict: {
    // flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    borderRadius: 4,
    backgroundColor: color.primaryBackground,
    marginVertical: 10
  },
  btnFilter: {
    alignItems: 'center',
    flexDirection: 'row',
    marginLeft: 8,
  },
  customStar: {
    marginRight: 2,
    marginTop: 3
  },
  discount: {
    backgroundColor: color.primary,
    borderRadius: 5,
    padding: 3,
    position: 'absolute',
    right: 3,
    top: 3
  },
  discountViewRow: {
    bottom: 10,
    flexDirection: 'row',
    left: 10,
    position: 'absolute'
  },
  distance: {
    color: color.primary,
    fontSize: 12
  },
  district: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '400',
    marginLeft: 4,
    paddingLeft: 20,
    paddingVertical: 15
  },
  freeShipIc: {
    height: 18,
    marginRight: 4,
    width: 18 * freeShipIconRatio
  },
  icArrowBack: {
    justifyContent: 'flex-start',
    marginLeft: 10,
    marginTop: 2
  },
  imgIcon: {
    height: 24,
    marginRight: 8,
    width: 24
  },
  loadMore: {
    alignItems: 'center',
    marginTop: 10
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
    ...ifIphoneX({
      marginTop: 0
    }, {
      marginTop: 5
    }),
  },
  price: {
    color: '#f3373a',
    fontSize: 12,
    fontWeight: 'bold',
    marginTop: 6,
    // textAlign: 'center'
  },
  priceSale: {
    color: '#9D9D9D',
    fontSize: 12,
    fontWeight: '400',
    marginBottom: 5,
    marginLeft: 8,
    textAlign: 'left',
    textDecorationLine: 'line-through'
  },
  productImg: {
    // borderRadius: 5,
    height: '100%',
    resizeMode: 'cover',
    width: '100%'
  },
  productName: {
    color: '#333',
    fontSize: 13,
    height: 40,
    marginTop: 11,
    textAlign: 'center'
  },
  renderProduct: {
    backgroundColor: '#F7F7F7',
    borderRadius: 4,
    // borderTopLeftRadius: 4,
    // borderTopRightRadius: 4,
    minHeight: 340,
    marginBottom: 10,
    marginRight: 8,
    width: Dimensions.get('window').width / 2 - 20
  },
  renderStar: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  rspTopViewText: {
    color: '#333',
    fontSize: 14,
    fontWeight: '400',
    height: 35,
    marginLeft: 8,
    marginVertical: 10,
    textAlign: 'left',
    width: '90%'
  },
  rspTopViewTextPrice: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 5,
    marginLeft: 8,
    textAlign: 'left'
  },
  safeAreaView: {
    backgroundColor: color.primaryBackground,
    flex: 1,
    marginTop: -4
  },
  saleIcon: {
    height: 18,
    width: 18 * saleIconRatio
  },
  sectionRate: {
    // flexDirection: 'row',
    // justifyContent: 'space-between',
    marginHorizontal: 8
  },
  storeName: {
    color: '#999999',
    fontSize: 11,
    marginTop: 4,
    textAlign: 'center'
  },
  textAddress: {
    color: '#9D9D9D',
    fontSize: 12,
    marginVertical: 5
  },
  textBtnAdd: {
    alignItems: 'center',
    borderColor: color.primary,
    borderRadius: 3,
    borderWidth: 1,
    color: color.primary,
    justifyContent: 'center',
    margin: 6,
    marginBottom: 8,
    padding: 8,
    textAlign: 'center'
  },
  textClose: {
    color: color.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  textNonProduct: {
    color: '#333',
    flex: 1,
    fontSize: 14,
    marginTop: 30,
    textAlign: 'center'
  },
  textOldPrice: {
    color: '#9D9D9D',
    fontFamily: typography.normal,
    fontSize: 10,
    textDecorationLine: 'line-through'
  },
  textSaleOff: {
    color: color.primary,
    fontFamily: typography.normal,
    fontSize: 10,
    marginLeft: 8
  },
  textTitle: {
    color: '#333',
    flex: 1,
    fontSize: 14,
    marginLeft: 10,
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    marginRight: -20,
    textAlign: 'center'
  },
  titleBar: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 6
  },
  titleLabel: {
    color: '#979797',
    fontSize: 16,
    fontWeight: '500',
    textTransform: 'uppercase'
  },
  viewAddress: {
    // flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 8,
    // marginTop: 10,
    height: 40,
    borderWidth: 0.5,
    borderColor: '#90A4AE',
    borderRadius: 5,
    backgroundColor: '#fff',
    // alignSelf: 'center'
  },
  viewDistrict: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5
  },
  viewFilter: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
    paddingHorizontal: 16,
    paddingVertical: 10
  },
  viewIcon: {
    bottom: 2,
    position: 'absolute',
    right: 2
  },
  viewImage: {
    backgroundColor: '#f7f7f7',
    // borderRadius: 10,
    height: 170,
    padding: 4,
    width: '100%',
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4
  },
  viewItem: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 15,
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
})
export default styles
