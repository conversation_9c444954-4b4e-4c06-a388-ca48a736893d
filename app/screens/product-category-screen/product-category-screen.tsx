import { SafeAreaView } from 'react-native-safe-area-context'
import React, { useEffect, useRef, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { View, Text, FlatList, TouchableOpacity, Image } from 'react-native'
import { useStores } from '../../models/root-store'
import { useNavigation, useRoute } from '@react-navigation/native'
import { numberFormat } from '@app/utils/number'
import styles from './styles'
import { ButtonBack, EmptyData, PlaceHolder, LazyImage, FilterDistrict } from '@app/components'
import { useTranslation } from 'react-i18next'
import { SCREENS } from '@app/navigation/screens'
import { color } from '../../theme'
import Icon from 'react-native-vector-icons/Ionicons'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { Badge, Header } from 'react-native-elements'
import { PickerSelect } from '@app/components/picker-select/picker-select'
import { FilterProduct } from '@app/components/filter-product/filter-product'
import StarRating from 'react-native-star-rating'
import { freeShipIcon, icFilter, icSetting, icShoppingCart, saleIcon } from '@app/assets/images'
import { useAuth } from '@app/use-hooks/use-auth'
import { Modalize } from 'react-native-modalize'
import { responsiveHeight } from 'react-native-responsive-dimensions'
import { LogEvent } from '@app/services/loggingServices'
import { saveString } from '@app/utils/storage'
import LinearGradient from 'react-native-linear-gradient'

export const ProductCategoryScreen = observer(function ProductCategoryScreen() {
  const { t } : any = useTranslation()
  const { productStore, searchStore, profileStore } = useStores()
  const [loading, setLoading] = useState(false)
  const { navigate, goBack } = useNavigation()
  const route: any = useRoute()
  const { categoryId, title } = route.params
  const [data, setData] = useState([])
  const [refreshing, setRefreshing] = useState(false)
  const [loadMore, setLoadMore] = useState(false)
  const [page, setPage] = useState(1)
  const [isFetched, setIsFetched] = useState(true) // event view placeholder
  const [isPickerSelectVisible, setIsPickerSelectVisible] = useState(false)
  const [district, setDistrict] = useState(null)
  const [order, setOrder] = useState('')
  const [isOpenFilter, setIsOpenFilter] = useState(false)
  const [valueShipping, setValueShipping] = useState('')
  const [valuePrice, setValuePrice] = useState('')
  const [valueRate, setValueRate] = useState('')
  const [distance, setDistance] = useState(20)
  const [rateValueStar, setRateValueStar] = useState(0)
  const { shoppingCount } = useAuth()
  const modalizeRef = useRef<Modalize>(null)
  const [item, setItem] = useState(null)
  const [idChoose, setIdChoose] = useState(-1)
  const modalChooseDistrict = useRef<Modalize>(null)

  const values = [
    { label: 'Mới nhất', value: 'newest', id: 1 },
    { label: 'Bán chạy', value: 'sales', hidden: false, id: 2 },
    { label: 'Đánh giá', value: 'rate', hidden: false, id: 3 },
    { label: 'Giá từ thấp tới cao', value: 'price-asc', id: 4 },
    { label: 'Giá từ cao tới thấp', value: 'price-desc', id: 5 }
  ]

  // const filterProduct = [valuePrice || page || valueShipping || order || distance]

  useEffect(() => {
    loadData().then(r => {})
  }, [valuePrice, valueShipping, distance, order, page, valueRate])

  const onOpenFilter = async () => {
    setIsOpenFilter(true)
    await searchStore.loadProductFilter()
  }

  const loadData = async () => {
    const isLoadMore = page > 1
    if (!isLoadMore) {
      setIsFetched(true)
    }
    await productStore.getCategoryById(categoryId, page, order, isLoadMore, valueShipping, valuePrice, valueRate)
    setData(productStore.categoryById)
    setLoadMore(false)
    setRefreshing(false)
    setIsFetched(false)
  }

  const refreshData = async () => {
    await productStore.getCategoryById(categoryId, 1, order, false, valueShipping, valuePrice, valueRate)
    setLoadMore(false)
    setRefreshing(false)
    setIsFetched(false)
  }

  const handleLoadMore = () => {
    // if (!loadMore) return
    // khi scroll dừng lại sẽ gọi vào hàm này
    const totalPage = productStore.totalPage
    if (page < totalPage) {
      setPage(page + 1)
    }
    if (page === totalPage) {
      __DEV__ && console.log('No more data...')
      setLoadMore(false)
    }
  }

  const onRefresh = () => {
    // __DEV__ && console.log('onRefresh ', page)
    setRefreshing(true)
    if (page > 1) {
      setPage(1)
      // __DEV__ && console.log('onRefresh then ', page)
    } else {
      refreshData().then(r => {
      })
    }
  }

  const navigateDetails = (item) => {
    navigate(SCREENS.productDetails, { id: item._id })
  }

  const renderFooter = () => {
    const Spinner = require('react-native-spinkit')
    return loadMore === true ? (
      <View
        style={styles.loadMore}
      >
        <Spinner isVisible={true} size={40} type='ThreeBounce' color={color.primary}/>
      </View>
    ) : <Text style={{ padding: 16, color: color.primary, textAlign: 'center' }}>{t('No_more_data')}</Text>
  }

  const goLoginScreenRequired = () => {
    navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  const navigateCart = () => {
    if (profileStore.isSignedIn()) {
      navigate(SCREENS.cartScreen)
    } else {
      goLoginScreenRequired()
    }
  }

  const onOpen = () => {
    modalizeRef.current?.open()
  }

  const onClose = () => {
    modalizeRef.current?.close()
  }

  const onOpenModalDistrict = () => {
    modalChooseDistrict.current?.open()
  }
  const onCloseModalDistrict = () => {
    modalChooseDistrict.current?.close()
  }

  // const renderSanPham = ({ item, index }) => {
  //   return <TouchableOpacity
  //     onPress={() => { navigateDetails(item) }}
  //     style={styles.renderProduct}>
  //     <View style={styles.viewImage}>
  //       <LazyImage source={{ uri: item.picture }} style={styles.productImg} />
  //     </View>
  //     <View style={{ flex: 1 } }>
  //       <Text numberOfLines={1}
  //         ellipsizeMode="tail"
  //         style={styles.rspTopViewText}>{item.name}</Text>
  //       <Text
  //         style={styles.rspTopViewTextPrice}>{numberFormat(item.price)} đ</Text>
  //       {/* <Text numberOfLines={1} ellipsizeMode="middle" style={styles.rspTopViewTextSeller}>{item.storeName}</Text> */}
  //       <View style={styles.viewIcon}>
  //         <Icon name={'add-circle'} size={24} color={color.primary}/>
  //       </View>
  //     </View>
  //   </TouchableOpacity>
  // }

  const onChangeDistrict = async (value) => {
    try {
      LogEvent('user_select_district', value)
      await saveString('districtSelected', value)
      setPage(1)
      loadData()
      onCloseModalDistrict()
    } catch (error) {
      // Error saving data
    }
  }

  useEffect(() => {
    let isLoad = true
    if (isLoad) {
      if (district) {
        onChangeDistrict(district).then(r => {
          isLoad = false
        })
      }
    }
    return () => { isLoad = false }
  }, [district])

  const renderBtnChooseDistrict = () => {
    return (
      <View style={styles.viewDistrict}>
        <TouchableOpacity
          onPress={
            () => onOpenModalDistrict()
            // () => setIsPickerSelectVisible(true)
          }
          style={styles.btnChooseDistrict}>
          <Text style={styles.district}>{district || t('CHON_quanhuyen')}</Text>
          <Icon size={18} style={{ marginTop: 2, marginLeft: 5, marginRight: 20 }} name="chevron-down-outline"/>
        </TouchableOpacity>
      </View>
    )
  }

  const renderSanPham = ({ item, index }) => {
    const discount = (item.price - item.priceOld) / item.priceOld

    return <TouchableOpacity
      onPress={() => { navigateDetails(item) }}
      style={styles.renderProduct}>
      <View style={styles.viewImage}>
        <LazyImage source={{ uri: item.picture }} style={styles.productImg} />
        <View style={styles.discountViewRow}>
          {item.typeShip === 0 && <Image source={freeShipIcon} style={styles.freeShipIc}/>}
          {item.priceOld && <Image source={saleIcon} style={styles.saleIcon}/>}
        </View>
      </View>
      <View style={{ flex: 1 }}>
        <Text numberOfLines={2}
          ellipsizeMode="tail"
          style={styles.rspTopViewText}>{item.name}</Text>
        <View>
          <Text style={styles.rspTopViewTextPrice}>{numberFormat(item.price)} đ</Text>
          {item.priceOld && item.priceOld > 0 && <View style={{ flexDirection: 'row', marginLeft: 8 }}>
            <Text style={styles.textOldPrice}>{numberFormat(item.priceOld)} đ</Text>
            <Text style={styles.textSaleOff}>{numberFormat(discount * 100)}%</Text>
          </View>}
        </View>
        <View style={styles.sectionRate}>
          <Text style={styles.textAddress}>{item.storeName}</Text>
          {rateValueStar > 0 ? <View style={styles.renderStar}>
            <StarRating
              fullStarColor={'#FFC107'}
              disabled={true}
              maxStars={5}
              rating={rateValueStar}
              emptyStarColor={'#edf1f7'}
              emptyStar={'star'}
              fullStar={'star'}
              halfStar={'star-half-o'}
              iconSet={'FontAwesome'}
              starSize={10}
              // containerStyle={styles.startContainer}
              starStyle={styles.customStar}
              // selectedStar={(rating) => ratingCompleted(rating)}
            />
          </View> : <Text style={styles.textAddress}>Chưa có đánh giá</Text>}
        </View>
      </View>
      <TouchableOpacity onPress={() => { navigateDetails(item) }} style={{ justifyContent: 'center' }}>
        {/* <Text style={styles.textBtnAdd}>Thêm vào giỏ</Text> */}
        <Text style={styles.textBtnAdd}>Mua ngay</Text>
      </TouchableOpacity>
    </TouchableOpacity>
  }

  const onChangeItem = (item) => {
    setOrder(item.value)
    setPage(1)
    setTimeout(() => { onClose() }, 1000)
  }

  const renderFilter = () => {
    return (
      <View style={styles.viewFilter}>
        <TouchableOpacity
          onPress={onOpen}
          style={{ flexDirection: 'row' }}>
          <Image source={icSetting} style={styles.imgIcon}></Image>
          <Text style={{ alignSelf: 'center' }}>Sắp xếp: </Text>
          {item ? <Text style={{ alignSelf: 'center' }}>{item.label}</Text> : null}
        </TouchableOpacity>
        <View style={{ flexDirection: 'row' }}>
          <TouchableOpacity onPress={() => {
            onOpenFilter()
          }} style={styles.btnFilter}>
            {/* <Icon size={24} name={'filter'} color={'#f3373a'} /> */}
            <Image source={icFilter} style={styles.imgIcon}></Image>
            <Text style={{ alignSelf: 'center' }}>Lọc</Text>
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  const renderHeaderModalBranch = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onClose} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>{t('Sắp xếp')}</Text>
      {/* <TouchableOpacity */}
      {/*  onPress={() => { */}
      {/*    onClose() */}
      {/*  }} */}
      {/* > */}
      {/*  <Text style={styles.textClose}>{t('APDUNG')}</Text> */}
      {/* </TouchableOpacity> */}
      <View style={{ width: 20 }}></View>
    </View>
  )

  const renderChooseSetting = (item, index) => {
    return (
      <View key={index}>
        <TouchableOpacity style={styles.viewItem}
          onPress={() => {
            onChangeItem(item)
            setIdChoose(item.id)
            setItem(item)
          }}
        >{item.id === idChoose
            ? <Icon size={24} color = {color.primary} name = {'checkmark-circle'} />
            : <Icon size={24} color = {color.primary} name = {'ellipse-outline'} />}
          {item ? <Text style={{ fontWeight: item.id === idChoose ? 'bold' : 'normal', marginLeft: 10 }}>{item.label}</Text> : null}
        </TouchableOpacity>
      </View>
    )
  }

  const renderHeaderModalDistrict = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onCloseModalDistrict} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>{t('Quận huyện')}</Text>
      <TouchableOpacity
        onPress={() => {
          onChangeDistrict('')
        }}
      >
        <Text style={styles.textClose}>{t('FILTERALL')}</Text>
      </TouchableOpacity>
    </View>
  )

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <Header
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
        centerComponent={{ text: t(t(`${title}`)), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        rightComponent={<TouchableOpacity
          onPress={navigateCart}
          style={{ flexDirection: 'row' }}
        >
          {/* <Icon size={25} color={'#333'} name={'cart-outline'} /> */}
          <Image source={icShoppingCart} style={{ width: 26, height: 26 }}></Image>
          {/* {shoppingCount && shoppingCount > 0 ? <Badge */}
          {/*  value={shoppingCount} */}
          {/*  status="error" */}
          {/*  containerStyle={{ position: 'absolute', top: -8, right: -8 }} */}
          {/* /> : null} */}
          {shoppingCount && shoppingCount > 0 ? <Badge value={shoppingCount > 99 ? 99 + '+' : shoppingCount} status="error" containerStyle={{ marginLeft: -12, marginTop: -7 }} /> : null}
        </TouchableOpacity>}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      {renderFilter()}
      {renderBtnChooseDistrict()}
      {/* <DropDownPicker */}
      {/*  items={values} */}
      {/*  defaultValue={order} */}
      {/*  placeholder="Sắp xếp theo" */}
      {/*  containerStyle={{ height: 40, width: '50%', paddingHorizontal: 16, backgroundColor: '#fff' }} */}
      {/*  style={{ backgroundColor: '#fff' }} */}
      {/*  itemStyle={{ */}
      {/*    justifyContent: 'flex-start' */}
      {/*  }} */}
      {/*  dropDownStyle={{ backgroundColor: '#fff', marginHorizontal: 8 }} */}
      {/*  onChangeItem={item => onChangeItem(item) } */}
      {/* /> */}
      <View style={styles.background}>
        <View style={{ flex: 1, marginTop: 8, marginLeft: 16 }}>
          {isFetched ? <PlaceHolder/> : <View style={{ flex: 1 }}>{productStore.categoryById.length === 0 ? <EmptyData title={'Chưa có sản phẩm'} message={'Sản phẩm của danh mục này sẽ được cập nhật trong thời gian sớm nhất'}/>
            : <FlatList
              data={productStore.categoryById}
              numColumns={2}
              // contentContainerStyle={{ padding: 10 }}
              showsVerticalScrollIndicator={false}
              keyExtractor={(item, index) => index + 'productsdm'}
              renderItem={renderSanPham}
              ListFooterComponent={renderFooter}
              onEndReachedThreshold={0.4}
              onEndReached={handleLoadMore}
              onRefresh={onRefresh}
              initialNumToRender={10}
              refreshing={refreshing}
              extraData={productStore.categoryById}
              onScrollBeginDrag={e => {
                __DEV__ && console.log('onScrollBeginDrag')
                setLoadMore(true)
              }}
              onMomentumScrollEnd={handleLoadMore}
            />}</View>
          }
        </View>

        {/* <Search screenProps={{ context: this, ref: (search) => { this.search = search } }} /> */}

      </View>
      <PickerSelect data={searchStore.listDistrict} title={t('CHON_quanhuyen')} isVisible={isPickerSelectVisible} defaultValue={''} onSelect={(e) => {
        setIsPickerSelectVisible(!isPickerSelectVisible)
        setDistrict(e.label)
      }}
      callBackVisible={() => {
        setIsPickerSelectVisible(!isPickerSelectVisible)
      }}
      goBack={() => {
        setIsPickerSelectVisible(!isPickerSelectVisible)
      }}
      />
      {/* </Header> */}
      <FilterProduct isOpen={isOpenFilter}
        dataFilter={data} distance={(value) => {
          setDistance(value)
        }}
        valueShipping={(shipping) => {
          setValueShipping(shipping)
        }}
        valuePrice={(price) => {
          setValuePrice(price)
        }}
        valueRate={(rate) => {
          setValueRate(rate)
        }}
        page={(page) => {
          setPage(page)
        }}
        onClose={
          (value) => {
            if (value === true) {
              setIsOpenFilter(false)
            }
          }
        }
        rateProduct={searchStore.rateProduct}
        priceRange={searchStore.priceRange}
        shipping={searchStore.shipping}
      />
      <Modalize
        HeaderComponent={renderHeaderModalBranch}
        ref={modalizeRef}
        modalTopOffset={40}
        adjustToContentHeight
        // snapPoint={405}
        // modalHeight={responsiveHeight(90)}
        // onClosed={() => { props.onClose(true) }}
        keyboardAvoidingBehavior={'padding'}
      >
        <View style={{ padding: 16 }}>
          <Text style={styles.titleLabel}>Sắp xếp sản phẩm theo</Text>
          {values.map((item, index) => renderChooseSetting(item, index))}
        </View>
      </Modalize>
      <Modalize
        HeaderComponent={renderHeaderModalDistrict}
        ref={modalChooseDistrict}
        modalTopOffset={40}
        // adjustToContentHeight
        // snapPoint={405}
        modalHeight={responsiveHeight(60)}
        // onClosed={() => { props.onClose(true) }}
        keyboardAvoidingBehavior={'padding'}
      >
        <FilterDistrict data={searchStore.listDistrict} onSelect={(e) => {
          setDistrict(e.label)
        }}/>
      </Modalize>
    </SafeAreaView>
  )
})
