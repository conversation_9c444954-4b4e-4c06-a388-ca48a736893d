/* eslint-disable */
import { Dimensions, StyleSheet } from 'react-native'
import { responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import { ifIphoneX } from "react-native-iphone-x-helper"
import { color, spacing, typography } from '@app/theme'

const styles = StyleSheet.create({
  safeAreaView: {
    backgroundColor: "#fff",
    height: responsiveHeight(100),
    flex:1,
    marginTop: -4
  },
  background: {
    flex: 1,
    backgroundColor: "#fff",
    height: responsiveHeight(100),
    // ...ifIphoneX({
    //   paddingBottom: 89,
    // }, {
    //   paddingBottom: 60,
    // }),
  },
  iconViewMore: {
    color: color.primary,
    // fontSize: 14,
    // lineHeight: 14,
    marginTop: 2
  },
  textTop: {
    color: color.primary,
    fontSize: 12,
    justifyContent: 'flex-end',
    letterSpacing: 0,
    textAlign: 'right',
  },
  boxTop: {
    // backgroundColor: '#f2f2f2',
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingVertical: 10,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#E4E4E4',
    justifyContent: 'center',
    width: 100,
  },
  icArrowBack: {
    justifyContent: 'flex-start',
    marginLeft: 10,
    marginTop: 2
  },
  address: {
    marginLeft: 4,
    fontFamily: typography.normal,
    paddingHorizontal: 4
  },
  viewAddress: {
    // flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 8,
    marginTop: 10,
    height: 40,
    borderWidth: 0.5,
    borderColor: '#90A4AE',
    borderRadius: 5,
    backgroundColor: '#fff',
  },
  loadMore: {
    alignItems: 'center',
    marginTop: 10,
  },
  price: {
    color: '#CB1016',
    fontSize: 12,
    fontWeight: 'bold',
    marginTop: 6,
    textAlign: 'center'
  },
  productImg: {
    borderRadius: 5,
    height: '100%',
    resizeMode: 'cover',
    width: '100%'
  },
  productName: {
    color: '#333',
    fontSize: 13,
    height: 40,
    marginTop: 11,
    textAlign: 'center'
  },
  renderProduct: {
    alignItems: 'center',
    borderRadius: 4,
    justifyContent: 'flex-start',
    margin: 10,
    width: Dimensions.get('window').width / 2 - 30
  },
  storeName: {
    color: '#999999',
    fontSize: 11,
    marginTop: 4,
    textAlign: 'center'
  },
  textNonProduct: {
    color: '#333',
    flex: 1,
    fontSize: 14,
    marginTop: 30,
    textAlign: 'center'
  },
  textTitle: {
    color: '#333',
    flex: 1,
    fontSize: 14,
    marginLeft: 10,
  },
  titleBar: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 6
  },
  viewImage: {
    backgroundColor: '#fff',
    borderRadius: 5,
    height: 210,
    padding: 1,
    width: '100%'
  },
  containerItem: {
    flex: 1,
    backgroundColor: '#fff',
    // minHeight: 100,
    marginLeft: 8
  },
  icSearch: {
    height: 24,
    margin: 6,
    marginLeft: spacing.small,
    resizeMode: "contain",
    width: 24,
  },
  icMap: {
    margin: 6,
    marginLeft: 14,
    resizeMode: "contain",
    width: 14,
    height: 14,
  },
  input: {
    flex: 1,
  },
  renderTopSection: {
    flexDirection: "row",
    backgroundColor: "#fff",
    height: 65,
  },
  btnBack:{
    ...ifIphoneX({
      marginTop: 18,
    }, {
      marginTop: 20,
    }),
    marginLeft: spacing.small,
    width:24
  },

  inputStyle: {
    marginTop: 10,
    width: "80%",
    alignItems: "center",
    backgroundColor: "#fff",
    borderColor: "#F4F4F4",
    borderRadius: 25,
    borderStyle: "solid",
    borderWidth: 1,
    flexDirection: "row",
    height: 44,
    justifyContent: "center",
    marginLeft: spacing.small,
    shadowColor: "#F4F4F4",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10,
    elevation: 1
  },
  searchInput: {
    flex: 1,
    flexDirection: "row",
  },
  textExit: {
    justifyContent: "center",
    marginTop: 31,
    marginLeft: 3,
    fontSize: 14,
    fontWeight: "600",
  },
  btnFilter:{
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft:15,
    marginRight:15,
  },
  icClose: {
    width: 22,
    height: 22,
    margin: 10,
  },
  contentText: {
    flexDirection: "column",
    justifyContent: "flex-start",
    alignItems: "flex-start",
    flex: 1,
  },
  rdkTopText: {
    fontSize: 14,
    paddingTop: 5,
    paddingLeft: 8,
    paddingRight: 8,
    fontWeight: "600",
    fontFamily: typography.normal,
    color: "#333"
  },
  rdkTopText1: {
    fontSize: 13,
    color: "#8f9bb3",
    paddingRight: 40,
    fontFamily: typography.normal,
    marginTop: 5
  },
  rdkTopImage: {
    width: 100,
    height: 100,
    alignSelf: "center",
    resizeMode: "cover",
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
  },
  rdkTop: {
    flex: 1,
    flexDirection: "row",
    height: 100,
    width: "100%",
    backgroundColor: "#fff",
    borderRadius: 8,
    marginBottom: 15,
  },
  pointsStar: {
    marginLeft: 5,
    marginTop: 2,
    fontSize: 12,
    color: "#333"
  },
  kilometer: {
    alignItems: "flex-end",
    fontSize: 12,
    textAlign: "right",
    color: "#8f9bb3"
  },
  flatListContainer: {
    marginTop: 8 ,
    // marginBottom: 70,
    flex: 1,
    width: responsiveWidth(100),
    backgroundColor: '#fff',
  },
  viewBtnBack: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tabBar: {
    // Remove border top on both android & ios
    backgroundColor: '#fff',
    borderTopColor: 'transparent',
    borderTopWidth: 0,
    elevation: 0,
    shadowColor: '#5bc4ff',
    shadowOffset: {
      height: 0,
    },
    shadowOpacity: 0,
    shadowRadius: 0,
  },
  tabBarText: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
  },
  textTitle: {
    ...ifIphoneX({
      marginTop: 18,
    }, {
      marginTop: 20,
    }),
    fontSize:20,
    fontWeight:'bold',
    color: "#333"
  }
})

export default styles
