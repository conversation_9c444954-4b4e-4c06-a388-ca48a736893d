import React from 'react'
import { observer } from 'mobx-react-lite'
import { StoreListItem } from '@app/components'
import { TouchableOpacity, View, Text, FlatList } from 'react-native'
import styles from './styles'
import { useNavigation } from '@react-navigation/native'
import { SCREENS } from '@app/navigation'
import { useStores } from '@app/models'
import Icon from 'react-native-vector-icons/Ionicons'
import { useTranslation } from 'react-i18next'
import { color } from '@app/theme'

export const TabServicesRenderScreen = observer(function TabServicesRenderScreen(props: any) {
  const { t } : any = useTranslation()
  const { navigate } = useNavigation()
  const { type } = props
  const { searchStore } = useStores()

  const renderServices = ({ item }) => (
    <StoreListItem key={new Date().toString()} item={item} onPress={(e) => {
      navigate(SCREENS.serviceDetail, { id: item.storeId, type: type })
    }
    } />
  )

  const renderFooter = () => {
    return (
      <View style={{ flexDirection: 'row', justifyContent: 'center', paddingVertical: 15, marginBottom: 30 }}>
        { props.data && props.data.length > 0 && props.data ? <TouchableOpacity
          onPress={() => {
            navigate(SCREENS.search, { filterType: type })
            searchStore.setTypeSearch(type)
          }}
          style={styles.boxTop}>
          <Text style={styles.textTop}>{t('KHAMPHADV_xemtatca')}</Text>
          <Icon style={styles.iconViewMore} name={'chevron-forward-outline'}/>
        </TouchableOpacity> : <Text style={{ justifyContent: 'center', alignItems: 'center', fontSize: 14, color: color.dim }}>Không có dữ liệu</Text>}
      </View>
    )
  }

  return (
    <View style={styles.flatListContainer}>
      <View style={styles.containerItem}>
        <FlatList
          numColumns={2}
          // index={props.index}
          // listKey={moment().valueOf().toString()}
          // key={props.type}
          data={props.data}
          extraData={props.data}
          showsHorizontalScrollIndicator={false}
          renderItem={renderServices}
          keyExtractor={item => item.storeId.toString() + props.type}
          ListFooterComponent={renderFooter}
        />
      </View>
    </View>
  )
})
