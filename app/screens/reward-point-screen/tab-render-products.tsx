import React, { useContext, useEffect, useRef, useState } from 'react'
import { observer } from 'mobx-react-lite'
import {
  View, FlatList, TouchableOpacity, StyleSheet, Image, Dimensions
} from 'react-native'
import { ButtonBack, ConfirmDialog, RenderBranch, TButton, Text } from '@app/components'
// import { SCREENS } from '@app/navigation'
// import { useNavigation } from '@react-navigation/native'
import { color } from '@app/theme'
import { Api } from '@app/services/api'
import { ModalContext } from '@app/components/modal-success'
import { SCREENS } from '@app/navigation'
import { useTranslation } from 'react-i18next'
import { noImage } from '@app/assets/images'
import { useStores } from '@app/models'
import BottomSheet, { BottomSheetBackdrop, BottomSheetView } from '@gorhom/bottom-sheet'
import FastImage from 'react-native-fast-image'
import { useNavigation } from '@react-navigation/native'
import { responsiveWidth } from 'react-native-responsive-dimensions'

const { width } = Dimensions.get('window')
const api = new Api()

interface TabRenderProps {
  index: number
}

export const TabRenderProduct = observer((props: TabRenderProps) => {
  const { profileStore } = useStores()
  const { navigate, goBack } = useNavigation()
  const { showError, showSuccess, showCustomError, showCustomSuccess } = useContext(ModalContext)
  const [data, setData] = useState([])
  const { t } : any = useTranslation()
  const [isShowConfirm, setIsShowConfirm] = useState(false)
  const [item, setItem] = useState(null)
  const findDefaultIndex = profileStore.addressList.findIndex((x) => x.default === true)
  const [selectedAddressShip, setSelectedAddressShip] = useState(profileStore.addressList.length > 0 ? findDefaultIndex : -1)
  const [addressShipping, setAddressShipping] = useState(null)

  // ref
  const bottomSheetRef = useRef<BottomSheet>(null)

  useEffect(() => {
    getRewardPointProducts()
  }, [])

  const getRewardPointProducts = async () => {
    const rs = await api.getRewardPointProducts()
    if (rs && rs?.data?.data) {
      setData(rs?.data?.data)
    }
  }

  const buyProductWithPoint = async () => {
    if (addressShipping) {
      const rs = await api.buyProductWithPoint({ productId: item.id, shippingInfo: JSON.stringify(addressShipping) })
      if (rs && rs.temporary) {
        showError(t('FAIL'), t('CANNOT_CONNECT'))
      } else if (rs && rs.data.error) {
        showError(t('FAIL'), t(`${rs.data.message}`))
      } else {
        showSuccess(t('THANHCONG'), t('Đổi quà thành công'))
        bottomSheetRef?.current.close()
        await profileStore.getProfile()
        await profileStore.getUserPointHistory()
      }
    } else {
      showError(t('FAIL'), 'Vui lòng chọn địa chỉ nhận hàng')
    }
  }

  useEffect(() => {
    const addressList = profileStore.addressList
    if (addressShipping === null && selectedAddressShip === 0) {
      const find = addressList.find((x) => x.default === true)
      if (!find) {
        setAddressShipping(addressList[0])
      } else {
        setAddressShipping(find)
        setSelectedAddressShip(findDefaultIndex !== -1 ? findDefaultIndex : 0)
      }
    } else {
      setAddressShipping(addressList[selectedAddressShip])
    }
  }, [selectedAddressShip])

  useEffect(() => {
    bottomSheetRef?.current.expand()
  }, [item])

  const renderProduct = ({ item }) => {
    const imgUrl = item?.attributes?.image?.data?.attributes.url
    return (
      <TouchableOpacity
        style={styles.renderProduct}>
        <View style={styles.viewImage}>
          <Image source={imgUrl ? { uri: imgUrl } : noImage} style={styles.imageSize} />
        </View>
        <View style={{ flex: 1 }}>
          <Text numberOfLines={2}
            ellipsizeMode="tail"
            style={styles.rspTopViewText}>{item?.attributes.productName}</Text>
          <Text style={styles.txtPoint}>{item?.attributes.point} điểm</Text>

        </View>
        <TouchableOpacity
          onPress={() => {
            setItem(item)
            __DEV__ && console.log(item)
            // setIsShowConfirm(true)
            // setTimeout(() => {
            //   bottomSheetRef?.current.expand()
            // }, 100)
          }}
          style={{ justifyContent: 'center' }}>
          <Text style={styles.textBtnAdd}>Đổi ngay</Text>
        </TouchableOpacity>
      </TouchableOpacity>
    )
  }

  return (
    <View key={props.index} style={{ flex: 1, paddingLeft: 8, backgroundColor: '#fff' }}>
      <FlatList
        style={{ backgroundColor: '#fff' }}
        numColumns={2}
        data={data}
        renderItem={renderProduct}
        // index={props.index}
        // ListHeaderComponent={props.header}
        keyExtractor={(item, index) => item._id + index.toString()}
      />
      <ConfirmDialog confirmText={t('DONGY')} cancelText={t('CANCEL')} isVisible={isShowConfirm} message={'Yêu cầu xác nhận đổi quà'} title={'Xác nhận'}
        onConfirm={() => {
          setIsShowConfirm(false)
          setTimeout(() => { buyProductWithPoint() }, 100)
        }}
        onClosed={
          () => setIsShowConfirm(false)
        }
      />
      <BottomSheet
        index={-1}
        ref={bottomSheetRef}
        snapPoints={['95%']}
        enablePanDownToClose={true}
        // backdropComponent={() => <View style={{ flex: 1, backgroundColor: '#333', width: 200, height: 200 }}/>}
        backdropComponent={(props) => (
          <BottomSheetBackdrop
            {...props}
            appearsOnIndex={0}
            disappearsOnIndex={-1}
          />
        )}
      >
        <BottomSheetView style={{ flex: 1 }}>
          <View style={{ marginBottom: 1 }}>
            <ButtonBack onPress={() => { bottomSheetRef?.current.close() }} style={{ position: 'absolute', left: 16, top: 0, zIndex: 99999 }} />
            <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: 10 }}>
              <Text style={{ color: '#333', fontSize: 14 }}>Thông tin quà tặng </Text>
            </View>
            <View style={styles.rSp}>
              <View style={styles.rSpView}>
                <FastImage source={{ uri: item?.attributes.image?.data?.attributes?.url }}
                  style={styles.rSpViewImage} />
              </View>
              <View style={styles.rSpView1}>
                <Text style={styles.rSpView1Text}>{item?.attributes.productName}</Text>
                <View style={{ flexDirection: 'row' }}>
                  <Text style={styles.rSpView1Text1}>{item?.attributes.description}</Text>
                </View>
                <View style={{ flexDirection: 'row' }}>
                  <Text style={styles.rSpView1Text2}>Số điểm: {item?.attributes.point}</Text>
                </View>
              </View>
            </View>
          </View>
          <View style={styles.viewShippingAddress}>
            <View style={styles.viewTextLabelAddressUser}>
              <View style={styles.containerAddressUS}>
                <Text style={styles.titleLabel}>{t('Địa chỉ nhận hàng')}</Text>
                <Text onPress={() => { navigate(SCREENS.renderAddAddress) }} style={styles.textAddaddress}>{t('ADDADDRESS')}</Text>
              </View>
            </View>
            <View>
              <RenderBranch dataBranch={profileStore.addressList} selectedAddressIndex={(selectedAddressUserIndex) => {
                setSelectedAddressShip(selectedAddressUserIndex)
              }} refreshAddress={profileStore.refreshAddress}/>
            </View>
          </View>
          <View>
            <Text style={{ color: '#333', fontSize: 14, paddingHorizontal: 16 }}>Bạn có chắc muốn đổi quà không? Nếu đồng ý hệ thống sẽ thực hiện trừ điểm tương ứng với món quà này và không được hoàn lại điểm hay đổi món quà khác!</Text>
            <Text style={{ color: '#333', fontSize: 14, paddingHorizontal: 16, marginTop: 10 }}>Quà tặng chưa bao gồm phí vận chuyển Bạn sẽ phải thanh toán phí vận chuyển theo quy định của đối tác vận chuyển.</Text>
          </View>
          <View style={styles.fixedButton}>
            <TButton typeRadius={'rounded'} buttonStyle={{ width: (responsiveWidth(100) - 45) / 2, backgroundColor: color.primaryBackground }} titleStyle={{ fontSize: 16, fontWeight: '500', color: '#333' }}
              title={t('Không')} onPress={() => {
              bottomSheetRef?.current.close()
              }}/>
            <TButton typeRadius={'rounded'} buttonStyle={{ width: (responsiveWidth(100) - 45) / 2 }} titleStyle={{ fontSize: 16, fontWeight: '500' }}
              title={t('Đồng ý')} onPress={buyProductWithPoint} />
          </View>
        </BottomSheetView>
      </BottomSheet>
    </View>
  )
})

const styles = StyleSheet.create({
  containerAddressUS: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
  },

  fixedButton: {
    backgroundColor: '#fff',
    borderTopColor: '#E9E9E9',
    borderTopWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 30,
    padding: 15
  },
  imageSize: {
    height: 170,
    width: '100%'
  },
  rSp: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    paddingBottom: 20,
    paddingHorizontal: 16,
    paddingTop: 15
  },

  rSpView: {
    alignItems: 'center',
    backgroundColor: '#f3f3f3',
    height: 82,
    justifyContent: 'center',
    padding: 2,
    width: 82
  },
  rSpView1: {
    flex: 1,
    paddingLeft: 20
  },
  rSpView1Text: {
    color: '#333',
    fontSize: 14,
    marginBottom: 10
  },
  rSpView1Text1: {
    color: '#333',
    fontSize: 13,
    marginTop: 10
  },
  rSpView1Text2: {
    color: color.primary,
    fontSize: 13,
    marginTop: 12,
  },
  rSpViewImage: {
    height: '100%',
    resizeMode: 'contain',
    width: '100%'
  },
  renderProduct: {
    backgroundColor: '#F7F7F7',
    borderRadius: 4,
    marginHorizontal: 4,
    marginVertical: 5,
    width: (width - 32) / 2,
  },
  rspTopViewText: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    margin: 8,
    textAlign: 'left',
    width: '90%'
  },
  textAddaddress: {
    color: color.primary,
    // fontFamily: typography.normal,
    fontSize: 14,
    textAlign: 'right',
  },
  textBtnAdd: {
    alignItems: 'center',
    borderColor: color.primary,
    borderRadius: 3,
    borderWidth: 1,
    color: color.primary,
    justifyContent: 'center',
    margin: 6,
    marginBottom: 8,
    padding: 8,
    textAlign: 'center'
  },
  titleLabel: {
    color: '#979797',
    fontSize: 14,
    fontWeight: '500',
    textTransform: 'uppercase'
  },
  txtPoint: {
    color: '#333',
    fontWeight: '700',
    marginHorizontal: 8
  },
  viewImage: {
    borderRadius: 2,
    height: 170,
    padding: 4,
    resizeMode: 'cover',
    width: '100%'
  },
  viewShippingAddress: {
    backgroundColor: '#fff',
    marginBottom: 5,
    paddingHorizontal: 16
  },
  viewTextLabelAddressUser: {
    alignItems: 'center',
    // backgroundColor: '#edf1f7',
    flexDirection: 'row',
    // height: 30,
    marginTop: 15
  },
})
