import React, { useContext, useState, useEffect, useRef, useCallback } from 'react'
import { observer } from 'mobx-react-lite'
import {
  View, StyleSheet, Dimensions, FlatList, TouchableOpacity
} from 'react-native'
import { EmptyData, Text } from '@app/components'
import { Api } from '@app/services/api'
import { useTranslation } from 'react-i18next'
import { ModalContext } from '@app/components/modal-success'
import { formatDateTime } from '@app/utils/time'
import { useStores } from '@app/models'
import BottomSheet, { BottomSheetBackdrop } from '@gorhom/bottom-sheet'
import FastImage from 'react-native-fast-image'
import { color } from '@app/theme'

const { width } = Dimensions.get('window')

const api = new Api()

export const TabHistory = observer((props: any) => {
  const { t } : any = useTranslation()
  const { showError, showSuccess } = useContext(ModalContext)
  const { profileStore } = useStores()
  const [item, setItem] = useState(null)
  // ref
  const bottomSheetRef = useRef<BottomSheet>(null)
  // callbacks
  const handleSheetChanges = useCallback((index: number) => {
    console.log('handleSheetChanges', index)
  }, [])

  useEffect(() => {
    getData()
  }, [])

  const getData = async () => {
    await profileStore.getUserPointHistory()
  }

  const renderItem = ({ item }) => {
    return (
      <TouchableOpacity onPress={() => {
        __DEV__ && console.log('renderItem', item.data)
        if (item?.data) {
          setItem(JSON.parse(item?.data || '{}'))
          bottomSheetRef?.current.expand()
        }
      }} style={styles.itemView}>
        <View style={styles.line}>
          <Text style={styles.date}>{formatDateTime(item.createAt)}</Text>
          <Text style={{ color: item.point > 0 ? '#4E9F3D' : '#D42023' }}>{item.point} điểm</Text>
        </View>
        <Text style={{ color: '#2D384C' }}>{item.note}</Text>
      </TouchableOpacity>
    )
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#fff' }}>
      { profileStore.pointHistory && profileStore.pointHistory.length > 0 ? <FlatList
        data={profileStore.pointHistory}
        extraData={profileStore.pointHistory}
        showsHorizontalScrollIndicator={false}
        renderItem={renderItem}
        keyExtractor={item => item._id}/> : <EmptyData title={t('Bạn chưa có điểm thưởng')} message={t('Nhận điểm thưởng khi bạn thực hiện giao dịch trên ứng dụng.')}/> }
      <BottomSheet
        index={-1}
        ref={bottomSheetRef}
        snapPoints={['50%']}
        enablePanDownToClose={true}
        onChange={handleSheetChanges}
        // backdropComponent={() => <View style={{ flex: 1, backgroundColor: '#333', width: 200, height: 200 }}/>}
        backdropComponent={(props) => (
          <BottomSheetBackdrop
            {...props}
            appearsOnIndex={0}
            disappearsOnIndex={-1}
          />
        )}
      >

        {item && <View style={{ flex: 1 }}>
          <View style={{ marginBottom: 1 }}>
            <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: 10 }}>
              <Text style={{ color: color.primary, fontSize: 14 }}>Bạn đã đổi điểm lấy sản phẩm</Text>
            </View>
            <View style={styles.rSp}>
              <View style={styles.rSpView}>
                <FastImage source={{ uri: item?.image?.data?.attributes?.url }}
                  style={styles.rSpViewImage} />
              </View>
              <View style={styles.rSpView1}>
                <Text style={styles.rSpView1Text}>{item?.productName} </Text>
                <View style={{ flexDirection: 'row' }}>
                  <Text style={styles.rSpView1Text1}>{item?.description}</Text>
                </View>
                <View style={{ flexDirection: 'row' }}>
                  <Text style={styles.rSpView1Text2}>Số điểm: {item?.point}</Text>
                </View>
              </View>
            </View>
          </View>
        </View>}
      </BottomSheet>
    </View>
  )
})

const styles = StyleSheet.create({
  date: {
    color: '#9D9D9D'
  },
  itemView: {
    borderBottomWidth: 0.5,
    borderColor: '#F3F3F3',
    padding: 16
  },
  line: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5
  },
  rSp: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    paddingBottom: 20,
    paddingHorizontal: 16,
    paddingTop: 15
  },
  rSpView: {
    alignItems: 'center',
    backgroundColor: '#f3f3f3',
    height: 82,
    justifyContent: 'center',
    padding: 2,
    width: 82
  },
  rSpView1: {
    flex: 1,
    paddingLeft: 20
  },
  rSpView1Text: {
    color: '#333333',
    fontSize: 14,
    marginBottom: 10
  },
  rSpView1Text1: {
    color: '#333',
    fontSize: 13,
    marginTop: 10
  },
  rSpView1Text2: {
    color: color.primary,
    fontSize: 13,
    marginTop: 12,
  },
  rSpViewImage: {
    height: '100%',
    resizeMode: 'contain',
    width: '100%'
  },
})
