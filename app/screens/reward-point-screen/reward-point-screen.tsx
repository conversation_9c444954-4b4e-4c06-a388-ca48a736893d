import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import React, { useState, useEffect, useRef, useContext } from 'react'
import {
  View,
  Text,
  ImageBackground,
  Dimensions,
  StatusBar,
} from 'react-native'

import styles from './styles'
import { useTranslation, withTranslation } from 'react-i18next'
import { useNavigation } from '@react-navigation/native'
import { Modalize } from 'react-native-modalize'
import { observer } from 'mobx-react-lite'
import { useStores } from '@app/models'
import moment from 'moment'
import 'moment/locale/vi'
import {
  ButtonBack,

} from '../../components'
import { ModalContext } from '@app/context'

import { useAbortableEffect } from '@app/use-hooks'

import { TabBar, TabView } from 'react-native-tab-view'
import { color } from '@app/theme'
import { Api } from '@app/services/api'
import { TabRenderProduct } from './tab-render-products'
import { TabHistory } from './tab_history'

const api = new Api()

const { width, height } = Dimensions.get('window')

const imgHeight = 190 * (width / 375)

moment.locale('vi')

const routes = [
  { key: 'shop', title: 'Đổi quà' },
  { key: 'history', title: 'Lịch sử điểm thưởng' }
]

export const RewardPointScreen = observer((props:any) => {
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()
  const modalizeAddService = useRef<Modalize>(null)
  const contentRef = useRef(null)
  const [refreshing] = useState(false)
  const [isFetched, setIsFetched] = useState(true)
  const { serviceStore, bookingStore, profileStore, productStore, notificationStore } = useStores()
  const { showError } = useContext(ModalContext)
  const [index, setIndex] = useState(0)
  //   const [routes, setRoutes] = useState([])
  __DEV__ && console.log('🚀 ~ file: reward-point-screen.tsx ~ line 77 ~ RewardPointScreen ~ routes', routes)
  // const { user, fetchUserData } = useContext(WebSocketContext)

  const [bgImageUrl, setBgImageUrl] = useState<any>('')

  useEffect(() => {
    return () => {

    }
  }, [])

  const getApiData = async () => {
    return Promise.all([
      getApiConfig(),
      profileStore.getProfile()
    ])
  }

  const getApiConfig = async () => {
    const rs = await api.getAppConfig()

    if (rs && rs?.data?.data.attributes?.bg_header_user_point.data) {
      setBgImageUrl(rs?.data?.data?.attributes?.bg_header_user_point.data.attributes.url)
    }
  }

  const loadData = async () => {
    setIsFetched(true)
    getApiData().then(data => {
      setIsFetched(false)
      // serviceStore.setTypeBooking(3)
    //   tabNames() // calculate tab name
    }).catch(err => {
      __DEV__ && console.log(err)
    })
  }

  const onCloseAddService = () => {
    modalizeAddService.current?.close()
  }

  useAbortableEffect(() => {
    // if (!user) {
    // nếu chưa sync kịp thì cố tình gọi check xem, không có token nghĩa là những user ở version cũ thì cần đăng nhập lại
    // fetchUserData().then(rs => {
    //   if (!rs || !rs.authToken) {
    //     logOut()
    //   }
    // }).catch(e => {
    //   logOut()
    // })
    // }
    loadData().then(r => {})
  }, [])

  useEffect(() => {
    if (bookingStore.bookingStatus) {
      onCloseAddService()
    }
  }, [bookingStore.bookingStatus])

  useAbortableEffect(() => {
    if (serviceStore.isLastestComments) {
      loadData().then(r => {})
    }
  }, [serviceStore.isLastestComments])

  const renderHeader = () => {
    const currentYear = new Date().getFullYear()
    __DEV__ && console.log('url point', bgImageUrl)
    return (<View style={styles.top}>
      { bgImageUrl && <ImageBackground style={{ width: width, height: imgHeight }} source={{ uri: bgImageUrl }}>
        <View style={styles.header}>
          <ButtonBack style={{ color: '#fff' }} onPress={goBack}/>
          <Text style={styles.headerTitle}>Điểm thưởng   </Text>
          <View/>
        </View>
        <View style={{ alignItems: 'center', marginTop: 15 }}>
          <Text style={{ color: 'white' }}>Số điểm hiện có</Text>
          <Text style={styles.balance}>{ profileStore.point }<Text style={{ fontSize: 24 }}> điểm</Text></Text>
          <Text style={{ color: 'white' }}>Điểm sẽ hết hạn vào ngày 31/12/{currentYear}</Text>
        </View>
      </ImageBackground>}
    </View>
    )
  }

  const renderTabBar = (props: any) => {
    return <TabBar
      {...props}
      inactiveColor={'#333'}
      activeColor={'red'}
      indicatorStyle={{ backgroundColor: color.primary }}
      // labelStyle={{
      //   textTransform: '',
      // }}
      style={{
        backgroundColor: '#fff',
      }}
    />
  }

  /**
   * chú ý nếu gặp bug scroll bị hở khoảng trắng cần kiểm tra index
   * @param route
   */
  const renderScene = ({ route }) => {
    switch (route.key) {
      case 'shop':
        return <TabRenderProduct
          index={1}
          key={'product'}
        //   header={() => renderHeaderFlatList(t('SANPHAM'), [listProducts])}
        />
      case 'history':
        return <TabHistory
          index={2}
          key={'history'}
        //   onSelect={({ type, item }) => {
        //     handlerOpenModal(type, item)
        //     }}
        />

      // case 'review':
      //   return <TabRenderReviewScreen index={4} onPress={() => gotoTotalComment()}/>
      default:
        return null
    }
  }

  return (
    <SafeAreaView style={{ flex: 1, marginTop: -useSafeAreaInsets().top + 20 }} edges={['right', 'top', 'left']}>
      <StatusBar backgroundColor='transparent' barStyle={'dark-content'} />
      <View style={{ backgroundColor: color.primaryBackground, flex: 1 }}>
        {renderHeader()}
        <TabView
          navigationState={{ index, routes }}
          renderScene={renderScene}
          onIndexChange={setIndex}
          initialLayout={{ width: width }}
          renderTabBar={renderTabBar}
        />

        {/* <CollapsibleHeaderTabView
          renderScrollHeader={() => renderHeader()}
          navigationState={{ index, routes }}
          renderScene={renderScene}
          headerHeight={310}
          onIndexChange={setIndex}
          initialLayout={{ width: Dimensions.get('window').width }}
          renderTabBar={renderTabBar}
        /> */}
      </View>

    </SafeAreaView>
  )
})

export default withTranslation()(RewardPointScreen)
