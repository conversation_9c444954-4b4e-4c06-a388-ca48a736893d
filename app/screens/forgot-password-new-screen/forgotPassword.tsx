import { SafeAreaView } from 'react-native-safe-area-context'
import { Text, View, } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import styles from './styles'
import { observer } from 'mobx-react-lite'
import { useStores } from '@app/models'
import { useNavigation } from '@react-navigation/native'
import { SCREENS } from '@app/navigation'
import { useTranslation } from 'react-i18next'
import { TButton, TTextInput, ButtonBack } from '../../components'
import validate from 'validate.js'
import Icon from 'react-native-vector-icons/Ionicons'

import { ModalContext } from '@app/context'

export const ForgotPassword = observer(() => {
  const { t } : any = useTranslation()
  const { accountStore } = useStores()
  const { navigate, goBack } = useNavigation()
  const { showError, showSuccess } = useContext(ModalContext)
  const [isSubmitting, setSubmitting] = useState(false)

  useEffect(() => {
    accountStore.reset()
  }, [])

  function onChangeText(phone) {
    accountStore.setPhoneNumber(phone)
  }

  const validateFields = () => validate.isEmpty(accountStore.phoneNumber)

  async function handlePhoneAuth() {
    const rs = await accountStore.forgotYourPassword()
    if (rs && !rs.data.error) {
      navigate(SCREENS.confirmCode, { phoneNumber: accountStore.phoneNumber, typeConfirm: 'recover' })
    } else {
      // return Alert.alert(rs.data.message)
      showError(t('FAIL'), t('SO_DT_KHONGCO_TRONG_HETHONG'))
    }
  }
  function onBack() {
    goBack()
  }

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <View style={styles.container}>
        <ButtonBack onPress={onBack}/>
        <View style={styles.content}>
          <View>
            <View>
              <Text numberOfLines={2} style={styles.textTitle}>{t('TIM_LAI_MK')}</Text>
            </View>
            <View style={styles.viewTextContent}>
              <Text numberOfLines={2} style={styles.text}>{t('ENTER_PHONE_NUMBER_TO_RECOVER_PASSWORD')}</Text>
            </View>
            <View style={styles.mainTextInput}>
              <TTextInput
                typeInput={'phone'}
                keyboardType="numeric"
                maxLength={12}
                autoCapitalize={'none'}
                placeholder={t('MOBILE')}
                value={accountStore.phoneNumber || ''}
                onChangeText={onChangeText}
                iconRightClick={() => { accountStore.setPhoneNumber('') }}
                iconRight={accountStore?.phoneNumber?.length > 0 ? <Icon
                  name='close-circle'
                  size={24}
                  color='#c5cee0'
                /> : null }
              />
            </View>
            <View style={styles.buttonContainer}>
              <TButton disabled={validateFields() || isSubmitting} loading={isSubmitting} title={t('TIEP_THEO')} onPress={() => handlePhoneAuth()} />
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  )
})
