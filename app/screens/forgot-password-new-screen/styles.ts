import { StyleSheet } from 'react-native'
import { typography } from '../../theme'

const styles = StyleSheet.create({
  buttonContainer: {
    marginTop: 30,
  },
  buttonLogin: {
    alignItems: 'center',
    backgroundColor: '#ff8ba1',
    borderRadius: 22,
    flexDirection: 'row',
    height: 44,
    justifyContent: 'center',
  },
  buttonLoginText: {
    color: '#ffffff',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold',
    height: 20,
    textAlign: 'center',
  },
  container: {
    backgroundColor: '#fff',
    flex: 1,
    justifyContent: 'center',
    textAlign: 'left',
  },
  content: {
    flex: 1,
    marginLeft: 30,
    marginRight: 30,
  },
  icArrowBack: {
    margin: 11,
  },
  mainTextInput: {
    alignItems: 'center',
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  text: {
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginBottom: 40,
    marginTop: 12,
    textAlign: 'left',

  },
  textInput: {
    backgroundColor: '#ffffff',
    borderColor: '#edf1f7',
    borderRadius: 22,
    borderStyle: 'solid',
    borderWidth: 1,
    height: 44,
    marginTop: 40,
    paddingLeft: 14,
    width: '100%',
  },
  textTitle: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
    letterSpacing: 0,
    marginTop: 16,
  },
  viewTextContent: {
    alignItems: 'center',
    flexDirection: 'row',

  },
})
export default styles
