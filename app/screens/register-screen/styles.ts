import { StyleSheet } from 'react-native'

const styles = StyleSheet.create({
  btnClose: {
    alignItems: 'center',
    backgroundColor: '#ff8ba1',
    borderRadius: 22,
    flexDirection: 'row',
    height: 44,
    justifyContent: 'center',
    width: 140,
  },
  buttonContainer: {
    marginTop: 50,
  },
  buttonDangky: {
    color: '#000811',
    fontSize: 14,
    marginLeft: 5,
    marginTop: 12,
    paddingTop: 5,
    textAlign: 'left',
  },
  buttonLoginText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  container: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flex: 1,
    justifyContent: 'space-between',
    marginHorizontal: 30
  },
  content: {
    flex: 1,
    marginTop: 10,
    width: '100%'
  },
  contentErr: {
    color: '#333',
    fontSize: 14,
    textAlign: 'center'
  },
  contentTitle: {
    color: '#333',
    fontSize: 22,
    fontWeight: 'bold',
    textAlign: 'center'
  },
  icArrowBack: {
    margin: 11,
  },
  logoRed: {
    height: 75,
    marginTop: 40,
    resizeMode: 'contain',
    width: 75,
  },
  mainTextInput: {
    alignItems: 'center',
    marginTop: 40,
  },
  modal: {
    alignItems: 'center',
    backgroundColor: 'white',
    borderColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 8,
    flexDirection: 'column',
    height: 300,
    justifyContent: 'space-between',
    marginTop: 220,
    padding: 22
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  subTitle: {
    color: '#46474D',
    fontSize: 14,
    marginTop: 12,
    paddingTop: 5,
    textAlign: 'center',
  },
  text: {
    color: '#46474D',
    fontSize: 14,
    marginTop: 12,
    textAlign: 'left',

  },
  textClose: {
    color: '#fff'
  },
  textTitle: {
    color: '#333',
    fontSize: 26,
    fontWeight: 'bold',
    letterSpacing: 0,
    marginTop: 15,
  },
  viewLogo: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 30,
    paddingRight: 30,
  },
})
export default styles
