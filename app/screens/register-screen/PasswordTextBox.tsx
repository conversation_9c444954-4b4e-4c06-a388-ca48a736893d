import React from 'react'
import { TextInput, View, StyleSheet } from 'react-native'
import Icon from 'react-native-vector-icons/Ionicons'
import { color } from '@app/theme'

export const Password = props => {
  const [value, onChangeText] = React.useState(props.value)
  const [visible, setVisibility] = React.useState(false)
  const [errorStatus, displayErrors] = React.useState(false)

  const icon = !visible ? 'eye-outline' : 'eye-off-outline'
  const color = !visible ? '#c5cee0' : '#18203a'

  return (
    <View style={styles.passwordContainer}>
      <TextInput
        style={styles.inputStyle}
        onChangeText={text => {
          onChangeText(text)
          props.onChange(text)
        }}
        onBlur={() => {
          displayErrors(true)
        }}
        defaultValue={value}
        placeholder={props.label}
        secureTextEntry={!visible}
      />

      <Icon
        size={22}
        name={icon}
        color={color}
        onPress={() => setVisibility(!visible)}
        style={styles.iconEye}
      />
    </View>
  )
}
Password.defaultProps = {
  label: '',
  height: (20),
}
const styles = StyleSheet.create({
  iconEye: {
    height: 24,
    marginRight: 15,
    width: 24,

  },
  inputStyle: {
    flex: 1,
  },
  passwordContainer: {
    alignItems: 'center',
    backgroundColor: color.primaryBackground,
    // borderColor: 'transparent',
    borderRadius: 4,
    // borderStyle: 'solid',
    // borderWidth: 1,
    flexDirection: 'row',
    height: 44,
    justifyContent: 'space-between',
    marginTop: 20,
    paddingLeft: 15,
    width: '100%'
  },
})
