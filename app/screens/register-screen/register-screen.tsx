import {
  Image,

  Text,
  View,
  ScrollView
} from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import styles from './styles'
import { observer } from 'mobx-react-lite'
import { useNavigation } from '@react-navigation/native'
import { useStores } from '@app/models'
import { TButton, TTextInput, ButtonBack } from '../../components'
import { SCREENS } from '@app/navigation'
import { logoMypet } from '@app/assets/images'
import { useTranslation } from 'react-i18next'
import { ModalContext } from '@app/context'
import validate from 'validate.js'
import Icon from 'react-native-vector-icons/Ionicons'
import { useAuth } from '@app/use-hooks/use-auth'
import { SafeAreaView } from 'react-native-safe-area-context'
// import { GoogleSignin } from 'react-native-google-signin'
// import signIn = GoogleSignin.signIn

export const RegisterScreen = observer((props: any) => {
  const { accountStore, profileStore } = useStores()
  const { navigate, goBack } = useNavigation()
  const { t } : any = useTranslation()
  const { data, registerType } = props?.route?.params || {}
  const { showError, showSuccess } = useContext(ModalContext)
  const [isSubmitting, setSubmitting] = useState(false)
  const { signUp, signIn } = useAuth()

  const validateFields = () => validate.isEmpty(accountStore.phoneNumber)

  useEffect(() => {
    accountStore.clearFields()
  }, [])

  async function handlePhoneAuth() {
    setSubmitting(true)
    if (!accountStore.phoneNumber) {
      showError(t('FAIL'), t('PHONE_NUMBER_CANNOT_BE_EMPTY'))
      setSubmitting(false)
    }
    if (accountStore.phoneNumber && accountStore.phoneNumber?.length >= 12) {
      // call api check phone exist
      const rs = await accountStore.forgotYourPassword()
      // error true tai khoan khong ton tai
      if (rs && rs.kind === 'cannot-connect') {
        setSubmitting(false)
        showError(t('FAIL'), 'Không thể kết nối dữ liệu')
        setSubmitting(false)
      } else if (rs && rs.data.error) {
        navigate(SCREENS.confirmCode, { phoneNumber: accountStore.phoneNumber, typeConfirm: 'register', data: data })
        setSubmitting(false)
      } else {
        showError(t('FAIL'), t('PHONE_NUMBER_ALREADY_REGISTERED'))
        setSubmitting(false)
      }
    } else {
      showError(t('FAIL'), t('PHONE_NUMBER_INCORRECT_FORMAT'))
      setSubmitting(false)
    }
  }

  function onChangeText(phone) {
    accountStore.setPhoneNumber(phone)
  }

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <ScrollView showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
        <ButtonBack onPress={goBack} style={styles.icArrowBack}/>
        <View style={styles.container}>
          <View style={styles.content}>
            <View>
              <View>
                {registerType == 'FB' ? <Text numberOfLines={2} style={styles.textTitle}>
                  {t('Enter_a_phone_number_to_continue')}
                </Text> : <Text numberOfLines={2} style={styles.textTitle}>
                  {t('CREATE_NEW_ACCOUNT')}
                </Text>}
              </View>
              {/* eslint-disable-next-line react-native/no-inline-styles */}
              <View style={{ flexDirection: 'row', justifyContent: 'flex-start' }}>
                <Text style={styles.text}>{t('ENTER_PHONE_NUMBER')}</Text>
              </View>
              <View style={styles.viewLogo}>
                <Image style={styles.logoRed} source={logoMypet}/>
              </View>
              <View style={styles.mainTextInput}>
                <TTextInput
                  typeInput={'phone'}
                  keyboardType="phone-pad"
                  maxLength={12}
                  autoCapitalize={'none'}
                  placeholder={t('MOBILE')}
                  onChangeText={onChangeText}
                  value={accountStore.phoneNumber}
                  iconRightClick={() => { accountStore.setPhoneNumber('') }}
                  iconRight={accountStore.phoneNumber?.length ? <Icon
                    name='close-circle'
                    size={24}
                    color='#c5cee0'
                  /> : null }
                />
              </View>
              {registerType === 'FB' ? <View style={styles.buttonContainer}>
                <TButton disabled={validateFields() || isSubmitting} loading={isSubmitting} title={t('TIEP_THEO')} onPress={handlePhoneAuth} />
              </View> : <View style={styles.buttonContainer}>
                <TButton disabled={validateFields() || isSubmitting} loading={isSubmitting} title={t('REGISTER')} onPress={handlePhoneAuth} />
              </View>}

            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
})
