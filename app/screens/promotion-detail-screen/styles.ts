/* eslint-disable */
import { StyleSheet } from "react-native"
import { responsiveHeight, responsiveWidth } from "react-native-responsive-dimensions"
import { ifIphoneX } from "react-native-iphone-x-helper"
import { color } from '@app/theme'

const styles = StyleSheet.create({
  safeAreaView: {
    backgroundColor: "#fff",
    height: responsiveHeight(100),
    marginTop: -4

  },
  background: {
    backgroundColor: "#fff",
    height: responsiveHeight(100),
    marginTop: -30,
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    flexDirection: 'column',
    flex: 1,
    // shadowColor: "#000",
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 0.23,
    // shadowRadius: 2.62,
    // elevation: 4,
  },
  container: {
    marginTop: 0,
    paddingTop: 0,
    paddingBottom: 0,
    marginBottom: 0,
    height: 50,
    backgroundColor: '#fff',
    justifyContent: 'space-around',
  },
  viewTouch: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 15,
    height: 30,
    justifyContent: 'center',
    left: 15,
    position: 'absolute',
    top: 15,
    width: 30,
  },
  screenTitle: {
    color: '#333',
    fontSize: 14
  },
  textTime: {
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
    alignSelf: 'center',
    color: '#fff',
    paddingHorizontal: 10,
    fontWeight: '500'
  },
  boxTimeBackground: {
    height: 30,
    borderRadius: 4,
    backgroundColor: color.primary,
    flexDirection: 'row',
    marginBottom: 15,
    justifyContent: 'center'
  },
  renderItem : {
    paddingHorizontal: 15,
    shadowColor: '#e6e8ef',
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 1,
    shadowRadius: 5,
    paddingTop: 10
  },

  icMap: {
    margin: 6,
    marginLeft: 14,
    resizeMode: "contain",
    width: 14,
    height: 14,
  },

  btnBack:{
    ...ifIphoneX({
      marginTop: 18,
    }, {
      marginTop: 20,
    }),
    marginLeft:15,
    width:24
  },

  contentText: {
    flexDirection: "column",
    justifyContent: "space-between",
    alignItems: "flex-start",
    flex: 1,
  },
  rdkTopText: {
    fontSize: 14,
    paddingTop: 5,
    paddingLeft: 15,
    paddingRight: 15,
    fontWeight: "600",
    color: "#333"
  },
  rdkTopText1: {
    fontSize: 13,
    color: "#8f9bb3",
    paddingRight: 40,
    fontWeight: "600",
    marginTop: 5
  },
  rdkTopImage: {
    width: 82,
    height: 82,
    alignSelf: "center",
    resizeMode: "cover",
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8
  },
  rdkTop: {
    flex: 1,
    flexDirection: "row",
    height: 82,
    width: "100%",
    backgroundColor: "#fff",
    borderRadius: 8,
    marginBottom: 10,
  },
  resultCount: {
    fontSize: 16,
    fontWeight: "bold",
    textAlign: "center",
    color: "#333333",
    width: '100%',
    paddingHorizontal: 50,
    paddingVertical: 10,
  },
  star: {
    color: "#ff8900",
    fontSize: 14
  },
  starRate: {
    paddingLeft: 15,
    paddingRight: 15,
    paddingBottom: 4,
    flexDirection: "row",
    width: '100%',
  },
  pointsStar: {
    marginLeft: 5,
    marginTop: 2,
    fontSize: 12,
    color: "#333"
  },
  kilometer: {
    alignItems: "flex-end",
    fontSize: 12,
    textAlign: "right",
    color: "#8f9bb3"
  },
  flatListContainer: {
    flex: 1,
    marginLeft: 8
    // height: responsiveHeight(100),
    // ...ifIphoneX({
    //   paddingBottom: 89,
    // }, {
    //   paddingBottom: 60,
    // }),

  },
  btnXemthem: {
    // alignSelf: 'flex-start',
    borderColor: color.primaryBackground,
    borderTopWidth: 1,
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginTop: 20,
    alignItems: 'center',
    backgroundColor: '#fff',
    borderBottomWidth: 1
  },
  txtXemthem: {
    color: '#979797',
    // flex: 1,
    fontSize: 14,
    marginRight: 8
  }
})

export default styles
