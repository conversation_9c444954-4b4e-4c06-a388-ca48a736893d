import React, { useEffect, useState } from 'react'
import { observer } from 'mobx-react-lite'
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  useWindowDimensions,
  ScrollView, Dimensions
} from 'react-native'
import { SCREENS } from '@app/navigation'
import { useNavigation, useRoute } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import { useStores } from '@app/models'
import { StoreListItem, LazyImage, PlaceHolder, ButtonBack } from '@app/components'
import styles from './styles'
import Icon from 'react-native-vector-icons/Ionicons'
import { useAbortableEffect } from '@app/use-hooks'
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import RenderHtml from 'react-native-render-html'

const { width, height } = Dimensions.get('window')
const imgHeight = 222 * (width / 375)

export const PromotionDetailScreen = observer(function PromotionDetailScreen(props) {
  const { navigate, goBack } = useNavigation()
  const { t } : any = useTranslation()
  const { searchStore, homeStore } = useStores()
  const [refreshing, setRefreshing] = useState(false)
  const [loadMore, setLoadMore] = useState(false) // mark scrollEnd to load more
  const [isFetched, setIsFetched] = useState(true) // event view placeholder
  const [page, setPage] = useState(1)
  const route: any = useRoute()
  const { width } = useWindowDimensions()
  const { id } = route.params
  const [isShowMore, setIsShowMore] = useState(false)
  const [promotion, setPromotion] = useState(null)

  useAbortableEffect(() => {
    loadData().then(r => {
    })
  }, [])

  useEffect(() => {
    __DEV__ && console.log('promotionId', id)
  }, [])

  /**
   * call Store
   */
  const loadData = async () => {
    setIsFetched(true)
    const rs = await homeStore.getPromotionById(id)
    if (rs) {
      setPromotion(rs)
    }
    setLoadMore(false)
    setRefreshing(false)
    setIsFetched(false)
  }

  const refreshData = async () => {
    loadData().then(r => {})
  }

  /**
   * onRefresh
   */
  const onRefresh = () => {
    __DEV__ && console.log('onRefresh ', page)
    setRefreshing(true)
    refreshData().then(r => {
    })
    setRefreshing(false)
  }

  const renderFlatList = ({ item }) => (
    <View>
      <StoreListItem item={item} onPress={(e) => {
        navigate(SCREENS.serviceDetail, { id: e })
      }
      }/>
    </View>
  )

  return (
    <SafeAreaView style={[styles.safeAreaView, { paddingBottom: useSafeAreaInsets().bottom }]} edges={['right', 'top', 'left']}>
      {isFetched ? <PlaceHolder />
        : <View style={{ flex: 1 }}>
          <View style={{ marginTop: -useSafeAreaInsets().top, height: imgHeight + useSafeAreaInsets().top }}>
            <ButtonBack style={{ color: '#fff', position: 'absolute', top: useSafeAreaInsets().top + 16, left: 16, zIndex: 9999 }} onPress={goBack} />
            <LazyImage
              resizeMode={'cover'}
              style={{ width: '100%', height: imgHeight + useSafeAreaInsets().top }}
              source={{ uri: homeStore.imagePromotionById }}>
            </LazyImage>
          </View>
          {/* <TouchableOpacity */}
          {/*  onPress={goBack} */}
          {/*  style={styles.viewTouch}> */}
          {/*  <Icon */}
          {/*    size={20} */}
          {/*    name={'arrow-back-outline'} */}
          {/*  /> */}
          {/* </TouchableOpacity> */}
          <ScrollView showsVerticalScrollIndicator={false} style={styles.background}>
            <View>
              <Text style={styles.resultCount}>{homeStore.title}</Text>
            </View>
            <View style={{ flexDirection: 'row', justifyContent: 'center' }}>
              <View style={styles.boxTimeBackground}>
                {homeStore.startTime && promotion?.status == 1
                  ? <Text style={styles.textTime}>{t('FROM')}: {homeStore.startTime} {t('TO')} {homeStore.endTime}</Text>
                  : null}
                { promotion?.status == 0 ? <Text style={styles.textTime}>Chương trình đã hết hạn</Text> : null}
              </View>
            </View>
            <View style={{ paddingHorizontal: 16, marginBottom: 30, backgroundColor: '#fff', paddingTop: isShowMore ? 10 : 0 }}>
              { isShowMore && <RenderHtml
                contentWidth={width}
                source={{ html: homeStore?.content }}
                ignoredTags={['script']}
                ignoredStyles={['font-family']}
                renderersProps={{
                  img: {
                    enableExperimentalPercentWidth: true
                  }
                }}
              /> }
              <TouchableOpacity style={styles.btnXemthem} onPress={() => {
                setIsShowMore(!isShowMore)
              }}>
                {!isShowMore ? <View style={{ flexDirection: 'row' }}>
                  <Text style={styles.txtXemthem}>{t('Xem chương trình khuyến mại')}</Text>
                  <Icon name={'chevron-down-outline'} size={18} color={'#979797'} />
                </View> : <View style={{ flexDirection: 'row' }}>
                  <Text style={styles.txtXemthem}>{t('Đóng chi tiết')}</Text>
                  <Icon name={'chevron-up-outline'} size={18} color={'#979797'} />
                </View>}
              </TouchableOpacity>
            </View>
            <View style={styles.flatListContainer}>
              <FlatList
                data={homeStore.dataServicesById}
                initialNumToRender={50}
                refreshing={refreshing}
                numColumns={2}
                onRefresh={onRefresh}
                keyExtractor={(item, index) => item.id + index.toString()}
                renderItem={renderFlatList}
                extraData={homeStore.dataServicesById}
                showsVerticalScrollIndicator={false}
              />
            </View>
          </ScrollView>
        </View> }
    </SafeAreaView>
  )
})
