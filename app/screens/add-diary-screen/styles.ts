import { StyleSheet, Dimensions } from 'react-native'
import { color, spacing, typography } from '@app/theme'
import { responsiveHeight } from 'react-native-responsive-dimensions'
const { width, height } = Dimensions.get('window')
const styles = StyleSheet.create({
  add_circle: {
    bottom: 16,
    position: 'absolute',
    right: 0
  },
  background: {
    backgroundColor: '#fff',
    flex: 1,
    // ...ifIphoneX({
    //   paddingBottom: 89,
    // }, {
    //   paddingBottom: 60,
    // }),
    paddingLeft: 15,
    paddingRight: 15
  },
  buttonAdd: {
    alignItems: 'center',
    // backgroundColor: '#edf1f7',
    borderColor: '#c5cee0',
    borderRadius: 10,
    // borderStyle: 'dashed',
    borderWidth: 1,
    height: 48,
    justifyContent: 'center',
    marginBottom: 60,
    marginHorizontal: 15,
    marginTop: 50
  },
  chooseAddress: {
    color: '#ffa8b4',
    fontSize: 12,
    marginLeft: 5,
  },
  container: {
    flex: 1,
    // height: responsiveHeight(100),
    // marginBottom: 100
  },
  containerDs: {
    // alignItems: 'center',
    borderBottomColor: '#F4F4F4',
    // borderBottomLeftRadius: 8,
    // borderBottomRightRadius: 10,
    borderBottomWidth: 1,
    borderStyle: 'solid',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  content: {
    alignItems: 'center',
    alignSelf: 'center',
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8
  },

  dsViewText: {
    color: 'rgba(0, 0, 0, 0.5)',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginTop: 9
  },
  fieldChoosePicture: {
    flexDirection: 'column',
    marginVertical: 8
  },
  fieldInput: {
    // flexDirection: 'row',
    marginVertical: 8
  },
  icArrowBack: {
    marginVertical: 15,
    paddingRight: 20
  },
  icon: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginRight: 15,
    marginTop: 50,

  },
  iconLeft: {
    marginRight: spacing.small,
  },
  iconPetSelect: {
    height: 50,
    width: 50
  },
  iconRight: {
    justifyContent: 'flex-end',
    marginLeft: 15
  },
  image: {
    borderRadius: 50,
    height: 60,
    width: 60
  },
  input: {
    flex: 1
  },
  inputContent: {
    backgroundColor: '#f3f3f3',
    borderRadius: 4,
    fontSize: 14,
    marginHorizontal: 15,
    minHeight: 150,
    padding: 10,

  },
  inputDiary: {
    marginVertical: 8
  },
  itemGender: {
    // backgroundColor: '#f3f3f3',
    borderRadius: 4,
    color: '#333',
    flexDirection: 'row',
    // justifyContent: 'center',
    marginBottom: 16,
    marginHorizontal: 16,
  },
  label: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    marginLeft: 16
  },
  mainContainer: {
    // backgroundColor: 'red',
    flex: 1,
    justifyContent: 'flex-start',
    // paddingBottom: 89,
    paddingTop: 15,
    // height: '100%'
  },
  mainTitle: {
    // backgroundColor: '#ffffff',
    paddingLeft: 15,
    paddingTop: 16,
  },
  mainTitleText: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
  },
  mainUser: {
    alignItems: 'center',
    // backgroundColor: 'red',
    flexDirection: 'column',
    justifyContent: 'center',
  },
  modalHeaderPetSpecies: {
    // borderBottomColor: '#eee',
    // borderBottomWidth: 1,
    // flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 10,
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
  },
  petAvatar: {
    borderRadius: 9,
    height: 70,
    // padding: spacing.small,
    resizeMode: 'contain',
    width: 70
  },
  placeholder: {
    borderRadius: 4,
    marginHorizontal: 15,
    marginVertical: 6,
  },
  renderItemListAddress: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 12,
    paddingLeft: 14,
    paddingRight: 14,
  },
  renderItemListPhone: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: spacing.small,
    paddingLeft: 14,
    paddingRight: spacing.small
  },
  sAvView2: {
    borderColor: '#edf1f7',
    borderRadius: 22,
    borderStyle: 'solid',
    borderWidth: 1,
    flexDirection: 'row',
    height: 44,
    marginTop: 8
  },
  safeAreaView: {
    backgroundColor: '#f7f9fc',
    flex: 1,
    marginTop: -4
  },
  scrollView: {
    flex: 1
  },
  selectPet: {
    color: '#333',
    fontSize: 13,
    // marginLeft: 15,
    // marginTop: 26,
    textAlign: 'center',
    fontWeight: 'bold'
  },
  selectPiker: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 13,
    fontWeight: '500'
  },
  selectProvince: {
    color: '#333',
    fontSize: 15,
    marginLeft: 15,
    marginTop: 26,
  },
  showPicker: {
    backgroundColor: '#f3f3f3',
    borderRadius: 4,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    paddingVertical: 10
  },
  textAddressBranch: {
    color: '#333',
    fontSize: 15,
    marginLeft: 9,
    width: 245,
  },
  textAddressPhone: {
    color: '#333',
    fontSize: 15,
    marginLeft: 7,
  },
  textButton: {
    color: '#acb1c0',
    fontSize: 15,
    fontWeight: '600',
    textAlign: 'center',

  },
  textChooeseImg: {
    color: color.primary,
    marginLeft: 10
  },
  textInput: {
    alignItems: 'center',
    backgroundColor: '#f3f3f3',
    borderRadius: 4,
    color: '#333',
    fontSize: 14,
    marginHorizontal: 16,
    padding: 12
  },
  textName: {
    color: '#333',
    fontSize: 15,
    fontWeight: 'bold',
    marginTop: 12
  },
  textPhone: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 15,
    marginLeft: 15,
    marginTop: 6,
  },
  textProvince: {
    color: '#9E9E9E',
    fontFamily: typography.normal,
    fontSize: 14
  },
  textSave: {
    color: color.primary,
    fontFamily: typography.normal,
    fontSize: 15,
    fontWeight: '600',
    marginTop: 2,
    textAlign: 'right'
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    marginRight: -20,
    textAlign: 'center'
  },
  textTitleTotal: {
    color: '#333',
    fontSize: 26,
    fontWeight: 'bold',
    marginHorizontal: spacing.small
  },
  textUpdate: {
    color: '#fff',
    fontFamily: typography.normal,
    fontSize: 15,
    fontWeight: '600',
    letterSpacing: 0,
  },
  textUploadPicture: {
    color: color.primary,
    marginLeft: 10
  },
  touchUser: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 20,
    marginTop: 15
  },
  unChooseAddress: {
    color: '#b0aeae',
    fontSize: 12,
    marginLeft: 5,
    textDecorationLine: 'underline'
  },

  viewCapture: {
    alignItems: 'center',
    backgroundColor: '#d6d6d6',
    borderRadius: 50,
    height: 50,
    justifyContent: 'center',
    padding: 20,
    width: 50
  },
  viewChoosePicture: {
    borderColor: '#ff9c9c',
    borderRadius: 4,
    borderStyle: 'dashed',
    flexDirection: 'row',
    flex: 1,
    marginHorizontal: 16
  },
  viewFlatlist: {
    height: responsiveHeight(100) - 220,
    // justifyContent: 'space-between',
    // marginBottom: 90
    marginTop: 27
  },
  viewImage: {
    borderRadius: 9,
    marginLeft: spacing.small,
    marginTop: spacing.small,
  },
  viewLabel: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  viewName: {
    flexDirection: 'column',
    flex: 1,
    justifyContent: 'flex-start',
    marginTop: 29,
  },
  viewSecond: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    flexDirection: 'column',
    marginLeft: 15,
    marginRight: 15,
    marginTop: 20,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10
  },
  viewThird: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    flexDirection: 'column',
    height: 58,
    marginBottom: 15,
    marginHorizontal: 15,
    shadowColor: 'rgba(85, 85, 85, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 10
  },
  viewTopNavbar: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15
  },
  viewTouchButtonTop: {
    // marginTop: 2
  },
  viewUpload: {
    alignItems: 'center',
    flexDirection: 'row',
    margin: 10,
  },
})
export default styles
