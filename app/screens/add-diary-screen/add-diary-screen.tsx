import React, { Fragment, useContext, useEffect, useImperativeHandle, useRef, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { TextInput, View, Text, Image, TouchableOpacity, Platform, ScrollView } from 'react-native'

import styles from './styles'
import { useTranslation } from 'react-i18next'
import { TButton } from '@app/components'
import ImagePicker from 'react-native-image-crop-picker'
import { useStores } from '@app/models'
import { ModalContext } from '@app/components/modal-success'
import DateTimePickerModal from 'react-native-modal-datetime-picker'
import moment from 'moment'
import Icon from 'react-native-vector-icons/Ionicons'
import { icCapture } from '@app/assets/images'
import SimpleToast from 'react-native-simple-toast'
import FastImage from 'react-native-fast-image'
import { styled } from 'nativewind'
import CurrencyInput from 'react-native-currency-input'
import BottomSheet, { BottomSheetBackdrop, BottomSheetView } from '@gorhom/bottom-sheet'
import { useSafeAreaInsets } from 'react-native-safe-area-context'

const Div = styled(View)
const StyledText = styled(Text)

export interface ModalAddDiary {
  handleClose?:any
  id?: any
}

export const AddDiaryScreen = observer(function AddDiaryScreen(props: ModalAddDiary, ref) {
  const { t } : any = useTranslation()
  const { carStore, insuranceStore } = useStores()
  const { showError, showSuccess } = useContext(ModalContext)
  const [pickerDate, setPickerDate] = useState(null)
  const [strPiker, setStrPicker] :any = useState('')
  const [date, setDate] = useState(new Date().getTime())
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [pickerDateVisible, setPickerDateVisible] = useState(false)
  const [base64Upload, setBase64Upload] = useState('')
  const [filesUpload, setFilesUpload] = useState([])
  const [amount, setAmount] :any = useState()
  const [catText, setCatText] = useState('')
  // ref
  const bottomSheetRef = useRef<BottomSheet>(null)
  const [objValue, setObjValue] = useState({
    title: '',
    carId: props?.id, // khóa phụ
    // car: props?.id, // khóa phụ
    cost_type: null, // khóa phụ
    cast_value: 0,
    description: '',
    image: null,
    dateTime: new Date()
  })

  useEffect(() => {
    carStore.clearFields()
    carStore.getCostType()
  }, [])

  useImperativeHandle(ref, () => {
    return {
      onCreateDiary: onCreateDiary
    }
  })

  const hidePicker = () => {
    setPickerDateVisible(false)
    setPickerDate(null)
    // setPickerTime(null)
  }

  const handleConfirm = (date) => {
    if (Date.parse(date) <= (new Date().getTime())) {
      setDate(new Date(date).getTime())
      onChangeText('dateTime', new Date(date).getTime())
      hidePicker()
    } else {
      // showError(t('FAIL'), t('Bạn không thể chọn ngày chưa tới'))
      SimpleToast.show('Bạn không thể chọn ngày chưa tới')
      return Platform.OS === 'android' ? setPickerDateVisible(false) : null
    }
  }

  const getPicture = () => {
    ImagePicker.openPicker({
      writeTempFile: true,
      width: 200,
      mediaType: 'photo',
      height: 200,
      // compressImageMaxHeight: 200,
      // compressImageMaxWidth: 200,
      includeBase64: true,
      cropping: true,
      multiple: false,
    }).then(async (response: any) => {
      __DEV__ && console.log(response)
      setBase64Upload(`data:${response.mime};base64,${response.data}`)

      const files = []
      files.push({
        uri: response.path,
        name: 'files',
        fileName: response.filename + '.png',
        type: response.mime,
        size: response.size,
      })
      setFilesUpload(files)
      // const rsUpload = await api.uploadImagesV2('/api/upload', files) // upload
      // __DEV__ && console.log('upload rs', rsUpload)
      // profileStore.uploadImageDropBox(files).then(rs => {
      // })
    })
  }

  const renderCategoriesItem = (item, index) => {
    return (
      <TouchableOpacity
        style={styles.itemGender}
        key={index}
        onPress={() => {
          setCatText(item.attributes.name)
          onChangeText('cost_type', item.id)
          bottomSheetRef?.current.close()
        }}
      >
        <StyledText>{item.attributes.name}</StyledText>
      </TouchableOpacity>
    )
  }

  // const showDatePicker = () => {
  //   setPickerDate('date')
  //   setStrPicker(t('CHOOSE_A_DATE'))
  // }

  const onChangeText = (field, value) => {
    setObjValue((prev) => ({ ...prev, [field]: value }))
  }

  const onCreateDiary = async () => {
    setIsSubmitting(true)
    if (objValue.description === '' || objValue.title === '') {
      showError(t('FAIL'), t('FILL_OUT_THE_FORM'))
    } else {
      const rsUpload = await carStore.uploadOneImage('/api/upload', filesUpload) // upload
      if (rsUpload) {
        onChangeText('image', rsUpload.id)
        const body : any = { data: { ...objValue, image: rsUpload.id } }

        const rsCreate = await carStore.createDiary(body)

        if (rsCreate && !rsCreate.data.error) {
          showSuccess(t('THANHCONG'), t('Đã thêm nhật ký'))
          props.handleClose(true)
        } else {
          showError(t('FAIL'), t(`${rsCreate.error.message}`))
          setIsSubmitting(false)
        }
      } else {
        showError(t('FAIL'), t('Xảy ra lỗi không thể tải ảnh'))
      }
    }
    setIsSubmitting(false)
  }

  const iconRight = <Icon name={'chevron-down-outline'} size={20} color={'#333'} style={styles.iconLeft}/>
  // const iconLeft = <Image source={icAddRed} style={{ width: 22, height: 22, marginRight: 8 }}></Image>

  // @ts-ignore
  return (
    <Fragment>
      <View style={{ flex: 1, backgroundColor: '#fff', marginTop: 15 }}>
        <View style={styles.fieldInput}>
          <Text style={styles.label}>Ngày giờ:</Text>
          <TouchableOpacity onPress={() => setPickerDateVisible(true)} style={styles.showPicker}>
            <View style={{ flexDirection: 'row', marginLeft: 2 }}>
              {/* {iconRight} */}
              <Text style={styles.content}>{moment(date).format('DD/MM/YYYY')}</Text>
            </View>
            {iconRight}
          </TouchableOpacity>
        </View>
        <View style={styles.fieldInput}>
          <Text style={styles.label}>Tiêu đề</Text>
          <TextInput
            style={styles.textInput}
            selectTextOnFocus={false}
            placeholderTextColor="#9E9E9E"
            placeholder={t('Nhập tiêu đề')}
            defaultValue={''}
            onChangeText={(e) => {
              onChangeText('title', e)
            }}
          />
        </View>
        <Div className="flex flex-row justify-between">
          <Div className="w-1/2">
            <View style={styles.fieldInput}>
              <Text style={styles.label}>Số tiền</Text>
              <CurrencyInput
                style={styles.textInput}
                value={amount}
                onChangeValue={setAmount}
                delimiter=","
                separator="."
                precision={0}
                placeholder={'Nhập số tiền'}
                onChangeText={(v) => {
                  onChangeText('cost_value', parseInt(v.split(',').join('')))
                }} />
            </View>
          </Div>
          <Div className="w-1/2">
            <View style={styles.fieldInput}>
              <Text style={styles.label}>Loại chi phí</Text>
              <TouchableOpacity onPress={() => {
                bottomSheetRef?.current.expand()
              }} style={styles.showPicker}>
                <View style={{ flexDirection: 'row', marginLeft: 2 }}>
                  {/* {iconRight} */}
                  <Text style={styles.content}>{catText}</Text>
                </View>
                {iconRight}
              </TouchableOpacity>
            </View>
          </Div>
        </Div>

        <View style={styles.fieldChoosePicture}>
          <Text style={styles.label}>Chọn ảnh</Text>
          <TouchableOpacity onPress={getPicture} style={[styles.viewChoosePicture, { borderWidth: carStore.picture ? 0 : 1 }]}>
            {base64Upload.length > 0 ? <View style={styles.viewUpload}>
              <FastImage
                resizeMode="cover"
                style={styles.image}
                source={{ uri: base64Upload }}
              />
            </View> : <View style={styles.viewUpload}>
              <View style={styles.viewCapture}><Image source={icCapture} style={{ width: 30, height: 20 }}></Image></View>
              <Text style={styles.textUploadPicture}>{t('Tải ảnh lên')}</Text>
            </View>}
          </TouchableOpacity>
        </View>
        <View style={styles.inputDiary}>
          <Text style={styles.label}>Nội dung </Text>
          <TextInput
            style={styles.inputContent}
            selectTextOnFocus={false}
            placeholderTextColor="#9E9E9E"
            placeholder={t('Nhập nội dung')}
            defaultValue={''}
            multiline={true}
            onChangeText={(e) => {
              onChangeText('description', e)
            }}
          />
        </View>
        <TButton typeRadius={'rounded'} title={t('Create_diary')} onPress={onCreateDiary} buttonStyle={{ margin: 16 }} titleStyle={{ fontWeight: '500', fontSize: 16 }} disabled={isSubmitting} />
        <DateTimePickerModal
          isVisible={pickerDateVisible}
          // mode={pickerDate}
          locale="vi_VN"
          cancelTextIOS={t('CANCEL')}
          confirmTextIOS={t('XACNHAN')}
          headerTextIOS={strPiker}
          onConfirm={handleConfirm}
          onCancel={hidePicker}
          isDarkModeEnabled={false}
        />
      </View>
      <BottomSheet
        index={-1}
        ref={bottomSheetRef}
        snapPoints={['80%']}
        enablePanDownToClose={true}
        // backdropComponent={() => <View style={{ flex: 1, backgroundColor: '#333', width: 200, height: 200 }}/>}
        backdropComponent={(props) => (
          <BottomSheetBackdrop
            {...props}
            appearsOnIndex={0}
            disappearsOnIndex={-1}
          />
        )}
      >
        <BottomSheetView style={{ flex: 1 }}>
          <ScrollView style={{ marginVertical: 20, marginBottom: useSafeAreaInsets().bottom + 30 }}>
            {carStore.costCategories.map((item, index) => renderCategoriesItem(item, index))}
          </ScrollView>
        </BottomSheetView>
      </BottomSheet>
    </Fragment>
  )
}, { forwardRef: true })
