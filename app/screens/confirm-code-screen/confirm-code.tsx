import { SafeAreaView } from 'react-native-safe-area-context'
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { Animated, Text, View, TouchableOpacity, Alert } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import { useRoute, useNavigation } from '@react-navigation/native'
import Icon from 'react-native-vector-icons/Ionicons'

import {
  Code<PERSON>ield,
  Cursor,
  useBlurOnFulfill,
  useClearByFocusCell,
} from 'react-native-confirmation-code-field'

import styles, {
  ACTIVE_CELL_BG_COLOR,
  CELL_BORDER_RADIUS,
  CELL_SIZE,
  DEFAULT_CELL_BG_COLOR,
  NOT_EMPTY_CELL_BG_COLOR,
} from './styles'
import { observer } from 'mobx-react-lite'
import auth from '@react-native-firebase/auth'
import { useStores } from '../../models/root-store'
import { TButton, ButtonBack } from '@app/components'
import { SCREENS } from '@app/navigation'
import { useTranslation } from 'react-i18next'
import { ModalContext } from '@app/context'
import validate from 'validate.js'
import { useAuth } from '@app/use-hooks/use-auth'
import { color, typography } from '../../theme'
import SimpleToast from 'react-native-simple-toast'

const { Value, Text: AnimatedText } = Animated
const CELL_COUNT = 6
const TIME = 59
const animationsColor = [...new Array(CELL_COUNT)].map(() => new Value(0))
const animationsScale = [...new Array(CELL_COUNT)].map(() => new Value(1))
const animateCell = ({ hasValue, index, isFocused }) => {
  Animated.parallel([
    Animated.timing(animationsColor[index], {
      useNativeDriver: false,
      toValue: isFocused ? 1 : 0,
      duration: 250,
    }),
    Animated.spring(animationsScale[index], {
      useNativeDriver: false,
      toValue: hasValue ? 0 : 1,
      duration: hasValue ? 300 : 250,
    }),
  ]).start()
}
export const ConfirmCode = observer((props: any) => {
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation()
  const route: any = useRoute()
  const { accountStore, profileStore } = useStores()
  const [value, setValue] = useState('')
  const ref = useBlurOnFulfill({ value, cellCount: CELL_COUNT })
  const phoneNumber = route.params.phoneNumber
  const { showError, showSuccess } = useContext(ModalContext)
  const [isSubmitting, setSubmitting] = useState(false)
  const [isResend, setIsResend] = useState(true)
  const [currentUser, setCurrentUser] = useState(null)
  const validateFields = () => validate.isEmpty(value)
  const { data, registerType, typeConfirm } = props?.route?.params || {}

  // @ts-ignore
  const { signOut, userToken, signUp, signIn } = useAuth() // should be signUp

  const [mins, setMins] = useState(1)
  const [secs, setSecs] = useState(0)

  useEffect(() => {
    const timerId = setInterval(() => {
      setIsResend(true)
      if (secs <= 0) {
        if (mins <= 0) {
          setIsResend(false)
        } else {
          setMins(m => m - 1)
          setSecs(TIME)
        }
      } else setSecs(s => s - 1)
    }, 1000)
    return () => clearInterval(timerId)
  }, [secs, mins])

  const [propsCode, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue,
  })
  const [confirm, setConfirm] = useState(null)

  useEffect(() => {
    if (!route.params.phoneNumber || !phoneNumber) {
      goBack()
      return null
    }
    const unsubscribe = auth().onAuthStateChanged(async (user) => {
      __DEV__ && console.log('onAuthStateChanged', user)
      __DEV__ && console.log('onAuthStateChanged', user)
      if (user && user.phoneNumber == phoneNumber) {
        setCurrentUser(user)
        nextStepRegister()
      } else {
        try {
          await auth().signOut()
        } catch (e) {
        }
        setCurrentUser(null)
        signInWithPhoneNumber().then(r => {
        })
      }
    })
    return () => {
      // Unsubscribe from further state changes
      unsubscribe()
    }
  }, [])

  const nextStepRegister = () => {
    if (typeConfirm === 'register') {
      navigate(SCREENS.createPasswordNewAccount, { phoneNumber: accountStore.phoneNumber, data: data })
      setValue('')
      setSubmitting(false)
    } else if (typeConfirm === 'recover') {
      navigate(SCREENS.recoverPassword, { phoneNumber: accountStore.phoneNumber })
      setValue('')
      setSubmitting(false)
    } else {
      showError(t('FAIL'), t('Có lỗi xảy ra, vui lòng thử lại'))
    }
  }

  const setCountDown = () => {
    setMins(0)
    setSecs(TIME)
  }

  async function signInWithPhoneNumber() {
    SimpleToast.show('Đã gửi mã xác nhận')
    setCountDown()
    try {
      // await auth().signOut()
    } catch (e) {
    }
    try {
      if (!currentUser) {
        const confirmation = await auth().signInWithPhoneNumber(phoneNumber)
        setConfirm(confirmation)
      } else {
        __DEV__ && console.log('Lỗi firebase signInWithPhoneNumber')
      }
    } catch (error) {
      Alert.alert('Phone Auth Error', error.message)
      setSubmitting(false)
    }
  }

  const confirmCode = async () => {
    setSubmitting(true)
    if (!value) {
      showError(t('FAIL'), t('AUTHENTICATION_CODE_CANNOT_BE_EMPTY'))
      setSubmitting(false)
    } else if (confirm) {
      confirm.confirm(value).then(rs => {
        __DEV__ && console.log(rs)
        __DEV__ && console.log('rs confirm', rs)
        if (rs && rs.user) {
          nextStepRegister()
        }
      }).catch(error => {
        // showError(t('FAIL'), 'Nhập sai mã xác thực. ' + error.message)
        showError(t('FAIL'), 'Nhập sai mã xác thực. Vui lòng thử lại')
        setValue('')
        setSubmitting(false)
        __DEV__ && console.log(error)
      })
    } else {
      Alert.alert('Thông báo', 'Có lỗi xảy lòng thử lại')
      setSubmitting(false)
    }
  }

  const renderCell = ({ index, symbol, isFocused }) => {
    const hasValue = Boolean(symbol)
    const animatedCellStyle = {
      backgroundColor: hasValue
        ? animationsScale[index].interpolate({
          inputRange: [0, 1],
          outputRange: [NOT_EMPTY_CELL_BG_COLOR, ACTIVE_CELL_BG_COLOR],
        })
        : animationsColor[index].interpolate({
          inputRange: [0, 1],
          outputRange: [DEFAULT_CELL_BG_COLOR, ACTIVE_CELL_BG_COLOR],
        }),
      borderRadius: animationsScale[index].interpolate({
        inputRange: [0, 1],
        outputRange: [CELL_SIZE, CELL_BORDER_RADIUS],
      }),
      transform: [
        {
          scale: animationsScale[index].interpolate({
            inputRange: [0, 1],
            outputRange: [1.1, 1],
          }),
        },
      ],
    }

    // Run animation on next event loop tik
    // Because we need first return new style prop and then animate this value
    setTimeout(() => {
      animateCell({ hasValue, index, isFocused })
    }, 0)
    return (
      <AnimatedText
        key={index}
        style={[styles.cell, animatedCellStyle]}
        onLayout={getCellOnLayoutHandler(index)}>
        {symbol || (isFocused ? <Cursor/> : null)}
      </AnimatedText>
    )
  }
  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <ButtonBack onPress={goBack} style={styles.icArrowBack}/>
      <View style={styles.viewContainer}>
        <View>
          <Text style={styles.title}>{t('ENTER_AUTH_CODE')}</Text>
          <Text style={styles.subTitle}>
            {t('ENTER_THE_VERIFICATION_CODE')} {phoneNumber}
          </Text>
        </View>
        <CodeField
          ref={ref}
          {...propsCode}
          value={value}
          onChangeText={setValue}
          cellCount={CELL_COUNT}
          rootStyle={styles.codeFieldRoot}
          keyboardType="number-pad"
          textContentType="oneTimeCode"
          renderCell={renderCell}
        />
        <View style={{ flexDirection: 'row', marginTop: 60 }}>
          <Text style={styles.subTitle}>
            {t('YOU_DID_NOT_RECEIVE_VERIFICATION_CODE')}
          </Text>
          { isResend ? <Text style={styles.subTitle}> đợi gửi lại sau </Text> : <TouchableOpacity style={{ flexDirection: 'row' }} onPress={() => signInWithPhoneNumber() }>
            <Icon style={{ marginTop: 10, marginLeft: 5 }} name={'refresh-outline'} size={24} color={color.primary} />
            <Text style={{
              fontFamily: typography.normal,
              marginLeft: 5,
              marginTop: 12,
              paddingTop: 5,
              color: color.primary,
              textAlign: 'left',
              fontSize: 14,
            }}>{t('RESEND')} </Text>
          </TouchableOpacity>}
          { isResend ? <Text style={styles.subTitle}>
            {mins}:{secs < 10 && 0}{secs}
          </Text> : null }
        </View>
        <View>
          <TButton typeRadius={'rounded'} disabled={validateFields() || isSubmitting} loading={isSubmitting} buttonStyle={styles.nextButton}
            title={t('ACCURACY')} onPress={confirmCode}/>
        </View>
      </View>
    </SafeAreaView>
  )
})
