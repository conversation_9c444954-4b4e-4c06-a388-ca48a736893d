import { StyleSheet, Platform } from 'react-native'
import { color, typography } from '../../theme'

export const CELL_SIZE = 40
export const CELL_BORDER_RADIUS = 8
export const DEFAULT_CELL_BG_COLOR = '#fff'
export const NOT_EMPTY_CELL_BG_COLOR = '#fff'
export const ACTIVE_CELL_BG_COLOR = '#f7fafe'

const styles = StyleSheet.create({
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  icArrowBack: {
    margin: 11
  },
  codeFieldRoot: {
    height: CELL_SIZE,
    justifyContent: 'center',
    marginTop: 30,
    paddingHorizontal: 20,

  },
  cell: {
    backgroundColor: '#ffffff',
    color: color.primary,
    height: CELL_SIZE,
    lineHeight: CELL_SIZE - 1,
    marginHorizontal: 8,
    width: CELL_SIZE,
    ...Platform.select({ web: { lineHeight: 32 } }),
    borderColor: color.primary,
    borderRadius: CELL_BORDER_RADIUS,
    borderWidth: 1,
    fontSize: 32,
    textAlign: 'center'
    // // IOS
    //
    // shadowColor: '#333',
    // shadowOffset: {
    //   width: 0,
    //   height: 1,
    // },
    // shadowOpacity: 0.22,
    // shadowRadius: 2.22,
    //
    // // Android
    // elevation: 3,
  },

  // =======================

  root: {
    backgroundColor: '#fff',
    minHeight: 22
    ,
  },
  title: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
    marginTop: 25,
  },
  icon: {
    height: 158 / 2.4,
    marginLeft: 'auto',
    marginRight: 'auto',
    width: 217 / 2.4,
  },
  subTitle: {
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 14,
    marginTop: 12,
    paddingTop: 5,
    textAlign: 'left',
  },
  nextButton: {
    // backgroundColor: '#ff8ba1',
    height: 44,
    justifyContent: 'center',
    marginBottom: 100,
    marginTop: 13,
    minWidth: 300,
  },
  nextButtonText: {
    color: '#fff',
    fontFamily: typography.normal,
    fontSize: 15,
    fontWeight: '700',
    textAlign: 'center',
  },
  viewContainer: {
    marginLeft: 30,
    marginRight: 30,
  },
})

export default styles
