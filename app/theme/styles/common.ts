import { Dimensions, StyleSheet } from 'react-native'
import { getStatusBarHeight, ifIphoneX, isIphoneX } from 'react-native-iphone-x-helper'
import { spacing } from '@app/theme'
import DeviceInfo from 'react-native-device-info'
__DEV__ && console.log('getStatusBarHeight ', -Math.abs(getStatusBarHeight()))
export default StyleSheet.create({
  headerContainer: {
    backgroundColor: '#fff',
    height: DeviceInfo.hasDynamicIsland ? 120 : 100,
    justifyContent: 'space-around',
    marginTop: DeviceInfo.hasDynamicIsland ? -59 : isIphoneX() ? -44 : -Math.abs(getStatusBarHeight()),
    paddingTop: DeviceInfo.hasDynamicIsland ? 50 : 0
  }
})

export const common = StyleSheet.create({
  headerContainer: {
    // backgroundColor: color.primary,
    // justifyContent: 'space-around',
    // marginTop: isIphoneX() ? -44 : 0,
    // ...Platform.select({
    //   android: { marginTop: 0 },
    // })
  },
  headerModal: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: spacing.small,
    paddingVertical: 15,
    ...ifIphoneX({
      marginTop: 0
    }, {
      marginTop: 5
    }),
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center'
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    position: 'absolute',
    top: 12,
    zIndex: 9999
  },
})

export const initialLayout = { width: Dimensions.get('window').width }

export const linearGradientProps = {
  colors: ['#e2e2e2', '#fff'],
  start: { x: 0, y: 0 },
  end: { x: 1, y: 0 },
}
