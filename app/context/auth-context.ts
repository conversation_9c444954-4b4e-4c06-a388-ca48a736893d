import { createContext } from 'react'

export type authContextProps = {
  signUp: () => Promise<void>,
  signIn: () => Promise<void>,
  signOut: () => Promise<void>,
  isLoading?: boolean,
  isSignedOut?: boolean,
  isSignedUp?: boolean,
  noAccount?: boolean,
  isSignedIn?: boolean,
  userToken?: string,
  setBoarded: () => Promise<void>,
  shoppingCount: number,
  updateShoppingCount: (count: number) => Promise<void>
}

export const AuthContext = createContext<authContextProps>(null)
