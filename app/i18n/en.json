{"GIOITHIEU_datlich": "Book an appoinment", "GIOITHIEU_shopping": "Place Order", "GIOITHIEU_accountinfo": "Account information", "DANGNHAP_email": "Email", "DANGNHAP_password": "Password", "DANGNHAP_forgotyourpassword": "Forgot your password", "DANGNHAP_login": "Sign In", "DANGXUAT": "Log out", "DANGNHAP_loginwithfacebook": "Sign in with Facebook", "DANGNHAP_loginwithgoogle": "Sign in with Google", "DANGNHAP_loginwithapple": "Sign in with Apple", "DANGNHAP_nothaveaccount": "Not have an account ?", "DANGKY_register": "Register", "DANGKY_fullname": "Full Name", "DANGKY_haveaccount": "Already have an account ? Let's", "QUENMATKHAU_sendrequest": "Send request", "BACK": "Back", "WELCOME": "Welcome to maxQ!", "TRANGCHU_dichvuthucung": "Pet Services", "TRANGCHU_danhmucsanpham": "Product category", "TRANGCHU_tintucchothucung": "News for pets", "VIEWPAGEINDEX_welcom": "Welcome to Mypet", "VIEWPAGEINDEX_discover": "Discover the services", "VIEWPAGEINDEX_shopping": "Shopping", "VIEWPAGEINDEX_chat": "Message", "VIEWPAGEINDEX_account": "Account", "KHAMPHADV_phongkhamnoibat": "Best pet hospitals", "KHAMPHADV_xemtatca": "See all", "KHAMPHADV_khachsantotnhat": "Best pet hotels", "KHAMPHADV_spadecu": "Recommended Spa", "TAIKHOAN_hosocanhan": "Profile", "TAIKHOAN_caidat": "Setting", "TAIKHOAN_lichsudatlich": "Booking history", "TAIKHOAN_lichsumuahang": "Purchase history", "TAIKHOAN_trogiup": "Help", "HOSOCANHAN_hosocanhan": "Profile", "HOSOCANHAN_chamdesua": "Touch to fix", "HOSOCANHAN_birthday": "Birthday", "HOSOCANHAN_sex": "Gender", "HOSOCANHAN_chongioitinh": "Selection gender", "HOSOCANHAN_nam": "Male", "HOSOCANHAN_nu": "Female", "DIACHI": "Address", "MOBILE": "Phone number", "PET": "Pet", "PETNAME": "Pet name", "TYPEPET": "Type of pet", "DOG": "Dog", "CAT": "Cat", "OTHERPET": "Other pets", "DESCRIPTION": "Description", "CHANGEPASSWORD": "Change your password", "CANCEL": "Cancel", "SAVE": "Save", "EDIT": "Edit", "CURRENTPASSWORD": "Current password", "NEWPASSWORD": "New password", "CONFIRMPASSWORD": "Confirm new password", "SELLINGPRODUCT": "Best selling product", "NEWPRODUCT": "New products updated", "CHAT_unread": "Unread", "CHAT_all": "All", "EXIT": "Exit", "STATUS": "Status", "COUNT": "Count", "PRICE": "Price: ", "TOTAL": "Total: ", "CONFIRMRECEIPT": "Confirm receipt of goods", "BANKTRANSFER": "Bank transfer", "LYDOTUCHOIDONHANG": "Cancellation reason", "VUILONGNHAPLYDO": "Please enter cancellation reason. This will help Mypet improve our service", "TIEPTUCHUY": "Continue to cancel", "LSMUAHANG_dsnganhang": "The list of banks can accept transfers", "LSMUAHANG_chonnganhang": "You can choose one of the banks below to transfer", "LSMUAHANG_nobank": "There are no banks", "LSMUAHANG_alertconfirm": "Confirm!", "LSMUAHANG_alertchacchanhuy": "Are you sure want to cancel this order?", "LSMUAHANG_alertchacchannhan": "You have already receive this order", "CHITIETLICHHEN_datphong": "booking", "CHITIETLICHHEN_datlich": "booking", "CHITIETLICHHEN_thongtin": "Information ", "CHITIETLICHHEN_alerthuylichhen": "Are you sure want to cancel this appointment?", "CHITIETLICHHEN_alertchacchandienra": "Are you sure this appointment has taken place?", "DONGY": "Agree", "CHITIET": "Detail ", "CHITIETLICHHEN_xacnhanhoanthanhdv": "Confirm completed service", "CHITIETLICHHEN_thoigiandenkham": "Arrival time", "WEIGHT": "Weight", "TRIEUCHUNG": "Symptom", "CHECKINTIME": "Check in time", "CHECKOUTTIME": "Check out time", "CHITIETLICHHEN_songay": "Number of days", "DAY": "date", "GIA/NGAY": "Price / day", "TONGSOPHONG": "Total number of rooms", "TONGCHIPHI": "Total cost", "NOTE": "Note", "USEDTIME": "Used time", "TTNGUOIDAT": "User information", "NAME": "Name", "CHATVOICUAHANG": "Chat now", "LYDOTUCHOILICHHEN": "Cancellation reason", "NHAPLYDOTUCHOI": "Please enter cancellation reason. This will help Mypet improve our service", "CHIPHIDATHANHTOAN": "The cost you paid for the service", "NHAPCHIPHIDATHANHTOAN": "Please enter the service cost you paid for MyPet to optimize the service for you", "XACNHAN": "Confirm", "LSBOOKING": "Booking history", "THANHTOAN_alert_thongbao": "Notification", "THANHTOAN_alert_cannhapsodienthoai": "You need to enter the phone number for the store to contact", "ALERT_cannhap_diachi": "You need to enter an address to receive your order", "ALERT_canchon_phuongthucthanhtoan": "You need to choose a payment method", "TIME": "Time", "CHOXACNHAN": "Wait for confirmation", "CHOLAMDV": "Waiting for service", "THANHCONG": "Success!", "Unsuccessful": "Unsuccessful", "THATBAI": "Failed", "LICHDATGANDAY": "Recently booked", "LICHDATQUAHAN": "Overdue Schedule", "DANHAN": "Order Received", "ALERT_magiamgia_hethan": "Your discount code is invalid or has expired. Click continue to buy without the discount code, or return to the cart to use another discount code", "ALERT_tieptucmuahang": "Continue shopping", "TENNGANHANG": "Bank name: ", "CHINHANH": "Branch: ", "CHUTAIKHOAN": "Account holder: ", "SOTAIKHOAN": "Account number: ", "THANHTOAN": "Proceed to Payment", "TENNGUOIDATHANG": "Order name", "DIACHI_NHANHANG": "Shipping Address", "SANPHAM": "product", "PHUONGTHUC_THANHTOAN": "Payment methods", "CHIPHI_VANCHUYEN": "Shipping Fee", "CHUAXACDINH": "Not determined", "MAGIAMGIA": "Discount code", "DONHANG/": "(Orders on ", "TONGTHANHTOAN": "Sub total", "DATHANG": "Place Order", "DS_NGANHANG_COTHECHUYENKHOAN": "The list of banks that can accept transfers", "CHONNGANHANG_DECHUYENKHOAN": "You can choose one of the banks below to transfer", "KHONGCO_NGANHANG": "There are no banks", "PHUONGTHUCTT_Luuy": "Note: With pay before delivery/services, please chat with the store for product status, delivery information, and make sure there is available product", "PHUONGTHUCTT_COD": "Cash on delivery", "PHUONGTHUCTT_taiquay": "Cash", "PHUONGTHUCTT_online": "Online payment", "PHUONGTHUCTT_online_ATM": "(ATM Internet Banking, visa, Master card, E-wallet,...)", "TRANSFER": "Transfer", "NOTE_xemtt_nganhang": "See bank transfer information next to the name of the item in the cart", "NOTE_chuyenkhoantruoc": "In case of bank transfer in advance, Please contact the store for more information!", "DUOI10": "Under 10 kg", "TREN10": "Over 10 kg", "DUOI5": "Under 5 kg", "TREN5": "Over 5 kg", "ALERT_chonlai_khoangthoigian": "Choose a time period again", "ALERT_chonlai_khoangthoigian_ngaynhantraphong": "The latest check-in and check-out date must be greater than or today", "ALERT_chonlai_thoigian": "Select the time again", "ALERT_chonlai_thoigian_ngaydengannhat": "The nearest arrival date must be greater than or today.", "ALERT_TTKHONGDAYDU": "Information is not complete", "ALERT_chuachon_loaithucung": "You have not selected a pet type", "ALERT_chuachon_gioitinh": "You have not selected a pet gender", "ALERT_chuanhap_cannang": "You have not entered your pet weight", "ALERT_chuanhap_ngaynhanphong": "You have not selected a check-in date", "ALERT_chuanhap_ngaytraphong": "You have not selected a check-out date ", "ALERT_chuanhap_thoigiannhanphong": "You have not selected a check-in time", "ALERT_chuanhap_thoigiantraphong": "You have not selected a check-out time", "ALERT_chuanhap_trieuchung": "Please enter symptoms", "ALERT_chuachon_tongsophong": "You have not selected the total number of rooms", "ALERT_nhapghichu": "Please enter a note", "ALERT_TTKHONGCHINHXAC": "Invalid Information", "ALERT_thoigiantra_sauthoigiannhan": "Check-out time must be after check-in time", "ALERT_dichvumongmuon": "Please enter the desired service", "PHONG": " room", "LICH": "calender", "BOOKING": "Booking", "/PHONG/NGAY": " / room/ day", "GIOMOCUA": "Business hours", "GIODONGCUA": "Close time", "CHONTHOIGIAN_NHANPHONG": "Select the check-in time", "CHONTHOIGIAN_TRAPHONG": "Select the check-out time", "CHONTHOIGIANDEN": "Select the arrival time", "PHONGKHAM": "Clinic", "GIOITINHTHUCUNG": "Pet gender", "DUC": "Male", "CAI": "Female", "DICHVUMONGMUON": "Desiring service", "CHONNGAY_tu_den": "Select date (from -> to) \n (select one day only please press twice on the date)", "CHONNGAYDEN": "Select the arrival date", "CHIPHUCVU": "Serve only", "THANG1": "January", "THANG2": "February", "THANG3": "March", "THANG4": "April", "THANG5": "May", "THANG6": "June", "THANG7": "July", "THANG8": "August", "THANG9": "September", "THANG10": "October", "THANG11": "November", "THANG12": "December", "CHON": "Select", "UYTIN": "Reputation", "KHAC": "", "OTHERS": "Others ", "CAIDAT_tinnhan": "Message settings", "CAIDAT_thongbao": "Notification settings", "CAIDAT_ngonngu": "Language settings", "CAIDAT_tinnhan_chophepnhantinnhan": "Allow to receive messages from others", "CAIDAT_thongbao_nhanthongbaomuahang": "Receive booking alerts", "CAIDAT_thongbao_nhanthongbaotinnhan": "Receive message notifications", "NGONNGU": "Language", "TIENGANH": "English", "TIENGVIET": "Vietnamese", "ALERT_chuanhapemail": "You did not enter an email", "ALERT_chuanhapsdt": "You did not enter a phone number", "XACNHANTT": "Confirm information", "THOIGIANDEN": "Arrival time", "TENNGUOIDATLICH": "Name of the person who schedules", "YEUCAU": "Request", "DATLICHKHAM": "book an health-care appointment", "DATPHONG": "book a room", "DATLICHSPA": "book a grooming and spa appointment", "CHUCMUNG_datlichkham_thanhcong": "Congratulations on your successful booking. Your request will be checked by doctor and inform you when accepted or denied", "CHUCMUNG_datkhachsan_thanhcong": "Congratulations on your successful pet hotel reservation. Your request will be checked by the receptionist and inform you upon acceptance or rejection", "CHUCMUNG_datspa_thanhcong": "Congratulations on your successful pet spa booking.Your request will be checked and accepted by the spa owner upon acceptance or rejection", "QUAYLAIDANHSACH": "Back to the list", "CHODUYET": "awaiting approval", "ERR_chuachonthanhpho": "You have not selected a province or city", "ERR_chuachonquanhuyen": "You have not selected a district", "ERR_chuachonphuongxa": "You have not selected a ward or commune", "ERR_cannhaptenduong": "You need to enter a street name or address", "TINH/TP": "Province / City *", "QUAN/HUYEN": "District *", "PHUONG/XA": "Wards *", "TENDUONG": "Street names", "DIACHINHA": "Address (house number, description of hamlet, hamlet, ...)", "CHON_thanhpho": "Select province / city", "CHON_quanhuyen": "Select district", "CHON_phuongxa": "Select wards", "ALERT_loiketnoi_fb": "FACEBOOK connection error", "ALERT_khongthetruycap_fb": "Unable to access your FACEBOOK information", "ALERT_loiketnoi_gg": "Google connection error", "ALERT_serverchuaduoccaidat": "PLAY SERVICES is not installed", "ALERT_khongthetruycap_gg": "Unable to access your GOOGLE information", "ALERT_ktlaiemail": "Please check and confirm your email so you can log in again", "CHONLOAITHUCUNG": "Choose a pet type", "ALERT_chacchandangxuat": "Are you sure want to log out?", "THONGTIN_cuahang": "Store information", "CHUACOSANPHAM": "No items", "THUONGHIEU": "Trademark", "HETHANG": "Out of stock", "VANCHUYEN": "Shipping", "CHITIETSANPHAM": "Product details", "XEMCHITIET": "Details", "Hide": "<PERSON>de", "SANPHAMLIENQUAN": "Related products", "THEMVAOGIOHANG": "Add to cart", "MUANGAY": "Buy now", "SPDADUOCTHEMVAOGIOHANG": "The product has been added to cart!", "SANPHAMCUACUAHANG": "Products of the store", "CHUY": "Attention", "VUILONGCHONTHUOCTINH": "Please select the full attributes of the product", "PHISHIP": "Shipping fee", "NHAPTT_thanhtoan": "Enter payment information", "ALERT_magiamgia_khonghople": "The discount code is invalid or has expired", "CHACCHANMUONXOABOSP": "Are you sure want to delete this product?", "KHONG": "No", "GIOHANG": "Shopping Cart", "APDUNG": "Apply", "MAGIAMGIA/DONHANG": "Discount code (Orders above", "NHAPMAGIAMGIA": "Enter the discount code", "XONG": "Done", "DATHANGTHANHCONG": "Your order was placed successfully", "CHUCMUNGDATHANGTHANHCONG": "Congratulations on your successful order.", "CHUCUAHANGLIENHE": "The owner of the store will contact you soon to confirm your order.", "KHONGCOKETNOI": "No connection", "CHONNGUONTRUYCAPANH": "Select a photo access source", "THUVIENANH": "Photo library", "NHAPNOIDUNG": "Enter content", "DAGUIBIEUTUONG": "Sent an icon", "SEARCH": " Search ...", "CHAPNHAN_donhang": "Your order has been accepted and will be delivered to you soon!", "TUCHOI_donhang": "Reason of cancellation:", "LICHHEN_khambenh": "Health check appointment", "YEUCAU_thuephong": "Hotel reservation", "LICHHEN_spa": "SPA appointment", "DONGYVOI": "Agree ", "CUABAN": "", "TUCHOIVOI": "Refuse ", "VOINOIDUNG": " with content: ", "PHONG_SAP_HET_HAN": "Your rental room is due soon", "KHONG_CO_THONG_BAO": "No notifications", "TIM_KIEM_GAN_DAY": "Recent searches", "SP_MOI_NHAT": "Latest product", "KS_MOI_NHAT": "Latest hotel", "PK_MOI_NHAT": "Latest clinic", "SPA_MOI_NHAT": "Lastest Spa", "DANG_KY_THANHCONG": "Sign up sucessfully", "DANG_KY_THANHCONG_KTEMAIL": "Account registration has been successful. Please check your email", "XN_DE_HOAN_TAT_DK": "and confirm to complete the registration.", "NEU_K_NHAN_DUOC_MAIL": "If you do not receive any email, please wait a few minutes, or press the button below ", "GUI_LAI_MAIL": "Resent an email", "NEU_DA_KICH_HOAT_MAIL": "If the email has been activated, return to the page", "LAY_LAI_MK": "Password retrieval", "MA_TU_EMAIL": "Code from email", "MAT_KHAU_MOI": "New password", "NHAP_LAI_MK_MOI": "Reenter new password", "TT_HO_TRO": "Support center", "CS_BAO_MAT": "Privacy Policy", "DIEU_KHOAN_DV": "Terms of service", "GIOI_THIEU": "Introduce", "CHI_TIET_TIN_TUC": "News details", "ALERT_CAN_NHAP_DC_NHAN_HANG": "You need to enter a shipping address", "CHUYEN_KHOAN_TRUOC": "Bank transfer in advance", "TAI_QUAY": "At the counter", "CHON_NGAN_HANG": "<PERSON><PERSON>", "CHO_THU_CUNG": "for pets", "SPA_GROOMING": "Spa & Grooming", "CHUC_NANG_YEU_CAU_DANG_NHAP": "This function requires authentication. Please login to continue!", "DE_SAU": "Later", "Alert_chua_cap_nhat_tt_phong": "No room information updated, please come back later", "CHITIETLICHHEN_giadichvutrenngay": "Service price / day", "chua_ro": "Undefined!", "ALERT_chuanhap_ngaydenkham": "You have not selected a visit date.", "ALERT_chuanhap_thoigiandenkham": "You have not selected a time to visit the clinic.", "CHITIETLICHHEN_loaiphong": "Kind of room", "CHITIETLICHHEN_giaphong": "room price", "CHITIETLICHHEN_dichvudachon": "Service selected", "CHITIETLICHHEN_giadichvu": "Service prices", "ALERT_loiDatatheokhoanggia": "No data updated! Please come back later!", "PETNOTSELECT": "Please select a pet type", "BANCHUACHON": "You have not selected", "ALERT_DangKyThanhCong": "Register successfully ! Please login", "PTTT": "No form of payment selected", "CNDCNH": "No shipping address entered", "ALERT_loitinhphivc": "Error charging shipping", "Loai_phong": "Type room", "CHUAROPHISHIP": "Shipping fee unknown. ", "CHUP_ANH": "Take a photo", "TIN_TUC": "News", "KHONG_CO_BAI_VIET": "emty news!", "ALERT_commenthanhcong": "Sent commment sucessfully!", "BAN_MUON_TIM_GI": "What do you want to search...", "TOI_DANG_O_DAU": "Where are you?", "Danh_muc": "Category", "UU_DAI": "Discount", "CHON_NGON_NGU": "Chose your language", "THONG_BAO": "Notification", "MOI_BAN_BE": "Invite friend", "VUI_LONG_NHAP_LAI_MAT_KHAU": "Please re-enter your password", "VUI_LONG_NHAP_MAT_KHAU": "Please enter your password", "VUI_LONG_NHAP_MAT__KHAU_MOI": "Please enter new password", "MAT_KHAU_KHONG_KHOP": "Confirmation password does not match", "Create_Account": "Create your password", "Enter_New_Password_And_Confirm": "Please enter your new password and confirm", "SO_DT_KHONGCO_TRONG_HETHONG": "This phone number is not in the system", "TIM_LAI_MK": "Forgot your password", "VUI_LONG_NHAP_SDT_DE_LAY_LAI_MK": "Please enter your phone number to retrieve your password", "TIEP_THEO": "Next", "NHAP_SO_DT": "Enter your phone number", "ERROR_LOI_TRUNG_MK": "Old password cannot be the same as new password!", "ERROR_MK": "password is too short", "REGISTER": "Register", "LOGIN": "<PERSON><PERSON>", "FULLNAME": "Full name", "ENTER_FULLNAME": "Enter full name", "ENTER_EMAIL": "Enter your email", "ALERT_UPDATE_ERR": "Update profile error", "ALERT_UPDATE_SUCCESS": "Update profile success", "CHOOSE_A_DATE": "Choose a date", "PET_HOSPITAL": "Pet hospital", "SPA": "Spa", "HANDBOOK": "Handbook", "FORUM": "Forum", "TRADE": "Trade", "INSEMINATION": "Insemination", "COMING_SOON": "Coming soon", "FEATURE_IS_UPDATING": "This feature is updating , thank you!", "SEARCH_RESULTS": "Search results!", "RECOVER_PASSWORD": "Recover password", "CREATE_NEW_ACCOUNT": "Create new account", "ENTER_PHONE_NUMBER": "Enter your phone number", "FORGOTTEN_PASSWORD": "Have you forgotten your password ?", "FILL_OUT_THE_FORM": "Please fill out the form", "FAIL": "Fail!", "CLOSE": "Close", "ENTER_PHONE_NUMBER_TO_RECOVER_PASSWORD": "Please enter your phone number to recover your password", "AUTHENTICATION_CODE_CANNOT_BE_EMPTY": "Authentication code cannot be empty!", "CANNOT_CONFIRM": "Fail! Cannot be authenticated", "ENTER_AUTH_CODE": "Enter Auth Code", "ENTER_THE_VERIFICATION_CODE": "Please enter the verification code sent to your phone number", "YOU_DID_NOT_RECEIVE_VERIFICATION_CODE": "You did not receive a verification code?", "ACCURACY": "Accuracy", "RESEND": "Resend", "CANNOT_REGISTER": "Can not register", "PHONE_NUMBER_ALREADY_REGISTERED": "Phone number already registered, please use another one", "PHONE_NUMBER_CANNOT_BE_EMPTY": "Phone number cannot be empty", "PHONE_NUMBER_INCORRECT_FORMAT": "Phone number incorrect format", "PLEASE_LOGIN": "Please login to continue", "SELECT_DATE": "Select date", "SELECT_TIME": "Select time", "ERROR_SELECT_DATE": "Select date error", "ERROR_SELECT_TIME": "Select time error please try again!", "ERROR_SELECT_TIME_GO": "Time checkin must be greater than 60 minute", "SERVICE": "Service", "REVIEW": "Review", "DETAILS": "Details", "BUTTON_COMMENT": "here", "QUESTION_ASK_VIEW_COMMENT": "leave a review", "PRESS": "Press", "COMMENT": "Comment", "SEE_ALL": "See all", "BOOKING_SPA_OK": "Booking spa success .....", "CHOOSE_INFO_PET": "Choose info pet", "CHOOSE_WEIGHT_PET": "Choose weight pet", "PRICE_WEIGHT": "Price/ weight", "INPUT_NOTE": "Please input note!", "TOTAL_MONEY": "Total bill", "SERVICE_PRICE": "Service price", "ERROR_OLD_PASSWORD": "Old password invalid", "UPDATE": "Update", "BOOKING_HISTORY": "Booking history", "FULL_NAME": "Full Name", "ALERT_UPDATE_PROFILE_SUCCESS": "Congratulations on the successful update!", "ORDER": "Order", "CANNOT_CONNECT": "Unable to connect data", "register_success": "Sign up success", "err_account_does_not_exist": "Account does not exist", "CANNOT_CREATE_PASSWORD": "Can't create password, please try again later!", "err_email_already_exist": "Email already exists!", "Err_Incorrect_PhoneNumber_Or_Password": "Phone number or password is incorrect", "PASSWORD_MUST_BE_GREATER_THAN_5": "Password must be greater than 5 characters", "PHONE_MUST_BE_LESS_THAN_12": "Phone number must be less than 12 characters", "Score": "Score", "ERR_ACCOUNT_HAS_BEEN_DELETED": "Your account has been deleted", "INTRODUCE": "More than 1000 clinics across the country", "DISCOUNT": "Discount", "HAVETOPAY": "Must pay", "ADD_SERVICE": "Add services", "SEE_OTHER_ADDRESSES": "See also other addresses", "VERSION": "Version ", "OPTION": "Option", "ALLCOMMENT": "All comment", "LIST_BRANCH": "List branch", "DEFAULT-ADDRESS": "Default address", "INTRO_APP": "The first pet care application", "CONTENT_BOARDING1": "The right choice for these Pet lover in the house", "TITLE_BOARDING2": "Make a quick appointment fast", "CONTENT_BOARDING2": "Find the clinic closest to you", "CHOOSE_PROVINCE": "Choose your province", "FEATURED": "Featured", "ONBOARDING_START": "Start", "CASH": "CASH", "PAYMENT_CASH": "Payment at the store", "DONE": "Done", "CANCELED": "Canceled", "NO_HISTORY": "There is no history", "DONT_HAVE_PURCHASE_HISTORY": "You don't have any purchase history yet", "DONT_HAVE_BOOKING_SPA_HISTORY": "You do not have any history of Spa booking", "DONT_HAVE_MEDICAL_HISTORY": "You do not have medical history", "CHOOSE_LOCATION": "Choose your location", "CANCEL_BOOKING_COMPLETE": "you have cancel booking complete", "PAID_SUCCESSFULLY": "Your order has been paid successfully", "UNPAID": "Unpaid or defective order.", "PAID_AT_STORE": "This order will be paid at the store.", "PAYMENT_STATUS": "Payment status", "ORDER_CODE": "Order code", "REFUND_REGULATION": "Refund regulations", "REFUND_ONLY_FOR_CUSTOMER_PAYING_ONLINE": "Refund only for customers paying online", "REFUND_REQUEST_WHEN_THE_PET_HAS_NOT_BEEN_SHIPPED": "Refund requests are only accepted when the pet has not been shipped", "CUSTOMER_MUST_PROVIDE_THE_CORRECT-ACCOUNT_NUMBER": "Customers must provide the correct account number", "TOUCH_TO_CHOOSE_THE_REASON": "Touch to choose the reason", "clearly_explain_the_reason": "Clearly explain the reason", "reason_for_cancellation": "Reason for cancellation", "enter_the_reason": "Enter the reason", "CHOOSE_THE_REASON": "Choose the reason", "transportation_staff_arrived_too_slow": "Transportation staff arrived too slow", "change_the_address": "Change the address", "schedule_change": "Schedule change", "wrong_operation": "Wrong operation", "change_your_mind": "Change your mind", "send_request": "Send request", "request_to_cancel_the_order": "Request to cancel the order", "other_reason": "Orther reason", "FILTER": "Filter", "FILTERALL": "Filter all", "FILTERSPA": "Filter spa", "FILTERHOTEL": "Filter hotel", "FILTERCLICNIC": "Filter clicnic", "FILTERDISTANCE": "Distance", "ADDRESSSHIPPET": "Address ship pet", "ADDADDRESS": "Add address", "FILTERPRODUCT": "Filter product", "CART_CHOOSE": "selected items", "EXIST_ITEM_CART": "Product or service already in your shopping cart!", "ODER_STATUS": "Oder status", "TIME_ODER": "Oder time", "TITLE_CANCEL_ODER": "You want cancel oder ?", "ALERT_DELETE": "You want delete?", "REPORT_COMMENT": "You want report comment ?", "INPUT_COUPON": "Input coupon", "DELETE": "Delete", "FROM": "From", "TO": "to", "Undefined": "Unknown time", "shipping_address": "Shipping address", "all_product": "All product", "PRODUCT_TAB'": "Product", "wait_for_shipping": "Wait for shipping", "returns": "Returns", "delivering": "delivering", "BRANCH": "Branch", "NEAREST": "Nearest", "RATING": "Rating", "BOOKNOW": "Book now", "promotion_details": "Promotion details", "SETDEFAULT-ADDRESS": "set default address", "VALIDATE_PRODUCT_ORDER_ERROR": "An error has occurred while ordering", "ERROR_WEIGHT": "Product without weight. Cannot add to cart", "The_order_has_been_canceled": "The order has been canceled", "Payment_on_delivery": "Payment on delivery", "the_order_is_not_valid_or_has_been_deleted": "Order is invalid or has been deleted", "BOOKING_PRODUCT_SUCCESS": "Order success", "See_map": "See map", "Connection_to_Mypet_server_failed": "Connection to Mypet server failed", "add_pet": "Add pet", "select_pet_type": "Select type pet", "enter_pet_name": "Enter your pet name", "customer_information": "Customer information", "status": "Status", "code": "Code order", "CHAT_ROOM": "Inbox", "top_branch": "Top branch", "may_be_u_like": "Maybe you like", "sieu_thi": "Super market", "adopt": "<PERSON><PERSON><PERSON>", "vui_long_dang_nhap_lai": "Please relogin again to use new feature", "address": "Address", "enter_your_address": "Enter your address", "Enter_a_phone_number_to_continue": "Enter a phone number to continue", "mypet_community": "MyPet Community", "Add_note": "Add note", "Community": "Community", "News": "News for pet", "Advisory": "Advisory", "Your_Car": "Your car", "Version": "Version", "Species": "Species", "Pet_management": "Pet management", "Car_diary": "Pet diary", "Choose_image": "Choose avatar", "Choose_gender": "Choose gender", "Change_avatar": "Change pet avatar", "Add": "Add", "Add_picture_to_albums": "Add picture to the albums", "Create": "Create", "Create_diary": "Create diary", "Year_old": "year old", "Months": "months", "Days": "days", "Press_plus_in_the_right_corner_to_create_a_diary": "Press \"+\" in the right corner to create a diary", "Press_plus_in_the_right_corner_to_add_picture_to_albums": "Press \"+\" in the right corner to add picture to the albums", "Diary": "Diary", "Noti": "<PERSON>i", "Cart": "<PERSON><PERSON>", "Account": "Account", "Search": "Search", "HOTEL": "Hotel", "Clinic": "Clinic", "View_more": "View more", "Back_to_shopping": "Back to shopping", "Change_password_successful": "Change password successful!", "Product_price": "Product price", "No_more_data": "No more data", "Err_time_has_passed": "You can't choose the elapsed time!", "Choose_time_check_in": "Choose check in time", "Choose_time_check_out": "Choose check out time", "Check_in_time": "Check in time", "Check_out_time": "Check out time", "Choose_time": "Choose time"}