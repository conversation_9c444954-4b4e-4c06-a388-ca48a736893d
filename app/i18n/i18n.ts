
import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import AsyncStorage from '@react-native-async-storage/async-storage'
const translationEn = require('./en.json')
const translationVi = require('./vi.json')

const STORAGE_KEY = 'i18nextLng'

const languageDetector: any = {
  type: 'languageDetector',
  async: true, // flags below detection to be async
  detect: async (callback) => {
    const lng = await AsyncStorage.getItem(STORAGE_KEY)
    callback(lng || 'vi')
  },
  init: () => {},
  cacheUserLanguage: async (lng) => {
    await AsyncStorage.setItem(STORAGE_KEY, lng)
  },
}

// the translations
// (tip move them in a JSON file and import them)
const resources = {
  en: {
    translation: translationEn
  },
  vi: {
    translation: translationVi
  }
}

i18n
  .use(initReactI18next)
  .use(languageDetector)
  .init({
    resources,
    fallbackLng: 'en',
    keySeparator: false, // we do not use keys in form messages.welcome,
    interpolation: {
      escapeValue: false // react already safes from xss
    }
  })

export default i18n
