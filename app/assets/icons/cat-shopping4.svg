<?xml version="1.0" encoding="UTF-8"?>
<svg width="70px" height="72px" viewBox="0 0 70 72" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>cat-shopping</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FF938B" offset="0%"></stop>
            <stop stop-color="#FE4085" offset="100%"></stop>
        </linearGradient>
        <rect id="path-2" x="0" y="0" width="50" height="50.2347418" rx="18"></rect>
        <filter x="-32.0%" y="-27.9%" width="164.0%" height="163.7%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.902167206   0 0 0 0 0.910894256   0 0 0 0 0.937075408  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50.0002531%" y1="100%" x2="50.0002531%" y2="0.000186696082%" id="linearGradient-4">
            <stop stop-color="#FD3A84" offset="0%"></stop>
            <stop stop-color="#FFA68D" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9996927%" y1="100%" x2="49.9996927%" y2="0.000578113044%" id="linearGradient-5">
            <stop stop-color="#FD3A84" offset="0%"></stop>
            <stop stop-color="#FFA68D" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.0002212%" y1="100%" x2="50.0002212%" y2="0%" id="linearGradient-6">
            <stop stop-color="#FD3A84" offset="0%"></stop>
            <stop stop-color="#FFA68D" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-7">
            <stop stop-color="#FFC8D1" offset="0%"></stop>
            <stop stop-color="#FFF6F7" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-8">
            <stop stop-color="#FFC2CC" offset="0%"></stop>
            <stop stop-color="#FFF2F4" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="[Setup]:-Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="danhmuc-component" transform="translate(-291.000000, -40.000000)">
            <g id="Group-2" transform="translate(13.000000, 15.000000)">
                <g id="item-cat" transform="translate(278.000000, 33.219982)">
                    <g id="cat-shopping" transform="translate(10.000000, 0.000000)">
                        <g id="Rectangle-Copy-2">
                            <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                            <use fill="url(#linearGradient-1)" fill-rule="evenodd" xlink:href="#path-2"></use>
                        </g>
                        <g id="Group" transform="translate(8.000000, 8.117371)" fill-rule="nonzero">
                            <g id="shopping-basket">
                                <path d="M5.09359881,13.6 C4.90890951,13.6 4.7225315,13.5192227 4.56666296,13.3520765 C4.20308229,12.9602048 4.14369576,12.2474153 4.4347742,11.7581424 L11.1896403,0.423633759 C11.4823512,-0.0611809557 12.0688425,-0.143091671 12.4332675,0.246513171 C12.7969607,0.638384915 12.856291,1.35117438 12.5652125,1.84044733 L5.75405585,13.1749559 C5.58670404,13.453936 5.34184014,13.6 5.09359881,13.6 Z" id="Path" fill="url(#linearGradient-4)"></path>
                                <path d="M28.9007799,13.6 C28.6509398,13.6 28.4043863,13.4538732 28.2359185,13.174993 L21.4360064,1.84147334 C21.1429868,1.35224308 21.2027694,0.639515803 21.5687747,0.24767825 C21.9365366,-0.143025952 22.469423,-0.0622557348 22.7640859,0.424783384 L29.563998,11.7583031 C29.8570176,12.2475333 29.797235,12.9602606 29.4311731,13.3520981 C29.2742651,13.5192298 29.0867009,13.6 28.9007799,13.6 Z" id="Path" fill="url(#linearGradient-5)"></path>
                                <g id="Shopping_basket_2_" transform="translate(0.000000, 10.880000)">
                                    <path d="M27.0273437,21.7598549 L6.97265625,21.7598549 C6.52813281,21.7598549 6.13706641,21.437952 6.01448047,20.9704021 L2.03010547,5.73840213 C1.94450781,5.41011627 2.00480469,5.05731413 2.19253516,4.78429867 C2.38126172,4.51229867 2.67597266,4.35185493 2.98821484,4.35185493 L31.0116523,4.35185493 C31.3238945,4.35185493 31.6186719,4.51229867 31.807332,4.78429867 C31.9950625,5.05738667 32.0553594,5.41011627 31.9697617,5.73840213 L27.9853867,20.9704021 C27.8629336,21.437952 27.4718672,21.7598549 27.0273437,21.7598549 Z" id="Path" fill="url(#linearGradient-6)"></path>
                                    <path d="M33.0039062,-0.000145066667 L0.99609375,-0.000145066667 C0.445519531,-0.000145066667 0,0.486481067 0,1.08785493 L0,5.43985493 C0,6.0412288 0.445519531,6.52785493 0.99609375,6.52785493 L33.0039062,6.52785493 C33.5544805,6.52785493 34,6.0412288 34,5.43985493 L34,1.08785493 C34,0.486481067 33.5544805,-0.000145066667 33.0039062,-0.000145066667 Z" id="Path" fill="url(#linearGradient-7)"></path>
                                    <path d="M10.9570312,8.70385493 C10.406457,8.70385493 9.9609375,9.19048107 9.9609375,9.79185493 L9.9609375,16.3198549 C9.9609375,16.9212288 10.406457,17.4078549 10.9570312,17.4078549 C11.5076055,17.4078549 11.953125,16.9212288 11.953125,16.3198549 L11.953125,9.79185493 C11.953125,9.19048107 11.5076055,8.70385493 10.9570312,8.70385493 Z M17,8.70385493 C16.4494258,8.70385493 16.0039062,9.19048107 16.0039062,9.79185493 L16.0039062,16.3198549 C16.0039062,16.9212288 16.4494258,17.4078549 17,17.4078549 C17.5505742,17.4078549 17.9960938,16.9212288 17.9960938,16.3198549 L17.9960938,9.79185493 C17.9960938,9.19048107 17.5505742,8.70385493 17,8.70385493 Z M23.0429688,8.70385493 C22.4923945,8.70385493 22.046875,9.19048107 22.046875,9.79185493 L22.046875,16.3198549 C22.046875,16.9212288 22.4923945,17.4078549 23.0429688,17.4078549 C23.593543,17.4078549 24.0390625,16.9212288 24.0390625,16.3198549 L24.0390625,9.79185493 C24.0390625,9.19048107 23.593543,8.70385493 23.0429688,8.70385493 Z" id="Shape" fill="url(#linearGradient-8)"></path>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>