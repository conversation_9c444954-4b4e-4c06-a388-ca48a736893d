<?xml version="1.0" encoding="UTF-8"?>
<svg width="70px" height="72px" viewBox="0 0 70 72" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>cat-shopping</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFA100" offset="0%"></stop>
            <stop stop-color="#FD6800" offset="100%"></stop>
        </linearGradient>
        <rect id="path-2" x="0" y="0" width="50" height="50.2347418" rx="18"></rect>
        <filter x="-32.0%" y="-27.9%" width="164.0%" height="163.7%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.902167206   0 0 0 0 0.910894256   0 0 0 0 0.937075408  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="4.16333634e-15%" id="linearGradient-4">
            <stop stop-color="#FD5900" offset="0%"></stop>
            <stop stop-color="#FFDE00" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="-0.000303031221%" id="linearGradient-5">
            <stop stop-color="#FFE59A" offset="0%"></stop>
            <stop stop-color="#FFFFD5" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="[Setup]:-Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="danhmuc-component" transform="translate(-199.000000, -138.000000)">
            <g id="Group-2" transform="translate(13.000000, 15.000000)">
                <g id="item-cat-copy-6" transform="translate(186.000000, 131.680076)">
                    <g id="cat-shopping" transform="translate(10.000000, 0.000000)">
                        <g id="Rectangle-Copy-2">
                            <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                            <use fill="url(#linearGradient-1)" fill-rule="evenodd" xlink:href="#path-2"></use>
                        </g>
                        <g id="2924782" transform="translate(8.000000, 8.037559)" fill-rule="nonzero">
                            <path d="M15.0664,9.30910047 C15.4678,9.65056275 15.8955333,10.0125878 16.3447333,10.4040839 C16.5332,10.5679161 16.7666,10.6493634 17,10.6493634 C17.2334,10.6493634 17.4668,10.5679161 17.6552667,10.4040839 C18.1054667,10.0116501 18.5322,9.64862034 18.9326,9.30910047 C21.3593333,7.24773459 22.9618667,5.88590423 22.9618667,3.71067293 C22.9618667,1.629682 21.4326,0 19.4804,0 C18.3818667,0 17.5674,0.504289828 17,1.139123 C16.4326,0.504289828 15.6181333,0 14.5195333,0 C12.5674,0 11.0380667,1.629682 11.0380667,3.71067293 C11.0380667,5.89474554 12.7275333,7.32723944 15.0664,9.30910047 L15.0664,9.30910047 Z M17,19.6452 C17.64,20.6900156 18,21.9358372 18,23.2419405 C18,24.5480438 17.64,25.7939324 17,26.8386811 C16.3000667,28.0242879 15.26,28.988728 14,29.5915449 C13.1,30.0336776 12.0799333,30.2748044 11,30.2748044 C7.14,30.2748044 4,27.1200626 4,23.2419405 C4,19.3638185 7.14,16.2090767 11,16.2090767 C12.0799333,16.2090767 13.1,16.4502034 14,16.8923362 C14.8000667,15.746984 15.8199333,14.7422892 17,13.9787211 C15.28,12.8534629 13.22,12.1903643 11,12.1903643 C4.92006667,12.1903643 0,17.1334629 0,23.2419405 C0,29.3504182 4.92006667,34.2935837 11,34.2935837 C13.22,34.2935837 15.28,33.6304182 17,32.5052269 C18.1800667,31.7416588 19.1999333,30.736964 20,29.5916119 C21.26,27.8033221 22,25.6130873 22,23.2420075 C22,20.8709277 21.26,18.680626 20,16.8924031 C18.74,17.4950861 17.6999333,18.4595931 17,19.6452 Z" id="Shape" fill="url(#linearGradient-4)"></path>
                            <path d="M34,23.2419405 C34,29.3504182 29.0799333,34.2935837 23,34.2935837 C20.78,34.2935837 18.72,33.6304182 17,32.5052269 C18.1800667,31.7416588 19.1999333,30.736964 20,29.5916119 C20.9,30.0336776 21.9200667,30.2748044 23,30.2748044 C26.86,30.2748044 30,27.1200626 30,23.2419405 C30,19.3638185 26.86,16.2090767 23,16.2090767 C21.9200667,16.2090767 20.9,16.4502034 20,16.8923362 C18.74,17.4951531 17.6999333,18.4595931 17,19.6452 C16.36,20.6900156 16,21.9358372 16,23.2419405 C16,24.5480438 16.36,25.7939324 17,26.8386811 C16.3000667,28.0242879 15.26,28.988728 14,29.5915449 C12.74,27.8033221 12,25.6130203 12,23.2419405 C12,20.8708607 12.74,18.680559 14,16.8923362 C14.8000667,15.746984 15.8199333,14.7422892 17,13.9787211 C18.72,12.8534629 20.78,12.1903643 23,12.1903643 C29.0799333,12.1903643 34,17.1334629 34,23.2419405 Z" id="Path" fill="url(#linearGradient-5)"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>