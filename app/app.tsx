/**
 * Welcome to the main entry point of the app. In this file, we'll
 * be kicking off our app or storybook.
 *
 * Most of this file is boilerplate and you shouldn't need to modify
 * it very often. But take some time to look through and understand
 * what is going on here.
 *
 * The app navigation resides in ./app/navigation, so head over there
 * if you're interested in adding screens and navigators.
 */
import './i18n'
import './utils/ignore-warnings'

import { NavigationContainerRef } from '@react-navigation/native'
import React, { useEffect, useMemo, useReducer, useRef, useState } from 'react'
import { SafeAreaProvider, initialWindowMetrics } from 'react-native-safe-area-context'
import {
  TourGuideProvider, // Main provider
} from 'rn-tourguide'
// This puts screens in a native ViewController or Activity. If you want fully native
// stack navigation, use `createNativeStackNavigator` in place of `createStackNavigator`:
// https://github.com/kmagiera/react-native-screens#using-native-stack-navigator
import { enableScreens } from 'react-native-screens'

import { RootStore, RootStoreProvider, setupRootStore } from './models'
import {
  RootNavigator,
  canExit,
  setRootNavigation,
  useBackButtonHandler,
  useNavigationPersistence,
} from './navigation'
import * as storage from './utils/storage'
import SplashScreen from 'react-native-splash-screen'
// import auth, { FirebaseAuthTypes } from "@react-native-firebase/auth"
// eslint-disable-next-line react-native/split-platform-components
import { authReducer, initialState } from './reduder'
import { getToken, removeToken, saveToken, getCountProductGioHang } from '@app/services'
import { AuthContext } from '@app/context'
import { loadString, saveString } from './utils/storage'
import auth, { FirebaseAuthTypes } from '@react-native-firebase/auth'
import useAppState from 'react-native-appstate-hook'
import moment from 'moment'
import { LogEvent } from './services/loggingServices'
import { ModalProvider, AppLoadingProvider } from '@app/components'
import { NetworkProvider } from 'react-native-offline'
import NetInfo from '@react-native-community/netinfo'
import SimpleToast from 'react-native-simple-toast'
import remoteConfig from '@react-native-firebase/remote-config'

const MIN_BACKGROUND_DURATION_IN_MIN = 30

enableScreens()

export const NAVIGATION_PERSISTENCE_KEY = 'NAVIGATION_STATE'

type User = FirebaseAuthTypes.User | null

// export const UserContext = createContext<User>(null)

/**
 * This is the root component of our app.
 */
// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
function App() {
  const navigationRef = useRef<NavigationContainerRef>()
  const [rootStore, setRootStore] = useState<RootStore | undefined>(undefined)
  const [initializing, setInitializing] = useState(true)
  const [listenUser] = useState(false)
  const [user, setUser] = useState<User>(null)
  const [state, dispatch] = useReducer(authReducer, initialState)
  const [lastBackgroundedTime, setLastBackgroundedTime] = useState(null)

  setRootNavigation(navigationRef)
  useBackButtonHandler(navigationRef, canExit)

  const { initialNavigationState, onNavigationStateChange } = useNavigationPersistence(
    storage,
    NAVIGATION_PERSISTENCE_KEY,
  )

  useAppState({
    onChange: (newAppState) => {
      // __DEV__ && console.log('App state changed to ', newAppState)
    }, // 'active' | 'background' | 'inactive' | 'unknown' | 'extension'
    async onForeground() {
      // Only run the sync if app has been in the background for a certain amount of time
      if (moment.duration(moment().diff(lastBackgroundedTime)).asMinutes() > MIN_BACKGROUND_DURATION_IN_MIN) {
      }
    },
    onBackground: () => {
      setLastBackgroundedTime(moment())
    },
  })

  /** Listen for user changes */
  useEffect(() => {
    let userListener: () => void

    if (listenUser) {
      userListener = auth().onUserChanged(result => {
        setUser(result)
      })
    }
    return () => {
      if (userListener) {
        userListener()
      }
    }
  }, [listenUser])

  // Handle user state changes
  function onAuthStateChanged(user) {
    if (user) {
      setUser(user)
      // console.log('User Login: ', JSON.stringify(user))
    } else {
      // console.log('User Signout')
    }
    if (initializing) setInitializing(false)
  }

  useEffect(() => {
    const subscriber = auth().onAuthStateChanged(onAuthStateChanged)
    return subscriber // unsubscribe on unmount
  }, [])

  // Kick off initial async loading actions, like loading fonts and RootStore
  useEffect(() => {
    SplashScreen.hide()
    bootstrapAsync().then(r => {})
    checkOnBoarding()

    ;(async () => {
      setupRootStore().then(setRootStore)
    })()

    // Subscribe
    const unsubscribe = NetInfo.addEventListener(state => {
      if (!state.isConnected) {
        SimpleToast.show('Mất kết nối internet, vui lòng kiểm tra lại')
      }
    })

    return () => {
      // console.log('componentWillUnmount')
      // Unsubscribe
      unsubscribe()
    }
  }, [])

  const checkOnBoarding = async () => {
    const stateOnBoarding = await loadString('isOnBoarding')
    __DEV__ && console.log('stateOnBoarding', stateOnBoarding)
    const isShowOnBoarding = stateOnBoarding === null
    dispatch({ type: 'SET_ONBOARDED', isOnBoarding: isShowOnBoarding })
  }

  // Fetch the token from storage then navigate to our appropriate place
  const bootstrapAsync = async () => {
    await auth().signInAnonymously() // đăng nhập fire guest

    // get remote config

    const config = {
      API_URL: 'http://27.71.27.94:4000',
      API_URL2: 'http://27.71.27.94:1337',
      API_URL_BH_PVI: 'https://apiwebview.pvi.com.vn/',
      API_URL_BH_BIC: 'https://mybic.vn/resapi/',
      URL_PVCOMBANK: 'https://digitalbanking.pvcombank.com.vn/',
      PVI_CPID: '7708feab193d47b7bba35104b5060b29',
      PVI_KEY: '43a954cb129d4f4383765b1237b1008f',
      BIC_DEVICE_ID: 'maxq_app',
      BIC_USERNAME: 'maxq_app_user',
      BIC_PASSWORD: 'U2gNtQIlu2',
      PVCB_APP_ID: 'TGCORP',
      PVCB_APP_CODE: 'TGCORP',
      PVCB_KEY_TRACKING: 'eN8yfKS6eeE4CgtFjCxGwJ',
      PVCB_IS_PROD: false
    }

    const apiURL = config.API_URL
    const apiURL2 = config.API_URL2
    saveString('API_URL', apiURL) // TODO: remove
    saveString('API_URL2', apiURL2) // TODO: remove
    saveString('DEV_CONFIG', config.toString())
    saveString('LIVE_CONFIG', config.toString())

    // get remote config from firebase configuration
    remoteConfig().setConfigSettings({
      minimumFetchIntervalMillis: 3600 * 10,
    }).then(r => {
      remoteConfig()
        .setDefaults({})
        .then(() => remoteConfig().fetchAndActivate())
        .then(fetchedRemotely => {
          if (fetchedRemotely) {
            __DEV__ && console.log('FIREBASE ****** Configs were retrieved from the backend and activated. Update', remoteConfig().getAll())
            const apiURL = config.API_URL
            const apiURL2 = config.API_URL2
            saveString('API_URL', apiURL) // TODO: remove
            saveString('API_URL2', apiURL2) // TODO: remove
            saveString('DEV_CONFIG', config.toString())
            saveString('LIVE_CONFIG', config.toString())
          } else {
            __DEV__ && console.log('FIREBASE ******  No configs were fetched from the backend, and the local configs were already activated')
          }
        }).catch((e) => {
          __DEV__ && console.log('FIREBASE ******  Error get configs from firebase')
        })
    })

    let userToken
    let shoppingCount = 0
    try {
      userToken = await getToken()
      shoppingCount = getCountProductGioHang(null)
      // After restoring token, we may need to validate it in production apps
      // This will switch to the App screen or Auth screen and this loading
      // screen will be unmounted and thrown away.
      dispatch({ type: 'RESTORE_TOKEN', token: userToken })
      dispatch({ type: 'UPDATE_SHOPPING_COUNT', shoppingCount })
    } catch (e) {
      // Restoring token failed
      __DEV__ && console.error(e)
    }
  }

  const authContext = useMemo(
    () => ({
      signIn: async () => {
        // được gọi sau khi đăng nhập
        const token = await getToken()
        if (token) {
          dispatch({ type: 'SIGN_IN', token: token, isSignedIn: true })
          LogEvent('sign_in', token)
        } else {
          // chuyển hướng qua trang đăng nhập
          dispatch({ type: 'TO_SIGNIN_PAGE', isSignedIn: false })
          LogEvent('open_sign_in_screen', {})
        }
      },
      signOut: async () => {
        // logout firebase
        auth().signOut().then(() => {
          __DEV__ && console.log('User firebase logout')
        })
        await removeToken() // xoá token
        dispatch({ type: 'SIGN_OUT', token: null, isSignedIn: false })
      },
      signUp: async () => {
        const token = await getToken() // save token api
        if (token) {
          // lưu token localstorage
          await saveToken(token)
          dispatch({ type: 'SIGNED_UP', token: token })
        } else {
          // chuyển hướng qua trang đăng ký
          dispatch({ type: 'TO_SIGNUP_PAGE' })
        }
      },
      setBoarded: async () => {
        await saveString('isOnBoarding', 'true')
        // logout firebase
        dispatch({ type: 'SET_ONBOARDED', isOnBoarding: false })
      },
      updateShoppingCount: async (count: number) => {
        dispatch({ type: 'UPDATE_SHOPPING_COUNT', shoppingCount: count })
      }
    }),
    [],
  )

  // Before we show the app, we have to wait for our state to be ready.
  // In the meantime, don't render anything. This will be the background
  // color set in native by rootView's background color. You can replace
  // with your own loading component if you wish.
  if (!rootStore) return null

  // otherwise, we're ready to render the app
  return (
    <RootStoreProvider value={rootStore}>
      <TourGuideProvider {...{ borderRadius: 16 }}>
        <ModalProvider>
          <AppLoadingProvider>
            <NetworkProvider pingServerUrl={'https://google.com.vn'} pingOnlyIfOffline={true}>
              <AuthContext.Provider value={{ ...authContext, ...state, ...user }}>
                {/* <WebSocketProvider> */}
                <SafeAreaProvider initialMetrics={initialWindowMetrics}>
                  <RootNavigator
                    state={state}
                    ref={navigationRef}
                    initialState={initialNavigationState}
                    onStateChange={onNavigationStateChange} />
                </SafeAreaProvider>
                {/* </WebSocketProvider> */}
              </AuthContext.Provider>
            </NetworkProvider>
          </AppLoadingProvider>
        </ModalProvider>
      </TourGuideProvider>
    </RootStoreProvider>
  )
}

// const codePushOptions = { checkFrequency: codePush.CheckFrequency.MANUAL }
// export default codePush(codePushOptions)(App)
export default App
