/**
 * The root navigator is used to switch between major navigation flows of your app.
 * Generally speaking, it will contain an auth flow (registration, login, forgot password)
 * and a "main" flow (which is contained in your PrimaryNavigator) which the user
 * will use once logged in.
 */
import React, { useContext, useEffect, useState } from 'react'
import { NavigationContainer, NavigationContainerRef } from '@react-navigation/native'
import { createNativeStackNavigator } from 'react-native-screens/native-stack'
import { TabNavigator } from './primary-navigator'
import { AuthStackNavigator } from './auth-navigator'
import { SCREENS } from './screens'
import { Platform } from 'react-native'
import { AppLoadingScreen, } from '../screens'
import { useStores } from '@app/models'
import messaging from '@react-native-firebase/messaging'
import { registerAppWithFCM, requestUserPermission } from '@app/services'
import { LogEvent } from '../services/loggingServices'
import PushNotification from 'react-native-push-notification'
import { ModalContext } from '@app/components/modal-success'
import useAppState from 'react-native-appstate-hook'
import { EVENT } from '@app/constants/event'
import { EventRegister } from 'react-native-event-listeners'
import Geolocation from '@react-native-community/geolocation'
import { save } from '@app/utils/storage'

const Stack = createNativeStackNavigator()

function RootStack(props) {
  const { state } = props
  // const navigation = useNavigation()
  __DEV__ && console.log('RootStack State App', props.state)
  return (
    <Stack.Navigator
      initialRouteName={state?.isOnBoarding ? SCREENS.onBoarding : SCREENS.primaryStack}
      screenOptions={{
        headerShown: false,
        gestureEnabled: true
      }}
    >
      <>
        <Stack.Screen name={SCREENS.primaryStack} component={TabNavigator} />
        <Stack.Screen name={SCREENS.appLoading} component={AppLoadingScreen} />
        <Stack.Screen name={SCREENS.authStack} component={AuthStackNavigator} />
      </>
    </Stack.Navigator>
  )
}

interface ComponentProps {
  state: any
}

export type NavigationContainerProps = React.ComponentProps<typeof NavigationContainer> & ComponentProps

export const RootNavigator = React.forwardRef<
  NavigationContainerRef,
  Partial<NavigationContainerProps>
>((props, ref) => {
  const { profileStore } = useStores()
  const { showSuccess, showError } = useContext(ModalContext)
  const [watchId, setWatchId] = useState<number | null>(null)

  useAppState({
    onChange: (newAppState) => {
      __DEV__ && console.log('RootNavigator =>>>> App state changed to ', newAppState)
      // getFcmToken()
    }, // 'active' | 'background' | 'inactive' | 'unknown' | 'extension'
  })

  const saveTokenToDatabase = async (token: any) => {
    if (token) {
      LogEvent('save_token_firebase_app_state_change', token)
      // call store
      if (profileStore.isSignedIn) {
        await profileStore.updateTokenNotification(token)
      }
    }
  }

  // useEffect(() => {
  //   const listener: any = EventRegister.addEventListener(EVENT.allowLocation, (data) => {
  //     __DEV__ && console.log('allowLocation EVENT', data)
  //     showError('', data)
  //   })
  //   const listenerWatchLocation: any = EventRegister.addEventListener(EVENT.watchLocationChange, (data) => {
  //     __DEV__ && console.log('watchLocationChange EVENT', data)
  //     callLocation()
  //   })
  //
  //   return () => {
  //     EventRegister.removeEventListener(listener)
  //     EventRegister.removeEventListener(listenerWatchLocation)
  //
  //     // Clear watch on unmount if watchId exists
  //     if (watchId !== null) {
  //       Geolocation.clearWatch(watchId)
  //     }
  //     try {
  //       Geolocation.stopObserving()
  //     } catch (e) {
  //
  //     }
  //   }
  // }, [watchId])

  const callLocation = () => {
    // Stop observing to prevent duplicate listeners if already observing
    try {
      Geolocation.stopObserving()
    } catch (e) {

    }

    // Start watching the location
    const id = Geolocation.watchPosition(
      async (pos) => {
        __DEV__ && console.log('watchPosition', pos)
        await save('location', {
          latitude: pos.coords.latitude,
          longitude: pos.coords.longitude
        })
      },
      (error) => {
        __DEV__ && console.log(error.message)
      },
      { enableHighAccuracy: true, timeout: 20000, maximumAge: 1000 }
    )

    setWatchId(id)

    // Get the current position
    Geolocation.getCurrentPosition(
      async (pos) => {
        __DEV__ && console.log('Current position', pos)
        await save('location', {
          latitude: pos.coords.latitude,
          longitude: pos.coords.longitude
        })
      },
      (error) => {
        __DEV__ && console.log(error.message)
      },
      { enableHighAccuracy: true, timeout: 20000, maximumAge: 1000 }
    )
  }

  useEffect(() => {
    setTimeout(() => {
      // Get the device token
      getFcmToken()
    }, 1000)

    // If using other push notification providers (ie Amazon SNS, etc)
    // you may need to get the APNs token instead for iOS:
    // if(Platform.OS == 'ios') { messaging().getAPNSToken().then(token => { return saveTokenToDatabase(token); }); }
    messaging().onTokenRefresh(async (fcmToken) => {
      console.log('New FCM Token:', fcmToken)
      saveTokenToDatabase(fcmToken)
    })
  }, [])

  // @ts-ignore
  const getFcmToken = () => {
    messaging().requestPermission().then(r => {
      messaging().getToken().then(token => {
        __DEV__ && console.log('TOKEN FCM root-navigator.tsx', token)
        saveTokenToDatabase(token).then(r => {})

        // Register device to topics
        if (Platform.OS === 'ios') {
          // Register to iOS-specific topic
          messaging().subscribeToTopic('ios').then(r => {})
        } else if (Platform.OS === 'android') {
          // Register to Android-specific topic
          messaging().subscribeToTopic('android').then(r => {})
        }

        // Register to general topic
        messaging().subscribeToTopic('all').then(r => {})
      })
    })
  }

  const goBookingDetail = ({ orderId, typeService }) => {
    // ref?.current.navigation.dispatch(
    //   StackActions.replace(SCREENS.bookingHistoryDetail, { orderId: orderId })
    // )
    if (profileStore.isSignedIn()) {
      // ref?.current?.navigate(SCREENS.notificationScreen)
      // @ts-ignore
      ref?.current?.navigate(SCREENS.settingStack, { screen: SCREENS.bookingHistoryDetail, params: { orderId, bookingType: typeService } })
    } else {
      // @ts-ignore
      ref?.current?.navigate(SCREENS.authStack, { screen: SCREENS.login })
    }
  }

  /** Listen for auth state changes */
  useEffect(() => {
    // cố tình hardcode để test open notification
    // goBookingDetail({ orderId: '2021013172215', bookingType: BookingType.SPA }) // go detail spa

    registerAppWithFCM()
    // 1. notification - request permision
    requestUserPermission()
    // 2. Register background handler
    messaging().setBackgroundMessageHandler(async remoteMessage => {
      __DEV__ && console.log('Message handled in the background!', remoteMessage)
    })

    messaging().onNotificationOpenedApp(notification => {
      __DEV__ && console.log(
        'Notification caused app to open from background state:',
        notification,
      )
      if (notification && notification.data && notification.data.notifyType) {
        const item: any = notification.data
        if (item.userId !== profileStore._id) return
        if (item.watched === '0') {
          // TODO: update api cập nhật đã xem
          // this.props.request(`/da-xem-thong-bao/${item._id}.html`, {}, false, null, false, null, false, 'get')
        }
        // TODO: xử lý theo từng trường hợp

        if (item.orderId && item.typeService) {
          goBookingDetail(item)
        } else {
          setTimeout(() => {
            if (Number(item.notifyType)) { // @ts-ignore
              ref?.current?.navigate(SCREENS.notificationScreen)
            }
          }, 700)
        }

        // mở tab thông báo
        resetCountBadge()
      }
    })

    const resetCountBadge = () => {
      if (Platform.OS === 'ios') {
        setTimeout(() => {
          PushNotification.setApplicationIconBadgeNumber(0)
        }, 500)
      } else {
        setTimeout(async () => {
          PushNotification.setApplicationIconBadgeNumber(0)
        }, 500)
      }
    }

    // Check whether an initial notification is available
    messaging()
      .getInitialNotification()
      .then(notification => {
        if (notification) {
          console.log('Notification caused app to open from quit state:', notification)
        }
      })

    return messaging().onMessage(async remoteMessage => {
      console.log('A new FCM message arrived!', JSON.stringify(remoteMessage))
    })
  }, [])

  return (
    <NavigationContainer {...props} ref={ref}>
      <RootStack {...props}/>
    </NavigationContainer>
  )
})

RootNavigator.displayName = 'RootNavigator'
