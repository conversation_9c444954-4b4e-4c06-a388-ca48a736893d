import {
  Confirm<PERSON><PERSON>,
  EditProfileScreen,
  ForgotPassword,
  Login,
  RecoverPasswordScreen,
  RegisterScreen,
  CreatePasswordNewAccount
} from '../screens'
import React from 'react'
import { createNativeStackNavigator } from 'react-native-screens/native-stack'
import { SCREENS } from './screens'
import { View } from 'react-native'

const AuthStack = createNativeStackNavigator()

// TAB SETTING
export const AuthStackNavigator = (props: any) => {
  const defaultScreen = props.route?.params?.screen || SCREENS.login
  return (
    <>
      {/* <SafeAreaView style={styles.AndroidSafeArea}/> */}
      <View style={{ flex: 1, position: 'relative' }}>
        <AuthStack.Navigator
          initialRouteName={defaultScreen}
          screenOptions={{
            headerShown: false,
            gestureEnabled: true,
          }}>
          <AuthStack.Screen
            name={SCREENS.login}
            component={Login}
          />
          <AuthStack.Screen
            name={SCREENS.editProfile}
            component={EditProfileScreen}
          />
          <AuthStack.Screen
            name={SCREENS.register}
            component={RegisterScreen}
          />
          <AuthStack.Screen
            name={SCREENS.confirmCode}
            component={ConfirmCode}
          />
          <AuthStack.Screen
            name={SCREENS.createPasswordNewAccount}
            component={CreatePasswordNewAccount}
          />
          <AuthStack.Screen
            name={SCREENS.forgotPassword}
            component={ForgotPassword}
          />
          <AuthStack.Screen
            name={SCREENS.recoverPassword}
            component={RecoverPasswordScreen}
          />
        </AuthStack.Navigator>
      </View>
    </>
  )
}
