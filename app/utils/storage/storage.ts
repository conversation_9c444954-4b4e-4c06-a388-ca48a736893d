import AsyncStorage from '@react-native-async-storage/async-storage'
import { ApiConfig, DEFAULT_API_CONFIG } from '@app/services/api/api-config'

/**
 * Loads a string from storage.
 *
 * @param key The key to fetch.
 */
export async function loadString(key: string): Promise<string | null> {
  try {
    return await AsyncStorage.getItem(key)
  } catch {
    // not sure why this would fail... even reading the RN docs I'm unclear
    return null
  }
}

/**
 * Saves a string to storage.
 *
 * @param key The key to fetch.
 * @param value The value to store.
 */
export async function saveString(key: string, value: string): Promise<boolean> {
  try {
    await AsyncStorage.setItem(key, value)
    return true
  } catch {
    return false
  }
}

/**
 * Loads something from storage and runs it thru JSON.parse.
 *
 * @param key The key to fetch.
 */
export async function load(key: string): Promise<any | null> {
  try {
    const almostThere = await AsyncStorage.getItem(key)
    return JSON.parse(almostThere)
  } catch {
    return null
  }
}

/**
 * Saves an object to storage.
 *
 * @param key The key to fetch.
 * @param value The value to store.
 */
export async function save(key: string, value: any): Promise<boolean> {
  try {
    await AsyncStorage.setItem(key, JSON.stringify(value))
    return true
  } catch {
    return false
  }
}

/**
 * Removes something from storage.
 *
 * @param key The key to kill.
 */
export async function remove(key: string): Promise<void> {
  try {
    await AsyncStorage.removeItem(key)
  } catch {}
}

/**
 * Burn it all to the ground.
 */
export async function clear(): Promise<void> {
  try {
    await AsyncStorage.clear()
  } catch {}
}

export async function getRemoveConfig(): Promise<ApiConfig> {
  return loadString(__DEV__ ? 'DEV_CONFIG' : 'LIVE_CONFIG').then(e => {
    __DEV__ && console.log('******CONFIG REMOTE *******', __DEV__, e)
    const config = DEFAULT_API_CONFIG
    if (e) {
      const json = JSON.parse(e)
      config.url2 = json['API_URL2']
      config.url = json['API_URL']

      // PVI Config
      config.apiBhPvi = json['API_URL_BH_PVI']
      config.pviKey = json['PVI_KEY']
      config.pviCpid = json['PVI_CPID']

      // BIC Config
      config.apiBhBic = json['API_URL_BH_BIC']
      config.bicDeviceId = json['BIC_DEVICE_ID']
      config.bicUserName = json['BIC_USERNAME']
      config.bicPassword = json['BIC_PASSWORD']

      config.url_pvcombank = json['URL_PVCOMBANK']

      config.pvcbAppId = json['PVCB_APP_ID']
      config.pvcbAppCode = json['PVCB_APP_CODE']
      config.pvcbKeyTracking = json['PVCB_KEY_TRACKING']
      config.pvcbIsProd = json['PVCB_IS_PROD']

      __DEV__ && console.log('config remote object', config)
      return config
    }
    return config
  })
}
