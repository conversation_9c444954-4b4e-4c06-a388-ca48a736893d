/**
 * Ignore some yellowbox warnings. Some of these are for deprecated functions
 * that we haven't gotten around to replacing yet.
 */
import { LogBox } from 'react-native'

// prettier-ignore
LogBox.ignoreLogs([
  'Require cycle:',
  'useNativeDriver',
  'Accessing the \'state\' property of the \'route\' object is not supported',
  '[auth/no-current-user]',
  'EventEmitter',
  'Attempt to read an array index',
  'VirtualizedLists should never be nested'
])
