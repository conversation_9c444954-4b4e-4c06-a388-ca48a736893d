const numeral = require('numeral')
const { API_URL } = require('../config/env')

export const removeUtf8 = function (str) {
  if (!str) {
    return str
  }
  str = str.toLowerCase()
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a')
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e')
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i')
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o')
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u')
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y')
  str = str.replace(/đ/g, 'd')
  str = str.replace(/\W+/g, ' ')
  str = str.replace(/\s/g, '-')
  str = str.replace(/[^a-zA-Z0-9]/g, '-')

  // THANHNX remove tp in case tp-ha-noi and tp-ho-chi-minh
  str = str.replace('tp-', '')

  const max = 10
  for (let index = max; index >= 0; index--) {
    let inc_ = ''
    for (let index2 = 0; index2 <= index; index2++) {
      inc_ += '-'
    }
    str = str.replace(inc_, '-')
  }
  return str
}

export const formatMoney = (value) => {
  return numeral(value).format('0,0')
}

export const formatCash = n => {
  if (n < 1e3) return n
  if (n >= 1e3 && n < 1e6) return +(n / 1e3).toFixed(0) + 'K'
  if (n >= 1e6 && n < 1e9) return +(n / 1e6).toFixed(0) + 'M'
  if (n >= 1e9 && n < 1e12) return +(n / 1e9).toFixed(0) + 'B'
  if (n >= 1e12) return +(n / 1e12).toFixed(0) + 'T'
}

// @ts-ignore
export const aliasProvince = function (str) {
  str = str.replace('Tỉnh ', '')
  str = str.replace('Thành phố', 'TP ')
  str = str.replace('Hồ Chí Minh', 'HCM')
  return str
}

export const getUserNameWithEmail = function (str) {
  if (str.indexOf('@') !== -1) {
    return str.split('@')[0]
  }
  return str
}

export const builderPathImage = (path) => {
  if (path.indexOf('fbsbx.com') === -1 || path.indexOf('storage.googleapis.com') === -1) {
    // handler domain
    if (path.indexOf(API_URL) !== -1 || path.indexOf('http://localhost') !== -1) {
      path = path.slice(path.indexOf('files') - 1)
    }
    const fileName = path.substring(path.lastIndexOf('/') + 1)
    if (!path.startsWith('/')) {
      path = '/' + path
    }
    path = path.replace('//', '/')
    path = path.substring(0, path.lastIndexOf('/'))
    path = `https://storage.googleapis.com/car-city-20204.appspot.com${path}/${encodeURI(fileName)}`
    // __DEV__ && console.log('path Image', path)
    return path
  }
  return path
}

export const getPhoneOnly = (phone: string) => {
  return phone ? phone.replace('+84', '0') : ''
}

export const noWhitespace = (str: string) => {
  return str ? str.replace(/\s/g, '') : str
}

export const getNumberPrice = (v: string) => {
  v = v + ''
  return v && v?.indexOf(',') ? parseInt(v.split(',').join('')) : v
}

export const Capitalize = (v: string) => v.replace(/(^\w{1})|(\s+\w{1})/g, letter => letter.toUpperCase())
