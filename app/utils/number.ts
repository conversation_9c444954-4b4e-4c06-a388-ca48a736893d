function formatMoney(amount, decimalCount = 2, decimal = '.', thousands = ',') {
  try {
    decimalCount = Math.abs(decimalCount)
    decimalCount = isNaN(decimalCount) ? 2 : decimalCount

    const negativeSign = amount < 0 ? '-' : ''

    const i: any = parseInt(amount = Math.abs(Number(amount) || 0).toFixed(decimalCount)).toString()
    const j = (i.length > 3) ? i.length % 3 : 0

    return negativeSign + (j ? i.substr(0, j) + thousands : '') + i.substr(j).replace(/(\d{3})(?=\d)/g, '$1' + thousands) + (decimalCount ? decimal + Math.abs(amount - i).toFixed(decimalCount).slice(2) : '')
  } catch (e) {
    return null
  }
}
export const numberFormat = function (number, fixLength = 0) {
  if (isNaN(number)) {
    return ''
  }
  if (fixLength == null) {
    const stringNum = number + ''
    const arrInc = stringNum.split('.')
    let fixNum = 0
    if (arrInc.length == 2) {
      fixNum = arrInc[1].length
    }

    fixNum = fixNum > 3 ? 3 : fixNum

    return formatMoney(number, fixNum, '.', ',')
  } else {
    return formatMoney(number, fixLength, '.', ',')
  }
}
