import moment from 'moment'

export const currentDateAdded = function (add) {
  const d = new Date()
  d.setDate(d.getDate() + add)
  let month = d.getMonth() + 1
  if (month < 10) month = `0${month}`
  let date = d.getDate()
  if (date < 10) date = `0${date}`

  return `${date}/${month}/${d.getFullYear()}`
}

export const currentDateTimeAdded = function (add) {
  const d = new Date()
  d.setDate(d.getDate() + add)
  let month = d.getMonth() + 1
  if (month < 10) month = `0${month}`
  let date = d.getDate()
  if (date < 10) date = `0${date}`

  return `${d.getHours()}:${d.getMinutes()}, ${date}/${month}/${d.getFullYear()}`
}

export const getDateLocalFormat = function (str) {
  const strArr = str.split('/')
  return `${strArr[1]}/${strArr[0]}/${strArr[2]}`
}

export const getDateTimeLocalFormat = function (str) {
  const strArr = str.split(',')
  const date = strArr[1].trim()
  const time = strArr[0].trim()

  const arrDate = date.split('/')
  return `${time} ${arrDate[1]}/${arrDate[0]}/${arrDate[2]}`
}

export const baseTimeFormat = function (date) {
  return moment(Number(date)).format('DD/MM/YYYY')
}

export const formatDateTime = function (time) {
  return moment(Number(time)).format('HH:mm DD/MM/YYYY')
}

export const getTimeFromStr = function (str) {
  const strArr = str.split(' ')
  const date = strArr[1].trim()
  const time = strArr[0].trim()

  const arrDate = date.split('/')
  return `${time} ${arrDate[1]}/${arrDate[0]}/${arrDate[2]}`
}
