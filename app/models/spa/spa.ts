import { Instance, SnapshotOut, types } from 'mobx-state-tree'

/**
 * Model description here for TypeScript hints.
 */
export const SpaModel = types
  .model('Spa')
  .props({
    userId: types.maybe(types.string),
    orderId: types.maybe(types.string),
    storeId: types.maybe(types.string),
    phone: types.maybe(types.string),
    typePet: types.maybe(types.number),
    weight: types.maybe(types.string),
    time: types.maybe(types.frozen()),
    service: types.maybe(types.string),
    note: types.maybe(types.string),
    branchName: types.maybe(types.string),
    branchAddress: types.maybe(types.string),
    serviceDetail: types.maybeNull(types.frozen()),
    price: types.maybe(types.number),
    branchPhone: types.maybe(types.string),
    bankCode: types.maybe(types.string),
    coupon: types.maybeNull(types.string),
    valueCoupon: types.maybeNull(types.frozen()),
  })
  .views(self => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions(self => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars

/**
  * Un-comment the following to omit model attributes from your snapshots (and from async storage).
  * Useful for sensitive data like passwords, or transitive state like whether a modal is open.

  * Note that you'll need to import `omit` from ramda, which is already included in the project!
  *  .postProcessSnapshot(omit(["password", "socialSecurityNumber", "creditCardNumber"]))
  */

type SpaType = Instance<typeof SpaModel>
export interface Spa extends SpaType {}
type SpaSnapshotType = SnapshotOut<typeof SpaModel>
export interface SpaSnapshot extends SpaSnapshotType {}
