import { Instance, SnapshotOut, types, flow } from 'mobx-state-tree'
import firestore from '@react-native-firebase/firestore'
/**
 * Model description here for TypeScript hints.
 */
export const AuthorModel = types
  .model('Author')
  .props({
    displayName: types.maybeNull(types.string),
    email: types.optional(types.string, ''),
    avatar: types.optional(types.string, ''),
    uid: types.optional(types.string, ''),
  })
  .views(self => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions(self => ({
    getAuthor: flow(function * (authorId: string) {
      const db = firestore()
      const user = yield db
        .collection('users')
        .where('uid', '==', authorId)
        .get()
      if (user) {
        const item = user.docs[0]
        self.avatar = item.data().photoURL
        self.displayName = item.data().displayName
        self.email = item.data().email
        __DEV__ && console.log('item', item)
      }
    }),
  })) // eslint-disable-line @typescript-eslint/no-unused-vars

/**
  * Un-comment the following to omit model attributes from your snapshots (and from async storage).
  * Useful for sensitive data like passwords, or transitive state like whether a modal is open.

  * Note that you'll need to import `omit` from ramda, which is already included in the project!
  *  .postProcessSnapshot(omit(["password", "socialSecurityNumber", "creditCardNumber"]))
  */

type AuthorType = Instance<typeof AuthorModel>;
export interface Author extends AuthorType {}
type AuthorSnapshotType = SnapshotOut<typeof AuthorModel>;
export interface AuthorSnapshot extends AuthorSnapshotType {}
