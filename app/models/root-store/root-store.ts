import { ChatStoreModel } from '../chat-store/chat-store'
import { CarStoreModel } from '../car-store/car-store'
import { OrderProductStoreModel } from '../order-product-store/order-product-store'
import { ProductStoreModel } from '../product-store/product-store'
import { PostsStoreModel } from '../posts-store/posts-store'
import { NotificationStoreModel } from '../notification-store/notification-store'
import { SupportStoreModel } from '../support-store/support-store'
import { SearchStoreModel } from '../search-store/search-store'
import { RenderFlatListStoreModel } from '../render-flat-list-store/render-flat-list-store'
import { BookingStoreModel } from '../booking-store/booking-store'
import { CommentStoreModel } from '../comment-store/comment-store'
import { ServiceStoreModel } from '../service-store/service-store'
import { HomeStoreModel } from '../home-store/home-store'
import { Instance, SnapshotOut, types } from 'mobx-state-tree'
import { AccountStoreModel } from '../../models/account-store'
import { ArticleStoreModel } from '../../models/article-store'
import { ProfileStoreModel } from '../../models/profile-store'
import { ArticleModel } from '../article'
import { AuthorModel } from '../author'
import { LoginStoreModel } from '../login-store'
import { QuestionStoreModel } from '../question-store'
import { InsuranceStoreModel } from '../insurance-store/InsuranceStore'
import { BhtndsStoreModel } from '../insurance-store/BhtndsStore'
import { LuckyWheelStoreModel } from '@app/models/lucky-wheel-store/lucky-wheel-store'

/**
 * A RootStore model.
 */
export const RootStoreModel = types.model('RootStore').props({
  searchStore: types.optional(SearchStoreModel, {}),
  notificationStore: types.optional(NotificationStoreModel, {}),
  bookingStore: types.optional(BookingStoreModel, {}),
  commentStore: types.optional(CommentStoreModel, {}),
  serviceStore: types.optional(ServiceStoreModel, {}),
  renderFlatListStore: types.optional(RenderFlatListStoreModel, {}),
  homeStore: types.optional(HomeStoreModel, {}),
  author: types.optional(AuthorModel, {} as any),
  article: types.optional(ArticleModel, {} as any),
  articleStore: types.optional(ArticleStoreModel, {}),
  accountStore: types.optional(AccountStoreModel, {}),
  profileStore: types.optional(ProfileStoreModel, {}),
  loginStore: types.optional(LoginStoreModel, {} as any),
  questionStore: types.optional(QuestionStoreModel, {} as any),
  supportStore: types.optional(SupportStoreModel, {} as any),
  postsStore: types.optional(PostsStoreModel, {} as any),
  productStore: types.optional(ProductStoreModel, {}),
  orderProductStore: types.optional(OrderProductStoreModel, {}),
  carStore: types.optional(CarStoreModel, {}),
  chatStore: types.optional(ChatStoreModel, {}),
  insuranceStore: types.optional(InsuranceStoreModel, {}),
  bhtndsStore: types.optional(BhtndsStoreModel, {}),
  luckyWheelStore: types.optional(LuckyWheelStoreModel, {} as any),
})

// TODO: import Model trực tiếp không qua khai báo index, nếu không sẽ gặp lỗi
//  Error: [mobx-state-tree] expected mobx-state-tree type as argument 1, got undefined instead,

/**
 * The RootStore instance.
 */
export interface RootStore extends Instance<typeof RootStoreModel> {}

/**
 * The data of a RootStore.
 */
export interface RootStoreSnapshot extends SnapshotOut<typeof RootStoreModel> {}
