import { cast, flow, Instance, SnapshotOut, types } from 'mobx-state-tree'
import { withEnvironment } from '@app/models'

export const Month = types.model(
  {
    dateYear: types.maybeNull(types.frozen()),
    data: types.optional(types.array(types.frozen()), []),
  },
)

export const CarStoreModel = types
  .model('CarStore')
  .props({
    currentCarId: types.maybeNull(types.frozen()),
    fullName: types.maybe(types.string),
    birthDay: types.maybeNull(types.frozen()),
    dateOfCreatDiary: types.maybeNull(types.frozen()),
    picture: types.maybe(types.string),
    petAvatar: types.maybe(types.string),
    diaryImage: types.maybe(types.string),
    imageOfAlbums: types.maybe(types.string),
    dataListPet: types.optional(types.array(types.frozen()), []),
    reloadData: types.maybe(types.boolean),
    dob: types.maybeNull(types.number),
    // listMonth: types.optional(types.array(Month), []),
    listMonth: types.optional(types.array(types.frozen()), []),
    albums: types.optional(types.array(types.frozen()), []),
    dataAlbums: types.optional(types.array(types.frozen()), []),
    carLine: types.optional(types.array(types.frozen()), []),
    carType: types.optional(types.array(types.frozen()), []),
    carBrand: types.optional(types.array(types.frozen()), []),
    bikeBrand: types.optional(types.array(types.frozen()), []),
    otherBrand: types.optional(types.array(types.frozen()), []),
    lineChartData: types.maybeNull(types.frozen()),
    pieChartData: types.maybeNull(types.frozen()),
    pieChartSumData: types.maybeNull(types.frozen()),
    dataInsurancePosts: types.optional(types.array(types.frozen()), []),
    costCategories: types.optional(types.array(types.frozen()), []),
  })
  .extend(withEnvironment)
  .views(self => ({}))
  .actions(self => ({
    getCarLine: flow(function * (catName = '') {
      const result: any = yield self.environment.api.getCarLine(catName)
      if (result?.data?.data?.data) {
        const data = result.data.data.data
        self.carLine = cast(data)
      } else {
        self.carLine = cast([])
      }
    }),
    getCarType: flow(function * () {
      const result: any = yield self.environment.api.getCarType()
      if (result?.data?.data) {
        const data = result.data.data
        self.carType = cast(data)
      } else {
        self.carType = cast([])
      }
    }),
    getCarBrand: flow(function * (type = '') {
      const result: any = yield self.environment.api.getCarTypeByType(type)
      if (result?.data?.data) {
        const data = result.data.data
        self.carBrand = cast(data)
      } else {
        self.carBrand = cast([])
      }
    }),
    getBikeBrand: flow(function * (type = '') {
      const result: any = yield self.environment.api.getCarTypeByType(type)
      if (result?.data?.data) {
        const data = result.data.data
        self.bikeBrand = cast(data)
      } else {
        self.bikeBrand = cast([])
      }
    }),
    getOtherBrand: flow(function * (type = '') {
      const result: any = yield self.environment.api.getCarTypeByType(type)
      if (result?.data?.data) {
        const data = result.data.data
        self.otherBrand = cast(data)
      } else {
        self.otherBrand = cast([])
      }
    }),
    getDiary: flow(function * (id) {
      const result: any = yield self.environment.api.getDiary(id)
      if (result?.data?.data) {
        const data = result.data.data.map(item => {
          return {
            id: item.id,
            ...item.attributes
          }
        })
        self.listMonth = cast(data)
      }
    }),
    getListPet: flow(function * (userId) {
      if (userId) {
        const result: any = yield self.environment.api.getCarByUserId(userId)
        if (result?.data && result?.data?.data?.length > 0) {
          const listPet = result?.data?.data.map(item => {
            return {
              ...item
            }
          })
          listPet.push({
            name: 'Thêm',
            id: 'add',
            photo: require('../../assets/images/icons/plus-add.png')
          })
          self.dataListPet = cast(listPet)
        } else {
          self.dataListPet = cast([])
        }
      }
    }),
    getLichSuMuaBHByBKS: flow(function * (userId, bksoat) {
      __DEV__ && console.warn('userId', userId)
      if (userId) {
        const result: any = yield self.environment.api.getLichSuMuaBHByUserId(userId, bksoat)
        __DEV__ && console.warn('result', result)
        if (result?.data && result?.data?.data?.length > 0) {
          const arr = result?.data?.data.map(item => {
            return {
              ...item
            }
          })
          self.dataInsurancePosts = cast(arr)
        } else {
          self.dataInsurancePosts = cast([])
        }
      }
    }),
    deletePet: flow(function * (id) {
      const result: any = yield self.environment.api.deletePet(id)
      return result
    }),
    createDiary: flow(function * (body) {
      const result: any = yield self.environment.api.createDiary(body)
      return result
    }),
    updatePetProfile: flow(function * (id, body) {
      const result: any = yield self.environment.api.updatePetProfile(id, body)
      return result
    }),
    uploadImage: flow(function * (url, files) {
      const result: any = yield self.environment.api.uploadImagesV2(url, files)
      __DEV__ && console.log(result)
      if (result && result.data.picture) {
        self.picture = cast(result.data.picture)
      } else {
        __DEV__ && console.log(result)
      }
      return result
    }),
    uploadAvatar: flow(function * (url, files) {
      const result: any = yield self.environment.api.uploadImages(url, files)
      if (result && result.data.picture) {
        self.petAvatar = cast(result.data.picture)
      } else {
        __DEV__ && console.log(result)
      }
    }),
    updateAlbums: flow(function * (url, files) {
      const result: any = yield self.environment.api.uploadImages(url, files)
      if (result && result.data.picture) {
        self.imageOfAlbums = cast(result.data.picture)
      } else {
        __DEV__ && console.log(result)
      }
    }),
    addCar: flow(function * (body) {
      return yield self.environment.api.addCar(body)
    }),
    uploadOneImage: flow(function * (url, files) {
      const result: any = yield self.environment.api.uploadImagesV2(url, files)
      return result
    }),
    getCarById: flow(function * (id) {
      self.currentCarId = cast(id)
      const result: any = yield self.environment.api.getCarById(id)
      return result
    }),
    getLineChartData: flow(function * (id, m, y) {
      const result: any = yield self.environment.api.getLineChartData(id, m, y)
      if (result?.data) {
        const data = result.data
        self.lineChartData = cast(data.lines)
        self.pieChartData = cast(data.pies)
        self.pieChartSumData = cast(data.sum)
      } else {
        self.lineChartData = cast({
          labels: [],
          datasets: [
            {
              data: [
              ]
            }
          ]
        })
      }
      return result
    }),
    getCostType: flow(function * (type = '') {
      const result: any = yield self.environment.api.getCostCategories()
      if (result?.data?.data) {
        const data = result.data.data
        self.costCategories = cast(data)
      } else {
        self.costCategories = cast([])
      }
    })
  }))
  .actions(self => ({
    getAlbums: flow(function * (id) {
      const result: any = yield self.environment.api.getAlbums(id)
      return result
    }),
  }))

  .actions(self => ({
    setFullName: function(value) {
      self.fullName = value
    },
    setDataAlbums: function(value) {
      self.dataAlbums = value
    },
    setBirthDay: function(value) {
      self.birthDay = value
    },
    setDateOfCreatDiary: function(value) {
      self.dateOfCreatDiary = value
    },
    setPicture: function(value) {
      self.picture = value
    },
    setPetAvatar: function(value) {
      self.petAvatar = value
    },
    setImageOfAlbums: function(value) {
      self.imageOfAlbums = value
    },
    deletePetOfList: function(id) {
      const index = self.dataListPet.findIndex(x => x._id === id)
      const cloneArray = [...self.dataListPet]
      cloneArray.splice(index, 1)
      self.dataListPet = cast([...cloneArray])
    },
    setReloadData: function(value) {
      self.reloadData = value
    },
    clearFields: function() {
      self.picture = ''
      self.birthDay = ''
      self.dateOfCreatDiary = ''
      self.imageOfAlbums = ''
      self.petAvatar = ''
    },
    clearDataListPet: function() {
      self.dataListPet = cast([])
    },
    clearListMonth: function() {
      self.listMonth = cast([])
    },
  }))

/**
  * Un-comment the following to omit model attributes from your snapshots (and from async storage).
  * Useful for sensitive data like passwords, or transitive state like whether a modal is open.

  * Note that you'll need to import `omit` from ramda, which is already included in the project!
  *  .postProcessSnapshot(omit(["password", "socialSecurityNumber", "creditCardNumber"]))
  */

type CarStoreType = Instance<typeof CarStoreModel>
export interface CarStore extends CarStoreType {}
type CarStoreSnapshotType = SnapshotOut<typeof CarStoreModel>
export interface CarStoreSnapshot extends CarStoreSnapshotType {}
