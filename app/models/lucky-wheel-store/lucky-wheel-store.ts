import { cast, flow, Instance, types } from 'mobx-state-tree'
import { withEnvironment } from '@app/models'

// ProductGiftItemAttributes Model
const ProductGiftItemAttributes = types.model('ProductGiftItemAttributes', {
  productName: types.maybeNull(types.string),
  point: types.maybeNull(types.number),
  description: types.maybeNull(types.string),
  createdAt: types.maybeNull(types.string),
  updatedAt: types.maybeNull(types.string),
  publishedAt: types.maybeNull(types.string),
  idGiftCode: types.maybeNull(types.number),
  stockLuckyWheel: types.maybeNull(types.number),
  priceLuckyWheel: types.maybeNull(types.string),
  image: types.maybeNull(types.late(() => ImageModel)),
  gift_shop_danh_muc: types.maybeNull(types.late(() => GiftShopDanhMucModel)),
})

// GiftShopDanhMuc Model
const GiftShopDanhMucDataModel = types.model('GiftShopDanhMucData', {
  _id: types.maybeNull(types.number),
  attributes: types.maybeNull(types.late(() => PurpleAttributesModel)),
})

const GiftShopDanhMucModel = types.model('GiftShopDanhMuc', {
  data: types.maybeNull(GiftShopDanhMucDataModel),
})

// PurpleAttributes Model
const PurpleAttributesModel = types.model('PurpleAttributes', {
  name: types.maybeNull(types.string),
  description: types.maybeNull(types.string),
  createdAt: types.maybeNull(types.string),
  updatedAt: types.maybeNull(types.string),
  publishedAt: types.maybeNull(types.string),
})

// Image Model
const ImageDataModel = types.model('ImageData', {
  id: types.maybeNull(types.number),
  attributes: types.maybeNull(types.late(() => FluffyAttributesModel)),
})

const ImageModel = types.model('Image', {
  data: types.maybeNull(ImageDataModel),
})

// FluffyAttributes Model
const FluffyAttributesModel = types.model('FluffyAttributes', {
  name: types.maybeNull(types.string),
  alternativeText: types.maybeNull(types.string),
  caption: types.maybeNull(types.string),
  width: types.maybeNull(types.number),
  height: types.maybeNull(types.number),
  formats: types.maybeNull(types.late(() => FormatsModel)),
  hash: types.maybeNull(types.string),
  ext: types.maybeNull(types.string),
  mime: types.maybeNull(types.string),
  size: types.maybeNull(types.number),
  url: types.maybeNull(types.frozen()),
  previewUrl: types.maybeNull(types.null),
  provider: types.maybeNull(types.string),
  provider_metadata: types.maybeNull(types.null),
  createdAt: types.maybeNull(types.string),
  updatedAt: types.maybeNull(types.string),
})

// Formats Model
const FormatsModel = types.model('Formats', {
  small: types.maybeNull(types.late(() => SmallModel)),
  thumbnail: types.maybeNull(types.late(() => SmallModel)),
})

// Small Model
const SmallModel = types.model('Small', {
  ext: types.maybeNull(types.string),
  url: types.maybeNull(types.string),
  hash: types.maybeNull(types.string),
  mime: types.maybeNull(types.string),
  name: types.maybeNull(types.string),
  path: types.maybeNull(types.null),
  size: types.maybeNull(types.number),
  width: types.maybeNull(types.number),
  height: types.maybeNull(types.number),
})

// ProductGiftItem Model
const ProductGiftItemModel = types.model('ProductGiftItem', {
  id: types.maybeNull(types.number),
  attributes: types.maybeNull(ProductGiftItemAttributes),
})

/**
 * LuckyWheelStoreModel - A store to manage the state and interactions related to the Lucky Wheel.
 */
export const LuckyWheelStoreModel = types
  .model('LuckyWheelStore')
  .props({
    luckyWheels: types.optional(types.array(types.frozen()), []),
    lastLuckyWheel: types.optional(types.frozen(), null),
    listItems: types.optional(types.array(ProductGiftItemModel), []),
    listWinners: types.optional(types.array(types.frozen()), []),
    isLoading: types.optional(types.boolean, false),
    error: types.maybe(types.string)
  })
  .extend(withEnvironment)
  .views(self => ({}))
  .actions(self => ({
    getLuckyWheels: flow(function * () {
      self.isLoading = true
      try {
        const response = yield self.environment.api.getLuckyWheels()
        console.log('response getLuckyWheels:', response)
        // debugger
        if (response.kind === 'ok' && response?.data) {
          self.luckyWheels = cast(response?.data?.data)
          self.lastLuckyWheel = self.luckyWheels[0]
        } else {
          self.error = response.kind
        }
      } catch (error) {
        console.error('Error in getLuckyWheels:', error)
        self.error = error.message
      } finally {
        self.isLoading = false
      }
    }),
    getListItems: flow(function * () {
      self.isLoading = true
      try {
        const response = yield self.environment.api.getListItemLuckyWheel()
        if (response.kind === 'ok' && response?.data?.data) {
          self.listItems = cast(response?.data?.data)
        } else {
          self.error = response.kind
        }
      } catch (error) {
        self.error = error.message
      } finally {
        self.isLoading = false
      }
    }),
    postResult: flow(function * (body) {
      try {
        const result = yield self.environment.api.postResultLuckyWheel(body)
        console.log('response postResult:', result)
        if (result.kind === 'ok') {
          return result
        } else {
          __DEV__ && console.log(result.kind)
          return result
        }
      } catch (error) {
        return error
      } finally {
      }
    }),
    putUpdateResult: flow(function * (body, id) {
      try {
        const result = yield self.environment.api.putUpdateResultLuckyWheel(body, id)
        console.log('response postResult:', result)
        if (result.kind === 'ok') {
          return result
        } else {
          __DEV__ && console.log(result.kind)
          return result
        }
      } catch (error) {
        console.error('Error in getRewardPointProducts:', error)
        return error
      } finally {
      }
    }),
    getListWinners: flow(function * () {
      const result: any = yield self.environment.api.getWinners()
      if (result?.data && result?.data?.data?.length > 0) {
        const listPet = result?.data?.data.map(item => {
          return {
            ...item
          }
        })
        self.listWinners = cast(listPet)
      } else {
        self.listWinners = cast([])
      }
    }),
  }))
  .actions(self => ({
    clearLuckyWheels: function () {
      self.luckyWheels = cast([])
    },
    clearRewardPointProducts: function () {
      self.listItems = cast([])
    }
  }))

// eslint-disable-line @typescript-eslint/no-unused-vars
type LuckyWheelStoreType = Instance<typeof LuckyWheelStoreModel>
export interface LuckyWheelStore extends LuckyWheelStoreType {}
export type ProductGiftItem = Instance<typeof ProductGiftItemModel>;
