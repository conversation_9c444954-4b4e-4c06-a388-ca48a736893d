import { cast, flow, Instance, SnapshotOut, types } from 'mobx-state-tree'
import { withEnvironment } from '../extensions'
import _ from 'lodash'

/**
 * Model description here for TypeScript hints.
 */

export const NotificationStoreModel = types
  .model('NotificationStore')
  .props({
    notifications: types.optional(types.array(types.frozen()), []),
    totalPage: types.maybe(types.number),
    notSeen: types.maybeNull(types.frozen())
  })
  .extend(withEnvironment)
  .views(self => ({}
  ))
  // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions(self => ({
    resetCountNotification: flow(function * () {
      __DEV__ && console.log('get Profile action')
      const result: any = yield self.environment.api.resetCountNotification()
      // console.log('result', result)
      if (result) {
        const dataAddress = result.data.notifications.map(item => {
          return {
            ...item,
          }
        })
        self.notifications = cast(dataAddress)
      } else {
        __DEV__ && console.log(result.kind)
      }
      // save to db
    }),
    getNotification: flow(function * (page, isLoadMore) {
      const result: any = yield self.environment.api.getNotification(page)
      // console.log('result', result)
      if (result.kind === 'ok' && result?.data?.data?.notifications?.length) {
        const data = result.data.data
        const notifications = data.notifications.map(item => {
          const bookInfo = item.bookSpaInfo || item.bookRoomInfo || item.bookClinicInfo || item.orderProductInfo
          const imageNotify = bookInfo?.items ? bookInfo?.items[0]?.image : bookInfo?.products ? bookInfo?.products[0]?.thumbnail : ''
          const serviceName = bookInfo?.items ? bookInfo?.items[0]?.serviceName : ''
          const orderId = bookInfo?.orderId || ''
          return {
            ...item,
            avatarUser: item.avatarUser || '',
            bookInfo,
            imageNotify,
            serviceName,
            orderId
          }
        })
        if (isLoadMore) {
          const tempArray = [...self.notifications, ...notifications]
          _.uniqBy(tempArray, '_id')
          self.notifications = cast(tempArray)
        } else {
          self.notifications = cast(notifications)
        }
        self.totalPage = cast(data.totalPage)
        self.notSeen = cast(data.countUnread)
      } else {
        __DEV__ && console.log(result.kind)
      }
      // save to db
    }),
    watchedNotification: flow(function * (id) {
      const result: any = yield self.environment.api.watchedNotification(id)
      if (result.kind === 'ok') {
        self.notSeen = cast(result.data?.data?.countUnread)
        return result
      } else {
        __DEV__ && console.log(result.kind)
        return result
      }
    }),
    getNotificationByType: flow(function * (typeNotify = 2) {
      // type = 2 is update
      // type = 3 is ads banner
      const result: any = yield self.environment.api.getNotification(1, typeNotify)
      // console.log('result', result)
      if (result.kind === 'ok' && result?.data?.data?.notifications?.length) {
        return result.data.data
      } else {
        __DEV__ && console.log(result.kind)
        return null
      }
      // save to db
    })
  }))
  .actions((self) => ({
    setType: function (data) {
      // update watched =0 new =1 watched
      const objIndex = self.notifications.findIndex(obj => obj._id === data._id)
      self.notifications[objIndex].watched = 1
    },
    clearFields: function() {
      self.notifications = cast([])
    }
  }))

// eslint-disable-line @typescript-eslint/no-unused-vars
/**
  * Un-comment the following to omit model attributes from your snapshots (and from async storage).
  * Useful for sensitive data like passwords, or transitive state like whether a modal is open.

  * Note that you'll need to import `omit` from ramda, which is already included in the project!
  *  .postProcessSnapshot(omit(["password", "socialSecurityNumber", "creditCardNumber"]))
  */

type NotificationStoreType = Instance<typeof NotificationStoreModel>
export interface NotificationStore extends NotificationStoreType {}
type NotificationStoreSnapshotType = SnapshotOut<typeof NotificationStoreModel>
export interface NotificationStoreSnapshot extends NotificationStoreSnapshotType {}
