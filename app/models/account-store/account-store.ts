import auth from '@react-native-firebase/auth'
import { Instance, SnapshotOut, types, flow, cast, getSnapshot, applySnapshot } from 'mobx-state-tree'
import { LOCKED, LOGGED_IN, LOGGED_OUT, LOGIN_FAILED } from '../constants/constants'
import firestore from '@react-native-firebase/firestore'
import { withEnvironment } from '../extensions'
import { saveRocketChatData, saveToken } from '../../services/tokenService'
import _ from 'lodash'

export interface UserLoginModel {
  email: string
  password: string
}

export interface UserModel {
  uid: string
  displayName?: string
  email?: string
  password?: string
  photoURL?: string
  deviceToken?: string
}

/**
 * Model description here for TypeScript hints.
 */
export const AccountStoreModel = types
  .model('AccountStore')
  .props({
    user: types.maybe(types.string),
    uid: types.maybe(types.string),
    _id: types.maybe(types.string),
    picture: types.maybe(types.string),
    email: types.maybe(types.string),
    birthday: types.maybe(types.string),
    fullName: types.maybe(types.string),
    isLocked: types.maybe(types.boolean),
    phoneNumber: types.maybe(types.string),
    phone: types.maybe(types.string),
    isValidForm: types.maybe(types.boolean),
    error: types.maybe(types.boolean),
    status: types.maybe(types.enumeration('state', [LOGIN_FAILED, LOGGED_OUT, LOGGED_IN, LOCKED])),
    token: types.maybe(types.string),
    background: types.maybe(types.string),
    enterPassword: types.maybe(types.string),
    reEnterPassword: types.maybe(types.string),
    password: types.maybe(types.string),
  })
  .extend(withEnvironment)
  .views((self) => ({
    checkInvalid: function () {
      self.isValidForm =
        self.phoneNumber !== undefined && self.enterPassword === self.reEnterPassword
      // console.log('self.isValidForm', self.isValidForm)
    },
    isLogin: function () {
      if (self.token) {
        return self.token && self.token.length > 0
      }
      return null
    },
    getIsValidForm: function () {
      return !self.isValidForm
    },
  }))

  .actions((self) => ({
    loginAccount: flow(function * (data) {
      const result: any = yield self.environment.api.loginAccount(data)
      if (result && result.data && !result.data.error) {
        const response = result.data.data
        const token = response.token
        // https://prnt.sc/wgmuse
        self.token = cast(token)
        self._id = cast(response.user._id)
        self.phone = cast(response.user.phone)
        yield saveToken(token)
        yield saveRocketChatData(result?.data?.data?.user?.rocketChatData?.data)
        // yield saveUserLogin(response.user)
      }
      return result
    }),
    registerAccount: flow(function * (data) {
      // phoneNumber, password, fullName
      const result: any = yield self.environment.api.registerAccount(data)
      if (result && result.data && result.data.token) {
        const response = result?.data.data
        const token = response?.token
        // https://prnt.sc/wgmuse
        self.token = cast(token)
        self._id = cast(response?.user._id)
        self.phone = cast(response?.user.phone)
        yield saveToken(token)
        yield saveRocketChatData(response?.user?.rocketChatData?.data)
        yield saveToken(token)
      }
      return result
    }),
    recoverPassword: flow(function * (phoneNumber, password, confirmPassword) {
      const result: any = yield self.environment.api.recoverPassword(phoneNumber, password, confirmPassword)
      if (result && result.data && result.data.token) {
        const token = result.data.token
        self.token = cast(token)
        yield saveToken(token)
      }
      return result
    }),
    changePassword: flow(function * (data) {
      const result: any = yield self.environment.api.changePassword(data)
      return result
    }),

    forgotYourPassword: flow(function * () {
      const result: any = yield self.environment.api.forgotYourPassword(self.phoneNumber)
      return result
    }),
  }))
  .actions((self) => ({
    sendPasswordResetToEmail: flow(function * () {
      yield auth().sendPasswordResetEmail(self.email)
    }),
  }))

  .actions((self) => ({
    setEmail: function (value) {
      self.email = value
      self.checkInvalid()
    },
    setPhoneNumber: function (phone: string) {
      // validate đủ 10 số
      if (phone.length && _.startsWith(phone, '+840')) {
        const search = '+840'
        const replaceWith = '+84'
        self.phoneNumber = phone.replace(search, replaceWith)
      } else {
        self.phoneNumber = phone
      }
      // if (phone.length && phone.length > 9 && !_.startsWith(phone, '84')) {
      //   // nêu phone thoả mãn điều kiện thì mới gọi thư viện, tránh set nhiều lần khi gõ bàn phím
      //   const phoneNumber = parsePhoneNumberFromString(phone, 'VN')
      //   __DEV__ && console.log('parsePhoneNumberFromString:', phoneNumber)
      //   self.phoneNumber = `${phoneNumber.number}`.indexOf('+') !== -1 ? phoneNumber.number + '' : '+' + phoneNumber.number
      // } else {
      //   self.phoneNumber = phone
      // }
      self.checkInvalid()
    },
    setPassword: function (value) {
      self.enterPassword = value
      self.checkInvalid()
    },
    confirmPassword: function (value) {
      self.reEnterPassword = value
      self.checkInvalid()
    },
    setPicture: function (value) {
      self.picture = value
    },
    clearFields: function() {
      self.phoneNumber = ''
      self.phone = ''
    },
  }))
  .actions((self) => ({
    syncUserUpdateToDb: flow(function * (user: UserModel, isUpdate = false) {
      if (!user && isUpdate) {
        return
      }
      const db = firestore()
      if (isUpdate) {
        if (user.photoURL || user.displayName) {
          yield auth().currentUser.updateProfile(user)
        }
        if (user.password && user.email) {
          // 1. xac thuc mat khau cu
          const currentUser = auth().currentUser
          const cred = auth.EmailAuthProvider.credential(user.email, user.password)
          yield currentUser.reauthenticateWithCredential(cred)
          yield auth().currentUser.updateEmail(user.email)
        }
        if (user.uid) {
          const userFind = yield db.collection('users').where('uid', '==', user.uid).get()
          if (!userFind.empty) {
            const item = userFind.docs[0]
            yield db.collection('users').doc(item.id).set(user, { merge: true })
          }
        }
      } else {
        // create new
        if (user.displayName) {
          yield auth().currentUser.updateProfile(user)
        }
        yield db.collection('users').add(user)
      }
    }),
  }))
  .actions(self => {
    let initialState = {}
    return {
      afterCreate: () => {
        initialState = getSnapshot(self)
      },
      reset: () => {
        applySnapshot(self, {})
      },
    }
  })

/**
 * Un-comment the following to omit model attributes from your snapshots (and from async storage).
 * Useful for sensitive data like passwords, or transitive state like whether a modal is open.

 * Note that you'll need to import `omit` from ramda, which is already included in the project!
 *  .postProcessSnapshot(omit(["password", "socialSecurityNumber", "creditCardNumber"]))
 */

type AccountStoreType = Instance<typeof AccountStoreModel>

export interface AccountStore extends AccountStoreType {}

type AccountStoreSnapshotType = SnapshotOut<typeof AccountStoreModel>

export interface AccountStoreSnapshot extends AccountStoreSnapshotType {}
