import { Instance, SnapshotOut, types, flow } from 'mobx-state-tree'
import firestore from '@react-native-firebase/firestore'
import { AuthorModel } from '../author'

const db = firestore()
const getUrlParameter = (url, name) => {
  const regex = /[?&]([^=#]+)=([^&#]*)/g
  const params = {}
  let match
  // eslint-disable-next-line no-cond-assign
  while ((match = regex.exec(url))) {
    params[match[1]] = match[2]
  }
  return params[name]
}
export const ArticleModel = types
  .model('Article')
  .props({
    id: types.maybe(types.string),
    author: types.maybe(AuthorModel),
    // author: types.maybe(types.frozen()),
    title: types.maybe(types.string),
    content: types.maybe(types.string),
    date: types.maybe(types.string),
    image: types.optional(types.frozen(), []),
    likes: types.maybe(types.number),
    // comments: types.optional(types.array(CommentModel), []),
    comments: types.optional(types.frozen(), []),
    commentsCount: types.maybe(types.number),
  })
  .views(self => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars

  .actions(self => ({
    getComments: flow(function * (id: string) {
      const ref = yield firestore()
        .collection('post')
        .doc(id)
        .collection('comments')
        .get()
      if (ref.empty) {
        __DEV__ && console.log('No such document!')
      } else {
        self.comments = ref.docs
      }
    }),
  }))
  .actions(self => ({
    getData: flow(function * (id: string) {
      const ref = yield firestore()
        .collection('post')
        .doc(id)
        .get()
      if (ref.empty) {
        __DEV__ && console.log('No such document!')
      } else {
        // console.log('user in store', user)
        // console.log("data", ref)
        self.id = id
        self.author = ref.data().author
        self.title = ref.data().title
        self.content = ref.data().content
        self.date = ref.data().date
        self.likes = ref.data().likes
        self.image = ref.data().image
      }
    }),
    addComments: flow(function * (id: string, userId: string, text: string) {
      const date = new Date()
      // yield self.getComments(id)
      __DEV__ && console.log('123')
      const comment = {
        author: {
          uid: userId,
        },
        text: text,
        likesCount: 1,
        date: date,
      }
      db.collection('post')
        .doc(id)
        .collection('comments')
        .add(comment)
        .then(function(docRef) {
          __DEV__ && console.log('then')
        })
        .catch(function(error) {
          __DEV__ && console.error('Error adding document: ', error)
        })
    }),
    deleteComment: flow(function * (idPost: string, idCmt: string) {
      yield db
        .collection('post')
        .doc(idPost)
        .collection('comments')
        .doc(idCmt)
        .delete()
    }),
    deleteArticle: flow(function * (idPost: string, userId: string) {
      db.collection('post')
        .doc(idPost)
        .get()
        .then(function(docRef) {
          if (docRef.data().image.length > 0) {
            docRef.data().image.forEach(element => {
              __DEV__ && console.log('element', element)
              const pathImg = `/article/${userId}/${getUrlParameter(element, 'name')}`
              __DEV__ && console.log('pathImg', pathImg)
              //
              // storage()
              //   .ref(pathImg)
              //   .delete()
            })
          }
          db.collection('post')
            .doc(idPost)
            .delete()
        })
        .catch(function(error) {
          console.error('Error adding document: ', error)
        })
    }),
    getLikes: flow(function * (id: string) {
      const ref = yield firestore()
        .collection('post')
        .doc(id)
        .collection('likes')
        .get()
      if (ref.empty) {
        __DEV__ && console.log('No such document!')
      } else {
        self.likes = ref.docs
      }
    }),
    addLikes: flow(function * (id: string, userId: string) {
      const date = new Date()
      // yield self.getComments(id)
      const likes = [
        {
          uid: userId,
          date: date.getTime(),
        },
      ]
      likes.forEach(
        flow(function * (obj) {
          db.collection('post')
            .doc(id)
            .collection('likes')
            .add(obj)
            .then(function(docRef) {
              // console.log("Document written with ID: ", docRef);
              // getComments()
              // self.getLikes(id)
            })
            .catch(function(error) {
              console.error('Error adding document: ', error)
            })
        }),
      )
    }),
    addPost: flow(function * (post: any) {
      firestore()
        .collection('post')
        .add(post)
        .then(function(docRef) {
          __DEV__ && console.log('Document written with ID: ', docRef.id)
          return true
        })
        .catch(function(error) {
          __DEV__ && console.error('Error adding document: ', error)
          return false
        })
    }),
    editPost: flow(function * (articleId: any, post: any) {
      firestore()
        .collection('post')
        .doc(articleId)
        .set(post, { merge: true })
    }),
  }))

type ArticleType = Instance<typeof ArticleModel>;
export interface Article extends ArticleType {}
type ArticleSnapshotType = SnapshotOut<typeof ArticleModel>;
export interface ArticleSnapshot extends ArticleSnapshotType {}
