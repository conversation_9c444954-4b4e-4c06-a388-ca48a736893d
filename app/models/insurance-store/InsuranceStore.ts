import { applySnapshot, cast, flow, getSnapshot, Instance, SnapshotIn, SnapshotOut, types } from 'mobx-state-tree'
import { withEnvironment } from '@app/models'
import { getRemoveConfig } from '@app/utils/storage'
import { stringMd5 } from 'react-native-quick-md5'
import { ApiConfig } from '@app/services/api/api-config'
import { countriesData } from '@app/assets/countries'

/**
 * Model description here for TypeScript hints.
 */

const jsonData = [
  {
    id: 6,
    attributes: {
      name: '<PERSON><PERSON><PERSON> hiểm TNDS bắt buộc chủ xe Ô tô',
      des: 'BIC',
      price: 480700,
      discount: 30,
      createdAt: '2022-09-29T23:17:28.972Z',
      updatedAt: '2022-10-24T03:02:44.235Z',
      publishedAt: '2022-10-24T03:02:44.232Z',
      active: true,
      term: '<p><span style="color:rgb(238,28,36);"><strong><PERSON><PERSON><PERSON> tượng bảo hiểm</strong></span></p><p><span style="font-family:Tahoma, Geneva, sans-serif;">Ch<PERSON> xe cơ giới tham gia giao thông trên lãnh thổ Nước Cộng hòa Xã hội Chủ nghĩa Việt Nam.</span></p><p><span style="color:rgb(238,28,36);"><strong>Phạm vi bảo hiểm</strong></span></p><p><span style="font-family:Tahoma, Geneva, sans-serif;">BIC thay mặt cho chủ xe bồi thường cho các tổn thất về người và tài sản cho bên thứ ba (bên bị thiệt hai do xe của chủ xe gây ra).</span></p><p><span style="color:rgb(238,28,36);"><strong>Quyền lợi bảo hiểm</strong></span></p><p><span style="font-family:Tahoma, Geneva, sans-serif;">BIC sẽ thay mặt cho chủ xe bồi thường cho bên thứ ba những thiệt hại về người và tài sản, cho hành khách những thiệt hại về người với mức tối đa như sau:</span><br><span style="font-family:Tahoma, Geneva, sans-serif;">+ Về người: tối đa 150.000.000 đồng/người/vụ tai nạn</span><br><span style="font-family:Tahoma, Geneva, sans-serif;">+ Về tài sản: tối đa 100.000.000 đồng/vụ tai nạn</span></p>',
      type: 'BHTNDSCAR',
      images: {
        data: [
          {
            id: 365,
            attributes: {
              name: 'Group 2239.png',
              alternativeText: 'Group 2239.png',
              caption: 'Group 2239.png',
              width: 500,
              height: 500,
              formats: {
                thumbnail: {
                  ext: '.png',
                  url: 'https://storage.googleapis.com/car-city-20204.appspot.com/files/images-v2/thumbnail_Group-2239-1665640309304.png',
                  hash: 'thumbnail_Group_2239_720c12b768',
                  mime: 'image/png',
                  name: 'thumbnail_Group 2239.png',
                  path: null,
                  size: 47.08,
                  width: 156,
                  height: 156
                }
              },
              hash: 'Group_2239_720c12b768',
              ext: '.png',
              mime: 'image/png',
              size: 95.36,
              url: 'https://storage.googleapis.com/car-city-20204.appspot.com/files/images-v2/Group-2239-1665640307855.png',
              previewUrl: null,
              provider: '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2022-10-13T05:51:50.555Z',
              updatedAt: '2022-10-13T05:51:50.555Z'
            }
          }
        ]
      },
      loai_bao_hiems: {
        data: [
          {
            id: 2,
            attributes: {
              name: 'BIC',
              des: '',
              active: true,
              createdAt: '2022-09-29T07:47:16.276Z',
              updatedAt: '2022-10-26T07:56:03.259Z',
              publishedAt: '2022-09-29T07:47:20.651Z',
              hotline: null
            }
          }
        ]
      }
    }
  },
  {
    id: 5,
    attributes: {
      name: 'Bảo hiểm Vật chất Ô tô - BIC',
      des: 'bảo hiểm thân vỏ BIC',
      price: null,
      discount: 15,
      createdAt: '2022-09-29T23:14:11.771Z',
      updatedAt: '2022-10-24T03:02:56.809Z',
      publishedAt: '2022-10-24T03:02:56.807Z',
      active: true,
      term: '<p><span style="color:rgb(238,28,36);"><strong>Phạm vi bảo hiểm chính:</strong></span></p><p><span style="font-family:Tahoma, Geneva, sans-serif;">- Thiệt hại vật chất xe do thiên tai, tai nạn bất ngờ, ngoài sự kiểm soát của chủ xe, lái xe trong những trường hợp: đâm va, lật đổ, chìm, rơi toàn bộ xe, bị các vật thể khác rơi vào; hỏa hoạn/cháy, nổ; giông bão lũ lụt, mất toàn bộ xe do trộm, cướp …</span><br><span style="font-family:Tahoma, Geneva, sans-serif;">- Những chi phí cần thiết và hợp lý phát sinh trong tai nạn thuộc phạm vi bảo hiểm nhằm: ngăn ngừa, hạn chế tổn thất phát sinh thêm khi xe bị thiệt hại do các nguyên nhân trên; chi phí bảo vệ và đưa xe thiệt hại tới nơi sửa chữa gần nhất; chi phí giảm định để xác định nguyên nhân và mức độ tổn thất.</span></p><p><span style="color:rgb(238,28,36);"><strong>Quyền lợi bổ sung:</strong></span></p><p><span style="font-family:Tahoma, Geneva, sans-serif;">- Bảo hiểm sửa chữa tại gara tự chọn (ĐKBS-01)</span><br><span style="font-family:Tahoma, Geneva, sans-serif;">- Bảo hiểm mới thay cũ (ĐKBS-03)</span><br><span style="font-family:Tahoma, Geneva, sans-serif;">- Bảo hiểm đối với chi phí thuê xe trong thời gian sửa chữa (ĐKBS-04)</span><br><span style="font-family:Tahoma, Geneva, sans-serif;">- Bảo hiểm thủy kích (ĐKBS-05)</span><br><span style="font-family:Tahoma, Geneva, sans-serif;">- Bảo hiểm thiệt hại vật chất xe cho xe lưu hành tạm thời (ĐKBS-09; 16)</span><br><span style="font-family:Tahoma, Geneva, sans-serif;">- Bảo hiểm mất cắp bộ phận (ĐKBS-14)</span></p><p><span style="color:rgb(238,28,36);"><strong>Quyền lợi Bảo hiểm ô tô toàn diện - BIC AUTOCARE</strong></span></p><figure class="table" style="width:100%;"><table style="background-color:white;"><tbody><tr><td style="background-color:white;border:1px solid black;padding:2px;text-align:center;width:112.890625px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;"><strong>Quyền lợi</strong></span></td><td style="background-color:white;border-bottom:1px solid black;border-left-style:none;border-right:1px solid black;border-top:1px solid black;padding:2px;text-align:center;width:391.765625px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;"><strong>Phạm vi bảo hiểm</strong></span></td><td style="background-color:white;border-bottom:1px solid black;border-left-style:none;border-right:1px solid black;border-top:1px solid black;padding:2px;text-align:center;width:159.375px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;"><strong>Mức bồi thường</strong></span></td></tr><tr><td style="background-color:white;border-bottom:1px solid black;border-left:1px solid black;border-right:1px solid black;border-top-style:none;padding:2px;" colspan="3"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;"><strong>1. Phần bảo hiểm vật chất xe ô tô</strong></span></td></tr><tr><td style="background-color:white;border-bottom:1px solid black;border-left:1px solid black;border-right:1px solid black;border-top-style:none;padding:2px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">Quyền lợi cơ bản</span></td><td style="background-color:white;border-bottom:1px solid black;border-left-style:none;border-right:1px solid black;border-top-style:none;padding:2px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">+ Đâm, va (bao gồm cả va chạm với vật thể khác), lật, đổ, chìm, rơi toàn bộ xe ô tô, bị các vật thể khác rơi vào; hành động ác ý không phải của chủ xe</span><br><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">+ Hỏa họan, cháy, nổ;</span><br><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">+ Những tai họa bất khả kháng do thiên nhiên;</span><br><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">+ Mất toàn bộ xe do trộm cướp;</span></td><td style="background-color:white;border-bottom:1px solid black;border-left-style:none;border-right:1px solid black;border-top-style:none;padding:2px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;"><strong>Đối với tổn thất bộ phận:</strong></span><br><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">BIC chịu trách nhiệm thanh toán chi phí thực tế hợp lý để sửa chữa, thay thế (trường hợp không thể sửa chữa được) bộ phận hoặc trả bằng tiền cho chủ xe cơ giới để bù đắp tổn thất thuộc phạm vi bảo hiểm trên cơ sở xác định được chi phí hợp lý để sửa chữa, khắc phục tổn thất có thể phải trả sau khi áp dụng mức khấu trừ (nếu có)</span><br><span style="font-family:Tahoma, Geneva, sans-serif;">&nbsp;</span><br><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;"><strong>Đối với tổn thất toàn bộ (tổn thất trên 75% giá trị xe):</strong>&nbsp;Chi trả toàn bộ giá trị xe nhưng không vượt quá số tiền bảo hiểm.</span></td></tr><tr><td style="background-color:white;border-bottom:1px solid black;border-left:1px solid black;border-right:1px solid black;border-top-style:none;padding:2px;"><span style="font-family:Tahoma, Geneva, sans-serif;">&nbsp;</span></td><td style="background-color:white;border-bottom:1px solid black;border-left-style:none;border-right:1px solid black;border-top-style:none;padding:2px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">Thanh toán những chi phí cần thiết và hợp lý phát sinh trong tai nạn thuộc phạm vi bảo hiểm nhằm:</span><br><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">+ Ngăn ngừa, hạn chế tổn thất phát sinh thêm khi xe bị thiệt hại do các nguyên nhân trên;</span><br><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">+ Chi phí cứu hộ và vận chuyển xe ô tô bị thiệt hại đến nơi gần nhất; tối đa không vượt quá 10% số tiền bảo hiểm</span><br><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">+ Chi phí giám định .</span></td><td style="background-color:white;border-bottom:1px solid black;border-left-style:none;border-right:1px solid black;border-top-style:none;padding:2px;text-align:center;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">Có</span></td></tr><tr><td style="background-color:white;border-bottom:1px solid black;border-left:1px solid black;border-right:1px solid black;border-top-style:none;padding:2px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">Quyền lợi mở rộng bảo hiểm thủy kích</span></td><td style="background-color:white;border-bottom:1px solid black;border-left-style:none;border-right:1px solid black;border-top-style:none;padding:2px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">Khi phát sinh tổn thất liên quan thủy kích, khách hàng nếu có mua điều khoản này sẽ được chi trả tổn thất</span></td><td style="background-color:white;border-bottom:1px solid black;border-left-style:none;border-right:1px solid black;border-top-style:none;padding:2px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">Sửa chữa hoặc chi trả theo phát sinh thực tế nhưng khấu trừ 20% giá trị tổn thất và tối thiểu 3 triệu đồng/vụ, tùy thuộc số tiền nào lớn hơn</span></td></tr><tr><td style="background-color:white;border-bottom:1px solid black;border-left:1px solid black;border-right:1px solid black;border-top-style:none;height:61px;padding:2px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">Quyền lợi mở rộng bảo hiểm tự chọn gara sửa chữa</span></td><td style="background-color:white;border-bottom:1px solid black;border-left-style:none;border-right:1px solid black;border-top-style:none;height:61px;padding:2px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">Khi phát sinh tổn thất, khách hàng nếu có mua điều khoản này được quyền tự lựa chọn gara mình muốn đưa xe vào sửa chữa.</span></td><td style="background-color:white;border-bottom:1px solid black;border-left-style:none;border-right:1px solid black;border-top-style:none;height:61px;padding:2px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">Được chọn gara nếu mua kèm</span></td></tr><tr><td style="background-color:white;border-bottom:1px solid black;border-left:1px solid black;border-right:1px solid black;border-top-style:none;padding:2px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">Bảo lãnh chi phí sửa chữa</span></td><td style="background-color:white;border-bottom:1px solid black;border-left-style:none;border-right:1px solid black;border-top-style:none;padding:2px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">Khi đưa xe vào gara sửa chữa, nếu xe nằm trong "</span><a href="https://bic.vn/garage-lien-ket.html"><span style="color:rgb(238,28,36);font-family:Tahoma, Geneva, sans-serif;"><strong>Danh sách gara liên kết sửa chữa của BIC</strong></span></a><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">", Quý khách sẽ được BIC bảo lãnh thanh toán chi phí sửa chữa.</span><br><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">Trái lại, Quý khách phải tự thanh toán trước chi phí sửa chữa, sau đó hoàn thiện hồ sơ để BIC chi trả sau.</span></td><td style="background-color:white;border-bottom:1px solid black;border-left-style:none;border-right:1px solid black;border-top-style:none;padding:2px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">Bảo lãnh trong hạn mức bồi thường của tổn thất</span></td></tr><tr><td style="background-color:white;border-bottom:1px solid black;border-left:1px solid black;border-right:1px solid black;border-top-style:none;padding:2px;" colspan="3"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;"><strong>2. Phần Bảo hiểm tai nạn lái phụ xe và người ngồi trên xe</strong></span></td></tr><tr><td style="background-color:white;border-bottom:1px solid black;border-left:1px solid black;border-right:1px solid black;border-top-style:none;padding:2px;"><span style="font-family:Tahoma, Geneva, sans-serif;">&nbsp;</span></td><td style="background-color:white;border-bottom:1px solid black;border-left-style:none;border-right:1px solid black;border-top-style:none;padding:2px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">Chi trả cho các tổn thất về người đối với lái xe, phụ xe và người ngồi trên xe khi đi trên xe bị tai nạn. Khách hàng được tùy chọn mức bồi thường tối đa từ 10-50 triệu / người</span></td><td style="background-color:white;border-bottom:1px solid black;border-left-style:none;border-right:1px solid black;border-top-style:none;padding:2px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">10-50 triệu, tùy theo lựa chọn số tiền bảo hiểm của khách hàng</span></td></tr><tr><td style="background-color:white;border-bottom:1px solid black;border-left:1px solid black;border-right:1px solid black;border-top-style:none;padding:2px;" colspan="3"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;"><strong>3. Phần Bảo hiểm trách nhiệm dân sự của chủ xe</strong></span></td></tr><tr><td style="background-color:white;border-bottom:1px solid black;border-left:1px solid black;border-right:1px solid black;border-top-style:none;padding:2px;"><span style="font-family:Tahoma, Geneva, sans-serif;">&nbsp;</span></td><td style="background-color:white;border-bottom:1px solid black;border-left-style:none;border-right:1px solid black;border-top-style:none;padding:2px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">Chi trả các tổn thất về người và tài sản của bên thứ ba (người đi đường, xe khác, tài sản của người khác) bị thiệt hại do chiếc xe của chủ xe gây ra.</span></td><td style="background-color:white;border-bottom:1px solid black;border-left-style:none;border-right:1px solid black;border-top-style:none;padding:2px;"><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">Về người: đến 100 triệu đồng</span><br><span style="color:rgb(68,68,68);font-family:Tahoma, Geneva, sans-serif;">Về tài sản: đến 100 triệu đồng</span></td></tr></tbody></table></figure><p>&nbsp;</p>',
      type: 'BHVCCAR',
      images: {
        data: [
          {
            id: 364,
            attributes: {
              name: 'Group 2240.png',
              alternativeText: 'Group 2240.png',
              caption: 'Group 2240.png',
              width: 500,
              height: 500,
              formats: {
                thumbnail: {
                  ext: '.png',
                  url: 'https://storage.googleapis.com/car-city-20204.appspot.com/files/images-v2/thumbnail_Group-2240-1665640282791.png',
                  hash: 'thumbnail_Group_2240_a968c1fe7a',
                  mime: 'image/png',
                  name: 'thumbnail_Group 2240.png',
                  path: null,
                  size: 44.87,
                  width: 156,
                  height: 156
                }
              },
              hash: 'Group_2240_a968c1fe7a',
              ext: '.png',
              mime: 'image/png',
              size: 68.08,
              url: 'https://storage.googleapis.com/car-city-20204.appspot.com/files/images-v2/Group-2240-1665640281206.png',
              previewUrl: null,
              provider: '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2022-10-13T05:51:24.122Z',
              updatedAt: '2022-10-13T05:51:24.122Z'
            }
          }
        ]
      },
      loai_bao_hiems: {
        data: [
          {
            id: 2,
            attributes: {
              name: 'BIC',
              des: '',
              active: true,
              createdAt: '2022-09-29T07:47:16.276Z',
              updatedAt: '2022-10-26T07:56:03.259Z',
              publishedAt: '2022-09-29T07:47:20.651Z',
              hotline: null
            }
          }
        ]
      }
    }
  },
  {
    id: 4,
    attributes: {
      name: 'Bảo hiểm TNDS xe máy',
      des: 'Bảo hiểm TNDS bắt buộc đối với xe máy BIC',
      price: 66000,
      discount: 49,
      createdAt: '2022-09-29T23:13:31.245Z',
      updatedAt: '2022-10-24T03:02:50.405Z',
      publishedAt: '2022-10-24T03:02:50.402Z',
      active: true,
      term: '<p><span style="color:rgb(238,28,36);"><strong>Đối tượng bảo hiểm</strong></span></p><p><span style="font-family:Tahoma, Geneva, sans-serif;">Chủ xe máy tham gia giao thông trên lãnh thổ Nước Cộng hòa Xã hội Chủ nghĩa Việt Nam.</span></p><p><span style="color:rgb(238,28,36);"><strong>Phạm vi bảo hiểm</strong></span></p><p><span style="font-family:Tahoma, Geneva, sans-serif;">BIC thay mặt cho chủ xe bồi thường cho các tổn thất về người và tài sản cho bên thứ ba (bên bị thiệt hai do xe của chủ xe gây ra).&nbsp;</span><br><br>&nbsp;<span style="color:rgb(238,28,36);"><strong>Quyền lợi bảo hiểm:</strong></span></p><p><span style="font-family:Tahoma, Geneva, sans-serif;">BIC sẽ thay mặt cho chủ xe bồi thường cho bên thứ ba những thiệt hại về người và tài sản, cho hành khách những thiệt hại về người với mức tối đa như sau:</span></p><p><span style="font-family:Tahoma, Geneva, sans-serif;">Về người: tối đa 150.000.000 đ / người / vụ tai nạn. Cụ thể:</span></p><figure class="table" style="width:1005px;"><table style="background-color:rgb(242, 242, 242);"><tbody><tr><td style="text-align:center;"><span style="font-family:Tahoma, Geneva, sans-serif;"><strong>Quyền lợi bảo hiểm</strong></span></td><td style="text-align:center;"><span style="font-family:Tahoma, Geneva, sans-serif;"><strong>Mức bồi thường tối đa</strong></span></td></tr><tr><td><span style="font-family:Tahoma, Geneva, sans-serif;">Chết do tai nạn</span></td><td><span style="font-family:Tahoma, Geneva, sans-serif;">Trả đủ 150.000.000đ / 1 người / 1 vụ</span></td></tr><tr><td><span style="font-family:Tahoma, Geneva, sans-serif;">Thương tật do tai nạn</span></td><td><span style="font-family:Tahoma, Geneva, sans-serif;">Trả theo </span><a href="https://mybic.vn/uploads/nghi-dinh-03-2021-bao-hiem-bat-buoc-trach-nhiem-dan-su-cua-chu-xe-co-gioi.pdf"><span style="color:rgb(238,28,36);font-family:Tahoma, Geneva, sans-serif;">"Bảng quy định trả tiền bồi thường thiệt hại về người"</span></a></td></tr></tbody></table></figure><p><span style="font-family:Tahoma, Geneva, sans-serif;">Về tài sản: theo tổn thất thực tế, tối đa 50.000.000đ / vụ tai nạn.</span></p>',
      type: 'BHTNDSBIKE',
      images: {
        data: [
          {
            id: 366,
            attributes: {
              name: 'Group 2238.png',
              alternativeText: 'Group 2238.png',
              caption: 'Group 2238.png',
              width: 500,
              height: 500,
              formats: {
                thumbnail: {
                  ext: '.png',
                  url: 'https://storage.googleapis.com/car-city-20204.appspot.com/files/images-v2/thumbnail_Group-2238-1665640339987.png',
                  hash: 'thumbnail_Group_2238_1052a26b39',
                  mime: 'image/png',
                  name: 'thumbnail_Group 2238.png',
                  path: null,
                  size: 43.82,
                  width: 156,
                  height: 156
                }
              },
              hash: 'Group_2238_1052a26b39',
              ext: '.png',
              mime: 'image/png',
              size: 75.64,
              url: 'https://storage.googleapis.com/car-city-20204.appspot.com/files/images-v2/Group-2238-1665640338530.png',
              previewUrl: null,
              provider: '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2022-10-13T05:52:21.329Z',
              updatedAt: '2022-10-13T05:52:21.329Z'
            }
          }
        ]
      },
      loai_bao_hiems: {
        data: [
          {
            id: 2,
            attributes: {
              name: 'BIC',
              des: '',
              active: true,
              createdAt: '2022-09-29T07:47:16.276Z',
              updatedAt: '2022-10-26T07:56:03.259Z',
              publishedAt: '2022-09-29T07:47:20.651Z',
              hotline: null
            }
          }
        ]
      }
    }
  },
  {
    id: 3,
    attributes: {
      name: 'Bảo hiểm Vật chất Ô tô giá ưu đãi nhất',
      des: 'bảo hiểm thân vỏ PVI',
      price: null,
      discount: 15,
      createdAt: '2022-09-29T08:00:19.459Z',
      updatedAt: '2022-10-27T03:28:52.629Z',
      publishedAt: '2022-09-29T08:00:21.009Z',
      active: true,
      term: '<p style="margin-left:0px;"><strong>Đối tượng Bảo hiểm:</strong>&nbsp;Xe ô-tô hoạt động trong lãnh thổ Việt Nam, bao gồm: thân, khung, vỏ, máy móc và các trang thiết bị khác trên xe.</p><p style="margin-left:0px;"><strong>Quyền lợi Bảo hiểm:</strong>&nbsp;Chủ xe/ Người được bảo hiểm được bồi hoàn những thiệt hại vật chất do thiên tai, tai nạn bất ngờ, không lường trước được… trong những trường hợp sau:</p><ul><li>Đâm, va (bao gồm cả va chạm với vật thể khác ngoài xe cơ giới), lật, đổ, chìm, rơi toàn bộ xe, bị các vật thể khác rơi vào; &nbsp;</li><li>Hỏa hoạn, cháy, nổ;</li><li>Những tai họa bất khả kháng do thiên nhiên (Bao gồm nhưng không giới hạn bão, lũ, lụt, sét đánh, giông tố, động đất, sụt lở, sóng thần....)</li><li>Mất toàn bộ xe do trộm, cướp</li><li>Hành vi ác ý, cố tình phá hoại (loại trừ hành vi ác ý, cố tình phá&nbsp;hoại của Chủ xe/ Đại diện hợp pháp của Chủ xe/ Người được bảo hiểm / Lái xe / Người được giao sử dụng chiếc xe đó).</li></ul><p><strong>Quy tắc Bảo hiểm: </strong><a href="https://pvionline.com.vn/FileDownload/QTBH%20Xe%20co%20gioi.pdf">Xem văn bản</a></p><p><strong>Điều khoản mở rộng</strong></p><p><strong>ĐKBS 006/XCG-PVI:</strong> Không tính khấu hao phụ tùng, vật tư thay mới<br>Quyền lợi bảo hiểm: Bảo hiểm PVI sẽ bồi thường toàn bộ chi phí thay thế mới cho những bộ phận bị tổn thất của Xe Ô tô thuộc phạm vi bảo hiểm mà không áp dụng quy định khấu hao phụ tùng, vật tư thay mới tại Điểm 15.1.c.i Điều 15 Quy tắc bảo hiểm.<br><i><strong><u>Điều kiện bảo hiểm: </u></strong></i>Xe tham gia bảo hiểm vật chất xe tại Bảo hiểm PVI.</p><p><strong>ĐKBS 007/XCG-PVI: </strong>Lựa chọn cơ sở sửa chữa chính hãng<br>Quyền lợi bảo hiểm: Chủ xe/ Người được bảo hiểm được quyền yêu cầu sửa chữa Xe tại các Cơ sở sửa chữa chính hãng, gần nhất trong lãnh thổ Việt Nam và có chi phí sửa chữa, thay thế hợp lý, phù hợp với chủng loại xe được bảo hiểm.<br>Ngoài ra, Chủ xe/ Người được bảo hiểm có quyền yêu cầu đưa Xe vào sửa chữa tại các Cơ sở sửa chữa không chính hãng với điều kiện chi phí sửa chữa, thay thế thấp hơn chi phí sửa chữa chính hãng phù hợp với chủng loại xe được bảo hiểm.<br><i><strong><u>Điều kiện bảo hiểm: </u></strong></i>Xe tham gia bảo hiểm vật chất tại Bảo hiểm PVI.</p><p><strong>ĐKBS 008/XCG-PVI:</strong> Bảo hiểm thiệt hại động cơ do ảnh hưởng của nước<br>Quyền lợi bảo hiểm: Bảo hiểm PVI nhận bảo hiểm và bồi thường cho Xe bị thiệt hại động cơ (bị thủy kích do nước lọt vào động cơ đốt trong hoặc do nước gây hư hỏng động cơ điện của xe ô tô điện) do lỗi vô ý của lái xe điều khiển xe đi vào đường ngập nước, khu vực bị ngập nước mà không áp dụng loại trừ bảo hiểm tại Điểm 3 Điều 13 Quy tắc bảo hiểm.<br>Bảo hiểm PVI sẽ chi trả chi phí cứu hộ Xe tham gia bảo hiểm theo quy định tại Điểm 12.2.b Điều 12 Quy tắc bảo hiểm nếu tổn thất thuộc phạm vi bảo hiểm.<br><i><strong><u>Điều kiện bảo hiểm:</u></strong></i><br>- Xe tham gia bảo hiểm vật chất xe tại Bảo hiểm PVI.<br>- Mức khấu trừ: 10% số tiền bồi thường hoặc 3.000.000đ/vụ tổn thất, tùy thuộc vào số nào lớn hơn</p>',
      type: 'BHVCCAR',
      images: {
        data: [
          {
            id: 363,
            attributes: {
              name: 'Group 2237.png',
              alternativeText: 'Group 2237.png',
              caption: 'Group 2237.png',
              width: 500,
              height: 500,
              formats: {
                thumbnail: {
                  ext: '.png',
                  url: 'https://storage.googleapis.com/car-city-20204.appspot.com/files/images-v2/thumbnail_Group-2237-1665636724594.png',
                  hash: 'thumbnail_Group_2237_41d8c67c40',
                  mime: 'image/png',
                  name: 'thumbnail_Group 2237.png',
                  path: null,
                  size: 40.87,
                  width: 156,
                  height: 156
                }
              },
              hash: 'Group_2237_41d8c67c40',
              ext: '.png',
              mime: 'image/png',
              size: 79.41,
              url: 'https://storage.googleapis.com/car-city-20204.appspot.com/files/images-v2/Group-2237-1665636723141.png',
              previewUrl: null,
              provider: '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2022-10-13T04:52:05.935Z',
              updatedAt: '2022-10-13T04:52:05.935Z'
            }
          }
        ]
      },
      loai_bao_hiems: {
        data: [
          {
            id: 1,
            attributes: {
              name: 'PVI',
              des: '',
              active: true,
              createdAt: '2022-09-29T07:46:50.608Z',
              updatedAt: '2022-10-26T07:55:22.674Z',
              publishedAt: '2022-09-29T07:46:52.337Z',
              hotline: null
            }
          }
        ]
      }
    }
  },
  {
    id: 2,
    attributes: {
      name: 'Bảo hiểm TNDS Ô tô bắt buộc đối với chủ xe',
      des: 'Bảo hiểm TNDS bắt buộc đối với chủ xe PVI',
      price: 480700,
      discount: 35,
      createdAt: '2022-09-29T07:52:50.114Z',
      updatedAt: '2022-10-27T03:39:10.496Z',
      publishedAt: '2022-09-29T07:52:52.425Z',
      active: true,
      term: '<p><strong>Mô tả sản phẩm&nbsp;</strong><br>- Bảo hiểm bắt buộc chủ xe ô tô phải có khi tham gia giao thông theo quy định của Nhà nước.<br>- Khi có tai nạn xảy ra, bảo hiểm bắt buộc TNDS sẽ chi trả cho chủ xe ô tô (bên mua bảo hiểm) gây tai nạn trên cơ sở đền bù thiệt hại về người và tài sản của chủ xe ô tô (bên mua bảo hiểm) cho người bị tai nạn (bên thứ ba). Trường hợp chủ xe ô tô (bên mua bảo hiểm) chết hoặc thương tật toàn bộ vĩnh viễn, Bảo hiểm PVI thực hiện bồi thường trực tiếp cho người bị tai nạn (bên thứ ba).</p><p><strong>Người được bảo hiểm&nbsp;</strong><br>- Chủ xe ô tô tham gia giao thông trên lãnh thổ nước Cộng hòa xã hội chủ nghĩa Việt Nam.<br>- Chủ xe ô tô (tổ chức, cá nhân) là chủ sở hữu xe ô tô hoặc được chủ sở hữu xe ô tô giao chiếm hữu, sử dụng hợp pháp, điều khiển xe ô tô.</p><p><strong>Quyền lợi bảo hiểm&nbsp;</strong><br>- Thiệt hại về thân thể, tính mạng và tài sản đối với bên thứ ba do xe ô tô gây ra.<br>- Thiệt hại về thân thể và tính mạng của hành khách theo hợp đồng vận chuyển hành khách do xe ô tô gây ra.</p><p><strong>Mức trách nhiệm bảo hiểm&nbsp;</strong><br>- Đối với thiệt hại về người: 150,000,000 đồng/1 người/1 vụ tai nạn.<br>- Đối với thiệt hại về tài sản (do xe ô tô gây ra): 100,000,000 đồng/1 vụ tai nạn.</p><p><strong>Loại trừ bảo hiểm&nbsp;</strong><br>- Hành động cố ý của chủ xe, lái xe hoặc của người bị thiệt hại.<br>- Lái xe gây tai nạn cố ý bỏ chạy.<br>- Lái xe không có giấy phép lái xe phù hợp.<br>- Thiệt hại đối với tài sản bị mất cắp hoặc bị cướp trong tai nạn.<br>- Chiến tranh, khủng bố, động đất.<br>- Thiệt hại đối với vàng, bạc, đá quý, tiền, các loại giấy tờ có giá trị tiền, đồ cổ, tranh ảnh quý hiếm, thi hài, hài cốt.</p><p><strong>Thời hạn bảo hiểm&nbsp;</strong><br>- Thời điểm bắt đầu có hiệu lực của Giấy chứng nhận bảo hiểm được ghi cụ thể trên Giấy chứng nhận bảo hiểm nhưng không được trước thời điểm chủ xe ô tô đóng đủ phí bảo hiểm.<br>- Thời hạn bảo hiểm ghi trên Giấy chứng nhận bảo hiểm là 1 – 2.5 năm tùy vào nhu cầu của Khách hàng, tối đa bằng thời hạn đăng kiểm xe.</p><p>Trong một số trường hợp cụ thể, thời hạn bảo hiểm có thể dưới 1 năm.<br>&nbsp;</p>',
      type: 'BHTNDSCAR',
      images: {
        data: [
          {
            id: 361,
            attributes: {
              name: 'Group 2236.png',
              alternativeText: 'Group 2236.png',
              caption: 'Group 2236.png',
              width: 500,
              height: 500,
              formats: {
                thumbnail: {
                  ext: '.png',
                  url: 'https://storage.googleapis.com/car-city-20204.appspot.com/files/images-v2/thumbnail_Group-2236-1665636407764.png',
                  hash: 'thumbnail_Group_2236_5ffd202baa',
                  mime: 'image/png',
                  name: 'thumbnail_Group 2236.png',
                  path: null,
                  size: 47.41,
                  width: 156,
                  height: 156
                }
              },
              hash: 'Group_2236_5ffd202baa',
              ext: '.png',
              mime: 'image/png',
              size: 80.72,
              url: 'https://storage.googleapis.com/car-city-20204.appspot.com/files/images-v2/Group-2236-1665636406277.png',
              previewUrl: null,
              provider: '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2022-10-13T04:46:49.112Z',
              updatedAt: '2022-10-13T04:46:49.112Z'
            }
          }
        ]
      },
      loai_bao_hiems: {
        data: [
          {
            id: 1,
            attributes: {
              name: 'PVI',
              des: '',
              active: true,
              createdAt: '2022-09-29T07:46:50.608Z',
              updatedAt: '2022-10-26T07:55:22.674Z',
              publishedAt: '2022-09-29T07:46:52.337Z',
              hotline: null
            }
          }
        ]
      }
    }
  },
  {
    id: 1,
    attributes: {
      name: 'Bảo hiểm Bắt buộc TNDS của chủ xe mô tô - xe máy',
      des: 'PVI',
      price: 66000,
      discount: 49,
      createdAt: '2022-09-29T07:50:47.308Z',
      updatedAt: '2022-10-21T06:55:26.895Z',
      publishedAt: '2022-09-29T07:50:48.756Z',
      active: true,
      term: '<p><strong>Loại hình sản phẩm&nbsp;</strong><br>- Bảo hiểm bắt buộc chủ xe mô tô, xe máy phải có khi tham gia giao thông theo quy định của Nhà nước.<br>- Khi có tai nạn xảy ra, bảo hiểm bắt buộc TNDS sẽ chi trả cho chủ xe mô tô, xe máy (bên mua bảo hiểm) gây tai nạn trên cơ sở đền bù thiệt hại về người và tài sản của chủ xe mô tô, xe máy (bên mua bảo hiểm) cho người bị tai nạn (bên thứ ba). Trường hợp chủ xe mô tô, xe máy (bên mua bảo hiểm) chết hoặc thương tật toàn bộ vĩnh viễn, Bảo hiểm PVI thực hiện bồi thường trực tiếp cho người bị tai nạn (bên thứ ba).</p><p><strong>Người được bảo hiểm&nbsp;</strong><br>- Chủ xe mô tô, xe máy tham gia giao thông trên lãnh thổ nước Cộng hòa xã hội chủ nghĩa Việt Nam.<br>- Chủ xe mô tô, xe máy (tổ chức, cá nhân) là chủ sở hữu xe mô tô, xe máy hoặc được chủ sở hữu xe mô tô, xe máy giao chiếm hữu, sử dụng hợp pháp, điều khiển xe mô tô, xe máy.</p><p><strong>Phạm vi bảo hiểm&nbsp;</strong><br>- Thiệt hại về thân thể, tính mạng và tài sản đối với bên thứ ba do xe mô tô, xe máy gây ra.<br>- Thiệt hại về thân thể và tính mạng của hành khách theo hợp đồng vận chuyển hành khách do xe mô tô, xe máy gây ra.</p><p><strong>Mức trách nhiệm bảo hiểm&nbsp;</strong><br>- Đối với thiệt hại về người: 150,000,000 đồng/1 người/1 vụ tai nạn.<br>- Đối với thiệt hại về tài sản (do xe mô tô, xe máy gây ra): 50,000,000 đồng/1 vụ tai nạn.</p><p><strong>Loại trừ bảo hiểm&nbsp;</strong><br>- Hành động cố ý của chủ xe, lái xe hoặc của người bị thiệt hại.<br>- Lái xe gây tai nạn cố ý bỏ chạy.<br>- Lái xe không có giấy phép lái xe phù hợp.<br>- Thiệt hại đối với tài sản bị mất cắp hoặc bị cướp trong tai nạn.<br>- Chiến tranh, khủng bố, động đất.<br>- Thiệt hại đối với vàng, bạc, đá quý, tiền, các loại giấy tờ có giá trị tiền, đồ cổ, tranh ảnh quý hiếm, thi hài, hài cốt.</p><p>Thời hạn bảo hiểm 1 năm<br>&nbsp;</p>',
      type: 'BHTNDSBIKE',
      images: {
        data: [
          {
            id: 362,
            attributes: {
              name: 'Group 2235.png',
              alternativeText: 'Group 2235.png',
              caption: 'Group 2235.png',
              width: 500,
              height: 500,
              formats: {
                thumbnail: {
                  ext: '.png',
                  url: 'https://storage.googleapis.com/car-city-20204.appspot.com/files/images-v2/thumbnail_Group-2235-1665636442402.png',
                  hash: 'thumbnail_Group_2235_024db85f75',
                  mime: 'image/png',
                  name: 'thumbnail_Group 2235.png',
                  path: null,
                  size: 47.72,
                  width: 156,
                  height: 156
                }
              },
              hash: 'Group_2235_024db85f75',
              ext: '.png',
              mime: 'image/png',
              size: 87.02,
              url: 'https://storage.googleapis.com/car-city-20204.appspot.com/files/images-v2/Group-2235-1665636440958.png',
              previewUrl: null,
              provider: '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2022-10-13T04:47:23.790Z',
              updatedAt: '2022-10-13T04:47:23.790Z'
            }
          }
        ]
      },
      loai_bao_hiems: {
        data: [
          {
            id: 1,
            attributes: {
              name: 'PVI',
              des: '',
              active: true,
              createdAt: '2022-09-29T07:46:50.608Z',
              updatedAt: '2022-10-26T07:55:22.674Z',
              publishedAt: '2022-09-29T07:46:52.337Z',
              hotline: null
            }
          }
        ]
      }
    }
  }
]

export const InsuranceStoreModel = types
  .model('InsuranceStore')
  .props({
    dataPosts: types.optional(types.array(types.frozen()), []),
    dataInsurancePosts: types.optional(types.array(types.frozen()), []),
    dataPostCategories: types.optional(types.array(types.frozen()), []),
    totalPage: types.maybeNull(types.frozen()),
    arrMucDichSuDung: types.optional(types.array(types.frozen()), []),
    arrLoaiXe: types.optional(types.array(types.frozen()), []),
    arrLoaiXeMotor: types.optional(types.array(types.frozen()), []),
    arrLoaiHinh: types.optional(types.array(types.frozen()), []),
    arrHangXe: types.optional(types.array(types.frozen()), []),
    arrHangXeMotor: types.optional(types.array(types.frozen()), []),
    arrHanMucTrachNhiem: types.optional(types.array(types.frozen()), []),
    arrDongXe: types.optional(types.array(types.frozen()), []),
    arrCities: types.optional(types.array(types.frozen()), []),
    rsTinhPhiTNDSOto: types.maybeNull(types.frozen()),
    rsTinhPhiTNDSXM: types.maybeNull(types.frozen()),
    dataCreateOrder: types.maybeNull(types.frozen()),
    bicMotorData: types.maybeNull(types.frozen()),
    catSelected: types.maybeNull(types.frozen()),
    catNameSelected: types.maybeNull(types.frozen()),
    paymentStatus: types.maybe(types.boolean),
    paymentCancel: types.maybe(types.boolean),
    paymentId: types.maybe(types.string),
  })
  .extend(withEnvironment)
  .views(self => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions(self => ({
    getDanhMuc: flow(function * (name, parent_value = '') {
      const config: ApiConfig = yield getRemoveConfig()
      const body = {
        parent_value,
        ten_dmuc: name,
        ma_user: '',
        ma_donvi: '34',
        giatri_chon: '',
        CpId: config.pviCpid,
      }
      const Sign = stringMd5(config.pviKey + body.ten_dmuc + body.ma_donvi)
      body['Sign'] = Sign

      delete config.pviKey
      const result: any = yield self.environment.api.getLoaiXe({ env: __DEV__ ? 'dev' : 'prod', hangBaoHiem: self.catNameSelected, data: body })
      if (result?.data?.Data) {
        const arrayRs = result?.data?.Data.map(item => {
          return {
            label: item.Text,
            value: item.Value,
            checked: false
          }
        })
        return arrayRs
      }
      return []
    })
  }))
  .actions(self => ({
    getDanhMucMotorBic: flow(function * (name, parent_value = '') {
      const config: ApiConfig = yield getRemoveConfig()
      const body = {
        parent_value,
        ten_dmuc: name,
        ma_user: '',
        ma_donvi: '34',
        giatri_chon: '',
        CpId: config.pviCpid,
      }
      const Sign = stringMd5(config.pviKey + body.ten_dmuc + body.ma_donvi)
      body['Sign'] = Sign

      const result: any = yield self.environment.api.getLoaiXe({ env: __DEV__ ? 'dev' : 'prod', hangBaoHiem: self.catNameSelected, data: body })
      return result
    })
  }))
  .actions(self => ({
    getInsurancePosts: flow(function * (catName = '') {
      const result: any = yield self.environment.api.getInsurancePosts(catName)
      // if (__DEV__)
      // {
      //   self.dataPosts = cast(jsonData)
      // }
      if (result?.data?.data) {
        const data = result.data.data
        self.dataPosts = cast(data)
      } else {
        self.dataPosts = cast([])
      }
    }),
    getInsurancePostDetail: flow(function * (id) {
      const result: any = yield self.environment.api.getInsurancePostDetail(id)
      return result
    }),
    getInsuranceCategories: flow(function * () {
      const result: any = yield self.environment.api.getInsuranceCategories()
      if (result?.data?.data) {
        const data = result.data.data
        self.dataPostCategories = cast(data)
      } else {
        self.dataPostCategories = cast([])
      }
    }),
    getMucDichSuDung: flow(function * (cat = 'MDSD_AUTO') {
      const result: any = yield self.getDanhMuc(cat)
      self.arrMucDichSuDung = cast(result)
    }),
    getLoaiXe: flow(function * (idMucDichSuDUng, ChoNgoi, TrongTai = 0, type = 'tnds') {
      const config: ApiConfig = yield getRemoveConfig()
      const body = {
        Ma_MDSD: idMucDichSuDUng,
        SoChoNgoi: ChoNgoi,
        LoaiHinh: '',
        TrongTai: TrongTai || '0',
        CpId: config.pviCpid,
        type: type
      }
      const Sign = stringMd5(config.pviKey + body.SoChoNgoi + body.TrongTai + body.Ma_MDSD)
      body['Sign'] = Sign

      delete config.pviKey
      const result: any = yield self.environment.api.getMaLoaiXe({ env: __DEV__ ? 'dev' : 'prod', hangBaoHiem: self.catNameSelected, data: body })
      if (result?.data?.Data) {
        const arrayRs = result?.data?.Data.map(item => {
          return {
            ...item,
            label: item.Text,
            value: item.Value,
          }
        })
        self.arrLoaiXe = cast(arrayRs)
      }
    }),
    getUrlPvi: flow(function * (RequestId, type) {
      const body = {
        RequestId: RequestId,
        Type: type,
      }
      const result: any = yield self.environment.api.getUrlPvi({ env: __DEV__ ? 'dev' : 'prod', hangBaoHiem: self.catNameSelected, data: body })
      return result
    }),
    getHangXe: flow(function * () {
      __DEV__ && console.log('catNameSelected', self.catNameSelected)
      const result: any = yield self.getDanhMuc('HIEUXEAUTO', '')
      self.arrHangXe = cast(result)
    }),
    getDongXe: flow(function * (parent) {
      const result: any = yield self.getDanhMuc('DONGXE', parent)
      self.arrDongXe = cast(result)
    }),
    getLoaiHinh: flow(function * (idMucDichSuDUng) {
      const result: any = yield self.getDanhMuc('TNDS_LOAIHINH_AUTO', idMucDichSuDUng)
      self.arrLoaiHinh = cast(result)
    }),
    getLoaiXeMotor: flow(function * () {
      const result: any = yield self.getDanhMuc('LOAIXEMOTOR', '')
      self.arrLoaiXeMotor = cast(result)
      if (self.catNameSelected == 'BIC') {
        const result: any = yield self.getDanhMucMotorBic('LOAIXEMOTOR', '')
        self.bicMotorData = cast(result?.data)
      }
      return result
    }),
    getHangXeMotor: flow(function * () {
      const result: any = yield self.getDanhMuc('HIEUXEMOTOR', '')
      self.arrHangXeMotor = cast(result)
    }),
    tinhPhiTNDSOto: flow(function * (data) {
      const brand = self.catNameSelected
      __DEV__ && console.log('detected brand: ', brand)
      const config: ApiConfig = yield getRemoveConfig()
      const body = {
        ...data,
        CpId: config.pviCpid,
      }
      const Sign = stringMd5(config.pviKey + body.ma_trongtai + body?.so_cho)
      body['Sign'] = Sign

      delete config.pviKey
      const result: any = yield self.environment.api.tinhPhiTNDSOto({ hangBaoHiem: brand, data: body, env: __DEV__ ? 'dev' : 'prod' })
      __DEV__ && console.log(result)
      if (result?.data) {
        self.rsTinhPhiTNDSOto = cast(result?.data)
      } else {
        self.rsTinhPhiTNDSOto = cast(null)
      }
      return result
    }),
    tinhPhiVCXOto: flow(function * (data) {
      const config: ApiConfig = yield getRemoveConfig()
      const body = {
        ...data,
        CpId: config.pviCpid,
      }
      const Sign = stringMd5(config.pviKey + body.ma_trongtai + body?.so_cho)
      body['Sign'] = Sign
      if (body['data_gddk']) {
        body['data_gddk'] = JSON.stringify(body?.data_gddk || [])
      }

      delete config.pviKey
      const result: any = yield self.environment.api.tinhPhiVCXOtoV2({ hangBaoHiem: self.catNameSelected, data: body, env: __DEV__ ? 'dev' : 'prod' })
      // const result: any = yield self.environment.api.tinhPhiVCXOto(body)
      __DEV__ && console.log(result)
      if (result?.data) {
        self.rsTinhPhiTNDSOto = cast(result?.data)
      } else {
        self.rsTinhPhiTNDSOto = cast(null)
      }
      return result
    }),
    taoDonTNDSOto: flow(function * (data) {
      const config: ApiConfig = yield getRemoveConfig()
      const body: any = {
        ...data,
        CpId: config.pviCpid,
      }
      const Sign = stringMd5(config.pviKey + body.ma_giaodich)
      body['Sign'] = Sign
      if (body['data_gddk']) {
        body['data_gddk'] = JSON.stringify(body?.data_gddk || [])
      }

      delete config.pviKey
      const result: any = yield self.environment.api.taoDonOtoV2({ hangBaoHiem: self.catNameSelected, data: body, env: __DEV__ ? 'dev' : 'prod' })
      return result?.data ? result.data : result
    }),
    tinhPhiTNDSXM: flow(function * (data) {
      // tính phí TNDS XE MAY
      const config: ApiConfig = yield getRemoveConfig()
      const body = {
        ...data,
        CpId: config.pviCpid,
      }
      const Sign = stringMd5(config.pviKey + body.ngay_dau + body.ngay_cuoi + body.loai_xe)
      body['Sign'] = Sign

      delete config.pviKey
      const result: any = yield self.environment.api.tinhPhiTNDSXMV2({ hangBaoHiem: self.catNameSelected, data: body, env: __DEV__ ? 'dev' : 'prod' })
      __DEV__ && console.log(result)
      if (result?.data) {
        self.rsTinhPhiTNDSXM = cast(result?.data)
      } else {
        self.rsTinhPhiTNDSXM = cast(null)
      }
      return result
    }),
    taoDonTNDSXM: flow(function * (data) {
      const config: ApiConfig = yield getRemoveConfig()
      const body: any = {
        ...data,
        CpId: config.pviCpid,
      }
      const Sign = stringMd5(config.pviKey + body.bien_kiemsoat + body.email + body.so_dienthoai + body.nhan_hieu + body.loai_xe + body.nam_sanxuat)
      body['Sign'] = Sign

      delete config.pviKey
      const result: any = yield self.environment.api.taoDonTNDSXMV2({ hangBaoHiem: self.catNameSelected, data: body, env: __DEV__ ? 'dev' : 'prod' })
      return result
    }),
    getCities: flow(function * () {
      const cities = []
      for (const city in countriesData) {
        cities.push({
          label: city,
          value: city,
        })
      }
      self.arrCities = cast(cities)
    }),
    createOrderInsuranceHistory: flow(function * (body) {
      const result: any = yield self.environment.api.createOrderInsuranceHistory(body)
      return result
    }),
    createUrlPayment: flow(function * (data) {
      const result: any = yield self.environment.api.createUrlPayment(data)
      return result
    }),
    getLichSuMuaBHByUserId: flow(function * (userId) {
      if (userId) {
        const result: any = yield self.environment.api.getLichSuMuaBHByUserId(userId, '')
        if (result?.data && result?.data?.data?.length > 0) {
          const arr = result?.data?.data.map(item => {
            return {
              ...item
            }
          })
          self.dataInsurancePosts = cast(arr)
        } else {
          self.dataInsurancePosts = cast([])
        }
      }
    }),
    deleteInsuranceLink: flow(function * (id) {
      if (id) {
        const result: any = yield self.environment.api.xoaLichSuMuaBH(id)
        return result
      }
      return null
    }),
    resetTotalFreeTNDSOto: function () {
      self.rsTinhPhiTNDSOto = cast(null)
    },
    resetTotalFreeTNDSXM: function () {
      self.rsTinhPhiTNDSXM = cast(null)
    },
    setPaymentStatus: function(value) {
      self.paymentStatus = cast(value)
    },
    setPaymentCancel: function(value) {
      self.paymentCancel = cast(value)
    },
    setPaymentId: function(value) {
      self.paymentId = cast(value)
    },
    setArrLoaiHinh: function(value) {
      self.arrLoaiHinh = cast(value)
    },
    setDataCreateOrder: function(value) {
      self.dataCreateOrder = cast(value)
    },
    setCatSelected: function(value) {
      if (value) {
        self.catNameSelected = cast(value?.attributes?.loai_bao_hiems?.data?.length > 0 ? value?.attributes?.loai_bao_hiems?.data[0].attributes?.name : '')
        // self.catNameSelected = cast('BIC')
        self.catSelected = cast(value)
      }
    },
    clearArr: function() {
      self.arrMucDichSuDung = cast([])
      self.arrLoaiXe = cast([])
      self.arrDongXe = cast([])
      self.arrHangXe = cast([])
      self.arrLoaiHinh = cast([])
      self.arrHangXeMotor = cast([])
      self.arrLoaiXeMotor = cast([])
      self.paymentStatus = cast(false)
      self.paymentCancel = cast(false)
      self.paymentId = cast('')
    },
    setArrHanMucTrachNhiem: function(values) {
      const arr = Object.keys(values).map((key) => {
        return { label: values[key], value: key + '' }
      })
      __DEV__ && console.log(arr)
      self.arrHanMucTrachNhiem = cast(arr)
    },
  })).actions(self => {
    let initialState = {}
    return {
      afterCreate: () => {
        initialState = getSnapshot(self)
      },
      reset: () => {
        applySnapshot(self, {})
      },
    }
  })

export interface InsuranceStore extends Instance<typeof InsuranceStoreModel> {}
export interface InsuranceStoreSnapshotOut extends SnapshotOut<typeof InsuranceStoreModel> {}
export interface InsuranceStoreSnapshotIn extends SnapshotIn<typeof InsuranceStoreModel> {}
export const createInsuranceStoreDefaultModel = () => types.optional(InsuranceStoreModel, {})
