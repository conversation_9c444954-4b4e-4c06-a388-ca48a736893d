import { Instance, SnapshotIn, SnapshotOut, types } from 'mobx-state-tree'

/**
 * Model description here for TypeScript hints.
 */
export const BhtndsStoreModel = types
  .model('BhtndsStore')
  .props({})
  .views((self) => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions((self) => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars

export interface BhtndsStore extends Instance<typeof BhtndsStoreModel> {}
export interface BhtndsStoreSnapshotOut extends SnapshotOut<typeof BhtndsStoreModel> {}
export interface BhtndsStoreSnapshotIn extends SnapshotIn<typeof BhtndsStoreModel> {}
export const createBhtndsStoreDefaultModel = () => types.optional(BhtndsStoreModel, {})
