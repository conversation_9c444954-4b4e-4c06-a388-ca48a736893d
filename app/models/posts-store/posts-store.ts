import { flow, Instance, SnapshotOut, types, cast } from 'mobx-state-tree'
import { withEnvironment } from '../extensions'

/**
 * Model description here for TypeScript hints.
 */
export const PostsStoreModel = types
  .model('PostsStore')
  .props({
    dataPosts: types.optional(types.array(types.frozen()), []),
    dataPostCategories: types.optional(types.array(types.frozen()), []),
    totalPage: types.maybeNull(types.frozen())
  })
  .extend(withEnvironment)
  .views(self => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions(self => ({
    getPosts: flow(function * (catName = '') {
      const result: any = yield self.environment.api.getPosts(catName)
      if (result?.data?.data) {
        const data = result.data.data
        self.dataPosts = cast(data)
      } else {
        self.dataPosts = cast([])
      }
    }),
    getPostDetail: flow(function * (id) {
      const result: any = yield self.environment.api.getPostDetail(id)
      return result
    }
    ),
    getPostCategories: flow(function * () {
      const result: any = yield self.environment.api.getPostCategories()
      if (result?.data?.data?.data) {
        const data = result.data.data.data
        self.dataPostCategories = cast([{
          id: 0,
          attributes: {
            name: 'Tất cả'
          }
        }, ...data])
      } else {
        self.dataPostCategories = cast([])
      }
    }
    ),
    getPostsByCategories: flow(function * (categoriesID, page, isLoadMore) {
      const result: any = yield self.environment.api.getPostsByCategories(categoriesID, page)
      if (result.kind === 'ok' && result.data) {
        const data = result.data.data
        const totalPage = result.data.headers['x-wp-totalpages']
        if (isLoadMore) {
          self.dataPosts = cast([...self.dataPosts, ...data])
        } else {
          self.dataPosts = cast(data)
        }
        self.totalPage = cast(Number(totalPage))
      } else {
        self.dataPosts = cast([])
      }
    }
    ),

  })) // eslint-disable-line @typescript-eslint/no-unused-vars

/**
  * Un-comment the following to omit model attributes from your snapshots (and from async storage).
  * Useful for sensitive data like passwords, or transitive state like whether a modal is open.

  * Note that you'll need to import `omit` from ramda, which is already included in the project!
  *  .postProcessSnapshot(omit(["password", "socialSecurityNumber", "creditCardNumber"]))
  */

type PostsStoreType = Instance<typeof PostsStoreModel>
export interface PostsStore extends PostsStoreType {}
type PostsStoreSnapshotType = SnapshotOut<typeof PostsStoreModel>
export interface PostsStoreSnapshot extends PostsStoreSnapshotType {}
