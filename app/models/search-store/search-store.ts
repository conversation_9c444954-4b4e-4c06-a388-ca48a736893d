import { flow, Instance, SnapshotOut, types, cast, applySnapshot } from 'mobx-state-tree'
import { withEnvironment } from '@app/models'
import { calculateDistance } from '@app/services'
import _ from 'lodash'

/**
 * Model description here for TypeScript hints.
 */
const DataSearchRate = types.model({
  id: types.maybeNull(types.string),
  image: types.maybeNull(types.string),
  coupon: types.maybeNull(types.string),
  serviceName: types.maybeNull(types.string),
  address: types.maybeNull(types.string),
  totalRate: types.maybeNull(types.frozen()),
  distance: types.maybeNull(types.frozen()),
  price: types.maybeNull(types.frozen()),
  priceOld: types.maybeNull(types.frozen()),
  typeShip: types.maybeNull(types.number),
  storeName: types.maybeNull(types.string),
  thumbnail: types.maybeNull(types.string),
  branches: types.maybeNull(types.frozen()),
  calculated: types.maybeNull(types.frozen()),
  userId: types.maybeNull(types.string),
  serviceId: types.maybeNull(types.string),
  phone: types.maybeNull(types.frozen()),
  usersFavorites: types.optional(types.array(types.frozen()), []),
  isFavorite: types.maybe(types.boolean),
})
const DataSearchNear = types.model({
  id: types.maybeNull(types.string),
  image: types.maybeNull(types.string),
  coupon: types.maybeNull(types.string),
  serviceName: types.maybeNull(types.string),
  address: types.maybeNull(types.string),
  totalRate: types.maybeNull(types.frozen()),
  distance: types.maybeNull(types.frozen()),
  price: types.maybeNull(types.frozen()),
  priceOld: types.maybeNull(types.frozen()),
  typeShip: types.maybeNull(types.number),
  storeName: types.maybeNull(types.string),
  thumbnail: types.maybeNull(types.string),
  branches: types.maybeNull(types.frozen()),
  calculated: types.maybeNull(types.frozen()),
  userId: types.maybeNull(types.string),
  serviceId: types.maybeNull(types.string),
  phone: types.maybeNull(types.frozen()),
  usersFavorites: types.optional(types.array(types.frozen()), []),
  isFavorite: types.maybe(types.boolean),
})
const ListDistrict = types.model({
  value: types.maybeNull(types.frozen()),
  label: types.maybeNull(types.frozen()),
  _id: types.maybeNull(types.frozen()),
})

const DataSearchHistory = types.model({
  id: types.maybeNull(types.string),
  image: types.maybeNull(types.string),
  coupon: types.maybeNull(types.string),
  serviceName: types.maybeNull(types.string),
  address: types.maybeNull(types.string),
  totalRate: types.maybeNull(types.frozen()),
  distance: types.maybeNull(types.frozen()),
  price: types.maybeNull(types.frozen()),
  storeName: types.maybeNull(types.string),
  branches: types.maybeNull(types.frozen()),
  calculated: types.maybeNull(types.frozen()),
})
export const SearchStoreModel = types
  .model('SearchStore')
  .props({
    dataSearchRate: types.optional(types.array(DataSearchRate), []),
    dataSearchNear: types.optional(types.array(DataSearchNear), []),
    dataFavorites: types.optional(types.array(DataSearchNear), []),
    dataSearchHistory: types.optional(types.array(DataSearchHistory), []),
    totalPage: types.maybe(types.number),
    typeSearch: types.maybeNull(types.number),
    listDistrict: types.optional(types.array(ListDistrict), []),
    rateProduct: types.optional(types.frozen(), []),
    priceRange: types.optional(types.frozen(), []),
    shipping: types.optional(types.frozen(), []),
    keyword: types.optional(types.frozen(), []),
  })
  .extend(withEnvironment)
  .views(self => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions(self => ({
    searchRate: flow(function * (page, search, isLoadMore, type, distance, rating, shipping, price, rate, typeShip) {
      const result: any = yield self.environment.api.searchSpa(page, search, type, distance, rating, shipping, price, rate, typeShip)
      if (result.kind === 'ok' && result?.data?.data) {
        const data = result.data.data
        const datasRate:typeof DataSearchRate[] = yield Promise.all(data.result.map(async item => {
          const result2: any = await self.environment.api.getServiceSpaByStoreId(item._id)
          // __DEV__ && console.log('get branches store', result2)
          if (result2.kind === 'ok') {
            const data2 = result2.data
            const branchesStore = data2?.branchStore || []
            return {
              ...item,
              address: branchesStore.length > 0 ? branchesStore[0].address : '',
              image: item.pictures[0],
              id: item._id,
              serviceName: item.name,
              totalRate: item.totalRate || 0,
              calculated: item?.dist?.calculated
            }
          }
          return {
            ...item,
            image: item.pictures[0],
            id: item._id,
            serviceName: item.name,
            totalRate: item.totalRate || 0,
            calculated: item?.dist?.calculated
          }
        }))

        if (isLoadMore) {
          self.dataSearchRate = cast([...self.dataSearchRate, ...datasRate])
        } else {
          self.dataSearchRate = cast(datasRate)
        }
        self.totalPage = cast(data.totalPage)
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    searchNear: flow(function * (page, search, isLoadMore, type, distance, rating, shipping, price, rate, typeShip) {
      const result: any = yield self.environment.api.searchSpa(page, search, type, distance, rating, shipping, price, rate, typeShip)
      if (result.kind === 'ok' && result?.data?.data) {
        const data = result.data.data
        const datasNear:typeof DataSearchNear[] = yield Promise.all(data.result.map(async item => {
          let branchesStore = []
          const result2: any = await self.environment.api.getServiceSpaByStoreId(item._id)
          // __DEV__ && console.log('get branches store', result2)
          if (result2.kind === 'ok') {
            const data2 = result2.data
            branchesStore = data2?.branchStore || []
            return {
              ...item,
              address: branchesStore.length > 0 ? branchesStore[0].address : '',
              image: item.pictures[0],
              id: item._id,
              serviceName: item.name,
              totalRate: item.totalRate || 0,
              calculated: item?.dist?.calculated
            }
          }
          return {
            ...item,
            image: item.pictures[0],
            id: item._id,
            serviceName: item.name,
            totalRate: item.totalRate || 0,
            calculated: item?.dist?.calculated
          }
        }))

        if (isLoadMore) {
          self.dataSearchNear = cast([...self.dataSearchNear, ...datasNear])
        } else {
          self.dataSearchNear = cast(datasNear)
        }
        self.totalPage = cast(data.totalPage)
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    searchFavorite: flow(function * (page, isLoadMore = false) {
      const result: any = yield self.environment.api.loadFavorite(page)
      if (result.kind === 'ok' && result?.data?.data) {
        const data = result.data.data
        const dataFavorites:typeof DataSearchRate[] = yield Promise.all(data.result.map(async item => {
          const result2: any = await self.environment.api.getServiceSpaByStoreId(item.serviceId)
          // __DEV__ && console.log('get branches store', result2)
          if (result2.kind === 'ok') {
            const data2 = result2.data
            const branchesStore = data2?.branchStore || []
            const distanceCal = await calculateDistance({ lat: branchesStore[0].location.coordinates[1], lng: branchesStore[0].location.coordinates[0] })
            return {
              ...item,
              address: branchesStore.length > 0 ? branchesStore[0].address : '',
              image: item.pictures[0],
              id: item._id,
              serviceName: item.name,
              totalRate: item.totalRate || 0,
              distance: distanceCal
            }
          }
          return {
            ...item,
            image: item.pictures[0],
            id: item._id,
            serviceName: item.name,
            totalRate: item.totalRate || 0
          }
        }))

        if (isLoadMore) {
          const data = _.orderBy([...self.dataFavorites, ...dataFavorites], ['distance'], ['asc'])
          self.dataFavorites = cast(data)
        } else {
          const data = _.orderBy(dataFavorites, ['distance'], ['asc'])
          self.dataFavorites = cast(data)
        }
        self.totalPage = cast(data.totalPage)
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    getHistory: flow(function * (user, type) {
      const result: any = yield self.environment.api.getHistorySearchSpa(user, type)
      const data = result.data
      if (result.kind === 'ok') {
        const datasHistory:typeof DataSearchHistory[] = data.moiNhat.map(item => {
          return { image: item.pictures[0], id: item._id, serviceName: item.name, adress: item.adress }
        })
        self.dataSearchHistory = cast(datasHistory)
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    getDistrict: flow(function * () {
      const result: any = yield self.environment.api.getDistrict()
      if (result.kind === 'ok' && result?.data?.data) {
        const dataDistrict = result.data.data.map(item => {
          return {
            ...item,
            label: item.name
          }
        })
        self.listDistrict = cast(dataDistrict)
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    loadProductFilter: flow(function * () {
      const result: any = yield self.environment.api.loadProductFilter()
      // return result
      if (result.kind === 'ok' && result?.data?.fields) {
        const rateProduct = result.data.fields.rate
        const priceRange = result.data.fields.price
        const shipping = result.data.fields.shipping
        self.rateProduct = cast(rateProduct)
        self.priceRange = cast(priceRange)
        self.shipping = cast(shipping)
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    setTypeSearch: function(value) {
      self.typeSearch = value
    },
    setKeyword: function(value) {
      self.keyword = value
    },
    clearFields: function() {
      self.dataSearchHistory = cast([])
    },

  })) // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions(self => {
    return {
      afterCreate: () => {
      },
      reset: () => {
        applySnapshot(self, {})
      },
    }
  })
/**
 * Un-comment the following to omit model attributes from your snapshots (and from async storage).
 * Useful for sensitive data like passwords, or transitive state like whether a modal is open.

 * Note that you'll need to import `omit` from ramda, which is already included in the project!
 *  .postProcessSnapshot(omit(["password", "socialSecurityNumber", "creditCardNumber"]))
 */

type SearchStoreType = Instance<typeof SearchStoreModel>

export interface SearchStore extends SearchStoreType {
}

type SearchStoreSnapshotType = SnapshotOut<typeof SearchStoreModel>

export interface SearchStoreSnapshot extends SearchStoreSnapshotType {
}
