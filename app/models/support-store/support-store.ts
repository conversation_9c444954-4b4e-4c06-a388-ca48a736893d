import { Instance, SnapshotOut, types, flow, cast } from 'mobx-state-tree'
import { withEnvironment } from '../extensions'

/**
 * Model description here for TypeScript hints.
 */

export const SupportStoreModel = types
  .model('SupportStore')
  .props({
    trungTamHoTro: types.maybe(types.string),
    chinhSachBaoMat: types.maybe(types.string),
    dieuKhoanDichVu: types.maybe(types.string),
    gioiThieu: types.maybe(types.string),
  })
  .extend(withEnvironment)
  .views(self => ({}))
  .actions((self) => ({
    getDataSupportCenter: flow(function * () {
      const result: any = yield self.environment.api.getDataSupportCenter()
      const data = result.data
      self.trungTamHoTro = cast(data.trungTamHoTro)
      self.chinhSachBaoMat = cast(data.chinhSachBaoMat)
      self.dieuKhoanDichVu = cast(data.dieuKhoanDichVu)
      self.gioiThieu = cast(data.gioiThieu)
    }),
  }))

type SupportStoreType = Instance<typeof SupportStoreModel>
export interface SupportStore extends SupportStoreType {}
type SupportStoreSnapshotType = SnapshotOut<typeof SupportStoreModel>
export interface SupportStoreSnapshot extends SupportStoreSnapshotType {}
