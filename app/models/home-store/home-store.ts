import { flow, Instance, SnapshotOut, types, cast } from 'mobx-state-tree'
import { withEnvironment } from '../extensions'
import { aliasProvince } from '@app/utils'
import _ from 'lodash'
import { calculateDistance } from '@app/services'
import { dsDonVi } from '@app/models/home-store/data'
const { IMAGE_URL } = require('../../config/env')

const Banner = types.model({
  images: types.string,
  params: types.maybeNull(types.frozen()),
  screen: types.maybeNull(types.frozen()),
})

const DataServices = types.model({
  address: types.maybeNull(types.frozen()),
  picture: types.maybeNull(types.frozen()),
  name: types.maybeNull(types.frozen()),
  _id: types.maybeNull(types.frozen()),
})

const BannerPromotion = types.model({
  picture: types.maybeNull(types.frozen()),
  name: types.maybeNull(types.frozen()),
  _id: types.maybeNull(types.frozen()),
  params: types.maybeNull(types.frozen()),
  screen: types.maybeNull(types.frozen()),
  serviceName: types.maybeNull(types.frozen()),
  thumbail: types.maybeNull(types.frozen()),
  storeInfo: types.maybeNull(types.frozen())
})

const TopBranch = types.model({
  picture: types.maybeNull(types.frozen()),
  name: types.maybeNull(types.frozen()),
  _id: types.maybeNull(types.frozen()),
  params: types.maybeNull(types.frozen()),
  screen: types.maybeNull(types.frozen()),
  serviceName: types.maybeNull(types.frozen()),
  thumbail: types.maybeNull(types.frozen()),
  storeInfo: types.maybeNull(types.frozen()),
  branchs: types.maybeNull(types.frozen()),
})

const DataProvinces = types.model({
  value: types.maybeNull(types.frozen()),
  label: types.maybeNull(types.frozen()),
  _id: types.maybeNull(types.frozen()),
})
const Promotions = types.model({
  _id: types.maybeNull(types.frozen()),
  title: types.maybeNull(types.frozen()),
  description: types.maybeNull(types.frozen()),
  content: types.maybeNull(types.frozen()),
  thumbnail: types.maybeNull(types.frozen()),
  cover: types.maybeNull(types.frozen()),
  modifyAt: types.maybeNull(types.frozen()),
  createAt: types.maybeNull(types.frozen()),
  type: types.maybeNull(types.frozen()),
  status: types.maybeNull(types.frozen()),
  stores: types.maybeNull(types.frozen()),
})
const NewsForHome = types.model({
  _id: types.maybeNull(types.frozen()),
  title: types.maybeNull(types.frozen()),
  description: types.maybeNull(types.frozen()),
  content: types.maybeNull(types.frozen()),
  picture: types.maybeNull(types.frozen()),
  cover: types.maybeNull(types.frozen()),
  modifyAt: types.maybeNull(types.frozen()),
  createAt: types.maybeNull(types.frozen()),
  type: types.maybeNull(types.frozen()),
  status: types.maybeNull(types.frozen()),
  stores: types.maybeNull(types.frozen()),
  name: types.maybeNull(types.frozen()),
})

const BannerHome2 = types.model({
  picture: types.maybeNull(types.frozen()),
  _id: types.maybeNull(types.frozen()),
  params: types.maybeNull(types.frozen()),
  screen: types.maybeNull(types.frozen()),
})

/**
 * Model description here for TypeScript hints.
 */
export const HomeStoreModel = types
  .model('HomeStore')
  .props({
    banners: types.optional(types.array(Banner), []),
    images: types.optional(types.frozen(), []),
    imagesPromotion: types.optional(types.frozen(), []),
    province: types.maybeNull(types.string),
    promotion: types.optional(types.frozen(), []),
    promotions: types.optional(types.frozen(), []),
    provinces: types.optional(types.frozen(), []),
    promotionById: types.optional(types.frozen(), []),
    image: types.maybeNull(types.string),
    _id: types.maybeNull(types.frozen()),
    title: types.maybeNull(types.frozen()),
    description: types.maybeNull(types.frozen()),
    content: types.maybeNull(types.frozen()),
    dataServices: types.optional(types.array(DataServices), []),
    bannerPromotion: types.optional(types.array(BannerPromotion), []),
    topBranch: types.optional(types.array(TopBranch), []),
    dataServicesById: types.optional(types.array(types.frozen()), []),
    promotionImage: types.maybeNull(types.frozen()),
    endTime: types.maybeNull(types.frozen()),
    startTime: types.maybeNull(types.frozen()),
    imagePromotionById: types.maybeNull(types.string),
    params: types.maybeNull(types.frozen()),
    screen: types.maybeNull(types.frozen()),
    newsForHome: types.optional(types.array(types.frozen()), []),
    bannerHome2: types.optional(types.array(BannerHome2), dsDonVi),
    reloadData: types.maybe(types.boolean),
    appConfig: types.maybe(types.frozen()),
    homeCategories: types.optional(types.array(types.frozen()), []),
    dsYeuThich: types.optional(types.array(types.frozen()), []),
    homeHotCategories: types.optional(types.array(types.frozen()), []),
    listCarByUserId: types.optional(types.array(types.frozen()), []),
  })
  .extend(withEnvironment)
  .views(self => ({}))
  .actions(self => ({
    getBanners: flow(function * () {
      const result: any = yield self.environment.api.getBanners()
      if (result.kind === 'ok') {
        const banners: typeof Banner[] = result.data.banners.map(item => ({ images: item.thumbail, ...item }))
        const images = result.data.banners.map(item => (item.thumbail))
        self.banners = cast(banners)
        self.images = cast(images)
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    getAppConfig: flow(function * () {
      const result: any = yield self.environment.api.getAppConfig()
      if (result.kind === 'ok') {
        self.appConfig = cast(result?.data?.data?.attributes)
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
  }))
  .actions(self => ({
    setProvince: function(value) {
      self.province = value
    },
    setReloadData: function(value) {
      self.reloadData = value
    }
  }))
  .actions(self => ({
    getPromotion: flow(function * () {
      const result: any = yield self.environment.api.getPromotion()
      if (result.kind === 'ok') {
        const promotion = result.data.data
        self.promotion = cast(promotion)
        self.image = cast(promotion.cover ? promotion.cover : promotion.thumbnail)
        self._id = cast(promotion._id)
        self.title = cast(promotion.title)
        self.description = cast(promotion.description)
        self.content = cast(promotion.content)
        if (result?.data?.data?.dataServices) {
          const dataServices: typeof DataServices[] = result.data.data.dataServices.map(item => ({
            picture: item.pictures[0], ...item
          }))
          self.dataServices = cast(dataServices)
        }
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    // getBannerPromotion: flow(function * () {
    //   const result: any = yield self.environment.api.getBannerPromotion()
    //   // return result
    //   if (result.kind === 'ok') {
    //     const dataBannerPromotion: typeof BannerPromotion[] = result.data.data.banners.map(item => ({
    //       picture: item.thumbail, ...item, serviceName: item.name
    //     }))
    //     self.topBranch = cast(dataBannerPromotion)
    //   } else {
    //     __DEV__ && console.log(result.kind)
    //   }
    // }),
    getTopBranch: flow(function * () {
      const result: any = yield self.environment.api.getTopBranch()
      // return result
      if (result.kind === 'ok') {
        const dataTopBranch = result.data.data.banners.map(item => ({
          ...item,
          serviceName: item.name,
          picture: item.thumbail,
        }))
        self.topBranch = cast(dataTopBranch)
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    getBannerHome2: flow(function * () {
      const result: any = yield self.environment.api.getBannerHome2()
      // return result
      if (result.kind === 'ok' && result?.data?.data?.banners) {
        const dataBannerHome2 = result.data.data.banners.map(item => ({
          picture: item.thumbail, ...item
        }))
        console.log('dataBannerHome2', dataBannerHome2)
        self.bannerHome2 = cast(dataBannerHome2)
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
  }))

  .actions(self => ({
    getPromotionById: flow(function * (promotionId) {
      const result: any = yield self.environment.api.getPromotionById(promotionId)
      if (result.kind === 'ok') {
        const promotionById = result.data.data
        self.promotionById = cast(promotionById)
        self.imagePromotionById = cast(promotionById.cover)
        self._id = cast(promotionById._id)
        self.title = cast(promotionById.title)
        self.description = cast(promotionById.description)
        self.content = cast(promotionById.content)
        self.startTime = cast(promotionById.startTime)
        self.endTime = cast(promotionById.endTime)
        if (Array.isArray(result.data.data.dataServices)) {
          const dataServicesById: any = yield Promise.all(result.data.data.dataServices.map(async item => {
            const result2: any = await self.environment.api.getServiceSpaByStoreId(item._id)
            // __DEV__ && console.log('get branches store', result2)
            if (result2.kind === 'ok') {
              const data2 = result2.data
              const branchesStore = data2?.branchStore || []
              const distanceCal = await calculateDistance({ lat: branchesStore[0].location.coordinates[1], lng: branchesStore[0].location.coordinates[0] })
              return {
                ...item,
                address: branchesStore.length > 0 ? branchesStore[0].address : '',
                image: item.pictures[0],
                id: item._id,
                serviceName: item.name,
                totalRate: item.totalRate || 0,
                distance: distanceCal
              }
            }

            return {
              image: item.pictures[0],
              ...item,
              id: item._id,
              serviceName: item.name,
              branches: item?.branches,
              totalRate: item?.totalRate || 0
            }
          }))
          self.dataServicesById = cast(_.orderBy(dataServicesById, ['distance'], ['asc']))
        } else {
          self.dataServicesById = cast([])
        }
        return promotionById
      } else {
        __DEV__ && console.log(result.kind)
        return result
      }
    }),
    getProvinces: flow(function * () {
      const result: any = yield self.environment.api.getProvinces()
      if (result.kind === 'ok' && result?.data?.data) {
        const provinces:typeof DataProvinces = result.data.data.map((item, index) => {
          return {
            value: `${item.totalBrand} Cơ sở`,
            label: aliasProvince(item.province),
            _id: index++,
          }
        })
        const data = _.orderBy(provinces, ['label'], ['asc'])
        self.provinces = cast(data || [])
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
  }))
  .actions(self => ({
    getPromotions: flow(function * () {
      const result: any = yield self.environment.api.getPromotions()
      if (result.kind === 'ok' && result?.data?.data) {
        const data = result?.data?.data.result
        const promotionsData: typeof Promotions[] = data.map(item => ({ images: item?.thumbnail || item?.cover, ...item }))
        const imagesData = data.map(item => (item?.cover || item?.thumbnail))
        self.promotions = cast(promotionsData)
        self.imagesPromotion = cast(imagesData)
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
  }))
  .actions(self => ({
    getNewsForHome: flow(function * () {
      // const result: any = yield self.environment.api.getNewsForHome()
      const result: any = yield self.environment.api.getPosts('khuyến') // lay danh muc khuyen mai
      if (result.kind === 'ok' && result?.data?.data) {
        const data = result?.data?.data
        const dataNews = data.map(item => {
          return {
            // ...item,
            _id: item.id,
            picture: item?.attributes?.image?.data?.attributes.url,
            name: item.attributes?.title,
            ...item.attributes
          }
        })
        // const imagesData = data.map(item => (IMAGE_URL + item?.cover || item?.thumbnail))
        self.newsForHome = cast(dataNews)
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    getNewsDetail: flow(function * (id) {
      const result: any = yield self.environment.api.getNewsDetail(id)
      return result
      // if (result.kind === 'ok' && result?.data?.data) {
      //   const data = result?.data?.data
      //   const dataNews = data.map(item => {
      //     return {
      //       ...item,
      //       picture: item.thumbail || item.thumbnail,
      //       name: item.title
      //     }
      //   })
      //   // const imagesData = data.map(item => (IMAGE_URL + item?.cover || item?.thumbnail))
      //   self.newsForHome = cast(dataNews)
      // } else {
      //   __DEV__ && console.log(result.kind)
      // }
    }),
    getHomeCategories: flow(function * (tag = 'menu') {
      const result: any = yield self.environment.api.getHomeCategories(tag)
      if (result?.data?.data) {
        const data = result?.data?.data
        if (tag == 'menu') {
          self.homeCategories = cast(data)
          console.dir('self.homeCategories', self.homeCategories);
        }
        if (tag == 'hot') {
          self.homeHotCategories = cast(data)
        }
        return result
      } else {
        if (tag == 'menu') {
          self.homeCategories = cast([])
        }
        if (tag == 'hot') {
          self.homeHotCategories = cast([])
        }
        return result
      }
    }),
    getServicesCategories: flow(function * (type = '0,1,2') {
      const result: any = yield self.environment.api.getCategoriesByType(type)
      __DEV__ && console.log(result)
      if (result?.data?.data?.categories) {
        const data = result?.data?.data?.categories
        self.homeHotCategories = cast([...data])
        return result
      }
    }),
  }))

/**
 * Un-comment the following to omit model attributes from your snapshots (and from async storage).
 * Useful for sensitive data like passwords, or transitive state like whether a modal is open.

 * Note that you'll need to import `omit` from ramda, which is already included in the project!
 *  .postProcessSnapshot(omit(["password", "socialSecurityNumber", "creditCardNumber"]))
 */

type HomeStoreType = Instance<typeof HomeStoreModel>
export interface HomeStore extends HomeStoreType {}
type HomeStoreSnapshotType = SnapshotOut<typeof HomeStoreModel>
export interface HomeStoreSnapshot extends HomeStoreSnapshotType {}
