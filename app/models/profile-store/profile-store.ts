import { applySnapshot, cast, flow, getSnapshot, Instance, SnapshotOut, types } from 'mobx-state-tree'
import { withEnvironment } from '../extensions'
import { translate } from '../../i18n'
import { saveToken } from '../../services/tokenService'
import _ from 'lodash'
import { baseTimeFormat } from '../../utils/time'
import { OrderProductStoreModel } from '../order-product-store/order-product-store'
import DeviceInfo from 'react-native-device-info'
/**
 * Model description here for TypeScript hints.
 */

export const HistoryModel = types.model(
  {
    code: types.maybe(types.frozen()),
    phone: types.maybe(types.string),
    service: types.maybe(types.string),
    description: types.maybe(types.string),
    status: types.maybe(types.frozen()),
    storeAddress: types.maybe(types.string),
    storeId: types.maybe(types.string),
    storeManagerId: types.maybe(types.string),
    storeName: types.maybe(types.string),
    timeCheckIn: types.maybe(types.frozen()),
    typePet: types.maybe(types.frozen()),
    userId: types.maybe(types.string),
    weight: types.maybe(types.string),
    _id: types.maybe(types.string),
    note: types.maybe(types.string),
    price: types.maybe(types.frozen()),
    branchAddress: types.maybe(types.string),
    branchName: types.maybe(types.string),
    image: types.maybe(types.string),
    branchPhone: types.maybe(types.string),
    orderId: types.maybe(types.string),
    serviceDetail: types.maybe(types.frozen()),
    isPayOnline: types.maybe(types.frozen()),
    paymentMethod: types.maybe(types.frozen()),
    shortDes: types.maybe(types.frozen()),
    bankCode: types.maybe(types.frozen()),
    bookingType: types.maybe(types.frozen()),
    priceBooking: types.maybe(types.frozen()),
    createAt: types.maybe(types.frozen()),
  },
)

export const BookingHotelModel = types.model({
  code: types.maybe(types.frozen()),
  phone: types.maybe(types.string),
  service: types.maybe(types.string),
  description: types.maybe(types.string),
  status: types.maybe(types.frozen()),
  storeAddress: types.maybe(types.string),
  storeId: types.maybe(types.string),
  storeManagerId: types.maybe(types.string),
  storeName: types.maybe(types.string),
  timeCheckIn: types.maybe(types.frozen()),
  typePet: types.maybe(types.frozen()),
  userId: types.maybe(types.string),
  weight: types.maybe(types.string),
  _id: types.maybe(types.string),
  note: types.maybe(types.string),
  price: types.maybe(types.frozen()),
  branchAddress: types.maybe(types.string),
  branchName: types.maybe(types.string),
  image: types.maybe(types.string),
  branchPhone: types.maybe(types.string),
  orderId: types.maybe(types.string),
  serviceDetail: types.maybe(types.frozen()),
  isPayOnline: types.maybe(types.frozen()),
  paymentMethod: types.maybe(types.frozen()),
  shortDes: types.maybe(types.frozen()),
  bankCode: types.maybe(types.frozen()),
  createAt: types.maybe(types.frozen()),
},
)

export const BookingClinicModel = types.model(
  {
    code: types.maybe(types.frozen()),
    phone: types.maybe(types.string),
    service: types.maybe(types.string),
    description: types.maybe(types.string),
    status: types.maybe(types.frozen()),
    storeAddress: types.maybe(types.string),
    storeId: types.maybe(types.string),
    storeManagerId: types.maybe(types.string),
    storeName: types.maybe(types.string),
    timeCheckIn: types.maybe(types.frozen()),
    typePet: types.maybe(types.frozen()),
    userId: types.maybe(types.string),
    weight: types.maybe(types.string),
    _id: types.maybe(types.string),
    note: types.maybe(types.string),
    price: types.maybe(types.frozen()),
    branchAddress: types.maybe(types.string),
    branchName: types.maybe(types.string),
    image: types.maybe(types.string),
    branchPhone: types.maybe(types.string),
    orderId: types.maybe(types.string),
    serviceDetail: types.maybe(types.frozen()),
    isPayOnline: types.maybe(types.frozen()),
    paymentMethod: types.maybe(types.frozen()),
    shortDes: types.maybe(types.frozen()),
    bankCode: types.maybe(types.frozen()),
    createAt: types.maybe(types.frozen()),
  },
)

export const AddressModel = types.model(
  {
    _id: types.maybeNull(types.frozen()),
    district: types.maybeNull(types.string),
    province: types.maybeNull(types.string),
    ward: types.maybeNull(types.string),
    street: types.maybeNull(types.string),
    name: types.maybeNull(types.string),
    phone: types.maybeNull(types.string),
    address: types.maybeNull(types.string),
    default: types.optional(types.boolean, false),
  },
)

export const UpdateProfileModel = types.model(
  {
    _id: types.maybe(types.string),
    uid: types.maybe(types.string),
    photoUrl: types.maybe(types.string),
    email: types.maybe(types.string),
    displayName: types.maybe(types.string),
    isLocked: types.maybe(types.boolean),
    phoneNumber: types.maybe(types.string),
    password: types.maybe(types.string),
    error: types.maybe(types.boolean),
    fullName: types.maybe(types.string),
    phone: types.maybe(types.string),
    picture: types.maybe(types.string),
    avatarUser: types.maybe(types.string),
    birthday: types.maybe(types.string),
    sex: types.maybe(types.string),
    description: types.maybe(types.string),
    namePet: types.maybe(types.string),
    location: types.maybe(types.string),
    addressList: types.optional(types.array(AddressModel), []),
    branchPhone: types.maybe(types.string),
    fcmToken: types.maybeNull(types.string),
    status: types.maybe(types.frozen()),
    name: types.maybe(types.frozen()),
  },
)

export const ProfileStoreModel = types
  .model('ProfileStore')
  .props({
    _id: types.maybe(types.string),
    uid: types.maybe(types.string),
    photoUrl: types.maybe(types.string),
    email: types.maybe(types.string),
    displayName: types.maybe(types.string),
    isLocked: types.maybe(types.boolean),
    phoneNumber: types.maybe(types.string),
    password: types.maybe(types.string),
    isValidForm: types.maybe(types.boolean),
    error: types.maybe(types.boolean),
    fullName: types.maybe(types.string),
    phone: types.maybe(types.string),
    picture: types.maybe(types.string),
    avatarUser: types.maybe(types.string),
    birthday: types.maybe(types.string),
    sex: types.maybe(types.string),
    description: types.maybe(types.string),
    namePet: types.maybe(types.string),
    location: types.maybe(types.string),
    addressList: types.optional(types.array(AddressModel), []),
    typePet: types.maybe(types.string),
    bookingHistory: types.optional(types.array(types.frozen()), []),
    // bookingHotel: types.optional(types.array(types.frozen()), []),
    bookingHotel: types.optional(types.array(BookingHotelModel), []),
    bookingClinic: types.optional(types.array(BookingClinicModel), []),
    bookingProduct: types.optional(types.array(OrderProductStoreModel), []),
    bookingShowroomData: types.optional(types.array(types.frozen()), []),
    pointHistory: types.optional(types.array(types.frozen()), []),
    branchPhone: types.maybe(types.string),
    totalPage: types.maybe(types.number), // TODO: biến này sử dụng mục đích gì,
    fcmToken: types.maybeNull(types.string),
    status: types.maybe(types.frozen()),
    point: types.maybe(types.number),
    isPayOnline: types.maybe(types.string),
    paymentMethod: types.maybe(types.string),
    reloadStatus: types.maybe(types.boolean),
    reloadData: types.maybe(types.boolean),
    refreshAddress: types.maybe(types.boolean),
    name: types.maybe(types.frozen()),
  })
  .extend(withEnvironment)
  .views((self) => ({
    checkInvalid: function() {
      self.isValidForm = self.displayName !== undefined && self.email !== undefined
      __DEV__ && console.log('self.isValidForm', self.isValidForm)
    },
    getIsValidForm: function() {
      return !self.isValidForm
    },
  }))

  .actions((self) => ({
    getProfile: flow(function * () {
      __DEV__ && console.log('get Profile action')
      const result: any = yield self.environment.api.getProfileInfo()
      if (result && result.data && result.data.token) {
        const token = result.data.token
        yield saveToken(token)
        const user = result.data.user
        if (user) {
          if (user.addressList && user.addressList?.length) {
            const dataAddress: typeof AddressModel[] = user.addressList.map(item => {
              return {
                ...item
              }
            })
            self.addressList = cast(dataAddress)
          }
          // save to db
          self.fullName = cast(user.fullName || user.phone || '')
          self.phone = cast(user.phone)
          self.picture = cast(user.picture)
          self.avatarUser = cast(user.picture)
          self.email = cast(user.email)
          self.birthday = cast(baseTimeFormat(user.birthday))
          self.point = cast(user.point)
          self.password = cast(user.password)
          self.sex = cast(user.sex)
          self.description = cast(user.description)
          self.namePet = cast(user.namePet)
          self.typePet = cast(user.typePet)
          self._id = cast(user._id)
        }
      }
      return result
    }),
  }))

  .actions((self) => ({
    updateProfile: flow(function * (body: Instance<typeof UpdateProfileModel>) {
      if (body.birthday === 'Invalid date') {
        body.birthday = '01/01/1900'
      }
      const result: any = yield self.environment.api.updateProfile(body)
      if (result && result.data && result.data.data && result.data.data.token) {
        const token = result.data.data.token
        yield saveToken(token)
        self.getProfile()
      }
      return result
    }),
    uploadImage: flow(function * (url, files) {
      const result: any = yield self.environment.api.uploadImages(url, files)
      __DEV__ && console.log('uploadImage OK', result)
      if (result && result.data.picture) {
        self.avatarUser = cast(result.data.picture)
      } else {
        __DEV__ && console.log(result)
      }
    }),
    updateTokenNotification: flow(function * (token = null) {
      const fcmTk = token || self.fcmToken
      if (fcmTk) {
        __DEV__ && console.log('call api saveTokenFirebase')
        self.fcmToken = cast(fcmTk)
        const body = {
          token: token,
          os: DeviceInfo.getSystemName(), // Lấy hệ điều hành (ví dụ: "iOS", "Android")
          version: DeviceInfo.getSystemVersion(), // Lấy phiên bản hệ điều hành (ví dụ: "14.4")
          model: DeviceInfo.getModel(), // Lấy mẫu thiết bị (ví dụ: "iPhone 12")
          manufacturer: DeviceInfo.getManufacturerSync(), // Lấy nhà sản xuất (ví dụ: "Apple")
        }
        return yield self.environment.api.saveTokenFirebase(body)
      } else {
        __DEV__ && console.log('tokenNotification local is null')
      }
    }),
  }))

  .actions((self) => ({
    getBookingHistory: flow(function * (page, isLoadMore) {
      const result: any = yield self.environment.api.getBookingHistory(page)
      if (result && result.data) {
        const data = result.data
        const dataBookingHistory: typeof HistoryModel[] = data.lichsu.map(item => {
          return {
            ...item
          }
        })
        if (isLoadMore) {
          const tempArray = [...self.bookingHistory, ...dataBookingHistory]
          _.uniqBy(tempArray, '_id')
          self.bookingHistory = cast(tempArray)
        } else {
          self.bookingHistory = cast(dataBookingHistory)
        }
        self.totalPage = cast(data.totalPage || 0)
      } else {
        __DEV__ && console.log('result.data', result.data)
      }
    }),
  }))

  .actions((self) => ({
    getBookingHotel: flow(function * (page, isLoadMore) {
      const result: any = yield self.environment.api.getBookingHotel(page)
      if (result && result.data) {
        const data = result.data
        const dataBookingHotel = data.lichsu.map(item => {
          return {
            ...item
          }
        })
        if (isLoadMore) {
          const tempArray = [...self.bookingHotel, ...dataBookingHotel]
          _.uniqBy(tempArray, '_id')
          self.bookingHotel = cast(tempArray)
        } else {
          self.bookingHotel = cast(dataBookingHotel)
        }
        self.totalPage = cast(data.totalPage || 0)
      } else {
        __DEV__ && console.log('result.data', result.data)
      }
    }),
  }))
  .actions((self) => ({
    getBookingClinic: flow(function * (page, isLoadMore) {
      const result: any = yield self.environment.api.getBookingClinic(page)
      if (result && result.data) {
        const data = result.data
        const dataBookingClinic: typeof BookingClinicModel[] = data.lichsu.map(item => {
          return {
            ...item
          }
        })
        if (isLoadMore) {
          const tempArray = [...self.bookingClinic, ...dataBookingClinic]
          _.uniqBy(tempArray, '_id')
          self.bookingClinic = cast(tempArray)
        } else {
          self.bookingClinic = cast(dataBookingClinic)
        }
        self.totalPage = cast(data.totalPage || 0)
      } else {
        __DEV__ && console.log('result.data', result.data)
      }
    }),
    getBookingShowRoom: flow(function * (page, isLoadMore) {
      const result: any = yield self.environment.api.getBookingClassification(page)
      if (result && result?.data?.data) {
        const data = result.data.data
        const dataArr = data.lichsu.map(item => {
          return {
            ...item
          }
        })
        if (isLoadMore) {
          const tempArray = [...self.bookingShowroomData, ...dataArr]
          _.uniqBy(tempArray, '_id')
          self.bookingShowroomData = cast(tempArray)
        } else {
          self.bookingShowroomData = cast(dataArr)
        }
        self.totalPage = cast(data.totalPage || 0)
      } else {
        __DEV__ && console.log('result.data', result.data)
      }
    }),
  }))

  .actions((self) => ({
    getBookingProduct: flow(function * (page, isLoadMore) {
      const result: any = yield self.environment.api.getBookingProduct(page)
      if (result && result.data) {
        const data = result.data
        const dataBookingProduct: typeof OrderProductStoreModel[] = data.lichsu.map(item => {
          return {
            ...item
          }
        })
        if (isLoadMore) {
          const tempArray = [...self.bookingClinic, ...dataBookingProduct]
          _.uniqBy(tempArray, '_id')
          self.bookingProduct = cast(tempArray)
        } else {
          self.bookingProduct = cast(dataBookingProduct)
        }
        self.totalPage = cast(data.totalPage || 0)
      } else {
        __DEV__ && console.log('result.data', result.data)
      }
    }),
  }))
  .actions((self) => ({
    getUserPointHistory: flow(function * () {
      const result: any = yield self.environment.api.getUserPointHistory()
      if (result && result.data) {
        const dataArr = result.data.data.records.map(item => {
          return {
            ...item
          }
        })
        self.pointHistory = cast(dataArr)
      } else {
        __DEV__ && console.log('result.data', result.data)
      }
    }),
  }))

  .actions((self) => ({

  }))

  .views((self) => ({
    handlerError: function(errorCode: any) {
      let errorMsg = ''
      switch (errorCode) {
        case 'auth/invalid-email':
          errorMsg = translate('errors.invalidEmail')
          break
        case 'auth/user-disabled':
          errorMsg = translate('errors.userDisabled')
          break
        case 'auth/email-already-in-use':
          errorMsg = translate('errors.emailAlreadyInUse')
          break
        case 'auth/user-not-found':
          errorMsg = translate('errors.userNotFound')
          break
        case 'auth/wrong-password':
          errorMsg = translate('errors.wrongPassword')
          break
        case 'auth/no-current-user':
          errorMsg = translate('errors.noCurrentUser')
          break
        case 'auth/network-request-failed':
          errorMsg = translate('errors.noNetworkConnection')
          break
        case 'auth/too-many-requests':
          errorMsg = translate('errors.tooManyRequests')
          break
        default:
          errorMsg = errorCode
          break
      }
      return errorMsg
    },
    isSignedIn: function() {
      return !!self._id
    }
  }))
  .actions((self) => ({
    setReLoadStatus: function(value) {
      self.reloadStatus = value
    },
    setDisplay: function(value) {
      self.displayName = value
    },
    setEmail: function(value) {
      self.email = value
    },
    setBookingSpa: function(value) {
      self.bookingHistory = value
    },
    setBookingHotel: function(value) {
      self.bookingHotel = value
    },
    setBookingClinic: function(value) {
      self.bookingClinic = value
    },
    setBookingProduct: function(value) {
      self.bookingProduct = value
    },
    setPassword: function(value) {
      self.password = value
    },
    setBirthDay: function(value) {
      self.birthday = value
    },
    setPicture: function(value) {
      self.picture = value
    },
    setAvatarUser: function(value) {
      self.avatarUser = value
    },
    setFullName: function(value) {
      self.fullName = value
    },
    setPhone: function(value) {
      self.phone = value
    },
    setListAddress: function(address) {
      self.addressList.push(address)
    },
    setReloadData: function(value) {
      self.reloadData = value
    },
    setRefreshAddress: function(value) {
      self.refreshAddress = value
    },
    updateAddressOfList: function(address, index = -1) {
      if (address.default) {
        // reset default
        self.addressList.forEach(item => {
          item.default = false
        })
      }
      // Find index of specific object using findIndex method.
      const objIndex = index > -1 ? index : self.addressList.findIndex(obj => obj._id === address._id)
      // Update object's name property.
      self.addressList[objIndex].district = address.district
      self.addressList[objIndex].province = address.province
      self.addressList[objIndex].ward = address.ward
      self.addressList[objIndex].street = address.street
      self.addressList[objIndex].name = address.name
      self.addressList[objIndex].phone = address.phone
      self.addressList[objIndex].address = address.address
      self.addressList[objIndex].default = address.default
    },
    deleteAddressOfList: function(index) {
      // const index = self.addressList.findIndex(x => x._id === id)
      const cloneArray = [...self.addressList]
      cloneArray.splice(index, 1)
      self.addressList = cast([...cloneArray])
    },
    clearFields: function() {
      self.fullName = ''
      self.phone = ''
      self.picture = ''
      self.photoUrl = ''
      self.email = ''
      self.birthday = ''
      self.password = ''
      self.fullName = ''
      self.sex = ''
      self.description = ''
      self.namePet = ''
      self.typePet = ''
      self.location = ''
      self._id = cast('')
      self.addressList = cast([])
    },
    setPhoneNumber: function (phone: string) {
      // validate đủ 10 số
      if (phone.length && _.startsWith(phone, '+840')) {
        const search = '+840'
        const replaceWith = '+84'
        self.phoneNumber = phone.replace(search, replaceWith)
      } else {
        self.phoneNumber = phone
      }
      // self.checkInvalid()
    },
  }))
  .actions(self => {
    let initialState = {}
    return {
      afterCreate: () => {
        initialState = getSnapshot(self)
      },
      reset: () => {
        applySnapshot(self, {})
      },
    }
  })

/**
 * Un-comment the following to omit model attributes from your snapshots (and from async storage).
 * Useful for sensitive data like passwords, or transitive state like whether a modal is open.

 * Note that you'll need to import `omit` from ramda, which is already included in the project!
 *  .postProcessSnapshot(omit(["password", "socialSecurityNumber", "creditCardNumber"]))
 */

type ProfileStoreType = Instance<typeof ProfileStoreModel>

export interface ProfileStore extends ProfileStoreType {
}

type ProfileStoreSnapshotType = SnapshotOut<typeof ProfileStoreModel>

export interface ProfileStoreSnapshot extends ProfileStoreSnapshotType {
}
