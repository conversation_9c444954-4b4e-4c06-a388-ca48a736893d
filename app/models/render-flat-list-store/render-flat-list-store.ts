import { cast, flow, Instance, SnapshotOut, types } from 'mobx-state-tree'
import { withEnvironment } from '../extensions'

const Uudai = types.model({
  name: types.maybeNull(types.string),
  address: types.maybeNull(types.string),
  image: types.maybeNull(types.string),
  storeId: types.maybeNull(types.string),
  coupon: 'Đang HOT'
})

export const RenderFlatListStoreModel = types
  .model('RenderFlatListStore')
  .props({
    khachsan: types.optional(types.array(types.frozen()), []),
    spa: types.optional(types.array(types.frozen()), []),
    phongkham: types.optional(types.array(types.frozen()), []),
    uudai: types.optional(types.array(Uudai), [])
  })
  .extend(withEnvironment)
  .views(self => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions(self => ({
    getService: flow(function * (type, page, search) {
      // self.clearData()
      if (type === 1) {
        self.phongkham = cast([])
      }
      if (type === 2) {
        self.khachsan = cast([])
      }
      if (type === 3) {
        self.spa = cast([])
      }
      const result: any = yield self.environment.api.searchServices(type, page, search)
      if (result.kind === 'ok') {
        const arrResult: any = yield Promise.all(result.data.data.result.map(async item => {
          const result2: any = await self.environment.api.getServiceSpaByStoreId(item._id)
          // __DEV__ && console.log('get branches store', result2)
          if (result2.kind === 'ok') {
            const data2 = result2.data
            const branchesStore = data2?.branchStore || []
            return {
              ...item,
              address: branchesStore.length > 0 ? branchesStore[0].address : '',
              image: item.pictures[0],
              id: item._id,
              storeId: item?._id,
              serviceName: item.name,
              totalRate: item.totalRate || 0,
              calculated: item?.dist?.calculated
            }
          }
          return {
            ...item,
            image: item.pictures[0],
            id: item._id,
            serviceName: item.name,
            storeId: item?._id,
            totalRate: item.totalRate || 0,
            calculated: item?.dist?.calculated
          }
        }))

        if (type === 1) {
          self.phongkham = cast(arrResult || [])
        }
        if (type === 2) {
          self.khachsan = cast(arrResult || [])
        }
        if (type === 3) {
          self.spa = cast(arrResult || [])
        }
        // if (type === 3) {
        //   const uudai: typeof Spa[] = result.data.services.map(item => {
        //     return { image: item.pictures[0], name: item.name, address: item.address, storeId: item._id }
        //   })
        //   self.uudai = cast(uudai)
        // }
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    clearData: function() {
      self.uudai = cast([])
      self.spa = cast([])
      self.khachsan = cast([])
      self.phongkham = cast([])
    }
  })) // eslint-disable-line @typescript-eslint/no-unused-vars

/**
 * Un-comment the following to omit model attributes from your snapshots (and from async storage).
 * Useful for sensitive data like passwords, or transitive state like whether a modal is open.

 * Note that you'll need to import `omit` from ramda, which is already included in the project!
 *  .postProcessSnapshot(omit(["password", "socialSecurityNumber", "creditCardNumber"]))
 */

type RenderFlatListStoreType = Instance<typeof RenderFlatListStoreModel>

export interface RenderFlatListStore extends RenderFlatListStoreType {
}

type RenderFlatListStoreSnapshotType = SnapshotOut<typeof RenderFlatListStoreModel>

export interface RenderFlatListStoreSnapshot extends RenderFlatListStoreSnapshotType {
}
