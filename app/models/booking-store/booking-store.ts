import { flow, Instance, SnapshotOut, types, cast, getSnapshot, applySnapshot } from 'mobx-state-tree'
import { withEnvironment } from '../extensions'

const DataService = types.model({
  _id: types.maybeNull(types.string),
  name: types.maybeNull(types.string),
  description: types.maybeNull(types.string),
  image: types.maybeNull(types.string),
  classify: types.optional(types.array(types.frozen()), []),
  shortDes: types.maybeNull(types.string),
  price: types.maybeNull(types.frozen()),
  branchId: types.maybeNull(types.frozen()),
})
const DataServiceClinic = types.model({
  _id: types.maybeNull(types.string),
  name: types.maybeNull(types.string),
  description: types.maybeNull(types.string),
  image: types.maybeNull(types.string),
  classify: types.optional(types.array(types.frozen()), []),
  shortDes: types.maybeNull(types.string),
  price: types.maybeNull(types.frozen()),
})
const DataRoom = types.model({
  _id: types.maybeNull(types.string),
  name: types.maybeNull(types.string),
  description: types.maybeNull(types.string),
  image: types.maybeNull(types.string),
  classify: types.optional(types.array(types.frozen()), []),
  shortDes: types.maybeNull(types.string),
  price: types.maybeNull(types.frozen()),
})

const BranchData = types.model({
  _id: types.maybeNull(types.string),
  name: types.maybeNull(types.string),
  address: types.maybeNull(types.string),
  storeId: types.maybeNull(types.string),
  lat: types.maybeNull(types.string),
  lng: types.maybeNull(types.string),
  distance: types.maybeNull(types.frozen()),
  phone: types.maybeNull(types.frozen()),
  hotline: types.maybeNull(types.frozen()),
})

export const BookingStoreModel = types
  .model('BookingStore')
  .props({
    dataServiceOfSpa: types.optional(types.array(DataService), []),
    dataServiceOfClinic: types.optional(types.array(DataServiceClinic), []),
    dataRoomOfHotel: types.optional(types.array(DataRoom), []),
    dataShowrooms: types.optional(types.array(DataRoom), []),
    dataGas: types.optional(types.array(DataRoom), []),
    totalService: types.maybeNull(types.frozen()),
    totalRooms: types.maybeNull(types.frozen()),
    totalServiceClinic: types.maybeNull(types.frozen()),
    totalShowrooms: types.maybeNull(types.frozen()),
    totalGas: types.maybeNull(types.frozen()),
    betweenTime: types.maybeNull(types.frozen()),
    branchStore: types.optional(types.array(BranchData), []),
    valueCoupon: types.maybeNull(types.number),
    valueCouponG: types.maybeNull(types.number),
    generalCoupon: types.maybeNull(types.string),
    coupon: types.maybeNull(types.string),
    shopDiscount: types.maybeNull(types.number),
    discountType: types.maybeNull(types.number),
    storeIdCoupon: types.maybeNull(types.string),
    typeCode: types.maybeNull(types.number),
    orderId: types.maybeNull(types.string),
    bookingStatus: types.maybe(types.boolean),
    cart: types.optional(types.array(types.frozen()), []),
  })
  .extend(withEnvironment)
  .views(self => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions(self => ({
    getServiceOfSpa: flow(function * (storeId) {
      const result: any = yield self.environment.api.getServiceSpaByStoreId(storeId)
      if (result.kind === 'ok') {
        const data = result.data
        const services: typeof DataService[] = data.spa.map(item => {
          return {
            ...item,
            image: item.thumbail,
            price: item.price ? item.price : 0,
          }
        })
        const branchStore = data?.branchStore || []
        const branchList: () => Promise<any> = async () => {
          return Promise.all(branchStore.map(async item => {
            // const distanceCal = 0
            if (item && item.lat && item.lng) {
              // distanceCal = await calculateDistance({ lat: item.lat, lng: item.lng })
            }
            return {
              ...item,
              phone: item?.phone || '',
              // distance: distanceCal
            }
          }))
        }
        const branch = yield branchList()
        self.branchStore = cast(branch.length ? branch : [])
        self.dataServiceOfSpa = cast(services)
        self.totalService = cast(data.totalService.totalService)
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    // lấy danh sách phòng khách sạn
    getRoomOfHotel: flow(function * (storeId) {
      const result: any = yield self.environment.api.getRoomHotelByStoreId(storeId)
      if (result.kind === 'ok') {
        const data = result.data
        const rooms: typeof DataRoom[] = data.rooms.map(item => {
          return {
            ...item,
            image: item.thumbail,
            price: item.price ? item.price : 0,
          }
        })
        self.dataRoomOfHotel = cast(rooms)
        self.totalRooms = cast(data.totalRooms)
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    // lấy danh sách dịch vụ Garage
    getServiceOfClinic: flow(function * (storeId) {
      const result: any = yield self.environment.api.getServiceOfClinicByStoreId(storeId)
      if (result.kind === 'ok') {
        const data = result.data
        const rooms: typeof DataServiceClinic[] = data.clinics.map(item => {
          return {
            ...item,
            image: item.thumbail,
            price: item.price ? item.price : 0,
          }
        })
        self.dataServiceOfClinic = cast(rooms)
        self.totalServiceClinic = cast(data.totalClinics) // TODO dùng để hiện thị tổng số dịch vụ mà store cung cấp
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    getClassificationByType: flow(function * (storeId, type) {
      const result: any = yield self.environment.api.getClassificationDataByType(storeId, type)
      if (result.kind === 'ok') {
        const data = result.data.data
        const arrayRs = data.data.map(item => {
          return {
            ...item,
            image: item.thumbail,
            price: item.price ? item.price : 0,
          }
        })
        if (type == 'show-room') {
          self.dataShowrooms = cast(arrayRs)
          self.totalShowrooms = cast(data.total)
        }
        if (type == 'gas') {
          self.dataGas = cast(arrayRs)
          self.totalGas = cast(data.total)
        }
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    bookingRoomHotel: flow(function * (storeId, bookingType) {
      const result: any = yield self.environment.api.bookingRoomHotel(storeId, bookingType)
      if (result.kind === 'ok') {
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),

    handleCalculate: flow(function * (body) {
      const result: any = yield self.environment.api.handleCalculate(body)
      if (result.kind === 'ok') {
        __DEV__ && console.log('handleCalculate', result)
        const data = result?.data?.data
        if (data?.coupon) {
          self.valueCoupon = cast(data.coupon.value)
          self.coupon = cast(data.coupon.code)
          self.typeCode = cast(data.coupon.typeCode)
          self.discountType = cast(data.coupon.type)
        }
        return result
      } else {
        return null
        __DEV__ && console.log(result.kind)
      }
    }),
    bookingService: flow(function * (spaModel) {
      // const dataBookingSpa = SpaModel.create(spaModel)
      const result: any = yield self.environment.api.postBooking(spaModel)
      if (result.kind === 'ok') {
        return result
      } else {
        __DEV__ && console.log(result.kind)
        return result
      }
    }),
    bookingShowroom: flow(function * (data) {
      // const dataBookingSpa = SpaModel.create(spaModel)
      const result: any = yield self.environment.api.bookingShowroom(data)
      if (result.kind === 'ok') {
        return result
      } else {
        __DEV__ && console.log(result.kind)
        return result
      }
    }),
    checkCoupon: flow(function * (code, subTotal, feeShip, storeId) {
      const result: any = yield self.environment.api.checkCouponService(code, subTotal, feeShip, storeId)
      __DEV__ && console.log('result STORE', result)
      if (result && result.data && result.data.data) {
        // const data = result?.data?.data
        // if (data?.coupon && storeId) {
        //   // self.valueCoupon = cast(data.coupon.value)
        //   self.coupon = cast(data.coupon.code)
        //   self.typeCode = cast(data.coupon.typeCode)
        //   self.discountType = cast(data.coupon.type)
        //   self.shopDiscount = cast(data.coupon.discount)
        // }
        return result
      } else {
        __DEV__ && console.log(result.kind)
      }
    })
  }))
  .actions((self) => ({
    setCoupon: function(value) {
      self.coupon = value
    },
    setValueCoupon: function(value) {
      self.valueCoupon = value
    },
    setGeneralCoupon: function(value) {
      self.generalCoupon = value
    },
    setValueCouponG: function(value) {
      self.valueCouponG = value
    },
    setBookingStatus: function(value) {
      self.bookingStatus = value
    },
    setOrderId: function(value) {
      self.orderId = value
    },
    clearFields: function() {
      self.coupon = ''
      self.valueCoupon = 0
    },
    clearCart: function() {
      self.cart = cast([])
    },
    addCart: function(data) {
      if (self.cart.length) {
        const tempArray = [...self.cart, data]
        // _.uniqBy(tempArray, '_id')
        self.cart = cast(tempArray)
      } else {
        self.cart = cast([data])
      }
    },
    deleteCartOfList: function(id) {
      const index = self.cart.findIndex(x => x._id === id)
      const cloneArray = [...self.cart]
      cloneArray.splice(index, 1)
      self.cart = cast([...cloneArray])
    },
  })) // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions(self => {
    let initialState = {}
    return {
      afterCreate: () => {
        initialState = getSnapshot(self)
      },
      reset: () => {
        applySnapshot(self, {})
      },
    }
  })

/**
 * Un-comment the following to omit model attributes from your snapshots (and from async storage).
 * Useful for sensitive data like passwords, or transitive state like whether a modal is open.

 * Note that you'll need to import `omit` from ramda, which is already included in the project!
 *  .postProcessSnapshot(omit(["password", "socialSecurityNumber", "creditCardNumber"]))
 */

type BookingStoreType = Instance<typeof BookingStoreModel>

export interface BookingStore extends BookingStoreType {
}

type BookingStoreSnapshotType = SnapshotOut<typeof BookingStoreModel>

export interface BookingStoreSnapshot extends BookingStoreSnapshotType {
}
