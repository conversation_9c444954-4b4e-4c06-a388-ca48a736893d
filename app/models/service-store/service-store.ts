import { flow, Instance, SnapshotOut, types, cast, getSnapshot, applySnapshot } from 'mobx-state-tree'
import { withEnvironment } from '../extensions'
import { Comment } from '../comment'
import { calculateDistance } from '@app/services'
import _ from 'lodash'

const { IMAGE_URL } = require('../../config/env')
/**
 * Model description here for TypeScript hints.
 */

const Uudai = types.model({
  name: types.maybeNull(types.string),
  address: types.maybeNull(types.string),
  image: types.maybeNull(types.string),
  storeId: types.maybeNull(types.string),
  coupon: 'Giảm giá 15%',
  shortDes: types.maybeNull(types.string),
  price: types.maybeNull(types.frozen()),
  description: types.maybeNull(types.string),
  classify: types.optional(types.array(types.frozen()), []),
})

// https://appmypet.com/user/api/get-comment.html?page=1&storeId=5ee186f31f92f6523cab2004&output=json
const BannerStore = types.model({
  image: types.maybeNull(types.string),
})
const DataComment = types.model({
  _id: types.maybeNull(types.string),
  name: types.maybeNull(types.string),
  content: types.maybeNull(types.string),
  image: types.maybeNull(types.string),
  rate: types.maybeNull(types.frozen()),
  modifyAt: types.maybeNull(types.frozen()),
})
export const ServiceStoreModel = types
  .model('ServiceStore')
  .props({
    uudai: types.optional(types.array(Uudai), []),
    servicesFeature: types.optional(types.array(types.frozen()), []),
    storeId: types.maybe(types.string),
    userId: types.maybe(types.string),
    isShowModalComment: types.optional(types.string, 'hide'),
    bannerStore: types.optional(types.array(BannerStore), []),
    dataDetail: types.maybe(types.string),
    name: types.maybe(types.string),
    adress: types.maybe(types.string),
    topComment: types.optional(types.array(DataComment), []),
    saleServices: types.optional(types.array(types.string), []),
    businessTypes: types.optional(types.array(types.string), []),
    rateTotal1: types.optional(types.number, 0),
    rateTotal2: types.optional(types.number, 0),
    rateTotal3: types.optional(types.number, 0),
    rateTotal4: types.optional(types.number, 0),
    rateTotal5: types.optional(types.number, 0),
    totalComment: types.optional(types.number, 0),
    rateTotalValue: types.maybe(types.frozen()),
    totalPage: types.maybe(types.frozen()),
    getRatingByUserId: types.maybe(types.frozen()),
    shortDes: types.maybeNull(types.string),
    userStoreId: types.maybeNull(types.string),
    isLastestComments: types.maybe(types.boolean),
    betweenTime: types.maybe(types.frozen()),
    type: types.maybe(types.frozen()),
    typeBooking: types.maybeNull(types.frozen()),
    timeOpen: types.maybeNull(types.string),
    timeClose: types.maybeNull(types.string),
    userDetail: types.maybeNull(types.frozen())
  }).extend(withEnvironment)
  .views(self => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions(self => ({
    getStoreById: flow(function * (storeId) {
      const result: any = yield self.environment.api.getStoreById(storeId)
      __DEV__ && console.log('get detail', result)
      const data = result.data.data
      if (result.kind === 'ok') {
        const images: typeof BannerStore[] = (data.spa?.pictures || []).map(item => ({ image: item }))
        const infoSpa = data?.spa
        self.bannerStore = cast(images)
        self.dataDetail = infoSpa?.content || ''
        self.name = infoSpa?.name || ''
        self.adress = infoSpa?.address || ''
        self.userStoreId = infoSpa?.userId || ''
        self.betweenTime = infoSpa?.betweenTime || ''
        self.type = infoSpa?.type || ''
        self.timeOpen = infoSpa?.timeOpen || ''
        self.timeClose = infoSpa?.timeClose || ''
        self.userDetail = data?.userDetail || null
        self.saleServices = infoSpa?.saleServices || []
        self.businessTypes = infoSpa?.businessTypes || []
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    getLastestComments: flow(function * (storeId, userId) {
      const result: any = yield self.environment.api.getCommentStoreById(storeId, 1, userId)
      if (result.kind === 'ok') {
        const data = result.data.commentData ? result.data.commentData : ''
        const dataTotalrate = result.data.totalRate
        const dataRate = result.data
        const datas: Comment[] = data.filter(x => x.userId !== null).map(item => {
          const name = item.userId && item.userId.fullName ? item.userId.fullName : (item.userId && item.userId.phone ? item.userId.phone : '')
          return {
            _id: item._id,
            name: name,
            content: item.content,
            image: item.userId?.picture || '',
            rate: item.rate,
            modifyAt: item.modifyAt,
          }
        },
        )
        self.topComment = cast(datas)
        self.storeId = result.data.storeId
        self.userId = !data.userId ? data.userId : ''
        self.rateTotal1 = dataTotalrate.rateTotal1 || 0
        self.rateTotal2 = dataTotalrate.rateTotal2 || 0
        self.rateTotal3 = dataTotalrate.rateTotal3 || 0
        self.rateTotal4 = dataTotalrate.rateTotal4 || 0
        self.rateTotal5 = dataTotalrate.rateTotal5 || 0
        self.totalComment = dataTotalrate.totalComment || 0
        self.getRatingByUserId = result.data && result.data.getRatingByUserId.length ? dataRate.getRatingByUserId[dataRate.getRatingByUserId.length - 1].rate : 0
        self.rateTotalValue = isNaN(dataTotalrate.rateTotalValue) ? 0 : dataTotalrate.rateTotalValue
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
  })) // eslint-disable-line @typescript-eslint/no-unused-vars

  .actions(self => ({
    getFeatured: flow(function * (storeId) {
      const result: any = yield self.environment.api.getFeatured(storeId)
      __DEV__ && console.log('resultssss', result)
      if (result && result.data && result.data.data && result.data.data.spa) {
        const uudai: typeof Uudai[] = result.data.data.spa.map(item => {
          return {
            image: item.thumbail,
            name: item.name,
            address: item.address,
            storeId: item.userId,
            shortDes: item.shortDes,
            price: item.price ? item.price : 0,
            classify: item.classify,
            description: item.description,
          }
        })
        self.uudai = cast(uudai)
      } else {
        self.uudai = cast([])
      }
      return result
    }),
    getServiceFeature: flow(function * (categoryId, storeId) {
      const result: any = yield self.environment.api.getServiceFeature(categoryId, storeId)
      __DEV__ && console.log('getServiceFeature', result)
      if (result && result.data && result.data.data && result.data.data.spa) {
        const res = yield Promise.all(result.data.data.spa.map(async item => {
          const result2: any = await self.environment.api.getServiceSpaByStoreId(item.storeId)
          // __DEV__ && console.log('get branches store', result2)
          if (result2.kind === 'ok') {
            const data2 = result2.data
            const branchesStore = data2?.branchStore || []
            const distanceCal = await calculateDistance({
              lat: branchesStore[0].location.coordinates[1],
              lng: branchesStore[0].location.coordinates[0]
            })
            return {
              image: item.thumbail,
              name: item.name,
              address: item.address,
              storeId: item.storeId,
              storeName: item.storeName,
              shortDes: item.shortDes,
              price: item.price ? item.price : 0,
              classify: item.classify,
              description: item.description,
              distance: distanceCal
            }
          }
          return {
            image: item.thumbail,
            name: item.name,
            address: item.address,
            storeId: item.storeId,
            storeName: item.storeName,
            shortDes: item.shortDes,
            price: item.price ? item.price : 0,
            classify: item.classify,
            description: item.description,
          }
        }))
        // const arr = result.data.data.spa.map(item => {
        //   return {
        //     image: item.thumbail,
        //     name: item.name,
        //     address: item.address,
        //     storeId: item.userId,
        //     storeName: item.storeName,
        //     shortDes: item.shortDes,
        //     price: item.price ? item.price : 0,
        //     classify: item.classify,
        //     description: item.description,
        //   }
        // })
        self.servicesFeature = cast(_.orderBy(res, ['distance'], ['asc']))
      } else {
        self.servicesFeature = cast([])
      }
      return result
    }),
    setIsLastestComments: function(value) {
      self.isLastestComments = value
    },
    setTypeBooking: function(value) {
      self.typeBooking = value
    },
    clearData: function() {
      self.uudai = cast([])
      self.bannerStore = cast([])
      self.topComment = cast([])
      self.name = ''
      self.adress = ''
    }
  }))
  .actions(self => {
    __DEV__ && console.log('reset ServiceStoreModel')
    let initialState = {}
    return {
      afterCreate: () => {
        initialState = getSnapshot(self)
      },
      reset: () => {
        applySnapshot(self, {})
      },
    }
  })

type ServiceStoreType = Instance<typeof ServiceStoreModel>

export interface ServiceStore extends ServiceStoreType {
}

type ServiceStoreSnapshotType = SnapshotOut<typeof ServiceStoreModel>

export interface ServiceStoreSnapshot extends ServiceStoreSnapshotType {
}
