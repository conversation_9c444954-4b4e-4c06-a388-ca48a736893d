import { flow, Instance, SnapshotOut, types, cast } from 'mobx-state-tree'
import { CommentModel } from '../comment'
import { withEnvironment } from '../extensions'

// const { IMAGE_URL } = require('../../config/env')
/**
 * Model description here for TypeScript hints.
 */

const DataComment = types.model({
  id: types.maybe(types.string),
  name: types.maybe(types.string),
  content: types.maybe(types.string),
  userIdComment: types.maybe(types.string),
  image: types.maybe(types.string),
  rate: types.maybe(types.frozen()),
  modifyAt: types.maybe(types.frozen()),
})
export const CommentStoreModel = types
  .model('CommentStore', {
    storeId: types.maybe(types.string),
    userId: types.maybe(types.string),
    isShowModalComment: types.optional(types.string, 'hide'),
    dataDetail: types.maybe(types.string),
    name: types.maybe(types.string),
    adress: types.maybe(types.string),
    dataComment: types.optional(types.array(DataComment), []),
    rateTotal1: types.maybe(types.number),
    rateTotal2: types.maybe(types.number),
    rateTotal3: types.maybe(types.number),
    rateTotal4: types.maybe(types.number),
    rateTotal5: types.maybe(types.number),
    totalComment: types.maybe(types.number),
    rateTotalValue: types.maybe(types.frozen()),
    totalPage: types.maybe(types.frozen()),
    getRatingByUserId: types.maybe(types.frozen()),
  })
  .extend(withEnvironment)
  .views(self => ({
    getListComments: function() {
      return self.dataComment.filter(x => x)
    },
  })) // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions(self => ({
    createComment: flow(function * (commentModel) {
      const commentCreate = CommentModel.create(commentModel)
      const result: any = yield self.environment.api.postComment(commentCreate)
      if (result.kind === 'ok') {
        __DEV__ && console.log('insert comment ok')
        return result
      } else {
        __DEV__ && console.log(result.kind)
        return result
      }
    }),
    createCommentProduct: flow(function * (commentCreate) {
      const result: any = yield self.environment.api.postCommentProduct(commentCreate)
      if (result.kind === 'ok') {
        __DEV__ && console.log('insert comment ok')
        return result
      } else {
        __DEV__ && console.log(result.kind)
        return result
      }
    }),
    editComment: flow(function * (id, commentModel) {
      const dataCommentEdit = CommentModel.create(commentModel)
      const result: any = yield self.environment.api.editComment(id, dataCommentEdit)
      if (result.kind === 'ok') {
        __DEV__ && console.log('edit comment ok')
        return result
      } else {
        __DEV__ && console.log(result.kind)
        return result
      }
    }),
    deleteComment: flow(function * (id) {
      const result: any = yield self.environment.api.deleteComment(id)
      if (result.kind === 'ok') {
        __DEV__ && console.log('delete comment complete')
        return result
      } else {
        __DEV__ && console.log(result.kind)
        return result
      }
    }),
    getMoreCommentsListById: flow(function * (storeId, page, isLoadMore) {
      const result: any = yield self.environment.api.getAllCommentOfStore(storeId, page)
      if (result.kind === 'ok') {
        const data = result.data.commentData
        const totalPage = result.data.totalPage
        const datas = data.filter(x => x.userId !== null).map(item => {
          const name = item.userId && item.userId.fullName ? item.userId.fullName : (item.userId && item.userId.phone ? item.userId.phone : '')
          return {
            id: item._id,
            name: name,
            content: item.content,
            image: item.userId.picture,
            rate: item.rate,
            modifyAt: item.modifyAt,
            userIdComment: item.userId._id
          }
        },
        )
        if (isLoadMore) {
          self.dataComment = cast([...self.dataComment, ...datas])
        } else {
          self.dataComment.replace(datas)
        }
        self.storeId = result.data.storeId
        self.userId = data.userId
        self.totalPage = totalPage.totalPage
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    getCommentProductById: flow(function * (id) {
      const result: any = yield self.environment.api.getCommentProduct(id, 1)
      if (result?.data?.getRatingByUserId) {
        const dataCommentById = result?.data?.getRatingByUserId
        self.getRatingByUserId = dataCommentById.length > 0 ? dataCommentById[result.data.getRatingByUserId.length - 1].rate : 0
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
  })) // eslint-disable-line @typescript-eslint/no-unused-vars

/**
 * Un-comment the following to omit model attributes from your snapshots (and from async storage).
 * Useful for sensitive data like passwords, or transitive state like whether a modal is open.

 * Note that you'll need to import `omit` from ramda, which is already included in the project!
 *  .postProcessSnapshot(omit(["password", "socialSecurityNumber", "creditCardNumber"]))
 */

type CommentStoreType = Instance<typeof CommentStoreModel>

export interface CommentStore extends CommentStoreType {
}

type CommentStoreSnapshotType = SnapshotOut<typeof CommentStoreModel>

export interface CommentStoreSnapshot extends CommentStoreSnapshotType {
}
