import { Instance, SnapshotOut, types, flow } from 'mobx-state-tree'
import { ArticleModel, ArticleSnapshot, Article } from '../article'
import firestore from '@react-native-firebase/firestore'

/**
 * Model description here for TypeScript hints.
 */
// var start: any
let start: any

export const ArticleStoreModel = types
  .model('ArticleStore')
  .props({
    articles: types.optional(types.array(ArticleModel), []),
    countData: types.maybe(types.number),
    start: types.maybe(types.frozen())
  })
  .views(self => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions(self => ({
    savePosts: (articleSnapshot: ArticleSnapshot[]) => {
      // const articleModels: Article[] = articleSnapshot.map(c => ArticleModel.create(c)) // create model instances from the plain objects
      // self.articles.replace(articleModels) // Replace the existing data with the new data
    },

  }))
  .actions(self => ({
    countArticle: flow(function * () {
      const ref = yield firestore().collection('post').get()
      if (ref.empty) {
        self.countData = 0
      } else {
        self.countData = ref.docs.length
      }
    }),

    getArticleList: flow(function * (limit: number) {
      const ref = yield firestore().collection('post').orderBy('date', 'desc').limit(limit).get()
      start = ref.docs[ref.docs.length - 1]
      if (ref.empty) {
        __DEV__ && console.log('No such document!')
      } else {
        const articleModels: Article[] = ref.docs.map(c => ArticleModel.create({
          id: c.id,
          author: c.data().author,
          title: c.data().title ? c.data().title : '',
          content: c.data().content,
          date: c.data().date.seconds.toString(),
          likes: c.data().likes,
          image: c.data().image,
        // comments: c.data().comment
        }))
        self.articles.replace(articleModels)
        self.start = start
      }
    }),

    getMoreArticleList: flow(function * (limit: number) {
      const ref = yield firestore().collection('post').orderBy('date', 'desc').startAfter(self.start).limit(limit).get()
      start = ref.docs[ref.docs.length - 1]
      if (ref.empty) {
        __DEV__ && console.log('No such document!')
      } else {
        ref.forEach(doc => {
          const rs = self.articles.filter(x => x.id === doc.id)
          if (rs.length > 0) {
            return
          }
          let articleModels: Article[] = ref.docs.map(c => ArticleModel.create({
            id: c.id,
            author: c.data().author,
            title: c.data().title,
            content: c.data().content,
            date: c.data().date.seconds.toString(),
            likes: c.data().likes,
            image: c.data().image,
            comments: c.data().comment
          })) // create model instances from the plain objects
          // const articleModels: Article[] = ref.docs.map(c => ArticleModel.create(c.data()))

          articleModels = [...self.articles, ...articleModels]
          self.articles.replace(articleModels) // Replace the existing data with the new data
          self.start = start
        })
      }
    }),
    getArticleListByUid: flow(function * (limit: number, uid: string) {
      // self.articles = []
      const ref = yield firestore().collection('post').limit(limit).where('author.uid', '==', uid).orderBy('date', 'desc').get()
      start = ref.docs[ref.docs.length - 1]
      if (ref.empty) {
        __DEV__ && console.log('No such document!')
      } else {
        const articleModels: Article[] = ref.docs.map(c => ArticleModel.create({
          id: c.id,
          author: c.data().author,
          title: c.data().title ? c.data().title : '',
          content: c.data().content,
          date: c.data().date.seconds.toString(),
          likes: c.data().likes,
          image: c.data().image,
        // comments: c.data().comment
        }))
        self.articles.replace(articleModels)
        self.start = start
      }
    }),

  }))

type ArticleStoreType = Instance<typeof ArticleStoreModel>
export interface ArticleStore extends ArticleStoreType {}
type ArticleStoreSnapshotType = SnapshotOut<typeof ArticleStoreModel>
export interface ArticleStoreSnapshot extends ArticleStoreSnapshotType {}
