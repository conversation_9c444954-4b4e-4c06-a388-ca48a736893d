import { flow, Instance, SnapshotOut, types } from 'mobx-state-tree'
import { withEnvironment } from '../extensions'
/**
 * Model description here for TypeScript hints.
 */
export const OrderProductStoreModel = types
  .model('OrderProductStore')
  .props({
    _id: types.maybeNull(types.string),
    code: types.maybe(types.frozen()),
    phone: types.maybe(types.string),
    service: types.maybe(types.string),
    description: types.maybe(types.string),
    status: types.maybe(types.frozen()),
    storeAddress: types.maybe(types.string),
    storeId: types.maybe(types.string),
    storeManagerId: types.maybe(types.string),
    storeName: types.maybe(types.string),
    timeCheckIn: types.maybe(types.frozen()),
    typePet: types.maybe(types.frozen()),
    userId: types.maybe(types.string),
    weight: types.maybe(types.string),
    note: types.maybe(types.string),
    price: types.maybe(types.frozen()),
    branchAddress: types.maybe(types.string),
    branchName: types.maybe(types.string),
    image: types.maybe(types.string),
    branchPhone: types.maybe(types.string),
    orderId: types.maybe(types.string),
    serviceDetail: types.maybe(types.frozen()),
    isPayOnline: types.maybe(types.frozen()),
    paymentMethod: types.maybe(types.frozen()),
    shortDes: types.maybe(types.frozen()),
    bankCode: types.maybe(types.frozen()),
    products: types.maybe(types.frozen()),
    name: types.maybe(types.frozen()),
    bookingType: types.maybe(types.number),
    location: types.maybe(types.string),
    items: types.maybe(types.array(types.frozen())),
    totalAmount: types.maybe(types.number),
    shippingService: types.optional(types.string, 'ghtk'),
    transportFee: types.maybeNull(types.frozen()),
    couponCode: types.maybe(types.string),
    coupon: types.maybe(types.string),
    district: types.maybeNull(types.string),
    province: types.maybeNull(types.string),
    ward: types.maybeNull(types.string),
    street: types.maybeNull(types.string),
    address: types.maybeNull(types.string),
    userBuyFullName: types.maybe(types.frozen()),
    totalPriceShop: types.maybe(types.frozen()),
    totalWeight: types.maybe(types.frozen()),
    statusText: types.maybe(types.frozen()),
    shippingInfo: types.maybeNull(types.array(types.frozen())),
  })
  .extend(withEnvironment)
  .views(self => ({}))
  .actions(self => ({
    createOrder: flow(function * (dataBooking) {
      const result: any = yield self.environment.api.createOrder(dataBooking)
      return result
    }),
  }))
  .actions(self => ({
    setBankCode: function(value) {
      self.bankCode = value
    },
  }))

/**
  * Un-comment the following to omit model attributes from your snapshots (and from async storage).
  * Useful for sensitive data like passwords, or transitive state like whether a modal is open.

  * Note that you'll need to import `omit` from ramda, which is already included in the project!
  *  .postProcessSnapshot(omit(["password", "socialSecurityNumber", "creditCardNumber"]))
  */

type OrderProductStoreType = Instance<typeof OrderProductStoreModel>
export interface OrderProductStore extends OrderProductStoreType {}
type OrderProductStoreSnapshotType = SnapshotOut<typeof OrderProductStoreModel>
export interface OrderProductStoreSnapshot extends OrderProductStoreSnapshotType {}
