import { Instance, SnapshotOut, types } from 'mobx-state-tree'

/**
 * Model description here for TypeScript hints.
 */
export const CommentModel = types
  .model('Comment')
  .props({
    userId: types.maybeNull(types.string),
    storeId: types.maybeNull(types.string),
    content: types.maybeNull(types.string),
    rate: types.maybeNull(types.number),
    like: types.maybeNull(types.number),
  })
  .views(self => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions(self => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars

/**
  * Un-comment the following to omit model attributes from your snapshots (and from async storage).
  * Useful for sensitive data like passwords, or transitive state like whether a modal is open.

  * Note that you'll need to import `omit` from ramda, which is already included in the project!
  *  .postProcessSnapshot(omit(["password", "socialSecurityNumber", "creditCardNumber"]))
  */

type CommentType = Instance<typeof CommentModel>
export interface Comment extends CommentType {}
type CommentSnapshotType = SnapshotOut<typeof CommentModel>
export interface CommentSnapshot extends CommentSnapshotType {}
