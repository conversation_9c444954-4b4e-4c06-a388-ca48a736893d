import { Instance, SnapshotOut, types } from 'mobx-state-tree'
import { LOCKED, LOGGED_IN, LOG<PERSON>D_OUT, LOGIN_FAILED } from '../constants/constants'

/**
 * Model description here for TypeScript hints.
 */
export const LoginStoreModel = types
  .model('LoginStore')
  .props({
    userName: types.maybe(types.string),
    password: types.maybe(types.string),
    rememberMe: types.maybe(types.boolean),
    status: types.maybe(types.enumeration('state', [LOGIN_FAILED, LOGGED_OUT, LOGGED_IN, LOCKED])),
  })
  .views(self => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions(self => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars

/**
  * Un-comment the following to omit model attributes from your snapshots (and from async storage).
  * Useful for sensitive data like passwords, or transitive state like whether a modal is open.

  * Note that you'll need to import `omit` from ramda, which is already included in the project!
  *  .postProcessSnapshot(omit(["password", "socialSecurityNumber", "creditCardNumber"]))
  */

type LoginStoreType = Instance<typeof LoginStoreModel>
export interface LoginStore extends LoginStoreType {}
type LoginStoreSnapshotType = SnapshotOut<typeof LoginStoreModel>
export interface LoginStoreSnapshot extends LoginStoreSnapshotType {}
