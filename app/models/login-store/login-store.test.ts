import { LoginStoreModel, LoginStore } from './login-store'
import { useStores } from '../root-store'

test('can be created', () => {
  const instance: LoginStore = LoginStoreModel.create({})

  expect(instance).toBeTruthy()
})

test('10 phai lon hon 9', async () => {
  const { profileStore } = useStores()
  const emailRegister = '<EMAIL>'

  profileStore.email = emailRegister
  profileStore.password = '1234567'
  // await profileStore.registerAccount()
  // await profileStore.getCurrentUser()
  expect(profileStore.email === emailRegister).toBeTruthy()
})
