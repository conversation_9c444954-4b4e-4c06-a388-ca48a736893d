import { cast, flow, Instance, SnapshotOut, types } from 'mobx-state-tree'
import { withEnvironment } from '../extensions'

import _ from 'lodash'
// eslint-disable-next-line import/named
import { getGioHangGroupByShopId, removeProduct } from '@app/services'

type ProductStoreType = Instance<typeof ProductStoreModel>

const CategoryByIdModel = types.model({
  storeName: types.maybeNull(types.frozen()),
  thumbail: types.maybeNull(types.frozen()),
  picture: types.maybeNull(types.frozen()),
  name: types.maybeNull(types.frozen()),
  _id: types.maybeNull(types.frozen()),
})

const DataProduct = types.model({
  _id: types.maybeNull(types.string),
  name: types.maybeNull(types.string),
  description: types.maybeNull(types.string),
  image: types.maybeNull(types.string),
  classify: types.optional(types.array(types.frozen()), []),
  shortDes: types.maybeNull(types.string),
  price: types.maybeNull(types.frozen()),
  storeName: types.maybeNull(types.string)
})

const ShippingInfo = types.model({
  _id: types.maybeNull(types.string),
  statusText: types.maybeNull(types.string),
  label_id: types.maybeNull(types.string),
  partner_id: types.maybeNull(types.string),
  action_time: types.maybeNull(types.string),
  status_id: types.maybeNull(types.string),
  reason_code: types.maybeNull(types.string),
  reason: types.maybeNull(types.string),
  weight: types.maybeNull(types.string),
  fee: types.maybeNull(types.string),
  return_part_package: types.maybeNull(types.string),
  modifyAt: types.maybeNull(types.number),
  time: types.maybeNull(types.number),
  shippingCode: types.maybeNull(types.frozen()),
})

const BannerShopping = types.model({
  picture: types.maybeNull(types.frozen()),
  params: types.maybeNull(types.frozen()),
  screen: types.maybeNull(types.frozen()),
})

const BannerShoppingTop = types.model({
  images: types.string,
  params: types.maybeNull(types.frozen()),
  screen: types.maybeNull(types.frozen()),
})

const ProductByCategory = types.model({
  _id: types.maybeNull(types.string),
  picture: types.maybeNull(types.frozen()),
  name: types.maybeNull(types.string),
  categoryId: types.maybeNull(types.string),
  price: types.maybeNull(types.frozen()),
  modifyAt: types.maybeNull(types.number),
  createAt: types.maybeNull(types.number),
  storeName: types.maybeNull(types.string),
  priceOld: types.maybeNull(types.frozen()),
  typeShip: types.maybeNull(types.number),
})

const HomeProduct = types.model({
  _id: types.maybeNull(types.string),
  picture: types.maybeNull(types.frozen()),
  thumbail: types.maybeNull(types.frozen()),
  name: types.maybeNull(types.string),
  categoryId: types.maybeNull(types.string),
  price: types.maybeNull(types.frozen()),
  priceOld: types.maybeNull(types.frozen()),
  modifyAt: types.maybeNull(types.number),
  createAt: types.maybeNull(types.number),
  typeShip: types.maybeNull(types.number),
})

export const ProductStoreModel = types
  .model('ProductStore')
  .props({
    productSells: types.optional(types.frozen(), []),
    productNears: types.optional(types.frozen(), []),
    viewed: types.optional(types.frozen(), []),
    categoriesCho: types.optional(types.frozen(), []),
    categoriesMeo: types.optional(types.frozen(), []),
    categoriesThuCungKhac: types.optional(types.frozen(), []),
    categoryById: types.optional(types.frozen(), []),
    productRelates: types.optional(types.frozen(), []),
    shopProducts: types.optional(types.frozen(), []),
    dataProduct: types.optional(types.array(types.frozen()), []),
    shippingInFo: types.optional(types.array(ShippingInfo), []),
    product: types.maybeNull(types.frozen()),
    valueCoupon: types.maybeNull(types.number),
    generalCoupon: types.maybeNull(types.string),
    rsCoupon: types.maybeNull(types.string),
    discountType: types.maybeNull(types.number),
    storeIdCoupon: types.maybeNull(types.string),
    typeCode: types.maybeNull(types.number),
    orderId: types.maybeNull(types.string),
    storeId: types.maybeNull(types.frozen()),
    totalPrice: types.maybeNull(types.frozen()),
    generalPrice: types.maybeNull(types.frozen()),
    totalShopDiscount: types.maybeNull(types.frozen()), // tổng tiền đã được giảm all shop
    totalNoDiscount: types.maybeNull(types.frozen()),
    totalDiscount: types.maybeNull(types.frozen()), // my pet giảm giá
    count: types.maybeNull(types.frozen()),
    shopTotalPrice: types.maybeNull(types.frozen()),
    shopTotalCount: types.maybeNull(types.frozen()),
    totalPage: types.maybe(types.number),
    totalProducts: types.maybeNull(types.number),
    shippingCode: types.maybeNull(types.frozen()),
    bannerShopping: types.optional(types.array(BannerShopping), []),
    bannerShoppingTop: types.optional(types.array(BannerShoppingTop), []),
    productDog: types.optional(types.array(ProductByCategory), []),
    productCat: types.optional(types.array(ProductByCategory), []),
    productOther: types.optional(types.array(ProductByCategory), []),
    totalPageDog: types.maybe(types.number),
    totalPageCat: types.maybe(types.number),
    totalPageOther: types.maybe(types.number),
    homeProduct: types.optional(types.array(HomeProduct), []),
    images: types.optional(types.frozen(), []),
    totalRateValue: types.maybeNull(types.frozen()),
    rateTotal1: types.maybeNull(types.number),
    rateTotal2: types.maybeNull(types.number),
    rateTotal3: types.maybeNull(types.number),
    rateTotal4: types.maybeNull(types.number),
    rateTotal5: types.maybeNull(types.number),
    totalComment: types.maybeNull(types.number),
    commentData: types.optional(types.frozen(), []),
    productId: types.maybeNull(types.frozen()),
    getRatingByUserId: types.maybe(types.frozen()),
    bookingStatus: types.maybe(types.boolean),
    reloadData: types.maybe(types.boolean),
    reCalculate: types.maybe(types.boolean),
    indexProductSelected: types.maybeNull(types.number), // sử dụng khi xoá giỏ hàng
    indexShopSelected: types.maybeNull(types.number), // sử dụng khi xoá giỏ hàng
  })
  .extend(withEnvironment)
  .views(self => ({}))
  .actions(self => ({
    getProducts: flow(function * () {
      const result: any = yield self.environment.api.getProducts()
      if (result?.data?.data) {
        const products = result.data.data
        self.productSells = cast(products.productSells)
        self.productNears = products.productNears
        self.categoriesCho = products.categoriesCho
        self.categoriesMeo = products.categoriesMeo
        self.categoriesThuCungKhac = products.categoriesThuCungKhac
        return result
      }
    }),
    getProductForHome: flow(function * () {
      const result: any = yield self.environment.api.getProductForHome()
      if (result?.data?.data?.result) {
        const data = result?.data?.data?.result.map(item => {
          return {
            ...item
          }
        })
        self.homeProduct = cast(data)
      }
    }),
    getProductOfStore: flow(function * (storeId) {
      const result: any = yield self.environment.api.getProductSpaByStoreId(storeId)
      if (result.kind === 'ok') {
        const data = result.data
        let allProduct: typeof DataProduct[] = []
        const totalProducts = 0
        if (data?.products) {
          allProduct = data.products.map(item => {
            return {
              ...item,
              price: item.price ? item.price : 0,
              image: item.thumbail || item.pictures[0],
              shortDes: item.shortDes || item.description
            }
          })
        }
        self.dataProduct = cast(allProduct)
        self.totalProducts = cast(data.totalProducts)
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    getBannerShopping: flow(function * () {
      const result: any = yield self.environment.api.getBannerShopping()
      // return result
      if (result.kind === 'ok') {
        const dataBannerShopping = result.data.data.banners.map(item => ({
          picture: item.thumbail, ...item
        }))
        self.bannerShopping = cast(dataBannerShopping)
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    getBannerShoppingTop: flow(function * () {
      const result: any = yield self.environment.api.getBannerShoppingTop()
      // return result
      if (result.kind === 'ok') {
        const bannerTop = result.data.data.banners.map(item => ({
          images: item.thumbail, ...item
        }))
        const images = result.data.data.banners.map(item => (item.thumbail))
        self.bannerShoppingTop = cast(bannerTop)
        self.images = cast(images)
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    getProductByCategory: flow(function * (type, isLoadMore, page) {
      const result: any = yield self.environment.api.getProductByCategory(type, page)
      if (result.kind === 'ok' && result?.data?.data?.result) {
        const productOfCat = result.data.data.result.map(item => ({
          picture: item.thumbail, ...item
        }))
        if (type === 0) {
          // self.productDog = cast(productOfCat)
          if (isLoadMore) {
            self.productDog = cast([...self.productDog, ...productOfCat])
          } else {
            self.productDog = cast(productOfCat)
          }
          self.totalPageDog = cast(result?.data?.data?.totalPage)
        }
        if (type === 1) {
          // self.productCat = cast(productOfCat)
          if (isLoadMore) {
            self.productCat = cast([...self.productCat, ...productOfCat])
          } else {
            self.productCat = cast(productOfCat)
          }
          self.totalPageCat = cast(result?.data?.data?.totalPage)
        }
        if (type === 2) {
          // self.productOther = cast(productOfCat)
          if (isLoadMore) {
            self.productOther = cast([...self.productOther, ...productOfCat])
          } else {
            self.productOther = cast(productOfCat)
          }
          self.totalPageOther = cast(result?.data?.data?.totalPage)
        }
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
    getViewedProducts: flow(function * () {
      const result: any = yield self.environment.api.getViewedProducts()
      if (result.kind === 'ok' && result?.data?.data?.result) {
        self.viewed = result.data.data.result
      } else {
        __DEV__ && console.log(result.kind)
      }
    }),
  }))
  .actions(self => ({
    getProductDetails: flow(function * (productId) {
      const result: any = yield self.environment.api.getProductDetails(productId)
      if (result?.data?.data) {
        const productDetails = result.data.data
        self.product = productDetails.product
        self.productRelates = productDetails.productRelates
        return result
      }
    }),
    getProductRate: flow(function * (productId) {
      const result: any = yield self.environment.api.getProductRate(productId)
      if (result?.data?.data?.totalRate) {
        const data = result.data.data.totalRate
        const commentData = result.data.data.commentData
        const dataRate = result.data.data
        self.totalRateValue = cast(data.rateTotalValue)
        self.rateTotal1 = cast(data.rateTotal1)
        self.rateTotal2 = cast(data.rateTotal2)
        self.rateTotal3 = cast(data.rateTotal3)
        self.rateTotal4 = cast(data.rateTotal4)
        self.rateTotal5 = cast(data.rateTotal5)
        self.totalComment = cast(data.totalComment)
        self.commentData = cast(commentData)
        self.getRatingByUserId = result?.data?.data?.getRatingByUserId?.length ? dataRate.getRatingByUserId[dataRate.getRatingByUserId.length - 1].rate : 0
        return result
      }
    }),
  }))
  .actions(self => ({
    getShoppingCartInformation: flow(function * (shopProducts) {
      const result: any = yield self.environment.api.getShoppingCartInformation(shopProducts)
      if (result) {
        return result
      } else {
        __DEV__ && console.log('111')
      }
    }),
    checkCoupon: flow(function * (code, subTotal, feeShip, storeId) {
      const result: any = yield self.environment.api.checkCoupon(code, subTotal, feeShip, storeId)
      __DEV__ && console.log('result STORE', result)
      if (result && result.data && result.data.data) {
        const data = result?.data?.data
        // if (data?.coupon && storeId) {
        //   self.coupon = cast(data.coupon.code)
        // }
        if (data?.coupon && !storeId) {
          self.valueCoupon = cast(data.coupon.value)
          self.generalCoupon = cast(data.coupon.code)
          self.typeCode = cast(data.coupon.typeCode)
          self.discountType = cast(data.coupon.type)
          // self.totalPrice = cast(data.coupon.type)
        } else {
        }
        return result
      } else { __DEV__ && console.log(result.kind) }
    }
    ),
    setPropertyProduct: function(key, value) {
      const cloneProduct = { ...self.product }
      cloneProduct[key] = value
      self.product = cast(cloneProduct)
      __DEV__ && console.log('price', self.product[key])
      __DEV__ && console.log('value', cast(value))
    }
  }))

  .actions(self => ({
    createOrder: flow(function * (dataBooking) {
      const result: any = yield self.environment.api.createOrder(dataBooking)
      return result
    }),
  }))

  .actions(self => ({
    getCategoryById: flow(function * (categoryId, page, order, isLoadMore, shipping, price, rate) {
      const result: any = yield self.environment.api.getCategoryById(categoryId, page, order, shipping, price, rate)
      if (result?.data?.data?.result) {
        const categoryById: typeof CategoryByIdModel[] = result.data.data.result.map(item => {
          return {
            ...item,
            picture: item.thumbail || item.pictures[0]
          }
        })
        if (isLoadMore) {
          const tempArray = [...self.categoryById, ...categoryById]
          _.uniqBy(tempArray, '_id')
          self.categoryById = cast(tempArray)
        } else {
          self.categoryById = cast(categoryById)
        }
        self.totalPage = cast(result.data.data.totalPage)
        __DEV__ && console.log('total page', result.data.data.totalPage)
      } else {
        __DEV__ && console.log(result.kind)
      }
    })
  }))

  .actions(self => ({
    getShippingInfo: flow(function * (orderId) {
      const result: any = yield self.environment.api.getShippingInfo(orderId)
      if (result?.data?.data) {
        const dataShippingInfo = result.data.data.map(item => {
          return {
            ...item,
            shippingCode: item.label_id,
            time: item.creataAt
          }
        })
        self.shippingInFo = cast(dataShippingInfo)
      } else {
        __DEV__ && console.log(result.kind)
      }
    })
  }))
  .actions(self => ({
    clearFields: function() {
      self.product = cast([])
    },
    clearDataViewed: function() {
      self.viewed = cast([])
    },
    SetValueCoupon: function(value) {
      self.valueCoupon = value
    },
    setGeneralCoupon: function(value) {
      self.generalCoupon = value
    },
    setRsCoupon: function(value) {
      self.rsCoupon = value
    },
    setTotalPrice: function(value) {
      self.totalPrice = value
    },
    setGeneralPrice: function(value) {
      self.generalPrice = value
    },
    setTotalDisCount: function(value) {
      self.totalDiscount = value
    },
    setCount: function(value) {
      self.count = value
    },
    clearFieldsCoupon: function() {
      self.generalCoupon = ''
      self.valueCoupon = 0
      self.typeCode = 0
      self.discountType = 0
    },
    setOrderId: function(value) {
      self.orderId = value
    },
    setShopProducts: function(value) {
      self.shopProducts = cast(value) // item buyed
    },
    setShopTotalPrice: function(value) {
      self.shopTotalPrice = cast(value)
    },
    setShopTotalCount: function(value) {
      self.shopTotalCount = cast(value)
    },
    setProductId: function(value) {
      self.productId = cast(value)
    },
    setBookingStatus: function(value) {
      self.bookingStatus = value
    },
    setReloadData: function(value) {
      self.reloadData = value
    },
    setReCalculate: function() {
      self.reCalculate = !self.reCalculate
    },
    setSelectProductRemove: function(indexProduct, indexShop) {
      self.indexProductSelected = indexProduct
      self.indexShopSelected = indexShop
    }
  }))
  .actions(self => ({
    calculateFeeShip: flow(function * (body) {
      const result: any = yield self.environment.api.calculateFeeShip(body)
      return result
    }),
    calculateTotalPrice: flow(function * () {
      let count = 0
      let total = 0
      // __DEV__ && console.log(self.shopProducts)
      self.shopProducts.forEach(shop => {
        let totalPriceShop = 0
        let shopCount = 0
        shop[shop.info._id].products.forEach(product => {
          if (product.select) {
            // neu co gia con
            if (product.classifyActive && Object.keys(product.classifyActive).length) {
              const regex = new RegExp(',', 'g')
              const p = product.classifyActive?.price.replace(regex, '')
              // product.classifyActive.mass = product.weight
              totalPriceShop += Number(product.count) * Number(parseInt(p))
            } else {
              totalPriceShop += Number(product.count) * Number(product.info.price)
            }
            shopCount += Number(product.count)
            // total Weight
            if (_.isEmpty(product.classifyActive)) delete product.classifyActive
            product.weight = product?.classifyActive?.mass || product.info.weight
            product.price = product.info.price
            product.thumbnail = product?.info?.pictures && product?.info?.pictures.length ? product?.info?.pictures[0] : ''
            product.productName = product.info.name
          }
        })
        shop.products = shop[shop.info._id].products
        shop.storeId = shop.info._id
        shop.totalPriceShop = totalPriceShop
        shop.totalPriceShopNoDiscount = totalPriceShop
        shop.shopCount = shopCount
        shop.coupon = ''
        shop.rsCoupon = ''
        shop.discount = 0
        count += shopCount
        total += totalPriceShop
        __DEV__ && console.log(shop)
      })
      self.totalPrice = total
      self.count = count
      // __DEV__ && console.log(self.shopProducts)
    }),
    calculateTotalPriceDiscount: flow(function * () {
      // __DEV__ && console.log(self.shopProducts)
      const total = _.sumBy(self.shopProducts, (o: any) => (o.totalPriceShop))
      self.totalNoDiscount = _.sumBy(self.shopProducts, (o: any) => (o.totalPriceShopNoDiscount))
      self.totalShopDiscount = _.sumBy(self.shopProducts, (o: any) => (o.discount))
      self.totalPrice = self.generalPrice - self.totalShopDiscount
      // self.totalPrice -= self.totalShopDiscount
      // console.log('Tổng tiền chưa trừ', self.totalNoDiscount)
      // console.log('Tiền được trừ', self.totalShopDiscount)
    }),
    removeCartItem: flow(function * (cartContext) {
      const shopIndex = self.indexShopSelected
      const prodIndex = self.indexProductSelected
      const shopId = self.shopProducts[shopIndex].info._id
      const prodId = self.shopProducts[shopIndex][shopId].products[prodIndex].productId

      // 1. xoa san pham trong localstorage
      yield removeProduct(prodId, cartContext)

      // 2. xoa tat ca sp cua shop trong store mobx
      const newProducts = []
      self.shopProducts[shopIndex][shopId].products.forEach((p, i) => {
        if (i != prodIndex) newProducts.push(p)
      })
      self.shopProducts[shopIndex][shopId].products = newProducts
      self.shopProducts[shopIndex].products = newProducts

      // 2.1 xoa shop neu khong co san pham
      if (!newProducts.length) {
        const newData = self.shopProducts.filter(s => s.products.length > 0)
        self.setShopProducts(newData)
      }
    }),
  }))
  .actions(self => ({
    fetchCartData: flow(function * (cartContext: any) {
      return new Promise((resolve, reject) => {
        getGioHangGroupByShopId().then((shopProducts) => {
          // load gio hang tu api
          self.getShoppingCartInformation(shopProducts).then((rs) => {
            const { shopProducts, removeProducts } = rs.data.data
            shopProducts.forEach((shop, iS) => {
              shopProducts[iS].select = true
              shopProducts[iS].haveItem = true
              shop[shop.info._id].products.forEach((p, iP) => {
                shopProducts[iS][shop.info._id].products[iP].select = true
              })
            })

            if (removeProducts && removeProducts.length > 0) {
              removeProducts.forEach(pId => {
                removeProduct(pId, cartContext)
              })
            }
            self.setShopProducts(shopProducts)
            resolve()
          }).catch(e => {
            resolve(e)
          })
        })
      })
    }),
  }))
export interface ProductStore extends ProductStoreType {}
type ProductStoreSnapshotType = SnapshotOut<typeof ProductStoreModel>
export interface ProductStoreSnapshot extends ProductStoreSnapshotType {}
