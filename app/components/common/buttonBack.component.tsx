import * as React from 'react'
import Icon from 'react-native-vector-icons/Ionicons'
import { StyleSheet, TextProps } from 'react-native'

interface ComponentProps {
  onPress?: any
}

export type ButtonBackProps = TextProps & ComponentProps;

const ButtonBack = (props: ButtonBackProps) => {
  return (
    // <Icon size={20} name={'chevron-back'} style={styles.iconBack} {...props} />
    <Icon size={22} name={'arrow-back-outline'} style={styles.iconBack} {...props} />
  )
}

export default ButtonBack

const styles = StyleSheet.create({
  iconBack: {
    marginLeft: 8,
    marginTop: 8
  }
})
