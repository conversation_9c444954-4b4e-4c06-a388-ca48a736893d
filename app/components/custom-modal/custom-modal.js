import Modal from 'react-native-modal'
import { StyleSheet } from 'react-native'
import React, { useState, useImperativeHandle } from 'react'

export const CustomModal = React.forwardRef((props, ref) => {
  const [visible, setVisible] = useState(false)

  const { renderContent, onModalHide, onBackPress, onModalWillShow, ...rest } = props

  useImperativeHandle(ref, () => ({
    show: () => setVisible(true),
    hide: () => setVisible(false),
  }))

  return (
    <Modal
      onBackButtonPress={onBackPress}
      onBackdropPress={onBackPress}
      propagateSwipe={true}
      avoidKeyboard={true}
      isVisible={visible}
      backdropOpacity={0.6}
      style={styles.wrapper}
      animationIn={'zoomIn'}
      animationOut={'zoomOut'}
      onModalHide={onModalHide}
      onModalWillShow={onModalWillShow}
      {...rest}>
      {renderContent && renderContent()}
    </Modal>
  )
})

const styles = StyleSheet.create({
  wrapper: {
    alignItems: 'center',
    justifyContent: 'center',
  },
})
