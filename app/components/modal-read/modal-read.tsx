import * as React from 'react'
import {
  View,
  ViewStyle, Text, TouchableOpacity, ScrollView, useWindowDimensions
} from 'react-native'
import { observer } from 'mobx-react-lite'
import Modal from 'react-native-modal'
import moment from 'moment'
import Icon from 'react-native-vector-icons/Ionicons'
import styles from './styles'
import FastImage from 'react-native-fast-image'

export interface ModalReadProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: ViewStyle,
  isVisible: boolean,
  goBack:any
  callBackVisible?:any
  title?:string,
  data?: any,
}

/**
 * Describe your component here
 */
export const ModalRead = observer(function ModalRead(props: ModalReadProps) {
  const { width } = useWindowDimensions()
  return (
    <Modal isVisible={props.isVisible} style={styles.containerView}
      animationIn="zoomIn"
      animationOut="zoomOut"
      onBackdropPress={() => {
        props.callBackVisible()
      }}
    >
      <View style={styles.viewContainer}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 16 }}>
          <View></View>
          <Text numberOfLines={2} style={styles.textTitle}>{props?.data?.title}</Text>
          <TouchableOpacity style={{ position: 'absolute', top: 10, right: 0 }} onPress={() => { props.callBackVisible() }}>
            <Icon name={'close-circle'} size={24} color={'#d2d2d2'} style={styles.icon}/>
          </TouchableOpacity>
        </View>
        <ScrollView style={{ paddingHorizontal: 8 }} showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
          <Text style={styles.textContent}>{props?.data?.description}</Text>
          <FastImage style={{ width: width - 90, height: 200 }} source={{ uri: `${props?.data?.image?.data?.attributes?.url}` }} resizeMode="cover"/>
        </ScrollView>
        <Text style={styles.textDateTime}>{moment(props?.data?.dateTime).format('DD/MM/YYYY')}</Text>
      </View>
    </Modal>
  )
})
