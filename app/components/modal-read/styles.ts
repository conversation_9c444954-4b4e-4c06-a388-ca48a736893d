import { StyleSheet } from 'react-native'
import { typography } from '@app/theme'
import { palette } from '@app/theme/palette'
import { responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'

const styles = StyleSheet.create({
  containerDs: {
    alignItems: 'center',
    borderBottomColor: '#F4F4F4',
    borderBottomWidth: 1,
    borderStyle: 'solid',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingLeft: 15,
    paddingRight: 15,
    paddingVertical: 10,
  },
  containerView: {
    margin: 0
  },
  dsViewText: {
    color: 'rgba(0, 0, 0, 0.5)',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginTop: 9
  },
  icon: {
    marginRight: 8
  },
  selectPiker: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold'
  },
  textContent: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    paddingBottom: 20
  },
  textDateTime: {
    bottom: 10,
    color: palette.lightGrey,
    fontSize: 13,
    fontWeight: '500',
    position: 'absolute',
    right: 16
  },
  textTitle: {
    color: palette.black,
    fontSize: 17,
    fontWeight: '500',
    // paddingVertical: 16,
    textAlign: 'center',
    flex: 1,
    paddingHorizontal: 20
  },
  viewContainer: {
    backgroundColor: palette.white,
    borderRadius: 10,
    height: responsiveHeight(50),
    marginHorizontal: responsiveWidth(100) - (responsiveWidth(100) - 30),
    paddingBottom: 35,
    paddingHorizontal: 8,
  },
  viewHeader: {
    height: 370,
    width: '100%'
  },
  viewLabel: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  }
})

export default styles
