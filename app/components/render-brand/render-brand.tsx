import * as React from 'react'
import { Dimensions, FlatList, TouchableOpacity, View } from 'react-native'
import { Text } from '../'
import styles from './styles'
import Icon from 'react-native-vector-icons/Ionicons'
import { useEffect, useRef, useState } from 'react'
import { color } from '@app/theme'
import { useStores } from '@app/models'

export interface RenderBrandProps {
  /**
   * An optional style override useful for padding & margin.
   */
  dataBranch: any
  selectedAddressIndex: any,
  defaultIndex?: number,
  refreshAddress?: any
}

/**
 * Describe your component here
 */

export function RenderBranch(props: RenderBrandProps) {
  const [selectedAddressIndex, setSelectedAddressIndex] = useState(-1)
  const refContainer = useRef(null)
  const { profileStore } = useStores()
  const find = props.dataBranch.findIndex((x) => x.default === true)

  useEffect(() => {
    if (props.defaultIndex !== undefined) {
      setSelectedAddressIndex(props.defaultIndex)
    } else {
      setSelectedAddressIndex(Number(find))
    }
  }, [])

  useEffect(() => {
    if (profileStore.refreshAddress) {
      setSelectedAddressIndex(find !== -1 ? find : 0)
      __DEV__ && console.log('find', find)
    }
  }, [profileStore.refreshAddress])

  useEffect(() => {
    if (refContainer?.current && props.dataBranch && selectedAddressIndex > -1 && selectedAddressIndex < props.dataBranch.length) {
      const wait = new Promise(resolve => setTimeout(resolve, 300))
      wait.then(() => {
        refContainer.current.scrollToIndex({ animated: true, index: selectedAddressIndex })
      })
    }
  }, [selectedAddressIndex])

  const onSelectedAddress = (index) => {
    setSelectedAddressIndex(index)
    props.selectedAddressIndex(index)
    profileStore.setRefreshAddress(false)
  }

  const IconCircleActive = () => {
    return (
      <View style={{ paddingRight: 10 }}>
        <Icon style={{ position: 'absolute', top: 4.5, left: 4.0 }} name={'ellipse'} size={16} color={color.primary}/>
        <Icon name={'ellipse-outline'} size={24} color={color.primary}/>
      </View>
    )
  }

  const IconCirCleOutline = () => {
    return (
      <View style={{ paddingRight: 10 }}>
        <Icon name={'ellipse-outline'} size={24} color={color.primary}/>
      </View>
    )
  }

  const renderBranch = ({ item, index }) => {
    const responsiveWidth = { width: props.dataBranch.length === 1 ? Dimensions.get('window').width - 35 : 288 }
    const backgroundActive = {
      backgroundColor: (selectedAddressIndex > -1 && selectedAddressIndex === index) ? '#fff' : color.primaryBackground,
      borderColor: (selectedAddressIndex > -1 && selectedAddressIndex === index) ? color.primary : color.primaryBackground,
    }
    return (
      <TouchableOpacity
        key={index}
        onPress={() => {
          onSelectedAddress(index)
        }}>
        <View
          style={[styles.boxViewBranchActive, responsiveWidth, backgroundActive]}>
          <View style={styles.renderItemListBranch}>
            {(selectedAddressIndex > -1 && selectedAddressIndex === index) ? <View>{IconCircleActive()}</View> : <View>{IconCirCleOutline()}</View>}
            <Text numberOfLines={2} style={styles.textBranch}>{item.name}<Text style={styles.textBranch}> - {item?.phone}</Text></Text>
          </View>
          <View style={styles.boxAddress}>
            {/* <Icon style={{ alignSelf: 'flex-start', marginTop: 1 }} name={'location-outline'} size={16} color={'#acb1c0'}/> */}
            <Text numberOfLines={2} style={styles.textAddressBranch}>{item?.address}{item?.distance > 0 ? <Text style={styles.textAddressBranch}> - {item?.distance} km</Text> : item?.distance <= 0 ? <Text style={styles.unknownDistance}>Chưa rõ khoảng cách</Text> : null}</Text>
          </View>
          {/* <View style={styles.renderItemListBranch}> */}
          {/*  /!* <View style={{ flexDirection: 'row' }}> *!/ */}
          {/*  /!*  <Icon name={'call-outline'} size={16} color={'#acb1c0'}/> *!/ */}
          {/*  /!*  <Text style={styles.textAddressPhone}>{item?.phone}</Text> *!/ */}
          {/*  /!* </View> *!/ */}
          {/*  {item?.distance > 0 ? <Text style={styles.textAddressPhone}>{item?.distance} km</Text> : item?.distance <= 0 ? <Text style={styles.unknownDistance}>Chưa rõ khoảng cách</Text> : null} */}
          {/* </View> */}
        </View>
      </TouchableOpacity>
    )
  }
  return (
    <View>
      <FlatList
        ref={refContainer}
        horizontal={true}
        data={props.dataBranch}
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item, index) => item._id + index.toString() }
        renderItem={renderBranch}
        refreshing={props.refreshAddress}
        onScrollToIndexFailed={info => {
          const wait = new Promise(resolve => setTimeout(resolve, 300))
          wait.then(() => {
            refContainer.current?.scrollToIndex({ index: info.index, animated: true })
          })
        }}
      />
    </View>
  )
}
