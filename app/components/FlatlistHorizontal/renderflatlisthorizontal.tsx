import React from 'react'
import { observer } from 'mobx-react-lite'
import {
  View,
  Text,
  FlatList,
  TouchableOpacity, Linking
} from 'react-native'
import styles from './styles'
import { useNavigation } from '@react-navigation/native'
import { useStores } from '@app/models'
import { useTranslation } from 'react-i18next'
import { SCREENS } from '@app/navigation'
import { StoreListItem, LazyImage } from '@app/components'
import { useWhyDidYouUpdate } from '@app/use-hooks'
import Icon from 'react-native-vector-icons/Ionicons'
import FastImage from 'react-native-fast-image'
import { noImage } from '@app/assets/images'
// import { showLocation } from 'react-native-map-link'
// import SimpleToast from 'react-native-simple-toast'
// import { spacing, typography } from '@app/theme'
// import { iconHotLabel } from '@app/assets/images'

const RenderListHeaderFlatlist = observer((props: any) => {
  useWhyDidYouUpdate('RenderListHeaderFlatlist =>>>', props)
  const { t } : any = useTranslation()
  const { homeStore } = useStores()
  const { navigate } = useNavigation()

  // useEffect(() => {
  //   __DEV__ && console.log(' useEffect const RenderListHeaderFlatlist', props.province)
  //   setDataKhachsan(renderFlatListStore.khachsan)
  //   setDataPhongkham(renderFlatListStore.phongkham)
  //   setDataSpa(renderFlatListStore.spa)
  //   setDataUuDai(renderFlatListStore.uudai)
  //   setReloadData(!reloadData)
  //   // return cleanUp = () => {
  //   // }
  // }, [])
  //
  // useEffect(() => {
  //   let didCancel = false
  //   const fetchData = async () => {
  //     if (!didCancel) { // Ignore if we started fetching something else
  //       setDataKhachsan(renderFlatListStore.khachsan)
  //       setDataPhongkham(renderFlatListStore.phongkham)
  //       setDataSpa(renderFlatListStore.spa)
  //       setDataUuDai(renderFlatListStore.uudai)
  //     }
  //   }
  //   fetchData()
  //   return () => { didCancel = true } // Remember if we start fetching something else
  // }, [])

  // const onRefresh = () => {
  //   if (refreshing) return
  //   loadData().then(r => {
  //   })
  // }

  // useEffect(() => {
  //   // loadData()
  // })

  // const loadData = async () => {
  //   await homeStore.getAllPromotions()
  // }

  const onPressImage = (item, navigate) => {
    if (item?.screen) {
      const params = item?.params || ''
      switch (item.screen) {
        case 'PROMOTION_DETAIL_SCREEN':
          navigate(item.screen, { id: params })
          break
        case 'SHOPPING_STACK':
          navigate(item.screen)
          break
        case 'PRODUCT_DETAILS':
          navigate(item.screen, { id: params })
          break
        case 'BLOG_DETAIL_SCREEN':
          navigate(item.screen, { id: params })
          break
        case 'SERVICE_DETAIL':
          navigate(item.screen, { id: params, userId: item?.storeInfo?.userId })
          break
        case 'POPUP_SERVICE_OF_BRAND':
          if (params && item?.screen_param_spaId) {
            navigate(SCREENS.serviceDetail, { id: params, spaId: item?.screen_param_spaId })
          }
          break
        case 'BLOG_SCREEN':
          navigate(item.screen)
          break
        default:
          Linking.openURL(params)
          break
      }
    }
  }

  const renderSpa = ({ item }) => (
    <StoreListItem item={item} onPress={(e) => {
      __DEV__ && console.log('click item', item)
      navigate(SCREENS.serviceDetail, { id: item.storeId })
    }
    } />
  )
  const renderUudai = ({ item }) => {
    return (
      <View>
        <TouchableOpacity
          onPress={() => navigate(SCREENS.promotionDetailScreen, { id: item._id })}
          style={styles.viewRenderItem}>
          <View>
            <View>
              <LazyImage style={styles.img_Uudai} source={{ uri: item.thumbnail || item.cover }}/>
              {/* <View style={styles.overlay} /> */}
            </View>
            <View style={styles.viewCoupe}>
              {/* <View style={styles.viewCoupe}> */}
              {/*  <Text style={styles.textCoupe}>{homeStore.title ? item.title : null}</Text> */}
              {/*  <Image resizeMode={'contain'} style={{ width: 50, height: 50 }} source={iconHotLabel}></Image> */}
              {/* </View> */}
            </View>
            {/* <Icon name={'bookmark-outline'} size={24} color={'#fff'} style={styles.iconBookmark}/> */}
            <TouchableOpacity
              onPress={() => onPressImage(item, navigate)}
            >
              <Text numberOfLines={2} style={styles.textSeverNamecoupel}>{item?.title}</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.viewAddressCoupel}>
            {/* <Icon style={{ marginTop: 2 }}name={'location-outline'} size={14} color={'#acb1c0'}/> */}
            <Text numberOfLines={1} style={styles.textAddressCoupel}>{item?.storeInfo?.address}</Text>
          </View>
        </TouchableOpacity>
      </View>
    )
  }

  // List horizontal màn home

  const renderTopBranch = ({ item }) => {
    return (
      <View>
        <View style={styles.viewRenderItem}>
          <TouchableOpacity
            onPress={() => onPressImage(item, navigate)}
          >
            <LazyImage style={styles.img_Uudai} source={{ uri: item.picture }}/>
            {/* <View style={styles.overlay} /> */}
          </TouchableOpacity>
          <View style={styles.viewCoupe}>
            {/* <Text style={styles.textCoupe}>{homeStore.title ? homeStore.title : null}</Text> */}
            {/* <Image resizeMode={'contain'} style={{ width: 50, height: 50 }} source={iconHotLabel}></Image> */}
          </View>
          {/* <Icon name={'bookmark-outline'} size={24} color={'#fff'} style={styles.iconBookmark}/> */}
          <TouchableOpacity
            onPress={() => onPressImage(item, navigate)}
          >
            <Text numberOfLines={1} style={styles.textSeverNamecoupel}>{item?.name}</Text>
          </TouchableOpacity>
          <View style={styles.viewAddressCoupel}>
            {/* <Icon style={{ marginTop: 2 }}name={'location-outline'} size={14} color={'#acb1c0'}/> */}
            <Text numberOfLines={1} style={styles.textAddressCoupel}>{item?.storeInfo?.address}</Text>
          </View>
        </View>
      </View>
    )
  }

  const renderNews = ({ item, index }) => {
    return (
      <View>
        <View style={styles.viewRenderItem}>
          <TouchableOpacity
            onPress={() => navigate(SCREENS.blogDetailScreen, { id: item._id })}
          >
            {/* <LazyImage style={styles.img_Uudai} source={{ uri: item.picture }}/> */}
            <FastImage
              style={styles.img_Uudai}
              source={item?.picture ? { uri: item?.picture } : noImage} />
            {/* <View style={styles.overlay} /> */}
          </TouchableOpacity>
          <View style={styles.viewCoupe}>
            {/* <Text style={styles.textCoupe}>{homeStore.title ? homeStore.title : null}</Text> */}
            {/* <Image resizeMode={'contain'} style={{ width: 50, height: 50 }} source={iconHotLabel}></Image> */}
          </View>
          {/* <Icon name={'bookmark-outline'} size={24} color={'#fff'} style={styles.iconBookmark}/> */}
          <TouchableOpacity
            onPress={() => navigate(SCREENS.newsDetailScreen, { id: item._id })}
          >
            <Text numberOfLines={3} style={styles.newsTitle}>{item.name}</Text>
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  return (
    <View style={styles.containerFlatlist}>
      {props?.topBranch?.length > 0 ? <View>
        <View style={styles.viewtextTitleFlatlist}>
          {/* <Text style={styles.textTitleFlatlist}>{t('UU_DAI')}</Text> */}
          <Text style={styles.textTitleFlatlist}>{t('top_branch')}</Text>
          {/* <TouchableOpacity */}
          {/*  onPress={() => navigate(SCREENS.promotionDetailScreen, { id: homeStore._id })} */}
          {/*  style={styles.boxTop}> */}
          {/*  <Text style={styles.textTop}>{t('KHAMPHADV_xemtatca')}</Text> */}
          {/*  <Icon style={styles.iconViewMore} name={'chevron-forward-outline'}/> */}
          {/* </TouchableOpacity> */}
          <TouchableOpacity
            onPress={() => navigate(SCREENS.topBranchScreen, { data: props.topBranch, title: 'top_branch' })}
            style={styles.boxTop}>
            <Text style={styles.textTop}>{t('KHAMPHADV_xemtatca')}</Text>
            <Icon style={styles.iconViewMore} name={'chevron-forward-outline'}/>
          </TouchableOpacity>
        </View>
        <View style={[styles.containerItem, { marginHorizontal: -10 }]}>
          <FlatList
            horizontal={true}
            data={props.topBranch}
            extraData={props.topBranch}
            showsHorizontalScrollIndicator={false}
            renderItem={renderTopBranch}
            keyExtractor={item => item._id}/>
        </View>
      </View> : null}

      {props?.promotion?.length > 0 ? <View>
        <View style={styles.viewtextTitleFlatlist}>
          <Text style={styles.textTitleFlatlist}>{t('Chương trình khuyến mãi')}</Text>
          {/* <TouchableOpacity */}
          {/*  onPress={() => navigate(SCREENS.promotionDetailScreen, { id: homeStore._id })} */}
          {/*  style={styles.boxTop}> */}
          {/*  <Text style={styles.textTop}>{t('KHAMPHADV_xemtatca')}</Text> */}
          {/*  <Icon style={styles.iconViewMore} name={'chevron-forward-outline'}/> */}
          {/* </TouchableOpacity> */}
        </View>
        <View style={[styles.containerItem, { marginHorizontal: -10 }]}>
          <FlatList
            horizontal={true}
            data={props.promotion}
            extraData={props.promotion}
            showsHorizontalScrollIndicator={false}
            renderItem={renderUudai}
            keyExtractor={item => item._id}/>
        </View>
      </View> : null}

      {props?.news?.length > 0 ? <View>
        <View style={styles.viewtextTitleFlatlist}>
          <Text style={styles.textTitleFlatlist}>{t('may_be_u_like')}</Text>
          <TouchableOpacity
            onPress={() => navigate(SCREENS.allNews, { data: props.news, title: 'may_be_u_like' })}
            style={styles.boxTop}>
            <Text style={styles.textTop}>{t('KHAMPHADV_xemtatca')}</Text>
            <Icon style={styles.iconViewMore} name={'chevron-forward-outline'}/>
          </TouchableOpacity>
        </View>
        <View style={[styles.containerItem, { marginHorizontal: -10 }]}>
          <FlatList
            horizontal={true}
            data={props.news}
            extraData={props.news}
            showsHorizontalScrollIndicator={false}
            renderItem={renderNews}
            keyExtractor={item => item._id}/>
        </View>
      </View> : null}

    </View>
  )
})

export default RenderListHeaderFlatlist
