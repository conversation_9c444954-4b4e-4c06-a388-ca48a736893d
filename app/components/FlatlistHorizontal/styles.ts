import { StyleSheet } from 'react-native'
import { color, spacing, typography } from '../../theme'
const styles = StyleSheet.create({
  ImageComponentStyle1: {
    borderRadius: 3,
    // marginTop: 15,
  },
  boxTop: {
    // backgroundColor: '#f2f2f2',
    flexDirection: 'row',
    backgroundColor: '#fff',
    padding: 5,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#E4E4E4'
  },
  containerFlatlist: {
    flexDirection: 'column',
    marginHorizontal: 15,
    marginTop: -20
  },
  containerItem: {
    minHeight: 100
    // paddingHorizontal: spacing.small
  },
  contentText: {
    alignItems: 'flex-start',
    flexDirection: 'column',
    flex: 1,
  },
  coverImg: {
    borderRadius: 8,
    height: 102,
    marginBottom: 20,
    width: '100%'
  },
  icHome: {
    height: 14,
    marginBottom: 3,
    marginRight: 1,
    opacity: 0.3,
    width: 14
  },
  icMap: {
    height: 14,
    marginHorizontal: 5,
    marginLeft: 14,
    marginTop: 1,
    resizeMode: 'contain',
    width: 14,
  },

  iconBookmark: {
    position: 'absolute',
    right: 4,
    top: 5
  },
  iconViewMore: {
    color: color.primary,
    // fontSize: 14,
    // lineHeight: 14,
    marginTop: 2
  },
  img_Ks: {
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    height: 119,
    width: '100%'
  },
  img_Uudai: {
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    height: 140,
    width: '100%',
  },
  kilometer: {
    alignItems: 'flex-end',
    fontSize: 12,
    textAlign: 'right'
  },
  newsTitle: {
    color: '#3f3f3f',
    fontSize: 12,
    fontWeight: '400',
    maxWidth: 135,
    padding: 10
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.3)',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  paginationBoxStyle1: {
    alignItems: 'center',
    alignSelf: 'center',
    bottom: 6,
    justifyContent: 'center',
    padding: 20,
    paddingVertical: 0,
    position: 'absolute',
  },
  pointsStar: {
    color: '#333',
    fontSize: 12,
    marginLeft: 5
  },
  rdkTop: {
    flex: 1,
    flexDirection: 'row',
    height: 84,
    width: '100%',
    backgroundColor: '#fff',
    borderRadius: 8,
    borderBottomWidth: 2,
    borderBottomColor: '#f6f6f7',
    padding: 0,
    marginBottom: 8
  },
  rdkTopImage: {
    alignSelf: 'center',
    borderBottomLeftRadius: 8,
    borderTopLeftRadius: 8,
    height: 82,
    resizeMode: 'cover',
    width: 82
  },
  rdkTopText: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    paddingLeft: 15,
    paddingRight: 15,
    paddingTop: 5
  },
  rdkTopText1: {
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 13,
    letterSpacing: 1,
    marginBottom: 8,
    paddingRight: 40
  },
  star: {
    color: '#ff8900',
    fontSize: 14
  },
  starRate: {
    flexDirection: 'row',
    paddingBottom: 4,
    paddingLeft: 15,
    paddingRight: 15,
    width: '100%',
  },
  tabBar: {
    // Remove border top on both android & ios
    backgroundColor: '#fff',
    borderTopColor: 'transparent',
    borderTopWidth: 0,
    elevation: 0,
    shadowColor: '#5bc4ff',
    shadowOffset: {
      height: 0,
    },
    shadowOpacity: 0,
    shadowRadius: 0,
  },
  tabBarText: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
  },
  textAddressCoupel: {
    color: '#9d9d9d',
    fontSize: 12,
    height: 14,
    // letterSpacing: 0,
    marginLeft: 2,
    width: '95%'
  },
  textAddressDichvu: {
    color: '#46474D',
    flex: 1,
    fontSize: 12,
    marginLeft: 1
  },
  textCountrate: {
    color: '#333',
    fontSize: 10,
    letterSpacing: 0,
    marginTop: 3,
    opacity: 0.58
  },
  textCoupe: {
    color: '#EC407A',
    fontSize: 10,
    paddingHorizontal: 8,
    textAlign: 'center'
  },
  textPoin: {
    color: '#333',
    fontSize: 12,
    marginLeft: 5,
    marginTop: 2
  },
  textSeverNamecoupel: {
    color: '#333',
    fontSize: 13,
    fontWeight: '500',
    marginLeft: spacing.small,
    marginTop: 10,
    maxWidth: 120
  },
  textTitleFlatlist: {
    flex: 1,
    flexDirection: 'row',
    color: '#333',
    fontSize: 17,
    fontWeight: '600',
    fontFamily: typography.bold,
  },
  textTop: {
    color: color.primary,
    fontSize: 12,
    justifyContent: 'flex-end',
    letterSpacing: 0,
    textAlign: 'right',
  },
  viewAddressCoupel: {
    alignItems: 'center',
    bottom: 10,
    flexDirection: 'row',
    marginLeft: 8,
    marginTop: 8,
    maxWidth: 120,
    position: 'absolute'
  },
  viewCoupe: {
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 9.5,
    elevation: 2,
    height: 19,
    justifyContent: 'center',
    left: 8,
    position: 'absolute',
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 1,
    shadowRadius: 4,
    top: 8,
  },
  viewRenderItem: {
    backgroundColor: '#f7f7f7',
    borderColor: '#e2e2e2',
    borderRadius: 4,
    borderWidth: 0.5,
    // elevation: 3,
    height: 200,
    marginBottom: 10,
    marginLeft: 11,
    marginTop: 10,
    // shadowColor: '#e6e8ef',
    // shadowOffset: {
    //   width: 0,
    //   height: 1
    // },
    // shadowOpacity: 2,
    // shadowRadius: 5,
    width: 140
  },
  viewRenderItemDichVu: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    elevation: 2,
    marginRight: 10,
    shadowColor: '#e6e8ef',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 1,
    shadowRadius: 10,
    width: 120
  },
  viewSeverNamecoupel: {
    marginBottom: 4,
    marginRight: 7,
  },
  viewtextTitleFlatlist: {
    flexDirection: 'row',
    flex: 1,
    marginTop: 32,
    justifyContent: 'space-between',
    // marginHorizontal: 15
  },
})
export default styles
