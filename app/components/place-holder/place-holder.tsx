import * as React from 'react'
import { StyleSheet, View, ViewStyle } from 'react-native'
import {
  Placeholder,
  PlaceholderMedia,
  PlaceholderLine, Fade
} from 'rn-placeholder'
import {
  responsiveHeight,
  responsiveWidth
} from 'react-native-responsive-dimensions'

export interface PlaceHolderProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: ViewStyle
}

/**
 * Describe your component here
 */
export function PlaceHolder(props: PlaceHolderProps) {
  const PlaceholderComponent = (props) => {
    return <Placeholder
      {...props}
      Animation={Fade}
      style={{
        marginVertical: 6,
        marginHorizontal: 15,
        borderRadius: 4,
      }}
      Left={props => (
        <PlaceholderMedia
          style={[
            props.style,
            {
              width: responsiveWidth(13),
              height: responsiveHeight(6)
            }
          ]}
        />
      )}
    >
      <PlaceholderLine style={{ marginTop: responsiveHeight(0) }} width={60}/>
      <PlaceholderLine style={{ marginTop: responsiveHeight(0) }} width={85}/>
      <PlaceholderLine width={40}/>
    </Placeholder>
  }
  const renderPlaceholders = () => {
    const placeholders = []
    for (let i = 0; i < 8; i++) {
      placeholders.push(PlaceholderComponent({ key: i + '_normal' }))
    }
    return <View>{placeholders}</View>
    // return this.state.posts.map((e, i) => <PlaceholderComponent key={i}/>);
  }
  return (
    <View style={{
      ...props.style
    }}>
      {renderPlaceholders()}
    </View>
  )
}
const styles = StyleSheet.create({

})
