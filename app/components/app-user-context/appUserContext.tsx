import React, { useState } from 'react'
import { useStores } from '@app/models'

const AppUserContext: any = ({ children }) => {
  const [loading, setLoading] = useState(false)
  const { profileStore } = useStores()

  // useEffect(() => {
  //   messaging().requestPermission().then(r => {
  //     messaging().getToken().then(token => {
  //       __DEV__ && console.log('TOKEN FCM', token)
  //       return saveTokenToDatabase(token)
  //     })
  //   })
  //
  //   // If using other push notification providers (ie Amazon SNS, etc)
  //   // you may need to get the APNs token instead for iOS:
  //   // if(Platform.OS == 'ios') { messaging().getAPNSToken().then(token => { return saveTokenToDatabase(token); }); }
  //
  //   // Listen to whether the token changes
  //   // return messaging().onTokenRefresh(token => {
  //   //   saveTokenToDatabase(token)
  //   // })
  // }, [])

  return (
    <AppUserContext.Provider value={{
      loading: loading,
      show: () => setLoading(true),
      hide: () => setLoading(false)
    }}>
      {children}
    </AppUserContext.Provider>
  )
}

export default AppUserContext
