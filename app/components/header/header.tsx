import React, { useEffect, useRef, useState } from 'react'
import { View, ViewStyle, Animated, StyleSheet, Text } from 'react-native'
import { responsiveWidth } from 'react-native-responsive-dimensions'
import ButtonBack from '../common/buttonBack.component'
import { spacing, typography } from '@app/theme'
import { usePrevious } from '@app/use-hooks'
import { useNavigation } from '@react-navigation/native'

export interface HeaderProps {
  /**
   * An optional style override useful for padding & margin.
   */
  onPressButtonLeft?: any, // truyền vào event onPress nếu muốn sử dụng back, hoặc tự động hiển thị nếu có thể back đc
  position?: any,
  title?: string,
  style?: ViewStyle,
  containerStyle?: ViewStyle,
  backText?: string;
  renderLeft?: () => React.ReactElement;
  renderRight?: () => React.ReactElement;
  renderSearchRight?: () => React.ReactElement;
  backStyle?: any;
  backTextStyle?: any;
  titleStyle?: any;
  toolbarColor?: string;
  headerMaxHeight?: number;
  disabled?: boolean;
  noBorder?: boolean;
  parallax?: boolean;
  imageSource?: any;
  children?: any,
}

/**
 * Describe your component here
 */
export function Header(props: HeaderProps) {
  const [offset, setOffset] = useState(0)
  const h1 = useRef(new Animated.Value(0)).current
  const h2 = useRef(new Animated.Value(0)).current
  const headerHeight = useRef(new Animated.Value(0)).current
  const duration = 60
  const prevOffset = usePrevious(offset) || 0
  const { style, title } = props
  const { canGoBack } = useNavigation()

  useEffect(() => {
    onScrollUp()
  }, [])

  // passing position via props
  useEffect(() => {
    if (props?.position) {
      setOffset(Number(props?.position))
    }
  }, [props.position])

  useEffect(() => {
    __DEV__ && console.log('***props.onScroll current', offset)
    if (offset) {
      __DEV__ && console.log('***props.onScroll last', prevOffset)
      if (offset > prevOffset) {
        onScrollDown()
      } else {
        offset <= 0 && onScrollUp()
      }
    }
  }, [offset])

  const config = {
    duration: 200,
    useNativeDriver: false
  }

  const timing = (animated, value) => {
    Animated.timing(animated, {
      toValue: value,
      ...config
    }).start()
  }

  const onScrollDown = () => {
    timing(h1, 0)
    timing(h2, 1)
    timing(headerHeight, 10)
  }

  const onScrollUp = () => {
    timing(h1, 1)
    timing(h2, 0)
    timing(headerHeight, 35)
  }

  const arr = React.Children.toArray(props.children)
  // if (arr.length === 0) {
  //   console.error('AnimatedHeader must have ScrollView or FlatList as a child')
  // }
  // if (arr.length > 1) {
  //   console.error('Invalid child, only 1 child accepted')
  // }

  const onScroll = e => {
    setOffset(e.nativeEvent?.contentOffset?.y)
  }

  const onTrackingScroll = e => {
    // __DEV__ && console.log('onTrackingScroll=>', e.nativeEvent?.contentOffset?.y)
    // setOffset(e.nativeEvent?.contentOffset?.y)
  }

  // const { headerMaxHeight } = props

  const scrollViewRef = useRef()

  const child = arr.length > 0 ? React.cloneElement(arr[0], {
    // style: { height: responsiveHeight(100) },
    // ref: scrollViewRef,
    scrollEventThrottle: 16,
    onScroll: onTrackingScroll,
    onScrollBeginDrag: onScroll,
    onScrollEndDrag: onScroll,
    // contentContainerStyle: { paddingTop: 1000 }
  }) : null

  const onPressButtonLeft = () => {
    if (props?.onPressButtonLeft) {
      props.onPressButtonLeft()
    } else {
      canGoBack()
    }
  }
  return (
    <View style={[styles.container, props.containerStyle]}>
      <View style={[styles.headerContainer, style]}>
        <Animated.View style={[styles.viewBtnTop, {
          marginBottom: props.onPressButtonLeft ? 8 : 0,
          opacity: h2
        }]}>
          <Text style={styles.title}> {props?.title}</Text>
        </Animated.View>
        {(props.onPressButtonLeft) && <ButtonBack onPress={onPressButtonLeft} style={styles.icArrowBack}/>}
        <Animated.View
          style={[
            styles.header,
            {
              paddingHorizontal: spacing.small,
              width: responsiveWidth(100),
              height: headerHeight,
              alignSelf: 'center'
            },
          ]}>
          <Animated.Text
            style={{
              fontWeight: 'bold',
              color: '#333',
              marginBottom: 8,
              fontSize: 20,
              opacity: h1,
            }}>
            {props?.title}
          </Animated.Text>
          {props.renderRight && <View style={styles.renderRight}>{props.renderRight()}</View>}
          {props.renderSearchRight && <View style={styles.renderSearchRight}>{props.renderSearchRight()}</View>}
          {props.renderLeft && <View style={styles.renderLeft}>{props.renderLeft()}</View>}
        </Animated.View>
      </View>
      {arr.length > 0 && child}
      {/* {props.children} */}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    // backgroundColor: 'red',
    // flex: 1
  },
  header: {
    alignItems: 'flex-end',
    alignSelf: 'center',
    borderBottomWidth: 1,
    borderColor: '#f2f2f2',
    flexDirection: 'row',
  },
  headerContainer: {
    // flex: 1,
    // height: 300
  },
  icArrowBack: {
    left: 0,
    position: 'absolute',
    top: 8
  },
  renderLeft: {
    bottom: 0,
    paddingBottom: 12,
    position: 'absolute',
    right: 15
  },
  renderRight: {
    bottom: 0,
    paddingBottom: 12,
    position: 'absolute',
    right: 15
  },
  renderSearchRight: {
    bottom: 0,
    paddingBottom: 12,
    position: 'absolute',
    right: 50
  },
  title: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold'
  },
  viewBtnTop: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 8
  }
})
