import * as React from 'react'
import { StyleSheet, View, ViewStyle } from 'react-native'
import { color } from '../../theme'
const Spinner = require('react-native-spinkit')

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flex: 1,
    justifyContent: 'center',
    zIndex: 9999999
  },
  spinner: {
    marginBottom: 50
  },
})

export interface LoadingProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: ViewStyle
}

/**
 * Describe your component here
 */
export function Loading(props: LoadingProps) {
  const { style } = props

  return (
    <View style={styles.container}>
      {/* <Text style={{ color: '#333' }}>Vui lòng đợi trong giây lát</Text> */}
      <Spinner style={styles.spinner} isVisible={true} size={24} type='Bounce' color={color.primary} {...props}/>
    </View>
  )
}
