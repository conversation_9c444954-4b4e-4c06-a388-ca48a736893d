import * as React from 'react'
import { TextStyle, View, ViewStyle } from 'react-native'
import { typography } from '../../theme'
import { Text } from '../'
import { calculateDistance } from '@app/services'
import _ from 'lodash'
import { useState } from 'react'
import { useAbortableEffect } from '@app/use-hooks'

const CONTAINER: ViewStyle = {
  justifyContent: 'center',
}

const TEXT: TextStyle = {
  fontSize: 13,
  color: '#46474D',
  paddingLeft: 4,
  fontFamily: typography.normal,
  // marginTop: 5
}

export interface NearDistanceProps {
  /**
   * An optional style override useful for padding & margin.
   */
  item:any
}

/**
 * Describe your component here
 */
export function NearDistance (props: NearDistanceProps) {
  const [nearDistance, setNearDistance] = useState(null)
  // const isMountedRef = useIsMountedRef();
  // if (item.branches && item.branches?.length) {
  //   const branchList: () => Promise<any> = async () => {
  //     return Promise.all(item.branches.map(async item => {
  //       let distanceCal = 0
  //       if (item && item.lat && item.lng) {
  //         distanceCal = await calculateDistance({ lat: item.lat, lng: item.lng })
  //       }
  //       return distanceCal
  //     }))
  //   }
  //   const branch = await branchList()
  //   nearDistance = _.min(branch)
  // }
  useAbortableEffect(() => {
    if (props.item.length) { distanceCal() }
  }, [])

  const distanceCal = () => {
    if (props.item && props.item.length) {
      const cal = Promise.all(props.item.map(async item => {
        let distanceCal = 0
        if (item && item.lat && item.lng) {
          distanceCal = await calculateDistance({ lat: item.lat, lng: item.lng })
        }
        return Number(distanceCal)
      })).then(result => {
        // const res
        setNearDistance(_.min(result))
      })
    }
  }
  return (
    <>
      {nearDistance ? <View style={CONTAINER}><Text style={TEXT}>{nearDistance} km</Text></View> : null}
    </>
  )
}
