import React, { createContext, useEffect, useRef, useState } from 'react'
import useWebSocket from 'react-native-use-websocket'
import uuid from 'uuid-random'
import { getRocketChatLogged } from '@app/services'

const { ROCKETCHAT_WS_URL } = require('../../config/env')

export interface webSocketContextProps {
  currentMessage: any,
  sendMessage: any,
  sendJsonMessage: any,
  lastMessage: any,
  lastJsonMessage: any,
  readyState: any,
  getWebSocket: any
  user: any,
  userId: string,
  fetchUserData: () => any,
  createDirectMessage: (users: []) => any, // create room chat with array users
}

export const WebSocketContext: React.Context<webSocketContextProps> = createContext<webSocketContextProps>(null)

export const WebSocketProvider: any = ({ children }) => {
  const [currentMessage, setCurrentMessage] = useState(null)
  const [userRocketChat, setUserRocketChat] = useState(null)
  const [userId, setUserId] = useState(null)
  const { sendMessage, sendJsonMessage, lastMessage, lastJsonMessage, readyState, getWebSocket } = useWebSocket(ROCKETCHAT_WS_URL, {
    onOpen: () => {
      __DEV__ && console.log('WS opened =>>> ')
      sendJsonMessage({
        msg: 'connect',
        version: '1',
        support: ['1', 'pre2', 'pre1']
      })
    },
    onMessage: async (msg) => {
      __DEV__ && console.log('WS onMessage =>>> ', msg.data, new Date().toString())
      const data = JSON.parse(msg.data)
      setCurrentMessage(data)
      if (data.msg === 'ping') {
        // duy trì kết nối với rocketchat
        sendJsonMessage({
          msg: 'pong'
        })
      }
      // connect socket status
      if (data.msg === 'connected') {
      //   loginRocketChat('thanhdevapp', 'Thanhnx@123$')
        await reConnectRocketChat()
      }
      // // user detail - method login via socket
      if (data.msg === 'added' && data?.collection === 'users') {
        setUserId(data.id)
        // setUserRocketChat({ ...userRocketChat, ...data.fields })
      }
      //
      // // login save token
      // if (data?.result?.token) {
      //   // case login - save token
      //   save('userRocketChat', data)
      //   setUserRocketChat(data?.result)
      // }
    },
    reconnectAttempts: 0,
    reconnectInterval: 3000,
    shouldReconnect: (closeEvent) => {
      return didUnmount.current === false
    },
  })

  // const loginRocketChat = (username: string, password: string) => {
  //   const usernameType = username.indexOf('@') !== -1 ? 'email' : 'username'
  //   sha256(password).then(hash => {
  //     const data = {
  //       msg: 'method',
  //       method: 'login',
  //       id: 'login_' + uuid(),
  //       params: [
  //         {
  //           user: { [usernameType]: username },
  //           password: {
  //             digest: hash,
  //             algorithm: 'sha-256'
  //           }
  //         }
  //       ]
  //     }
  //     console.log(data)
  //     sendJsonMessage(data)
  //   })
  // }

  const reConnectRocketChat = async () => {
    const dataLogged = await getRocketChatLogged()
    if (dataLogged?.authToken) {
      const data = {
        msg: 'method',
        method: 'login',
        id: 'resume_' + uuid(),
        params: [
          { resume: dataLogged.authToken }
        ]
      }
      console.log(data)
      setUserRocketChat(dataLogged)
      sendJsonMessage(data)
    }
  }

  /**
   * call sau khi login thanh cong qua api
   */
  const fetchUserData = async () => {
    const data = await getRocketChatLogged()
    __DEV__ && console.log('**************** getRocketChatLogged ********* ', data)
    setUserRocketChat(data)
    return data
  }

  /**
   * create room chat with array users
   */
  const createDirectMessage = (users: []) => {
    sendWsMessageMethod('createDirectMessage', [])
  }

  /**
   * common function method
   * @param methodName
   * @param params
   */
  const sendWsMessageMethod = (methodName: string, params: []) => {
    const data = {
      msg: 'method',
      method: methodName,
      id: methodName + '_' + uuid(),
      params
    }
    sendJsonMessage(data)
  }

  const didUnmount = useRef(false)

  useEffect(() => {
    return () => {
      didUnmount.current = true
    }
  }, [])
  return (
    <WebSocketContext.Provider value ={{
      currentMessage,
      sendMessage,
      sendJsonMessage,
      lastMessage,
      lastJsonMessage,
      readyState,
      getWebSocket,
      user: userRocketChat,
      userId,
      fetchUserData,
      createDirectMessage
    }}>
      {children}
    </WebSocketContext.Provider>
  )
}
