import * as React from 'react'
import { StyleSheet, View, ViewStyle } from 'react-native'

import { Fade, Placeholder, PlaceholderLine, PlaceholderMedia } from 'rn-placeholder'
import { responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import { SafeAreaView } from 'react-native-safe-area-context'
export interface PlaceHolderDetailsProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: ViewStyle
}

/**
 * Describe your component here
 */
export function PlaceHolderDetails(props: PlaceHolderDetailsProps) {
  const PlaceholderComponent = (props) => {
    return <Placeholder
      {...props}
      Animation={Fade}
      style={{
        marginVertical: 10,
        marginHorizontal: 15,
        borderRadius: 4,
      }}
      Left={props => (
        <PlaceholderMedia
          style={[
            props.style,
            {
              width: responsiveWidth(13),
              height: responsiveHeight(6),
            },
          ]}
        />
      )}
    >
      <PlaceholderLine style={{ marginTop: responsiveHeight(0) }} width={60}/>
      <PlaceholderLine style={{ marginTop: responsiveHeight(0) }} width={85}/>
      <PlaceholderLine width={40}/>
    </Placeholder>
  }
  const renderPlaceholders = () => {
    const placeholders = []
    for (let i = 0; i < 5; i++) {
      placeholders.push(PlaceholderComponent({ key: i + '_normal' }))
    }
    return (<View>
      <Placeholder
        Animation={Fade}
        style={{
          marginVertical: 6,
          marginHorizontal: 15,
          borderRadius: 4,
        }}
      >
        <PlaceholderMedia
          style={[
            props.style,
            {
              width: responsiveWidth(90),
              height: responsiveHeight(20),
            },
          ]}
        />
        <PlaceholderLine style={{ marginTop: responsiveHeight(1) }} width={40}/>
        <PlaceholderLine style={{ marginTop: responsiveHeight(1) }} width={60}/>
        <PlaceholderLine style={{ marginTop: responsiveHeight(1) }} width={80}/>
      </Placeholder>
      <View style={styles.list}>{placeholders}</View>
    </View>)
    // return this.state.posts.map((e, i) => <PlaceholderComponent key={i}/>);
  }
  return (
    <SafeAreaView>
      <View style={styles.container}>
        {renderPlaceholders()}
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    marginLeft: 5,
    marginRight: 15,
    marginTop: 15
  },

  list: {
    marginTop: 20
  },

})
