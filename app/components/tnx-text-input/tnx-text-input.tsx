import * as React from 'react'
import { Text, TextInput, TextInputProps, TextStyle, TouchableOpacity, View } from 'react-native'
import { TextInputMask } from 'react-native-masked-text'
import { color, typography } from '@app/theme'
import Icon from 'react-native-vector-icons/Ionicons'

const CONTAINER: any = {
  backgroundColor: '#ffffff',
  borderColor: '#edf1f7',
  borderRadius: 22,
  borderStyle: 'solid',
  borderWidth: 1,
  height: 45,
  width: '100%',
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
}
const CONTAINER_ROUNDED: any = {
  backgroundColor: color.primaryBackground,
  // borderColor: color.primaryBackground,
  borderRadius: 4,
  // borderStyle: 'solid',
  // borderWidth: 1,
  height: 45,
  width: '100%',
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
}

const TEXT: TextStyle = {
  height: 44,
  paddingLeft: 14,
  width: '100%',
}
const TEXT_ROUNDED: TextStyle = {
  alignItems: 'center',
  // backgroundColor: '#FB415A',
  // borderRadius: 1,
  paddingLeft: 14,
  flexDirection: 'row',
  height: 45,
  justifyContent: 'center',
  fontFamily: typography.normal
}

const LABEL_STYLE: TextStyle = {
  color: '#333',
  fontSize: 14,
  fontWeight: '500',
  paddingTop: 20
}
/**
 * Describe your component here
 */

export interface TTextInput extends TextInputProps{
  /**
   * An optional style override useful for padding & margin.
   */
  style? : any
  containerStyle? : any
  iconLeft? : any
  iconRight? : any
  iconRightClick? : any
  iconLeftClick? : any
  typeInput? : any
  typeRadius?:any
  rightViewStyle?:any
  labelText?: string,
  showClearButton?: boolean,
  placeholderTextColor?: string
}

export const TTextInput: any = (props: TTextInput) => {
  const { style, rightViewStyle, containerStyle, showClearButton, iconRight, iconLeft, placeholderTextColor = '#A0A0A0' } = props

  const radius = props?.typeRadius ? props?.typeRadius : 'rounded'

  const styles = radius ? { ...TEXT_ROUNDED, ...style } : { ...TEXT, ...style }
  const viewStyle = radius ? { ...CONTAINER_ROUNDED, ...rightViewStyle, ...containerStyle } : { ...CONTAINER, ...rightViewStyle, ...containerStyle }

  const iconLeftComp = iconLeft ? <TouchableOpacity onPress={ props.iconLeftClick ? props.iconLeftClick : null} style={{ marginRight: 10 }}>{iconLeft}</TouchableOpacity> : null
  const iconRightComp = iconRight || showClearButton ? <TouchableOpacity onPress={ props.iconRightClick ? props.iconRightClick : null} style={{ marginRight: 10 }}>{ (showClearButton && props?.value?.length) ? <Icon
    name='close-circle'
    size={24}
    color='silver'
  /> : iconRight}</TouchableOpacity> : null
  if (props?.typeInput === 'phone') {
    return (
      <View>
        { props.labelText && <Text style={{ ...LABEL_STYLE }}>{ props?.labelText }</Text> }
        <View style = {viewStyle}>
          {iconLeftComp}
          <View style={{ flex: 1 }}>
            <TextInputMask
              type={'custom'}
              options={
                {
                  mask: '+84999999999'
                }
              }
              {...props}
              style={styles}
            />
          </View>
          {iconRightComp}
        </View>
      </View>
    )
  }
  return (<View style={viewStyle}>
    {iconLeftComp}
    <View style={{ flex: 1 }}>
      <TextInput
        {...props}
        placeholderTextColor={placeholderTextColor}
        style={styles}
      />
    </View>
    {iconRightComp}
  </View>)
}

export default TTextInput
