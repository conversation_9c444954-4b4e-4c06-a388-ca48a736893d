import React, { useState } from 'react'
import { StyleSheet, View } from 'react-native'
import { color } from '@app/theme'
import { LoadingContext } from '@app/context'
import Modal from 'react-native-modal'
import { responsiveHeight } from 'react-native-responsive-dimensions'
const Spinner = require('react-native-spinkit')

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginTop: responsiveHeight(80) / 2,
    minHeight: 50,
    padding: 22,
  },
  modalContainer: {
    marginHorizontal: 50
  },
  spinner: {
    alignItems: 'center',
    // backgroundColor: 'red',
    height: 50,
    justifyContent: 'center',
    width: 50,
  },
})

const ModalLoading = (props: any) => {
  return (
    <Modal style={styles.modalContainer} isVisible={props.loading}
      backdropColor="#333"
      backdropOpacity={0.5}
      animationIn="zoomIn"
      animationOut="zoomOut"
      animationInTiming={300}
      animationOutTiming={300}
      backdropTransitionInTiming={300}
      backdropTransitionOutTiming={300}
    >
      {/* eslint-disable-next-line react-native/no-inline-styles */}
      <View style={{ flex: 1, }}>
        <View style={styles.container}>
          <Spinner style={styles.spinner} isVisible={true} size={40} type='ThreeBounce' color={color.primary}/>
        </View>
      </View>
    </Modal>
  )
}

const AppLoadingProvider: any = ({ children }) => {
  const [loading, setLoading] = useState(false)
  return (
    <LoadingContext.Provider value={{
      loading: loading,
      show: () => setLoading(true),
      hide: () => setLoading(false)
    }}>
      {loading && (<ModalLoading loading={loading}/>)}
      {children}
    </LoadingContext.Provider>
  )
}

export default AppLoadingProvider
