import * as React from 'react'
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native'
import { Modalize } from 'react-native-modalize'
import Icon from 'react-native-vector-icons/Ionicons'
import { useEffect, useRef, useState } from 'react'

import { ifIphoneX } from 'react-native-iphone-x-helper'
import { useTranslation } from 'react-i18next'
import { Slider } from 'react-native-elements'
import { ButtonBack } from '@app/components'
import { useStores } from '@app/models'
import { color, typography } from '@app/theme'
import CurrencyInput from 'react-native-currency-input'
import { DEFAULT_DISTANCE } from '@app/constants/configs'
import StarRating from 'react-native-star-rating'

export interface FilterProps {
  isOpen:boolean
  dataFilter?:any
  filter?:any,
  onClose?:any,
  onOk?:any,
  rateProduct?:any,
  priceRange?:any
  shipping?:any,
  page?:any
  // action?: ((event: GestureResponderEvent) => void) | undefined;
}

/**
 * Describe your component here
 */

export function Filter(props: FilterProps) {
  const modalizeRef = useRef<Modalize>(null)
  const { t } : any = useTranslation()
  const { searchStore } = useStores()
  const [value, setValue] = useState(DEFAULT_DISTANCE)
  const [item, setItem] = useState(null)
  const [idChoose, setIdChoose] = useState(searchStore.typeSearch || 0)
  const [maxDistance] = useState(2000)
  const [selectPrice, setSelectPrice] = useState('')
  const [valuePrice, setValuePrice] = useState('')
  const [selectShipping, setSelectShipping] = useState('')
  const [valueShipping, setValueShipping] = useState('')
  // const [selectRate, setSelectRate] = useState('')
  const [minPrice, setMinPrice] = useState('')
  const [maxPrice, setMaxPrice] = useState('')
  const [valueInputMax, setValueInputMax] = useState('')
  const [valueInputMin, setValueInputMin] = useState('')
  const [selectRate, setSelectRate] = useState('')
  const [rate, setRate] = useState('')
  const [isActivePrice, setIsActivePrice] = useState(false)
  const [indexItemPrice, setIndexItemPrice] = useState(-1)
  const [isActiveShipping, setIsActiveShipping] = useState(false)
  const [indexItemShipping, setIndexItemShipping] = useState(-1)
  const [isActiveRate, setIsActiveRate] = useState(false)
  const [indexItemRate, setIndexItemRate] = useState(-1)

  useEffect(() => {
  }, [valueShipping])

  useEffect(() => {
  }, [valuePrice])

  // listening item price change

  useEffect(() => {
    if (!isActivePrice) { setIsActivePrice(true) }
  }, [indexItemPrice])

  useEffect(() => {
    if (isActivePrice) {
      setSelectPrice(selectPrice)
      setValuePrice(valuePrice)
    } else {
      setSelectPrice('')
      setValuePrice('')
    }
  }, [isActivePrice])

  // listening item rate change

  useEffect(() => {
    if (!isActiveRate) { setIsActiveRate(true) }
  }, [indexItemRate])

  useEffect(() => {
    if (isActiveRate) {
      setSelectRate(selectRate)
      setRate(rate)
    } else {
      setSelectRate('')
      setRate('')
    }
  }, [isActiveRate])

  // listening item shipping change

  useEffect(() => {
    if (!isActiveShipping) { setIsActiveShipping(true) }
  }, [indexItemShipping])

  useEffect(() => {
    if (isActiveShipping) {
      setSelectShipping(selectShipping)
      setValueShipping(valueShipping)
    } else {
      setSelectShipping('')
      setValueShipping('')
    }
  }, [isActiveShipping])

  // =========

  useEffect(() => {
    if (minPrice && maxPrice) {
      setValuePrice(minPrice + '-' + maxPrice)
    } else if (minPrice || maxPrice) {
      setValuePrice(minPrice || maxPrice)
    }
    setSelectPrice('')
  }, [minPrice, maxPrice])

  const onOpen = () => {
    // __DEV__ && console.log('onOpen', props.isOpen)
      modalizeRef.current?.open()
  }
  const onClose = () => {
    // __DEV__ && console.log('onClose', props.isOpen)
    modalizeRef.current?.close()
  }
  const renderHeaderModalBranch: any = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onClose} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>{t('FILTER')}</Text>
      <TouchableOpacity
        onPress={() => {
          onClose()
          setTimeout(() => {
            props.filter(item)
            props.onOk({ page: 1, type: idChoose, filter: item, distance: value || DEFAULT_DISTANCE, valueShipping: valueShipping || '', valuePrice: valuePrice || '', valueRate: rate || '' }) // trigger reload data
          }, 100)
          // props.page(1)
          // console.log('item----->', item)
        }}
      >
        <Text style={styles.textClose}>{t('APDUNG')}</Text>
      </TouchableOpacity>

    </View>
  )
  useEffect(() => {
    if (props.isOpen) {
      onOpen()
      __DEV__ && console.log('useEffect', props.isOpen)
    }
  }, [props.isOpen])
  const renderChooseFilter = (item, index) => {
    return (
      <View key={index}>
        <TouchableOpacity style={styles.viewItem}
          onPress={() => {
            setIdChoose(item.id)
            setItem(item)
          }}
        >{
            item.id === idChoose
              ? <Icon size={24}
                color = {color.primary}
                name = {'checkmark-circle'}
              />
              : <Icon size={24}
                color = {color.primary}
                name = {'ellipse-outline'}
              />}
          <Text style={{ fontWeight: item.id === idChoose ? 'bold' : 'normal', marginLeft: 10 }}>{item.name}</Text>
        </TouchableOpacity>
      </View>
    )
  }
  const renderFilter = () => {
    return (<View style={styles.viewContainer}>
      {props.dataFilter.map((item, index) => renderChooseFilter(item, index))}
      {idChoose === 0 ? <View>
        {renderPriceRange()}
        {renderShippingPartner()}
        {renderRate()}
      </View> : null}
      {/* {idChoose === 0 ? renderShippingPartner() : null} */}
      <Slider
        value={value}
        style={styles.slider}
        maximumValue={maxDistance}
        minimumValue={3}
        allowTouchTrack={true}
        step={1}
        maximumTrackTintColor={color.primary}
        minimumTrackTintColor={color.primary}
        thumbTintColor={color.primary}
        onValueChange={(value) => setValue(value)}
      />
      <Text>{t('FILTERDISTANCE')} {value} km</Text>
    </View>)
  }

  const renderPriceRange = () => {
    return (<View style={styles.sectionProductFilter}>
      <Text style={styles.label}>{t(`${props.priceRange.title}`)}</Text>
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 4 }}>
        <CurrencyInput
          style={styles.textInput}
          value={valueInputMin}
          onChangeValue={setValueInputMin}
          delimiter=","
          separator="."
          placeholder={'Giá từ'}
          precision={0}
          onChangeText={(formattedValueMin) => {
            setMinPrice(formattedValueMin.split(',').join(''))
            console.log(formattedValueMin.split(',').join('')) // $2,310.46
          }}
        />
        <CurrencyInput
          style={styles.textInput}
          value={valueInputMax}
          onChangeValue={setValueInputMax}
          delimiter=","
          separator="."
          placeholder={'đến'}
          precision={0}
          onChangeText={(formattedValueMax) => {
            setMaxPrice(formattedValueMax.split(',').join(''))
            console.log(formattedValueMax.split(',').join('')) // $2,310.46
          }}
        />
      </View>
      {props?.priceRange?.data ? <View style={styles.renderItem}>
        {props.priceRange.data.map((item, index) => renderItemPriceRange(item, index))}
      </View> : null}
    </View>)
  }

  const renderShippingPartner = () => {
    return (<View style={styles.sectionProductFilter}>
      <Text style={styles.label}>{t(`${props.shipping.title}`)}</Text>
      {props?.shipping?.data ? <View style={styles.renderItem}>
        {props.shipping.data.map((item, index) => renderItemShippingPartner(item, index))}
      </View> : null}
    </View>)
  }

  const renderItemPriceRange = (item, index) => {
    const id = 'price_' + item.id
    return (<TouchableOpacity
      key={id}
      style={[styles.item, { borderColor: selectPrice === id && isActivePrice === true ? color.primary : 'transparent', backgroundColor: selectPrice === id && isActivePrice === true ? '#fff' : '#f3f3f3' }]}
      onPress={() => {
        setValueInputMax('')
        setValueInputMin('')
        if (indexItemPrice === index) {
          setIsActivePrice(!isActivePrice)
        }
        setSelectPrice(id)
        setValuePrice(item.value)
        setIndexItemPrice(index)
      }
      }
    >
      <Text style={styles.textItem}>{item.name}</Text>
    </TouchableOpacity>)
  }

  const renderItemShippingPartner = (item, index) => {
    const id = 'shipping_' + item.id
    return (<TouchableOpacity
      key={id}
      style={[styles.item, { borderColor: selectShipping === id && isActiveShipping === true ? color.primary : 'transparent', backgroundColor: selectShipping === id && isActiveShipping === true ? '#fff' : '#f3f3f3' }]}
      onPress={() => {
        setSelectShipping(id)
        setValueShipping(item.value)
        setIndexItemShipping(index)
        if (indexItemShipping === index) {
          setIsActiveShipping(!isActiveShipping)
        }
      }
      }
    >
      <Text style={styles.textItem}>{item.name}</Text>
    </TouchableOpacity>)
  }

  const renderRate = () => {
    return (<View style={styles.sectionProductFilter}>
      <Text style={styles.label}>{t(`${props.rateProduct.title}`)}</Text>
      {props?.rateProduct?.data ? <View style={styles.renderItem}>
        {props.rateProduct.data.map((item, index) => renderItemRate(item, index))}
      </View> : null}
    </View>)
  }

  const renderItemRate = (item, index) => {
    const id = 'rate_' + item.id
    return (<TouchableOpacity
      style={[styles.itemRate, { borderColor: selectRate === id && isActiveRate === true ? color.primary : 'transparent', backgroundColor: selectRate === id && isActiveRate === true ? '#fff' : '#f3f3f3' }]}
      onPress={() => {
        setSelectRate(id)
        setRate(item.value)
        setIndexItemRate(index)
        if (indexItemRate === index) {
          setIsActiveRate(!isActiveRate)
        }
      }
      }
    >
      {item.value === '' ? <Text style={styles.textItem}>{item.name}</Text> : null}
      {item.value !== 0
        ? <StarRating
          fullStarColor={'#FFC107'}
          disabled={true}
          maxStars={item.value}
          rating={item.value}
          fullStar={'star'}
          iconSet={'FontAwesome'}
          starSize={14}
          starStyle={styles.customStar}
        /> : null}
    </TouchableOpacity>)
  }

  return (
    <>
      <Modalize
        HeaderComponent={renderHeaderModalBranch}
        ref={modalizeRef}
        modalTopOffset={40}
        adjustToContentHeight
        // snapPoint={405}
        // modalHeight={responsiveHeight(90)}
        disableScrollIfPossible = {false}
        onClosed={() => { props.onClose(true) }}
        keyboardAvoidingBehavior={'padding'}
      >
        {renderFilter()}
      </Modalize>
    </>
  )
}

const styles = StyleSheet.create({
  customStar: {
    marginRight: 2,
    marginTop: 3
  },
  item: {
    borderRadius: 4,
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: 4,
    paddingVertical: 10,
    width: '49%'
  },
  itemPriceRange: {
    backgroundColor: '#FAFAFA',
    borderRadius: 4,
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: 4,
    paddingVertical: 10,
    width: '32%'
  },
  itemRate: {
    borderRadius: 4,
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: 4,
    paddingVertical: 10,
    textAlign: 'center',
    width: 90
  },
  label: {
    color: '#979797',
    paddingBottom: 10,
    paddingTop: 15,
    textTransform: 'uppercase'
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
    ...ifIphoneX({
      marginTop: 0
    }, {
      marginTop: 5
    }),
  },
  renderItem: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingBottom: 10
  },
  sectionProductFilter: {
    borderColor: '#e2e2e2',
    borderTopWidth: 1,
    marginVertical: 4,
  },
  slider: {
    marginTop: 20
  },
  textClose: {
    color: color.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  textInput: {
    alignItems: 'center',
    borderColor: '#edf1f7',
    borderRadius: 4,
    borderWidth: 1,
    color: '#333',
    flexDirection: 'row',
    fontFamily: typography.normal,
    fontSize: 14,
    height: 40,
    paddingLeft: 10,
    width: '49%'
  },
  textItem: {
    color: '#333'
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    marginRight: -35,
    textAlign: 'center'
  },
  viewBtn: {
    marginTop: 30,
  },
  viewContainer: {
    flex: 1,
    marginHorizontal: 16,
    paddingBottom: 30
  },
  viewItem: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 15,
  },
  viewProdFilter: {
    flex: 1,
    height: 50
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  }

})
