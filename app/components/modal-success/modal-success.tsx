import * as React from 'react'
import { View, ViewStyle, StyleSheet, TouchableOpacity, Image } from 'react-native'
import { observer } from 'mobx-react-lite'
import { Text } from '../'
import Modal from 'react-native-modal'
import {
  catCry, icAdded, icBigClose,
  imgSuccess
} from '../../assets/images'
import { useTranslation } from 'react-i18next'
import { color, typography } from '@app/theme'
import { useEffect, useState } from 'react'
import AwesomeAlert from 'react-native-awesome-alerts'
// import { responsiveWidth } from 'react-native-responsive-dimensions'
// import CoverflowExample from 'react-native-tab-view/lib/typescript/example/src/CoverflowExample'

export interface ModalSuccessProps {
  /**
   * An optional style override useful for padding & margin.
   */
  isVisible: boolean
  style?: ViewStyle
  close?: () => any
  onPress?: () => any
  onClosed?: () => any
  onConfirm?: () => any
  message: any
  title: any
  type: string
  textButton?:string
  cancelText?:string
  confirmText?:string
}

/**
 * Describe your component here
 */
const ModalSuccess = observer((props: ModalSuccessProps) => {
  const { t } : any = useTranslation()
  const [visible, setVisible] = useState(false)

  useEffect(() => {
    setVisible(props.isVisible)
  }, [props.isVisible])

  // trường hợp ko truyền vào sự kiện closed
  const onClosed = () => {
    if (props.close) props.close()
    else {
      setVisible(false)
    }
  }

  const switchIcon = () => {
    if (props.type === 'success') {
      return <Image style={styles.imageSuccess} source={imgSuccess} resizeMode="cover"/>
    }
    if (props.type === 'error') {
      return <Image style={styles.image} source={catCry} resizeMode="cover"/>
    }
    if (props.type === 'customError') {
      return <Image style={styles.image} source={icBigClose} resizeMode="cover"/>
    }
    return <Image style={{ width: 60, height: 48 }} source={icAdded} resizeMode="cover"/>
  }

  if (props.type === 'confirm') {
    return (
      <AwesomeAlert
        useNativeDriver={true}
        show={visible}
        actionContainerStyle={styles.alertConfirm}
        showProgress={false}
        title={props.title}
        message={props.message}
        closeOnTouchOutside={true}
        closeOnHardwareBackPress={false}
        showCancelButton={true}
        showConfirmButton={true}
        cancelText={props.cancelText}
        confirmText={props.confirmText}
        confirmButtonColor={color.primary}
        cancelButtonStyle={[styles.alertBtn, { backgroundColor: '#f3f3f3' }]}
        confirmButtonStyle={styles.alertBtn}
        cancelButtonTextStyle={[styles.btnTextStyles, { color: '#333' }]}
        confirmButtonTextStyle={styles.btnTextStyles}
        onCancelPressed={onClosed}
        onConfirmPressed={() => {
          onClosed()
          // props.onConfirm && props.onConfirm()
          console.log('AAA')
        }}
      />
    )
  } else {
    return (
      <Modal style={{ marginHorizontal: 60 }}
        isVisible={visible}
        backdropColor="#B4B3DB"
        backdropOpacity={0.8}
        animationIn="zoomIn"
        animationOut="zoomOut"
        animationInTiming={400}
        animationOutTiming={200}
        backdropTransitionInTiming={300}
        backdropTransitionOutTiming={300}
        onModalHide={onClosed}
      >
        {/* eslint-disable-next-line react-native/no-inline-styles */}
        <View style={{ flex: 1, }}>
          <View style={styles.modal}>
            { switchIcon()}
            {/* {props.type === 'success' ? <Image style={styles.image} source={catsmile} resizeMode="cover"/> : <Image style={styles.image} source={catCry} resizeMode="cover"/>} */}
            <Text style={styles.contentTitle}>{props.title}</Text>
            <Text style={styles.contentErr}>{props.message}</Text>
            <TouchableOpacity style={styles.btnClose} onPress={props.close ? props.close : onClosed }>
              <Text style={styles.textClose}>{props?.textButton ? props.textButton : t('CLOSE')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    )
  }
})

export default ModalSuccess

const styles = StyleSheet.create({
  alertBtn: {
    borderRadius: 4,
    width: 100
  },
  alertConfirm: {
    height: 80,
  },
  btnClose: {
    alignItems: 'center',
    // backgroundColor: '#ff8ba1',
    backgroundColor: '#D9D9D9',
    borderRadius: 4,
    flexDirection: 'row',
    height: 44,
    justifyContent: 'center',
    width: '50%',
  },
  btnTextStyles: {
    fontSize: 14,
    fontWeight: 'bold',
    paddingVertical: 5,
    textAlign: 'center'
  },
  content: {
    alignItems: 'center',
    backgroundColor: 'white',
    borderColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 4,
    justifyContent: 'center',
    padding: 22,
  },
  contentErr: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 24
  },
  contentTitle: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
    marginVertical: 12
  },
  image: {
    height: 40,
    width: 40
  },
  imageSuccess: {
    height: 105,
    width: 154
  },
  modal: {
    alignItems: 'center',
    backgroundColor: 'white',
    borderColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 6,
    flexDirection: 'column',
    justifyContent: 'space-between',
    marginTop: 220,
    minHeight: 240,
    paddingBottom: 20,
    paddingHorizontal: 22,
    paddingTop: 35
  },
  textClose: {
    color: '#333',
    fontSize: 14,
    fontWeight: '700'
  },
})
