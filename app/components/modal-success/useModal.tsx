import React from 'react'
import { useTranslation } from 'react-i18next'

export const useModal: any = () => {
  const { t } : any = useTranslation()
  const [type, setType] = React.useState('')
  const [isVisible, setIsVisible] = React.useState(false)
  const [title, setTitle] = React.useState("I'm the Modal Title")
  const [message, setMessage] = React.useState("I'm the Modal Content")
  const [textButton, setTextButton] = React.useState('')
  const [cancelText, setCancelText] = React.useState('')
  const [confirmText, setConfirmText] = React.useState('')
  const [onClosed, setOnClosed] = React.useState(null)
  const [onConfirm, setOnConfirm] = React.useState(null)

  const showSuccess = (title = '', content = '', textButton = t('CLOSE'), onClosed = null) => {
    showModal(title, content, 'success', onClosed, textButton)
    // showNotifications(title, content)
  }
  const showError = (title = '', content = '', textButton = t('CLOSE'), onClosed = null) => {
    showModal(title, content, 'error', onClosed, textButton)
  }

  const showCustomError = (title = '', content = '', textButton = t('CLOSE'), onClosed = null) => {
    showModal(title, content, 'customError', onClosed, textButton)
  }

  const showCustomSuccess = (title = '', content = '', textButton = t('CLOSE'), onClosed = null) => {
    showModal(title, content, 'customSuccess', onClosed, textButton)
  }

  const showConfirm = (title = '', content = '', cancelText = t('CLOSE'), confirmText = t('OK'), onClosed = null, onConfirm = null) => {
    setIsVisible(true)
    setTitle(title)
    setMessage(content)
    setCancelText(cancelText)
    setConfirmText(confirmText)
    setType('confirm')
    setOnClosed(onClosed || (() => {}))
    setOnConfirm(onConfirm || (() => {}))
  }

  const showModal = (title = '', content = '', type = 'error', onClosed: any, textButton) => {
    setIsVisible(true)
    setTitle(title)
    setMessage(content)
    setTextButton(textButton)
    setType(type)
    setOnClosed(onClosed || (() => {}))
  }
  const hideModal = () => {
    setIsVisible(false)
  }

  return { isVisible, showError, showSuccess, showCustomError, showCustomSuccess, showConfirm, showModal, hideModal, title, message, type, onClosed, textButton, cancelText, confirmText, onConfirm }
}
