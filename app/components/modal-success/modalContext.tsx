import React, { createContext } from 'react'
import { useModal } from './useModal'
import ModalSuccess from '@app/components/modal-success/modal-success'
export interface ModalContextProps {
  isVisible: boolean
  showSuccess: (title, content, textButton?, onClosed?) => any
  showError: (title, content, textButton?, onClosed?) => any,
  showCustomError: (title, content, textButton?, onClosed?) => any,
  showCustomSuccess: (title, content, textButton?, onClosed?) => any,
  showModal: (title, content, type, textButton?, onClosed?) => any,
  showConfirm: (title, content, cancelText?, confirmText?, onClosed?, onConfirm?) => boolean,
  hideModal: () => any,
  onClosed: () => any,
  onConfirm: () => any,
  message: any
  title: any
  type: string
  textButton?:string
  cancelText?:string
  confirmText?:string
}

export let ModalContext: React.Context<ModalContextProps>
const { Provider } = (ModalContext = createContext<ModalContextProps>(null))

export const ModalProvider: any = ({ children }) => {
  const { isVisible, showError, showSuccess, showCustomError, showCustomSuccess, showConfirm, hideModal, title, message, type, onClosed, textButton, cancelText, confirmText, onConfirm, showModal } = useModal()
  return (
    <Provider value ={{ isVisible, showError, showSuccess, showCustomError, showCustomSuccess, showConfirm, hideModal, title, message, type, onClosed, textButton, cancelText, confirmText, onConfirm, showModal }}>
      <ModalSuccess isVisible={isVisible} title={title} message={message} type={type} close={hideModal} textButton={textButton} onClosed={onClosed} onConfirm={onConfirm} cancelText={cancelText} confirmText={confirmText}/>
      {children}
    </Provider>
  )
}
