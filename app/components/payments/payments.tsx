import * as React from 'react'
import { View, Text, FlatList, StyleSheet, TouchableOpacity } from 'react-native'
import { useTranslation } from 'react-i18next'
import { useEffect, useRef, useState } from 'react'
import { responsiveWidth } from 'react-native-responsive-dimensions'
import { color, typography } from '../../theme'
import Icon from 'react-native-vector-icons/Ionicons'

export interface PaymentsProps {
  /**
   * An optional style override useful for padding & margin.
   */
  onPress:any
  defaultPayment: any
}

/**
 * Describe your component here
 */
export function Payments(props: PaymentsProps) {
  const { t } : any = useTranslation()
  const data = [
    {
      id: 0,
      name: t('BANKTRANSFER'),
      image: 'https://firebasestorage.googleapis.com/v0/b/mypet-app-2dafe.appspot.com/o/atm.svg?alt=media&token=abf379d9-f916-480f-8a5f-9e51f17266b6'
    },
    {
      id: 1,
      name: t('CASH'),
      image: 'https://firebasestorage.googleapis.com/v0/b/mypet-app-2dafe.appspot.com/o/money.svg?alt=media&token=d56269b2-a63d-4fcf-97d3-fa03be08371b'
    }]
  const [select, setSelect] = useState(props.defaultPayment || 1)
  const refContainer = useRef(null)

  useEffect(() => {
    setTimeout(() => {
      if (select > -1 && refContainer?.current && data?.length && select < data.length) {
        refContainer.current.scrollToIndex({ animated: true, index: select })
      }
    }, 1000)
  }, [select])

  const renderItem = ({ item, index }) => {
    return (
      <TouchableOpacity onPress={() => {
        setSelect(item.id)
        props.onPress(item.id)
      }} style={[styles.container, { borderColor: select === item.id ? color.primary : '#f2f2f2', backgroundColor: select === item.id ? '#fff' : color.primaryBackground }] }>
        {/* <View style={styles.imageView}> */}
        {/*  <SvgCssUri */}
        {/*    width= '50' */}
        {/*    height='33' */}
        {/*    uri={item.image} */}
        {/*  /> */}
        {/* </View> */}
        {select === item.id ? <View style={{ paddingRight: 10 }}>
          <Icon style={{ position: 'absolute', top: 4.5, left: 4.5 }} name={'ellipse'} size={15} color={color.primary}/>
          <Icon name={'ellipse-outline'} size={24} color={color.primary}/>
        </View> : null}
        <Text style={styles.text}>{item.name}</Text>
      </TouchableOpacity>
    )
  }
  return (
    <View style={{ paddingLeft: 7 }}>
      <FlatList
        ref={refContainer}
        horizontal={true}
        keyExtractor={(item, index) => item.id.toString()}
        data={data}
        renderItem={renderItem}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  )
}
const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    borderRadius: 4,
    borderStyle: 'solid',
    borderWidth: 1,
    flexDirection: 'row',
    height: 44,
    justifyContent: 'center',
    marginLeft: 8,
    marginTop: 10,
    width: responsiveWidth(50) - 19,
  },
  imageView: {
    marginHorizontal: 8,
    marginVertical: 10
  },
  text: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '500',
  },
})
