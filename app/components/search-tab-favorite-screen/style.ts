/* eslint-disable */
import { Dimensions, StyleSheet } from 'react-native'
import { responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import { ifIphoneX } from "react-native-iphone-x-helper"
import { color, radius, spacing, typography } from '@app/theme'
const width = Dimensions.get('window').width

const styles = StyleSheet.create({
  safeAreaView: {
    backgroundColor: "#f3f3f3",
    height: responsiveHeight(100),
    marginTop: -4,
    flex:1
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  viewListDistrict: {
    paddingLeft: 16,
    paddingRight: 8,
    paddingVertical: 20,
  },
  textName: {
    paddingVertical: 12,
    textAlign: 'center',
    color: '#333'
  },
  itemDistrict: {
    flex: 1,
    backgroundColor: '#f3f3f3',
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 3
  },
  background: {
    flex: 1,
    backgroundColor: color.primaryBackground,
    height: responsiveHeight(100),
    // ...ifIphoneX({
    //   paddingBottom: 89,
    // }, {
    //   paddingBottom: 60,
    // }),
  },
  textClose: {
    color: color.primary,
    fontSize: 14,
    fontWeight: '600',
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
    // marginRight: -40
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
    ...ifIphoneX({
      marginTop: 0
    }, {
      marginTop: 5
    }),
  },
  icArrowBack: {
    justifyContent: 'flex-start',
    marginLeft: 10,
    marginTop: 2
  },
  address: {
    marginLeft: 4,
    fontFamily: typography.normal,
    paddingLeft: 8,
    paddingVertical: 8,
    fontSize: 14,
    fontWeight: '400',
    color: '#333'
  },
  viewAddress: {
   // flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    borderRadius: 3,
    height: 36,
    backgroundColor: color.primaryBackground,
    marginVertical: 10
  },
  loadMore: {
    alignItems: 'center',
    marginTop: 10,
  },
  price: {
    color: '#CB1016',
    fontSize: 12,
    fontWeight: 'bold',
    marginTop: 6,
    textAlign: 'center'
  },
  productImg: {
    borderRadius: 5,
    height: '100%',
    resizeMode: 'cover',
    width: '100%'
  },
  productName: {
    color: '#333',
    fontSize: 13,
    height: 40,
    marginTop: 11,
    textAlign: 'center'
  },
  renderProduct: {
    alignItems: 'center',
    borderRadius: 4,
    justifyContent: 'flex-start',
    margin: 10,
    width: Dimensions.get('window').width / 2 - 30
  },
  storeName: {
    color: '#999999',
    fontSize: 11,
    marginTop: 4,
    textAlign: 'center'
  },
  textNonProduct: {
    color: '#333',
    flex: 1,
    fontSize: 14,
    marginTop: 30,
    textAlign: 'center'
  },
  titleBar: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 6
  },
  viewImage: {
    backgroundColor: '#fff',
    borderRadius: 5,
    height: 210,
    padding: 1,
    width: '100%'
  },
  containerItem: {
    flex: 1,
    backgroundColor: '#fff',
    // minHeight: 100,
    // marginLeft: 8
  },
  icSearch: {
    height: 24,
    margin: 6,
    marginLeft: spacing.small,
    resizeMode: "contain",
    width: 24,
  },
  icMap: {
    margin: 6,
    marginLeft: 14,
    resizeMode: "contain",
    width: 14,
    height: 14,
  },
  input: {
    flex: 1,
  },
  renderTopSection: {
    flexDirection: "row",
    height: 65,
    marginBottom: 5,
    backgroundColor: '#fff',
  },
  btnBack:{
    ...ifIphoneX({
      marginTop: 18,
    }, {
      marginTop: 20,
    }),
  marginLeft: spacing.small,
    width:24
  },

  inputStyle: {
    marginTop: 10,
    flex: 1,
    alignItems: "center",
    backgroundColor: "#f3f3f3",
    // borderColor: "#F4F4F4",
    borderRadius: 25,
    // borderStyle: "solid",
    // borderWidth: 1,
    flexDirection: "row",
    height: 44,
    justifyContent: "center",
    marginHorizontal: 16
    // shadowColor: "#F4F4F4",
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 1,
    // shadowRadius: 10,
    // elevation: 1
  },
  searchInputTopBar: {
    // flex: 1,
    alignItems: "center",
    backgroundColor: "#f3f3f3",
    // borderColor: "#F4F4F4",
    borderRadius: 25,
    // borderStyle: "solid",
    // borderWidth: 1,
    width: width - 150,
    flexDirection: "row",
    height: 36,
    justifyContent: "center",
    marginHorizontal: 16
    // shadowColor: "#F4F4F4",
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 1,
    // shadowRadius: 10,
    // elevation: 1
  },
  searchInput: {
    flex: 1,
    flexDirection: "row",
  },
  textExit: {
    justifyContent: "center",
    marginTop: 31,
    marginLeft: 3,
    fontSize: 14,
    fontWeight: "600",
  },
  btnFilter:{
    flexDirection: 'row',
    alignItems: 'center',
  },
  icClose: {
    width: 22,
    height: 22,
    margin: 10,
  },
  contentText: {
    flexDirection: "column",
    justifyContent: "flex-start",
    alignItems: "flex-start",
    flex: 1,
  },
  rdkTopText: {
    fontSize: 14,
    paddingTop: 5,
    paddingLeft: 8,
    paddingRight: 8,
    fontWeight: "600",
    fontFamily: typography.normal,
    color: "#333"
  },
  rdkTopText1: {
    fontSize: 13,
    color: "#8f9bb3",
    paddingRight: 40,
    fontFamily: typography.normal,
    marginTop: 5
  },
  rdkTopImage: {
    width: 100,
    height: 100,
    alignSelf: "center",
    resizeMode: "cover",
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
  },
  rdkTop: {
    flex: 1,
    flexDirection: "row",
    height: 100,
    width: "100%",
    backgroundColor: "#fff",
    borderRadius: 8,
    marginBottom: 15,
  },
  pointsStar: {
    marginLeft: 5,
    marginTop: 2,
    fontSize: 12,
    color: "#333"
  },
  kilometer: {
    alignItems: "flex-end",
    fontSize: 12,
    textAlign: "right",
    color: "#8f9bb3"
  },
  flatListContainer: {
    marginTop: 8 ,
    // marginBottom: 70,
    flex: 1,
    width: responsiveWidth(100),
    backgroundColor: '#fff',
    marginLeft: 8
  },
  viewBtnBack: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tabBar: {
    // Remove border top on both android & ios
    backgroundColor: '#fff',
    borderTopColor: 'transparent',
    borderTopWidth: 0,
    elevation: 0,
    shadowColor: '#5bc4ff',
    shadowOffset: {
      height: 0,
    },
    shadowOpacity: 0,
    shadowRadius: 0,
  },
  tabBarText: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
  },
  textTitle: {
    ...ifIphoneX({
      marginTop: 18,
    }, {
      marginTop: 20,
    }),
    fontSize:20,
    fontWeight:'bold',
    color: "#333"
  }
})


export default styles
