import * as React from 'react'
import { StyleSheet, View } from 'react-native'
import { observer } from 'mobx-react-lite'
import FAB from 'react-native-fab'
import Icon from 'react-native-vector-icons/Ionicons'
import { color } from '@app/theme'

export interface FloatActionButtonProps {
  /**
   * An optional style override useful for padding & margin.
   */
  onPress:any
}

/**
 * Describe your component here
 */
const FloatActionButton = observer(function FloatActionButton(props: FloatActionButtonProps) {
  return (
    <View style={styles.actionButtonIcon}>
      <FAB buttonColor={color.primary} iconTextColor="#FFFFFF" onClickAction={ props.onPress } visible={true} iconTextComponent={<Icon name="cart"/>} />
    </View>
  )
})
export default FloatActionButton

const styles = StyleSheet.create({
  actionButtonIcon: {
    bottom: 100,
  },
})
