import * as React from 'react'
import { TextStyle, ViewStyle } from 'react-native'
import { Button, ButtonProps } from 'react-native-elements'
import { observer } from 'mobx-react-lite'
import { typography } from '@app/theme'
import { palette } from '@app/theme/palette'

const BUTTON: TextStyle = {
  alignItems: 'center',
  backgroundColor: palette.angry,
  borderRadius: 22,
  flexDirection: 'row',
  height: 55,
  justifyContent: 'center',
}

const BUTTON_ROUNDED: TextStyle = {
  alignItems: 'center',
  backgroundColor: palette.angry,
  borderRadius: 4,
  flexDirection: 'row',
  height: 55,
  justifyContent: 'center',
  fontFamily: typography.normal
}

const BUTTON_DISABLED: ViewStyle = {
  backgroundColor: '#e2e2e2'
}

const BUTTON_DISTABLE_TITLE: TextStyle = {
  color: '#333',
  fontFamily: typography.normal,
  fontSize: 14,
  fontWeight: 'bold'
}

const TITLE: TextStyle = {
  fontFamily: typography.normal,
  fontSize: 14,
  fontWeight: 'bold'
}

/**
 * Learn more here
 * https://react-native-elements.github.io/react-native-elements/docs/button
 */
interface ComponentProps {
  typeRadius?: string
}

export type TButtonProps = ButtonProps & ComponentProps;

export const TButton = observer((props: TButtonProps) => {
  const { buttonStyle, titleStyle }: any = props
  // @ts-ignore
  const radius = props?.typeRadius ? props?.typeRadius : 'rounded'
  const style = radius === 'rounded' ? { ...BUTTON_ROUNDED, ...buttonStyle } : { ...BUTTON, ...buttonStyle }
  return (<Button {...props} buttonStyle={style} disabledStyle={BUTTON_DISABLED} disabledTitleStyle={BUTTON_DISTABLE_TITLE} titleStyle={[TITLE, titleStyle]} />)
})

export default TButton
