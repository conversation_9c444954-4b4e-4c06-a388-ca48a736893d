import { SafeAreaView } from 'react-native-safe-area-context'
import React, { useState, useEffect } from 'react'
import {

  View,
  Text, TouchableOpacity, FlatList, StyleSheet,
} from 'react-native'

import { useNavigation } from '@react-navigation/native'
import { useStores } from '@app/models'
import { PlaceHolder, ButtonBack, EmptyData, LazyImage } from '@app/components'
import { observer } from 'mobx-react-lite'

import { color, typography } from '@app/theme'
import { useTranslation } from 'react-i18next'
import { Header } from 'react-native-elements'
import common, { linearGradientProps } from '@app/theme/styles/common'
import LinearGradient from 'react-native-linear-gradient'
import moment from 'moment-timezone'

export const ListWinnerScreen: React.FC = observer((props) => {
  const { t } : any = useTranslation()
  const { navigate } = useNavigation()
  const navigation = useNavigation()
  const [data, setData] = useState([])
  const goBack = () => navigation.goBack()
  const [refreshing, setRefreshing] = useState(false)
  const [loadMore, setLoadMore] = useState(false)
  const [page, setPage] = useState(1)
  const [isFetched, setIsFetched] = useState(true) // event view placeholder
  const { luckyWheelStore } = useStores()

  useEffect(() => {
    loadData().then(r => {
    })
  }, [])

  useEffect(() => {
    __DEV__ && console.log('useEffect', page)
    loadData().then(r => {
    })
  }, [page])

  // load more
  // useEffect(() => {
  //   __DEV__ && console.log('useEffect handleLoadMore', page)
  //   handleLoadMore()
  // }, [page, loadMore])

  /**
   * call Store
   */
  const loadData = async () => {
    const isLoadMore = page > 1
    if (!isLoadMore) {
      setIsFetched(true)
    }
    await luckyWheelStore.getListWinners()
    setData(luckyWheelStore.listWinners)
    setLoadMore(false)
    setRefreshing(false)
    setIsFetched(false)
  }

  const refreshData = async () => {
    // __DEV__ && console.log('AAAAAA')
    // await notificationStore.getNotification(1, false)
    await luckyWheelStore.getListWinners()
    setLoadMore(false)
    setRefreshing(false)
    setIsFetched(false)
  }

  /**
   * onRefresh
   */
  const onRefresh = () => {
    __DEV__ && console.log('onRefresh ', page)
    setRefreshing(true)
    if (page > 1) {
      setPage(1)
    } else {
      refreshData().then(r => {
      })
    }
  }
  /**
   * onLoadMore Data
   */
  const handleLoadMore = () => {
    // if (!loadMore) return
    // khi scroll dừng lại sẽ gọi vào hàm này
    // const totalPage = notificationStore.totalPage
    const totalPage = 1
    if (page < totalPage) {
      setPage(page + 1)
    }
    if (page === totalPage) {
      __DEV__ && console.log('No more data...')
      setLoadMore(false)
    }
  }
  /**
   * render Footer UI
   */

  const renderFooter = () => {
    const Spinner = require('react-native-spinkit')
    return loadMore === true ? (
      <View
        style={{
          marginTop: 10,
          alignItems: 'center',
        }}
      >
        <Spinner isVisible={true} size={40} type='ThreeBounce' color={color.primary}/>
      </View>
    ) : <Text style={{ padding: 16, color: color.primary, textAlign: 'center' }}>{t('No_more_data')}</Text>
  }

  const renderItem = ({ item, index }) => {
    console.log('item trung thuong', item.attributes)
    return (
      <View>
        <TouchableOpacity style={{ backgroundColor: item.watched == 0 ? '#fff' : '#ffffff', borderBottomWidth: 0.5, borderBottomColor: '#e2e2e2' }} onPress={() => {
        }}>
          <View style={styles.container}>
            <View style={styles.boxviewImage}>
              <View style={styles.viewImage}>
                <LazyImage style={styles.imageBooking} resizeMode='cover' source={{ uri: item?.attributes?.userAvatar }}></LazyImage>
              </View>
            </View>
            <View style={styles.containerComment}>
              <View>
                <View>
                  <View style={styles.viewContent}>
                    <Text numberOfLines={1} style={styles.textTitle}>{item?.attributes?.fullName}</Text>
                  </View>
                  <Text style={styles.textTime}>Ngày trúng thưởng: {moment(item.createAt).locale('vi').format('HH:mm:ss, MMMM YYYY')}</Text>
                  <Text numberOfLines={2} style={styles.textContentNoti}>{item?.attributes?.shippingAddress?.address}</Text>
                  <Text numberOfLines={2} style={styles.textQuaTrungThuong}>Quà trúng thưởng: {item?.attributes?.gift_shop?.data?.attributes?.productName}</Text>
                </View>
              </View>
            </View>
            {item.watched === 0 ? <View style={styles.dot}/> : null}
          </View>
        </TouchableOpacity>
      </View>
    )
  }

  return (<SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
    {/* <ButtonBack onPress={goBack} style={styles.icArrowBack}/> */}
    {/* <Text style={styles.textTitleTotal}>Tất cả thông báo</Text> */}
    <Header
      // statusBarProps={{ barStyle: 'light-content' }}
      // barStyle="light-content" // or directly
      leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
      centerComponent={{ text: t(t('Danh sách trúng thưởng')), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
      // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
      //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
      // </TouchableOpacity> : null}
      containerStyle={common.headerContainer}
      statusBarProps={{ barStyle: 'light-content' }}
      ViewComponent={LinearGradient}
      linearGradientProps={linearGradientProps}
    />
    {isFetched ? <PlaceHolder/> : <View style={styles.viewFlatlist}>{!data || !data.length
      ? <EmptyData title={''} message={'Chưa có người trúng thưởng'}/>
      : <FlatList
        showsVerticalScrollIndicator={false}
        data={data}
        initialNumToRender={10}
        refreshing={refreshing}
        onRefresh={onRefresh}
        keyExtractor={(item, index) => item?.attributes?.createdAt }
        renderItem={renderItem}
        extraData={data}
        onScrollBeginDrag={e => {
          __DEV__ && console.log('onScrollBeginDrag')
          setLoadMore(true)
        }}
        onMomentumScrollEnd={handleLoadMore}
        // ListFooterComponent={renderFooter}
      />}</View>
    }
  </SafeAreaView>)
})

const styles = StyleSheet.create({
  admin: {
    marginTop: 10
  },
  boxviewImage: {
    width: 90
  },
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    paddingVertical: 10
  },
  containerComment: {
    flex: 1,
    marginLeft: 8,
    marginRight: 16
    // flexDirection: "column"
  },
  dot: {
    backgroundColor: '#6dd400',
    borderRadius: 8,
    bottom: 12,
    elevation: 2,
    height: 8,
    left: 6,
    position: 'absolute',
    shadowColor: 'rgba(0, 0, 0, 0.15)',
    shadowOffset: {
      width: 0,
      height: 0
    },
    shadowOpacity: 1,
    shadowRadius: 5,
    width: 8
  },
  imageBooking: {
    borderRadius: 45,
    height: 90,
    width: 90,
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
    // height: responsiveHeight(100),
  },
  textContentNoti: {
    color: '#616161',
    fontSize: 14,
  },
  textQuaTrungThuong: {
    color: '#616161',
    fontSize: 14,
  },
  textTime: {
    color: '#616161',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginTop: 0,
  },
  textTitle: {
    color: '#333',
    fontFamily: typography.normal,
    fontWeight: 'bold',
    fontSize: 14,
  },
  viewContent: {
    flexDirection: 'row',
    paddingVertical: 5
  },
  viewFlatlist: {
    flex: 1,
    // height: responsiveHeight(100),
    // ...ifIphoneX({
    //   paddingBottom: 89,
    // }, {
    //   paddingBottom: 60,
    // }),
    // width: responsiveWidth(100)
  },
  viewImage: {
    alignItems: 'center',
    flex: 1,
    height: 90,
    width: 90,
  }
})
