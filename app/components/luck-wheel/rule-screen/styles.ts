import { StyleSheet, Dimensions } from 'react-native'
import { responsiveHeight } from 'react-native-responsive-dimensions'
import { typography } from '@app/theme'

const { width, height } = Dimensions.get('window')
const styles = StyleSheet.create({
  TitleText: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 26,
    fontWeight: 'bold',
    padding: 15,
    paddingBottom: 5
  },
  background: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  containerItem: {
    paddingTop: 16
  },
  contentText: {
    alignItems: 'flex-start',
    flexDirection: 'column',
    flex: 1,
    position: 'relative',
    justifyContent: 'space-between'
  },
  date: {
    alignSelf: 'center',
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 10
  },
  flatListContainer: {
    flex: 1,
    height: responsiveHeight(100),
    // ...ifIphoneX({
    //   paddingBottom: 89,
    // }, {
    //   paddingBottom: 60,
    // }),
    // width: responsiveWidth(100),
    // marginLeft: 15,
    // marginRight: 15
  },
  icArrowBack: {
    marginLeft: 10,
  },
  icStatus: {
    paddingHorizontal: 3,
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
  },
  orderId: {
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 12,
    fontWeight: 'bold',
    paddingHorizontal: 8
  },
  paymentMethod: {
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 12,
  },
  rdkTop: {
    flex: 1,
    flexDirection: 'row',
    height: 85,
    backgroundColor: '#fff',
    borderRadius: 8,
    borderBottomWidth: 2,
    borderBottomColor: '#f6f6f7',
    marginHorizontal: 15,
    marginBottom: 10,
    justifyContent: 'space-between'
  },
  rdkTopImage: {
    borderBottomLeftRadius: 8,
    borderTopLeftRadius: 8,
    height: 83,
    resizeMode: 'cover',
    width: 87
  },
  rdkTopText: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '600',
    paddingTop: 5
  },
  rdkTopText1: {
    color: '#46474D',
    fontFamily: typography.normal,
    fontSize: 12,
    height: 40,
    paddingLeft: 8,
    paddingRight: 10,
    paddingTop: 3
  },
  renderTitle: {
    flexDirection: 'column',
    paddingTop: 15
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  scene: {
    flex: 1
  },
  starRate: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 3,
    paddingHorizontal: 8,
    width: '100%'
  },
  tabBar: {
    // Remove border top on both android & ios
    backgroundColor: '#fff',
    borderTopColor: 'transparent',
    borderTopWidth: 0,
    elevation: 0,
    shadowColor: '#5bc4ff',
    shadowOffset: {
      height: 0,
    },
    shadowOpacity: 0,
    shadowRadius: 0,
  },
  tabBarText: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  viewStatus: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
    width: '100%',
  },
  viewTouchButtonTop: {
    marginTop: 3
  },
})
export default styles
