import { SafeAreaView } from 'react-native-safe-area-context'
import React, { useEffect, useState } from 'react'
import {
  ScrollView, useWindowDimensions,
  View
} from 'react-native'
import RenderHtml from 'react-native-render-html'
import { useNavigation } from '@react-navigation/native'
import { observer } from 'mobx-react-lite'
import styles from './styles'
import { ButtonBack, PlaceHolder, TButton } from '@app/components'
import { Header } from 'react-native-elements'
import common, { linearGradientProps } from '@app/theme/styles/common'
import LinearGradient from 'react-native-linear-gradient'
import { useStores } from '@app/models'
import AsyncStorage from '@react-native-async-storage/async-storage'

export const RuleContentScreen = observer((props) => {
  const { luckyWheelStore, profileStore } = useStores()
  const navigation = useNavigation()
  const goBack = () => navigation.goBack()
  const { width } = useWindowDimensions()
  const [htmlRuleContent, setHtmlRuleContent] = useState('')
  const [isAgreed, setIsAgreed] = useState(false)

  useEffect(() => {
    const findOne = luckyWheelStore.lastLuckyWheel
    setHtmlRuleContent(findOne?.attributes?.programRules || '')
    extracted()
  }, [])

  const handleAgree = () => {
    // Khi người dùng bấm "Đồng ý", lưu giá trị vào localStorage
    AsyncStorage.setItem('agreed', 'true')
    setIsAgreed(true)
    goBack()
  }

  async function extracted() {
    // Kiểm tra trong localStorage xem người dùng đã đồng ý chưa
    const agreed = await AsyncStorage.getItem('agreed')
    if (agreed === 'true') {
      setIsAgreed(true)
    }
  }

  return (
    <SafeAreaView style={styles.background} edges={['right', 'top', 'left']}>
      <Header
        // statusBarProps={{ barStyle: 'light-content' }}
        // barStyle="light-content" // or directly
        leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
        centerComponent={{ text: 'Thể lệ chương trình', style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
        // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
        //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
        // </TouchableOpacity> : null}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
        ViewComponent={LinearGradient}
        linearGradientProps={linearGradientProps}
      />
      <ScrollView showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
        {!luckyWheelStore ? <PlaceHolder/>
          : <View>
            <View style={{ flexGrow: 0, backgroundColor: '#fff', paddingBottom: 15, paddingHorizontal: 16, marginTop: 10 }}>
              <RenderHtml
                contentWidth={width}
                source={{ html: htmlRuleContent }}
                renderersProps={{
                  img: {
                    enableExperimentalPercentWidth: true
                  }
                }}
                ignoredTags={['script']}
                ignoredStyles={['font-family']}
              />
            </View>
            {!isAgreed && (
              <TButton
                typeRadius={'rounded'}
                title={'Đồng ý'}
                onPress={handleAgree}
                buttonStyle={{ margin: 16 }}
                titleStyle={{ fontWeight: '500', fontSize: 16 }}
              />
            )}
          </View>
        }
      </ScrollView>

    </SafeAreaView>
  )
}
)
