import React, { useContext, useEffect, useRef, useState } from 'react'
import { observer } from 'mobx-react-lite'
import {
  View, FlatList, TouchableOpacity, StyleSheet, Image, Dimensions
} from 'react-native'
import { ButtonBack, Text } from '@app/components'

import { Api } from '@app/services/api'
import { ModalContext } from '@app/components/modal-success'
import { useTranslation } from 'react-i18next'
import { noImage } from '@app/assets/images'
import { useStores } from '@app/models'
import BottomSheet from '@gorhom/bottom-sheet'
import { useNavigation } from '@react-navigation/native'
import { responsiveHeight } from 'react-native-responsive-dimensions'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Header } from 'react-native-elements'
import LinearGradient from 'react-native-linear-gradient'
import { common, linearGradientProps } from '@app/theme/styles/common'
import { ProductGiftItem } from '@app/models/lucky-wheel-store/lucky-wheel-store'
import { numberFormat } from '@app/utils'

const { width } = Dimensions.get('window')
const api = new Api()

interface GiftItemProductScreenProps {
  index: number
}

export const GiftItemProductScreen = observer((props: GiftItemProductScreenProps) => {
  const { profileStore } = useStores()
  const { navigate, goBack } = useNavigation()
  const { showError, showSuccess, showCustomError, showCustomSuccess } = useContext(ModalContext)
  const { t } : any = useTranslation()
  const [isShowConfirm, setIsShowConfirm] = useState(false)
  const [item, setItem] = useState(null)
  const findDefaultIndex = profileStore.addressList.findIndex((x) => x.default === true)
  const [selectedAddressShip, setSelectedAddressShip] = useState(profileStore.addressList.length > 0 ? findDefaultIndex : -1)
  const [addressShipping, setAddressShipping] = useState(null)
  const { luckyWheelStore } = useStores()
  // ref
  const bottomSheetRef = useRef<BottomSheet>(null)

  useEffect(() => {
    luckyWheelStore.getListItems() // Call the store action when the component mounts
  }, [])

  useEffect(() => {
    const addressList = profileStore.addressList
    if (addressShipping === null && selectedAddressShip === 0) {
      const find = addressList.find((x) => x.default === true)
      if (!find) {
        setAddressShipping(addressList[0])
      } else {
        setAddressShipping(find)
        setSelectedAddressShip(findDefaultIndex !== -1 ? findDefaultIndex : 0)
      }
    } else {
      setAddressShipping(addressList[selectedAddressShip])
    }
  }, [selectedAddressShip])

  const renderProduct = (data: any) => {
    const item: ProductGiftItem = data?.item
    const imgUrl = item?.attributes?.image?.data?.attributes.url
    return (
      <TouchableOpacity
        style={styles.renderProduct}>
        <View style={styles.viewImage}>
          <Image source={imgUrl ? { uri: imgUrl } : noImage} style={styles.imageSize} />
        </View>
        <View style={{ flex: 1, marginBottom: 10 }}>
          <Text numberOfLines={2}
            ellipsizeMode="tail"
            style={styles.rspTopViewText}>{item?.attributes?.productName}</Text>
          <Text style={styles.txtPoint}>Mã số: {item?.attributes?.idGiftCode}</Text>
          <Text style={styles.txtPoint}>Số lượng : {item?.attributes?.stockLuckyWheel}/suất</Text>
          <Text style={styles.txtPoint}>Trị giá: {numberFormat(item?.attributes?.priceLuckyWheel)}đ/suất</Text>

        </View>
        {/* <TouchableOpacity */}
        {/*  onPress={() => { */}
        {/*    setItem(item) */}
        {/*    __DEV__ && console.log(item) */}
        {/*    // setIsShowConfirm(true) */}
        {/*    // setTimeout(() => { */}
        {/*    //   bottomSheetRef?.current.expand() */}
        {/*    // }, 100) */}
        {/*  }} */}
        {/*  style={{ justifyContent: 'center' }}> */}
        {/*  <Text style={styles.textBtnAdd}>Đổi ngay</Text> */}
        {/* </TouchableOpacity> */}
      </TouchableOpacity>
    )
  }

  return (<SafeAreaView style={styles.safeAreaView} edges={['right', 'left']}>
    {/* <ButtonBack onPress={goBack} style={styles.icArrowBack}/> */}
    {/* <Text style={styles.textTitleTotal}>Tất cả thông báo</Text> */}
    <Header
      // statusBarProps={{ barStyle: 'light-content' }}
      // barStyle="light-content" // or directly
      leftComponent={<ButtonBack style={{ color: '#fff' }} onPress={goBack}/>}
      centerComponent={{ text: t(t('Danh sách quà tặng')), style: { color: '#333', fontWeight: 'bold', fontSize: 16 } }}
      // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
      //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
      // </TouchableOpacity> : null}
      containerStyle={common.headerContainer}
      statusBarProps={{ barStyle: 'light-content' }}
      ViewComponent={LinearGradient}
      linearGradientProps={linearGradientProps}
    />
    <View key={props.index} style={{ flex: 1, paddingLeft: 8, backgroundColor: '#fff' }}>
      <FlatList
        style={{ backgroundColor: '#fff' }}
        numColumns={2}
        data={luckyWheelStore.listItems}
        renderItem={renderProduct}
        // index={props.index}
        // ListHeaderComponent={props.header}
        keyExtractor={(item, index) => item.id + index.toString()}
      />
    </View>
  </SafeAreaView>)
})

const styles = StyleSheet.create({
  imageSize: {
    height: 170,
    width: '100%'
  },
  renderProduct: {
    backgroundColor: '#F7F7F7',
    borderRadius: 4,
    marginHorizontal: 4,
    marginVertical: 5,
    width: (width - 32) / 2,
  },
  rspTopViewText: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    margin: 8,
    textAlign: 'left',
    width: '90%'
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    height: responsiveHeight(100),
    marginTop: -4
  },
  txtPoint: {
    color: '#333',
    marginHorizontal: 8
  },
  viewImage: {
    borderRadius: 2,
    height: 170,
    padding: 4,
    resizeMode: 'cover',
    width: '100%'
  }

})
