import React, { useContext, useEffect, useRef, useState } from 'react'
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ImageBackground, useWindowDimensions, Dimensions,
} from 'react-native'
import Animated, { useSharedValue, useAnimatedStyle, runOnJS, withTiming } from 'react-native-reanimated'
import { useStores } from '@app/models'
import { Api } from '@app/services/api'
import { Modalize } from 'react-native-modalize'
import { ButtonBack, Loading, RenderBranch, TButton } from '@app/components'
import { common } from '@app/theme/styles/common'

import { SafeAreaView } from 'react-native-safe-area-context'
import { ifIphoneX } from 'react-native-iphone-x-helper'
import { SCREENS } from '@app/navigation'
import { useNavigation } from '@react-navigation/native'
import { ModalContext } from '@app/components/modal-success'
import { useTranslation } from 'react-i18next'
import ModalLuckySuccess from '@app/components/luck-wheel/modal-lucky-success'
import { responsiveWidth } from 'react-native-responsive-dimensions'
import { color } from '@app/theme'
import FastImage from 'react-native-fast-image'
import { icTheLe, icVongQuay, icDsTrungThuong, icQuaTang } from '@app/assets/images'
import { convert } from 'html-to-text'
import { isAndroid } from '@app/utils/chat/deviceInfo'
const { width } = Dimensions.get('window')
const optionConvertHtml = {
  wordwrap: 130, // Giới hạn độ dài dòng
  preserveNewlines: true, // Bảo toàn ký tự xuống dòng
  selectors: [
    { selector: 'ol', format: 'inline' },
    { selector: 'li', format: 'inline', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },
    { selector: 'br', format: 'inline', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },
    { selector: 'strong', format: 'inline' }
  ]
}

const LuckyWheelScreen = () => {
  const navigation = useNavigation()
  const [isSpinning, setIsSpinning] = useState(false)
  const [activeScreen, setActiveScreen] = useState('VongQuay') // To track which screen is active
  const rotation = useSharedValue(0)
  const [bgImageUrl, setBgImageUrl] = useState('')
  const [imageWheelUrl, setImageWheelUrl] = useState('')
  const [objWheel, setObjWheel] = useState(null)
  const modalizeAddressRef = useRef<Modalize>(null)
  const { navigate, goBack } = useNavigation()
  const { showError, showSuccess, showCustomSuccess, showModal, showCustomError } = useContext(ModalContext)
  const [isShowModalResult, setIsShowModalResult] = useState(false)
  const [modalResult, setModalResult] = useState(null)
  const { t } : any = useTranslation()
  const [cooldown, setCooldown] = useState(0) // Cooldown in seconds
  const [minSpins, setMinSpins] = useState(0)
  const [maxSpins, setMaxSpins] = useState(0)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [debounceDisabled, setDebounceDisabled] = useState(false) // For preventing spam clicks

  const [itemWin, setItemWin] = useState(null)
  const {
    width,
    height
  } = useWindowDimensions()
  const { luckyWheelStore, profileStore } = useStores()

  const findDefaultIndex = profileStore.addressList.findIndex((x) => x.default === true)
  const [selectedAddressShip, setSelectedAddressShip] = useState(profileStore.addressList.length > 0 ? findDefaultIndex : -1)
  const [addressShipping, setAddressShipping] = useState(null)
  const [idWinner, setIdWinner] = useState()

  useEffect(() => {
    console.log('useEffect selectedAddressShip', selectedAddressShip)
    const addressList = profileStore.addressList
    if (addressShipping === null && selectedAddressShip === 0) {
      const find = addressList.find((x) => x.default === true)
      if (!find) {
        setAddressShipping(addressList[0])
      } else {
        setAddressShipping(find)
        setSelectedAddressShip(findDefaultIndex !== -1 ? findDefaultIndex : 0)
      }
    } else {
      setAddressShipping(addressList[selectedAddressShip])
    }
  }, [selectedAddressShip])

  useEffect(() => {
    loadData()
    // getApiConfig()
  }, [])

  const loadData = async () => {
    await luckyWheelStore.getListItems()
    await luckyWheelStore.getLuckyWheels()

    const findOne = luckyWheelStore.lastLuckyWheel

    if (findOne && findOne.attributes) {
      setObjWheel(findOne)

      const bgImageUrl = findOne.attributes.backgroundWheel?.data?.attributes?.url
      const imageWheelUrl = findOne.attributes.wheelImage?.data?.attributes?.url
      if (bgImageUrl) {
        setBgImageUrl(bgImageUrl)
      } else {
        console.warn('Background image URL not found')
      }

      if (imageWheelUrl) {
        setImageWheelUrl(imageWheelUrl)
      } else {
        console.warn('Wheel image URL not found')
      }

      setMinSpins(findOne.attributes.minNumber)
      setMaxSpins(findOne.attributes.maxNumber)
    } else {
      console.warn('Lucky wheel not found or attributes are missing')
    }
  }

  const goLoginScreenRequired = () => {
    navigation.navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  const getApiConfig = async () => {
    const api = new Api()
    const rs = await api.getAppConfig()
    if (rs && rs?.data?.data.attributes) {
      // bottomSheetRef?.current.expand()
    }
  }

  const startSpinning = async () => {
    if (!profileStore.isSignedIn()) {
      goLoginScreenRequired()
      return
    }
    if (isSpinning || cooldown > 0 || debounceDisabled) {
      return // Prevents further execution
    }

    setDebounceDisabled(true) // Disable button for debounce time

    setTimeout(() => {
      setDebounceDisabled(false)
    }, Number(objWheel?.attributes?.speed || '3000')) // Adjust debounce delay as needed

    try {
      const api = new Api()
      // TODO: check theo logic truong trinh con han, sẽ gui id chua chuong trinh len
      const response = await api.checkMaxSpin(profileStore._id, objWheel?.id)
      console.log('responseresponseresponseresponse', response)
      // Assuming your API response format is correct
      if (response.kind === 'ok' && response.data) {
        const { canSpin, spinsUsed, spinsRemaining, message } = response.data

        // Optional: Handle the case when spins are exhausted
        if (!canSpin) {
          showCustomError('Thông báo', message)
          return
        } else {
          if (!isSpinning) {
            setIsSpinning(true)

            const randomDegree = Math.floor(Math.random() * 360) + 720 // 720 ensures at least 2 full spins
            rotation.value = withTiming(rotation.value + randomDegree, { duration: Number(objWheel?.attributes?.speed || '3000') }, () => {
              runOnJS(handleSpinEnd)() // Use runOnJS to call the JS function
            })
            // Set cooldown to 120 seconds (2 minutes)
            setCooldown(Number(objWheel?.attributes?.intervalMinutes || '0') * 60)
            // Start the cooldown timer
            const countdown = setInterval(() => {
              setCooldown(prev => {
                if (prev === 1) {
                  clearInterval(countdown)
                }
                return prev - 1
              })
            }, 1000)
          }
        }
      } else if (response.kind === 'cannot-connect') {
        showCustomError(t('FAIL'), 'Không thể kết nối dữ liệu')
      } else {
        console.error('Failed to fetch spin data:', response)
        showCustomError('Thông báo', 'Hệ thống đang bận, vui lòng thử lại sau.')
        // Handle API failure response accordingly
      }
    } catch (error) {
      console.error('Error checking spins:', error)
      // Handle errors from the API call
    }
  }

  const handleSpinEnd = () => {
    setIsSpinning(false) // Reset the spinning state
    // Generate a random number between minSpins and maxSpins
    // const randomSpinResult = __DEV__ ? 1 : Math.floor(Math.random() * (maxSpins - minSpins + 1)) + minSpins
    const randomSpinResult = Math.floor(Math.random() * (maxSpins - minSpins + 1)) + minSpins
    // Find the reward based on the randomSpinResult (assuming idGiftCode matches the random number)
    const itemSelected = luckyWheelStore.listItems.find(item => item.attributes.idGiftCode === randomSpinResult)
    console.log('randomSpinResultrandomSpinResultrandomSpinResult', randomSpinResult)
    if (itemSelected) {
      // Store the result and show the modal
      console.log('itemSelected: ', itemSelected)
      setItemWin(itemSelected)
      addHistory(true, itemSelected?.id)
      setModalResult(itemSelected)
      setIsShowModalResult(true)
    } else {
      // Update the spin count for today
      addHistory(false, itemSelected?.id)
      // If no reward found, show a custom error message
      showCustomError('Chúc bạn may mắn lần sau', 'Bạn đã quay vào ô không trúng thưởng\nCảm ơn bạn đã tham gia chương trình.')
    }
  }

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${rotation.value}deg` }],
    }
  }, [])

  const renderScreen = () => {
    switch (activeScreen) {
      case 'QuaTang':
        return (
          <View style={styles.centeredView}>
            <Text>Quà Tặng</Text>
          </View>
        )
      default:
        return (
          <View style={styles.container}>
            <Text style={styles.newsTitle}>{objWheel?.attributes?.title}</Text>
            <Animated.View style={[styles.wheelContainer, animatedStyle]}>
              { imageWheelUrl && <FastImage source={{ uri: imageWheelUrl }} style={styles.wheel} /> }
            </Animated.View>
            <TouchableOpacity onPress={startSpinning} style={styles.spinButton} disabled={isSpinning || cooldown > 0 || debounceDisabled}>
              <Text style={styles.spinButtonText}>
                {cooldown > 0 ? `Vui lòng đợi ${cooldown} giây` : 'Bấm để quay'}
              </Text>
            </TouchableOpacity>
            {/* <Spinner style={styles.spinner} isVisible={true} size={24} type='Bounce' color={color.primary}/> */}
          </View>
        )
    }
  }

  const addHistory = (result = false, giftId) => {
    const body = {
      user_id: profileStore._id,
      date: new Date().getTime(),
      result: result,
      fullName: profileStore.fullName,
      userAvatar: profileStore.avatarUser,
      email: profileStore.email,
      phone: profileStore.phoneNumber || profileStore.phone,
      vong_quay_may_man: objWheel?.id
    }
    // Only add gift_shop to the body if the result is true
    if (result && giftId) {
      body['gift_shop'] = giftId
    }
    luckyWheelStore.postResult({ data: body }).then(result => {
      console.log('resultresultresultresultresultresultresult', result)
      if (result?.data?.data.id) {
        setIdWinner(result?.data?.data.id)
      }
    }).catch(err => {
      console.log('resultresultresultresultresultresultresult', err)
    })
  }

  /**
   * handler button click nhan thuong
   */
  const nhanThuong = () => {
    setIsShowModalResult(false)
    modalizeAddressRef?.current.open()
  }

  /**
   * xử lý sự kiện button xác nhận
   */
  function confirmAddress() {
    try {
      setIsSubmitting(true)
      const addressList = profileStore.addressList
      const confirmAddress = addressShipping || addressList[0]
      if (addressShipping) {
        console.log('confirmAddress', confirmAddress)
        const body = {
          shippingAddress: confirmAddress
        }
        luckyWheelStore.putUpdateResult({ data: body }, idWinner).then(result => {
          showSuccess(t('THANHCONG'), t('Xác nhận địa chỉ thành công'))
          modalizeAddressRef?.current.close()
          setIsSubmitting(false)
        }).catch(err => {
          setIsSubmitting(false)
          console.log('updateAddress', err)
        })
      } else {
        setIsSubmitting(false)
        showCustomError(t('FAIL'), 'Vui lòng chọn địa chỉ nhận hàng')
      }
    } catch (e) {

    }
  }

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'left']}>
      <View style={styles.mainContainer}>
        { bgImageUrl ? <ImageBackground
          style={{ width: width, height: height + (isAndroid ? 40 : 0), overflow: 'hidden' }}
          source={{ uri: bgImageUrl }}>
          <ButtonBack style={{ color: '#fff', position: 'absolute', zIndex: 9999, top: 45, left: 15 }} onPress={goBack}/>
          {renderScreen()}
          <View style={[styles.menuContainer, { backgroundColor: objWheel?.attributes?.backgroundMenuHexColor || '#E10714' }]}>
            <TouchableOpacity onPress={() => setActiveScreen('VongQuay')} style={styles.menuItem}>
              <FastImage style={styles.menuItemIcon} source={icVongQuay}></FastImage>
              <Text style={[styles.menuText, activeScreen === 'VongQuay' && styles.activeMenuText]}>
                Vòng quay
              </Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => {
              navigate(SCREENS.luckyWheelRuleScreen)
            }} style={styles.menuItem}>
              <FastImage style={styles.menuItemIcon} source={icTheLe}></FastImage>
              <Text style={[styles.menuText, activeScreen === 'TheLe' && styles.activeMenuText]}>
                Thể Lệ
              </Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => {
              navigate(SCREENS.ListWinnerScreen)
            }} style={styles.menuItem}>
              <FastImage style={styles.menuItemIcon} source={icDsTrungThuong}></FastImage>
              <Text style={[styles.menuText, activeScreen === 'DSTrungThuong' && styles.activeMenuText]}>
                DS Trúng thưởng
              </Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => {
              navigate(SCREENS.GiftItemProductScreen)
            }} style={styles.menuItem}>
              <FastImage style={styles.menuItemIcon} source={icQuaTang}></FastImage>
              <Text style={[styles.menuText, activeScreen === 'QuaTang' && styles.activeMenuText]}>
                Quà Tặng
              </Text>
            </TouchableOpacity>
          </View>
        </ImageBackground> : <Loading/> }
      </View>
      <ModalLuckySuccess isVisible={isShowModalResult} message={'afaf'} title={'Xin chúc mừng'} itemSelected={modalResult} onPress={nhanThuong}/>
      <Modalize
        HeaderComponent={<View style={common.headerModal}>
          <ButtonBack onPress={() => { modalizeAddressRef?.current?.close() }} style={common.viewTouchButtonTop} />
          <Text style={common.textTitleHeader}>Thông tin quà tặng</Text>
        </View>}
        ref={modalizeAddressRef}
        adjustToContentHeight
        keyboardAvoidingBehavior={'padding'}
      >
        <View style={{ marginBottom: 1 }}>
          <View style={styles.rSp}>
            <View style={styles.rSpView}>
              { itemWin?.attributes?.image?.data?.attributes?.url && <FastImage source={{ uri: itemWin?.attributes?.image?.data?.attributes?.url }} style={styles.rSpViewImage} />}
            </View>
            <View style={styles.rSpView1}>
              <Text style={styles.rSpView1Text}>{itemWin?.attributes.productName}</Text>
              <View style={{ flexDirection: 'row' }}>
                <Text numberOfLines={2} style={styles.rSpView1Text1}>{convert(itemWin?.attributes.description, optionConvertHtml)}</Text>
              </View>
              {/* <View style={{ flexDirection: 'row' }}> */}
              {/*  <Text style={styles.rSpView1Text2}>Số điểm: {itemWin?.attributes.point}</Text> */}
              {/* </View> */}
            </View>
          </View>
        </View>
        <View style={styles.viewShippingAddress}>
          <View style={styles.viewTextLabelAddressUser}>
            <View style={styles.containerAddressUS}>
              <Text style={styles.titleLabel}>{t('Địa chỉ nhận hàng')}</Text>
              <Text onPress={() => { navigate(SCREENS.renderAddAddress) }} style={styles.textAddaddress}>{t('ADDADDRESS')}</Text>
            </View>
          </View>
          <View>
            <RenderBranch dataBranch={profileStore.addressList} selectedAddressIndex={(selectedAddressUserIndex) => {
              setSelectedAddressShip(selectedAddressUserIndex)
            }} refreshAddress={profileStore.refreshAddress}/>
          </View>
        </View>
        <View>
          <Text style={{ color: '#333', fontSize: 14, paddingHorizontal: 16 }}>Quà tặng chưa bao gồm phí vận chuyển Bạn sẽ phải thanh toán phí vận chuyển theo quy định của đối tác vận chuyển.
            Mọi thông tin xin liên hệ: BTC chương trình</Text>
          {/* <Text style={{ color: '#333', fontSize: 14, paddingHorizontal: 16, marginTop: 10 }}>0982876180</Text> */}
        </View>
        <View style={styles.fixedButton}>
          <TButton typeRadius={'rounded'} buttonStyle={{ width: (responsiveWidth(100) - 45) / 2, backgroundColor: color.primaryBackground }} titleStyle={{ fontSize: 16, fontWeight: '500', color: '#333' }}
            title={t('Hủy bỏ')} onPress={() => {
            modalizeAddressRef?.current.close()
            }}/>
          <TButton typeRadius={'rounded'} buttonStyle={{ width: (responsiveWidth(100) - 45) / 2 }} titleStyle={{ fontSize: 16, fontWeight: '500' }}
            title={t('Xác nhận')} onPress={confirmAddress} disabled={isSubmitting}/>
        </View>
      </Modalize>
    </SafeAreaView>
  )
}
const styles = StyleSheet.create({

  activeMenuText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  centeredView: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  container: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  containerAddressUS: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
  },
  fixedButton: {
    backgroundColor: '#fff',
    borderTopColor: '#E9E9E9',
    borderTopWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 30,
    marginBottom: 30,
    padding: 15
  },
  mainContainer: {
    backgroundColor: '#FFFFFF',
    flex: 1,
  },
  menuContainer: {
    // borderTopColor: '#E0E0E0',
    // borderTopWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
    // paddingVertical: 60,
    height: 60,
    marginHorizontal: 15,
    marginBottom: 60,
    borderRadius: 10
  },
  menuItem: {
    paddingVertical: 10,
    alignItems: 'center'
  },
  menuItemIcon: {
    width: 24,
    height: 24
  },
  menuText: {
    color: 'white',
    fontSize: 12,
    marginTop: 5
  },
  newsTitle: {
    color: '#fff',
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 30,
    paddingHorizontal: 16,
    textAlign: 'left',
  },
  rSp: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    paddingBottom: 20,
    paddingHorizontal: 16,
    paddingTop: 15
  },
  rSpView: {
    alignItems: 'center',
    backgroundColor: '#f3f3f3',
    height: 82,
    justifyContent: 'center',
    padding: 2,
    width: 82
  },
  rSpView1: {
    flex: 1,
    paddingLeft: 20
  },

  rSpView1Text: {
    color: '#333',
    fontSize: 14,
    marginBottom: 10
  },

  rSpView1Text1: {
    color: '#333',
    fontSize: 13,
    marginTop: 10
  },
  rSpViewImage: {
    height: '100%',
    resizeMode: 'contain',
    width: '100%'
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: 0
    // height: responsiveHeight(100),
  },
  spinButton: {
    backgroundColor: 'red',
    borderColor: '#fff',
    borderRadius: 10,
    borderWidth: 1,
    padding: 15
  },
  spinButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
    textTransform: 'uppercase'
  },
  textAddaddress: {
    color: color.primary,
    // fontFamily: typography.normal,
    fontSize: 14,
    textAlign: 'right',
  },
  titleLabel: {
    color: '#979797',
    fontSize: 14,
    fontWeight: '500',
    textTransform: 'uppercase'
  },
  viewDetailNoti: {
    marginBottom: 30,
    marginLeft: 15,
    marginRight: 15,
    marginTop: 10,
    ...ifIphoneX({
      paddingBottom: 120,
    }, {
      paddingBottom: 90,
    }),
    height: '100%'
  },
  viewShippingAddress: {
    backgroundColor: '#fff',
    marginBottom: 5,
    paddingHorizontal: 16
  },
  viewTextLabelAddressUser: {
    alignItems: 'center',
    // backgroundColor: '#edf1f7',
    flexDirection: 'row',
    // height: 30,
    marginTop: 15
  },
  wheel: {
    height: '100%',
    resizeMode: 'contain',
    width: '100%',
  },
  wheelContainer: {
    height: 300,
    marginBottom: 30,
    width: 300,
  }
})

export default LuckyWheelScreen
