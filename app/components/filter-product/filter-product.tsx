import * as React from 'react'
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native'
import { Modalize } from 'react-native-modalize'
import { useEffect, useRef, useState } from 'react'

import { ifIphoneX } from 'react-native-iphone-x-helper'
import { useTranslation } from 'react-i18next'
import { Slider } from 'react-native-elements'
import { ButtonBack } from '@app/components'
import { useStores } from '@app/models'
import { color, typography } from '@app/theme'
import CurrencyInput from 'react-native-currency-input'
import StarRating from 'react-native-star-rating'

export interface FilterProductProps {
  isOpen:boolean
  dataFilter?:any
  distance?:any
  filter?:any,
  onClose?:any,
  rateProduct?:any,
  priceRange?:any
  shipping?:any,
  valueShipping?: any
  valuePrice?:any
  valueRate?:any
  page?:any
  // onOk? : any
  // action?: ((event: GestureResponderEvent) => void) | undefined;
}

/**
 * Describe your component here
 */

export function FilterProduct(props: FilterProductProps) {
  const modalizeRef = useRef<Modalize>(null)
  const { t } : any = useTranslation()
  const { searchStore } = useStores()
  const [value, setValue] = useState(5)
  const [item, setItem] = useState(null)
  const [idChoose, setIdChoose] = useState(searchStore.typeSearch || 0)
  const [maxDistance] = useState(2000)
  const [selectPrice, setSelectPrice] = useState('')
  const [price, setPrice] = useState('')
  const [selectShipping, setSelectShipping] = useState('')
  const [shipping, setShipping] = useState('')
  const [selectRate, setSelectRate] = useState('')
  const [rate, setRate] = useState('')
  // const [selectRate, setSelectRate] = useState('')
  const [minPrice, setMinPrice] = useState('')
  const [maxPrice, setMaxPrice] = useState('')
  const [valueInputMax, setValueInputMax] = useState('')
  const [valueInputMin, setValueInputMin] = useState('')
  const [isActivePrice, setIsActivePrice] = useState(false)
  const [indexItemPrice, setIndexItemPrice] = useState(-1)
  const [isActiveShipping, setIsActiveShipping] = useState(false)
  const [indexItemShipping, setIndexItemShipping] = useState(-1)
  const [isActiveRate, setIsActiveRate] = useState(false)
  const [indexItemRate, setIndexItemRate] = useState(-1)

  useEffect(() => {
  }, [shipping])

  useEffect(() => {
  }, [price])

  // listening item price change

  useEffect(() => {
    if (!isActivePrice) { setIsActivePrice(true) }
  }, [indexItemPrice])

  useEffect(() => {
    if (isActivePrice) {
      setSelectPrice(selectPrice)
      setPrice(price)
    } else {
      setSelectPrice('')
      setPrice('')
    }
  }, [isActivePrice])

  // listening item rate change

  useEffect(() => {
    if (!isActiveRate) { setIsActiveRate(true) }
  }, [indexItemRate])

  useEffect(() => {
    if (isActiveRate) {
      setSelectRate(selectRate)
      setRate(rate)
    } else {
      setSelectRate('')
      setRate('')
    }
  }, [isActiveRate])

  // listening item shipping change

  useEffect(() => {
    if (!isActiveShipping) { setIsActiveShipping(true) }
  }, [indexItemShipping])

  useEffect(() => {
    if (isActiveShipping) {
      setSelectShipping(selectShipping)
      setShipping(shipping)
    } else {
      setSelectShipping('')
      setShipping('')
    }
  }, [isActiveShipping])

  // =========

  useEffect(() => {
    if (minPrice && maxPrice) {
      setPrice(minPrice + '-' + maxPrice)
    } else if (minPrice || maxPrice) {
      setPrice(minPrice || maxPrice)
    }
  }, [minPrice, maxPrice])

  const onOpen = () => {
    // __DEV__ && console.log('onOpen', props.isOpen)
    modalizeRef.current?.open()
  }
  const onClose = () => {
    // __DEV__ && console.log('onClose', props.isOpen)
    modalizeRef.current?.close()
  }

  const renderHeaderModalBranch: any = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onClose} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>{t('FILTER')}</Text>
      <TouchableOpacity
        onPress={() => {
          onClose()
          setTimeout(() => {
            props.distance(value)
            props.valueShipping(shipping)
            props.valueRate(rate)
            props.page(1)
            props.valuePrice(price.split(',').join(''))
            // props.onOk({ type: item?.id ?? -1, filter: item, distance: value || DEFAULT_DISTANCE, valueShipping: shipping || '', valuePrice: price.replaceAll(',', '') || '', valueRate: rate || '' })
            // console.log('sssss', price)
          }, 100)
        }}
      >
        <Text style={styles.textClose}>{t('APDUNG')}</Text>
      </TouchableOpacity>
    </View>
  )
  useEffect(() => {
    if (props.isOpen) {
      onOpen()
      // __DEV__ && console.log('useEffect', props.isOpen)
    }
  }, [props.isOpen])

  const renderFilter = () => {
    return (<View style={styles.viewContainer}>
      <View>
        {renderPriceRange()}
        {renderShippingPartner()}
        {renderRate()}
      </View>
      {/* {idChoose === 0 ? renderShippingPartner() : null} */}
      <Slider
        value={value}
        style={styles.slider}
        maximumValue={maxDistance}
        minimumValue={3}
        allowTouchTrack={true}
        step={1}
        maximumTrackTintColor={color.primary}
        minimumTrackTintColor={color.primary}
        thumbTintColor={color.primary}
        onValueChange={(value) => setValue(value)}
      />
      <Text>{t('FILTERDISTANCE')} {value} km</Text>
    </View>)
  }

  const renderPriceRange = () => {
    return (<View style={styles.sectionProductFilter}>
      <Text style={styles.label}>{t(`${props.priceRange.title}`)}</Text>
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 4 }}>
        <CurrencyInput
          style={styles.textInput}
          value={valueInputMin}
          onChangeValue={(v) => {
            setValueInputMin(v)
            setIsActivePrice(false)
          }}
          delimiter=","
          separator="."
          precision={0}
          onChangeText={(formattedValueMin) => {
            setMinPrice(formattedValueMin.split(',').join(''))
            console.log(formattedValueMin.split(',').join('')) // $2,310.46
          }}
        />
        <CurrencyInput
          style={styles.textInput}
          value={valueInputMax}
          onChangeValue={(v) => {
            setValueInputMax(v)
            setIsActivePrice(false)
          }}
          delimiter=","
          separator="."
          precision={0}
          onChangeText={(formattedValueMax) => {
            setMaxPrice(formattedValueMax.split(',').join(''))
            console.log(formattedValueMax.split(',').join('')) // $2,310.46
          }}
        />
      </View>
      {props?.priceRange?.data ? <View style={styles.renderItem}>
        {props.priceRange.data.map((item, index) => renderItemPriceRange(item, index))}
      </View> : null}

    </View>)
  }

  const renderShippingPartner = () => {
    return (<View style={styles.sectionProductFilter}>
      <Text style={styles.label}>{t(`${props.shipping.title}`)}</Text>
      {props?.shipping?.data ? <View style={styles.renderItem}>
        {props.shipping.data.map((item, index) => renderItemShippingPartner(item, index))}
      </View> : null}
    </View>)
  }

  const renderItemPriceRange = (item, index) => {
    const id = 'price_' + item.id
    return (<TouchableOpacity
      style={[styles.item, { borderColor: selectPrice === id && isActivePrice === true ? color.primary : '#f2f2f2', backgroundColor: selectPrice === id && isActivePrice === true ? '#fff' : '#f3f3f3' }]}
      onPress={() => {
        setValueInputMin('')
        setValueInputMax('')
        setTimeout(() => {
          setSelectPrice(id)
          setPrice(item.value)
          setIndexItemPrice(index)
          if (indexItemPrice === index) {
            setIsActivePrice(!isActivePrice)
          }
        }, 50)
      }
      }
    >
      <Text style={styles.textItem}>{item.name}</Text>
    </TouchableOpacity>)
  }

  const renderItemShippingPartner = (item, index) => {
    const id = 'shipping_' + item.id
    return (<TouchableOpacity
      style={[styles.item, { borderColor: selectShipping === id && isActiveShipping === true ? color.primary : '#f2f2f2', backgroundColor: selectShipping === id && isActiveShipping === true ? '#fff' : '#f3f3f3' }]}
      onPress={() => {
        setSelectShipping(id)
        setShipping(item.value)
        setIndexItemShipping(index)
        if (indexItemShipping === index) {
          setIsActiveShipping(!isActiveShipping)
        }
      }
      }
    >
      <Text style={styles.textItem}>{item.name}</Text>
    </TouchableOpacity>)
  }

  const renderRate = () => {
    return (<View style={styles.sectionProductFilter}>
      <Text style={styles.label}>{t(`${props.rateProduct.title}`)}</Text>
      {props?.rateProduct?.data ? <View style={styles.renderItem}>
        {props.rateProduct.data.map((item, index) => renderItemRate(item, index))}
      </View> : null}
    </View>)
  }

  const renderItemRate = (item, index) => {
    const id = 'rate_' + item.id
    return (<TouchableOpacity
      style={[styles.itemRate, { borderColor: selectRate === id && isActiveRate === true ? color.primary : 'transparent', backgroundColor: selectRate === id && isActiveRate === true ? '#fff' : '#f3f3f3' }]}
      onPress={() => {
        setSelectRate(id)
        setRate(item.value)
        setIndexItemRate(index)
        if (indexItemRate === index) {
          setIsActiveRate(!isActiveRate)
        }
      }
      }
    >
      {/* <Text style={styles.textItem}>{item.name}</Text> */}
      {item.value === '' ? <Text style={styles.textItem}>{item.name}</Text> : null}
      {item.value !== 0
        ? <StarRating
          fullStarColor={'#FFC107'}
          disabled={true}
          maxStars={parseInt(item.value)}
          rating={item.value}
          fullStar={'star'}
          iconSet={'FontAwesome'}
          starSize={14}
          starStyle={styles.customStar}
        /> : null}
    </TouchableOpacity>)
  }

  return (
    <>
      <Modalize
        HeaderComponent={renderHeaderModalBranch}
        ref={modalizeRef}
        adjustToContentHeight
        // snapPoint={405}
        // modalHeight={405}
        disableScrollIfPossible = {false}
        onClosed={() => { props.onClose(true) }}
        keyboardAvoidingBehavior={'padding'}
      >
        {renderFilter()}
      </Modalize>
    </>
  )
}

const styles = StyleSheet.create({
  customStar: {
    marginRight: 2,
    marginTop: 3
  },
  item: {
    borderRadius: 4,
    borderWidth: 0.5,
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: 4,
    paddingVertical: 10,
    width: '49%'
  },
  itemRate: {
    borderRadius: 4,
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: 4,
    paddingVertical: 10,
    textAlign: 'center',
    width: 90
  },
  label: {
    color: '#333',
    paddingBottom: 10,
    paddingTop: 15,
  },
  modal__header: {
    borderBottomColor: '#eee',
    borderBottomWidth: 0.5,
    flexDirection: 'row',
    marginHorizontal: 15,
    paddingVertical: 15,
    ...ifIphoneX({
      marginTop: 0
    }, {
      marginTop: 5
    }),
  },
  renderItem: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingBottom: 10
  },
  sectionProductFilter: {
    borderColor: '#e2e2e2',
    borderTopWidth: 1,
    // marginVertical: 4,
  },
  slider: {
    marginTop: 20
  },
  textClose: {
    color: color.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  textInput: {
    alignItems: 'center',
    borderColor: '#edf1f7',
    borderRadius: 4,
    borderWidth: 1,
    color: '#333',
    flexDirection: 'row',
    fontFamily: typography.normal,
    fontSize: 14,
    height: 40,
    paddingLeft: 10,
    width: '49%'
  },
  textItem: {
    color: '#333',
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    marginRight: -30,
    textAlign: 'center'

  },
  viewContainer: {
    marginLeft: 15,
    marginRight: 15,
    paddingBottom: 30
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  }

})
