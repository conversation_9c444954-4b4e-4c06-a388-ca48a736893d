import * as React from 'react'
import { View, Text, FlatList, TouchableOpacity } from 'react-native'
import styles from '../../screens/render-choose-service/styles'
import { useState } from 'react'
export interface RenderClassifyProps {
  /**
   * An optional style override useful for padding & margin.
   */
  chooseData: any
  selectedClassifyIndex: any
}

/**
 * Describe your component here
 */
export function RenderClassify(props: RenderClassifyProps) {
  const [selectedClassifyIndex, setSelectedClassifyIndex] = useState(0)
  const onSelectedAddress = (index) => {
    setSelectedClassifyIndex(index)
    props.selectedClassifyIndex(index)
  }
  const renderClassify = ({ item, index }) => {
    return (
      <TouchableOpacity onPress={() => {
        onSelectedAddress(index)
      }}>
        <View style={styles.renderItemListClassify}>
          {selectedClassifyIndex === index
            ? <View style={styles.classifyActive}>
              <Text style={styles.textClassify}>{item.name}</Text>
            </View>
            : <View style={{ backgroundColor: '#f3f3f3' }}>
              <Text style={styles.textClassify}>{item.name}</Text>
            </View>}
          {/* <Text style={[styles.textClassify, { fontWeight: selectedClassifyIndex === index ? 'bold' : 'normal' }]}>{item.name}</Text> */}
        </View>
      </TouchableOpacity>
    )
  }
  return (
    <FlatList
      horizontal={true}
      data={props.chooseData.classify}
      showsHorizontalScrollIndicator={false}
      keyExtractor={(item, index) => item._id + index}
      renderItem={renderClassify}
    />
  )
}
