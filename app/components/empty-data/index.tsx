import * as React from 'react'
import { Image, StyleSheet, View, ViewStyle, Dimensions, Text } from 'react-native'
import { emptyData } from '@app/assets/images'
import { typography } from '@app/theme'

export interface EmptyDataProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: ViewStyle
  title:any
  message:any
}

/**
 * Describe your component here
 */
export const EmptyData = (props: EmptyDataProps) => {
  return (
    <View style={[styles.container, { ...props.style }]}>
      <Image style={styles.image} resizeMode='contain' source={emptyData}/>
      <Text style={styles.title}>{props.title}</Text>
      <Text style={styles.message}>{props.message}</Text>
    </View>
  )
}
const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    height: Dimensions.get('window').height - 250,
    justifyContent: 'center',
  },
  image: {
    alignItems: 'center',
    height: 80,
    justifyContent: 'center',
    width: 80,
  },
  message: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginTop: 10,
    textAlign: 'center'
  },
  title: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 20,
    fontWeight: '600',
    letterSpacing: 0,
    textAlign: 'center'
  }
})
