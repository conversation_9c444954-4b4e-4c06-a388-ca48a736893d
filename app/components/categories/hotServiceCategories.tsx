import { <PERSON><PERSON><PERSON>, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useContext } from 'react'

import { ModalContext } from '@app/context'
import { useNavigation } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'

import { spacing, typography } from '@app/theme'
import { useStores } from '@app/models'
import FastImage from 'react-native-fast-image'
import { observer } from 'mobx-react-lite'
import { builderPathImage } from '@app/utils'
import { SCREENS } from '@app/navigation'

const HotServiceCategories = observer((props: any) => {
  const { t } : any = useTranslation()
  const { showSuccess } = useContext(ModalContext)
  const { searchStore } = useStores()
  const { navigate } = useNavigation()

  __DEV__ && console.log('render Categories HOME')
  const onClickCat = (item) => {
    __DEV__ && console.log(item)
    navigate(SCREENS.hotServiceScreen, { categoryId: item._id, name: item?.name })
  }

  const renderIconCategories = ({ item }) => {
    return (
      <TouchableOpacity
        onPress={() => onClickCat(item)}
      >
        <View style={styles.viewDanhMuc}>
          <FastImage style={styles.imageCat} source={{ uri: builderPathImage(item?.picture || '') }}/>
          <Text style={styles.itemName}>{item?.name}</Text>
        </View>
      </TouchableOpacity>
    )
  }

  return (<View>
    {props?.data?.length > 0 ? <View style={styles.sectionHomeProduct}>
      <View>
        <Text style={{
          flex: 1,
          flexDirection: 'row',
          color: '#333',
          fontSize: 17,
          fontWeight: '600',
          marginLeft: 15,
          marginTop: 15,
          fontFamily: typography.bold,
        }}>{t('Dịch vụ hot')}</Text>
        {/* <TouchableOpacity */}
        {/*  onPress={() => navigate(SCREENS.promotionDetailScreen, { id: homeStore._id })} */}
        {/*  style={styles.boxTop}> */}
        {/*  <Text style={styles.textTop}>{t('KHAMPHADV_xemtatca')}</Text> */}
        {/*  <Icon style={styles.iconViewMore} name={'chevron-forward-outline'}/> */}
        {/* </TouchableOpacity> */}
      </View>
      <ScrollView
        horizontal
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
      >
        <FlatList
          style={{ marginLeft: spacing.small }}
          scrollEnabled={false}
          contentContainerStyle={{ backgroundColor: '#fff' }}
          showsHorizontalScrollIndicator={true}
          // horizontal={true}
          data={props.data}
          numColumns={Math.ceil(props.data.length / 2)}
          keyExtractor={(e, i) => (i + 1).toString()}
          renderItem={renderIconCategories} />
      </ScrollView>
    </View> : null}
  </View>
  )
})

export default HotServiceCategories

const styles = StyleSheet.create({
  sectionHomeProduct: {
    backgroundColor: '#fff',
    marginTop: 5,
    // paddingBottom: 10
  },
  // imageCat: {
  //   borderColor: '#9E9E9E',
  //   borderRadius: 10,
  //   borderWidth: 0.5,
  //   flex: 1,
  //   height: 45,
  //   width: 45
  // },
  imageCat: {
    // borderRadius: 18,
    height: 45,
    width: 45
  },
  itemName: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 13,
    marginTop: 5
  },

  viewDanhMuc: {
    alignItems: 'center',
    flex: 1,
    height: 100,
    marginBottom: 0,
    paddingVertical: 8,
    width: 100
  }
})
