import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>View, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useContext } from 'react'

import { ModalContext } from '@app/context'
import { SCREENS } from '@app/navigation'
import { useNavigation } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'

import { color, spacing, typography } from '@app/theme'
import { useStores } from '@app/models'
import { DEFAULT_API_CONFIG } from '@app/services/api/api-config'
import FastImage from 'react-native-fast-image'
import { observer } from 'mobx-react-lite'
import { InAppBrowser } from 'react-native-inappbrowser-reborn'
import { getToken } from '@app/services'

const Categories = observer((props: any) => {
  const { t } : any = useTranslation()
  const { showSuccess } = useContext(ModalContext)
  const { searchStore, profileStore } = useStores()
  const { navigate } = useNavigation()

  __DEV__ && console.log('render Categories HOME')

  const openLink = async (link) => {
    if (link) {
      try {
        await InAppBrowser.close()
        await InAppBrowser.open(link, {
          toolbarColor: color.primary,
          dismissButtonStyle: 'close',
          preferredBarTintColor: color.primary,
          preferredControlTintColor: '#fff',
          readerMode: false,
          enableUrlBarHiding: false,
          enableDefaultShare: false,
          animated: true,
          showTitle: false,
          // modalPresentationStyle: 'formSheet',
          modalTransitionStyle: 'coverVertical',
          modalEnabled: false,
          enableBarCollapsing: false,
        })
      } catch (error) {
        Alert.alert(error.message)
      }
    }
  }

  const goLoginScreenRequired = () => {
    navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  const onClickCat = async (item) => {
    if (item.attributes.screen_nav_to == SCREENS.search) {
      // trường hợp màn search thì cần set thêm type
      searchStore.setTypeSearch(item.attributes.params.filterType)
      navigate(SCREENS.search, item.attributes.params)
    } else if (item.attributes.screen_nav_to) {
      if (item.attributes.screen_nav_to === 'SCREEN_NAME' && item.attributes.params?.screen) {
        // các màn còn lại thì nếu data có chọn tên màn thì chuyển màn + param tương ứng nếu có
        navigate(item.attributes.params.screen)
      } else if (item.attributes.screen_nav_to === 'BROWSER_IN_APP' && item.attributes.params?.url) {
        // các màn còn lại thì nếu data có chọn tên màn thì chuyển màn + param tương ứng nếu có
        if (item.attributes.params?.login && !profileStore.isSignedIn()) {
          goLoginScreenRequired()
        } else {
          const userToken = await getToken()
          navigate(SCREENS.webview, {
            url: item.attributes.params?.url + '?token=' + userToken,
            title: item.attributes.params?.title || ''
          })
        }
      } else {
        // các màn còn lại thì nếu data có chọn tên màn thì chuyển màn + param tương ứng nếu có
        navigate(item.attributes.screen_nav_to, item.attributes.params)
      }
    } else {
      // api ko set tên màn thì show chức năng đang đc xây dựng
      showSuccess(t('COMING_SOON'), t('FEATURE_IS_UPDATING'))
    }
  }

  const renderIconCategories = ({ item }) => {
    let icon = item?.attributes.icon.data.attributes.url
    if (item?.attributes.icon.data.attributes.provider === 'local') {
      icon = `${DEFAULT_API_CONFIG.url2}${item?.attributes.icon.data.attributes.url}`
    }
    return (
      <TouchableOpacity
        onPress={() => onClickCat(item)}
      >
        <View style={styles.viewDanhMuc}>
          <FastImage style={styles.imageCat} source={{ uri: icon }}/>
          <Text style={styles.itemName}>{item?.attributes.name}</Text>
        </View>
      </TouchableOpacity>
    )
  }

  return (<View>
    {props?.data?.length > 0 ? <View>
      <ScrollView
        horizontal
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
      >
        <FlatList
          style={{ marginLeft: spacing.small }}
          scrollEnabled={false}
          contentContainerStyle={{ backgroundColor: '#fff' }}
          showsHorizontalScrollIndicator={true}
          // horizontal={true}
          key={Math.ceil(props.data.length / 2)}
          data={props.data}
          numColumns={Math.ceil(props.data.length / 2)}
          keyExtractor={(e, i) => (i + 1).toString()}
          renderItem={renderIconCategories} />
      </ScrollView>
    </View> : null}
  </View>
  )
})

export default Categories

const styles = StyleSheet.create({

  // imageCat: {
  //   borderColor: '#9E9E9E',
  //   borderRadius: 10,
  //   borderWidth: 0.5,
  //   flex: 1,
  //   height: 45,
  //   width: 45
  // },
  imageCat: {
    // borderRadius: 18,
    height: 45,
    width: 45
  },
  itemName: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 13,
    marginTop: 5
  },

  viewDanhMuc: {
    alignItems: 'center',
    flex: 1,
    height: 100,
    marginBottom: 0,
    paddingVertical: 8,
    width: 100
  }
})
