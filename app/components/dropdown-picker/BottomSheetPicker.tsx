import * as React from 'react'
import {
  StyleSheet, TouchableOpacity, Text, View, ViewProps
} from 'react-native'

import { useEffect, useImperativeHandle, useRef, useState } from 'react'
import { ButtonBack, Loading, TTextInput } from '@app/components'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { Modalize } from 'react-native-modalize'
import { responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'
import { observer } from 'mobx-react-lite'

export interface BottomSheetPicker {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: any,
  headerText?: any,
  search?: boolean,
  items: any,
  onSelectItem: any
}

/**
 * Describe your component here
 */

export type BottomSheetPickerProps = BottomSheetPicker & ViewProps;

export const BottomSheetPicker = observer((props, ref: React.Ref<any>) => {
  // const [open, setOpen]: any = useState(props.open)
  const [items, setItems] = useState(props.items)
  const [lists] = useState(props.items)
  const bottomSheetRef = useRef<Modalize>(null)
  const [keySearch, setKeySearch] = useState('')
  const [loading, setLoading] = useState(true)

  useImperativeHandle(ref, () => {
    return {
      open: open,
      close: close
    }
  })

  const open = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
    }, 1000)
    bottomSheetRef?.current?.open()
  }

  const close = () => {
    bottomSheetRef?.current?.close()
  }

  useEffect(() => {
    const delayDebounce = setTimeout(() => {
      __DEV__ && console.log('keySearch', keySearch)
      if (keySearch) {
        setItems(lists.filter(item => item.label.toLowerCase().includes(keySearch.toLowerCase())))
      } else {
        setItems(lists)
      }
    }, 1000)
    return () => {
      clearTimeout(delayDebounce)
    }
  }, [keySearch])

  const renderItem = (item, index) => {
    return (
      <TouchableOpacity
        style={styles.itemRender}
        key={index}
        onPress={() => {
          if (index !== -1) {
            props.onSelectItem(item)
            bottomSheetRef?.current?.close()
          } else {
            props.onSelectItem(null)
            bottomSheetRef?.current?.close()
          }
        }}
      >
        <Text style={ index === -1 ? { color: 'red', marginTop: 10 } : {}}>{(item?.label + '' || '').trim()}</Text>
      </TouchableOpacity>
    )
  }

  return (
    <Modalize
      HeaderComponent={<View style={{ paddingTop: 15 }}>
        <ButtonBack onPress={() => { bottomSheetRef?.current.close() }} style={{ position: 'absolute', left: 16, top: 15, zIndex: 99999 }} />
        {props.headerText ? <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: 0 }}>
          <Text style={{ color: '#333', fontSize: 14 }}>{props.headerText}</Text>
        </View> : null}
        { props?.search && <TTextInput
          containerStyle={styles.textInputContainer}
          keyboardType='default'
          placeholderTextColor='#a0a0a0'
          placeholder={'Tìm kiếm'}
          value={keySearch}
          onChangeText={e => setKeySearch(e)}
          showClearButton={true}
          iconRightClick={() => { setKeySearch('') }}
          blurOnSubmit={false}
          removeClippedSubviews={true}
        />}
        <View style={{ marginTop: 15, height: 1, width: responsiveWidth(100), backgroundColor: '#f2f2f2' }}></View>
      </View>}
      ref={bottomSheetRef}
      modalHeight={responsiveHeight(80)}
      keyboardAvoidingBehavior={'padding'}
    >
      { loading ? <View style={{ marginTop: 30 }}><Loading/></View> : <View style={{ marginVertical: 20, marginBottom: useSafeAreaInsets().bottom + 30 }}>
        {items.map((item, index) => renderItem(item, index))}
        {renderItem({ label: 'Xóa chọn', value: 0 }, -1)}
      </View>}

    </Modalize>
  )
}, { forwardRef: true })

const styles = StyleSheet.create({
  itemRender: {
    // backgroundColor: '#f3f3f3',
    borderRadius: 4,
    color: '#333',
    flexDirection: 'row',
    // justifyContent: 'center',
    marginBottom: 16,
    marginHorizontal: 16,
  },
  textInputContainer: {
    height: 40,
    marginHorizontal: 16,
    marginTop: 15,
    width: responsiveWidth(100) - 32
  }
})
