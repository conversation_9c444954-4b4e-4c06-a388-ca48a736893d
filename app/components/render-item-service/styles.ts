import { Dimensions, StyleSheet } from 'react-native'
import { color } from '@app/theme'
const tab1ItemSize = (Dimensions.get('window').width - 30) / 5
const styles = StyleSheet.create({
  containerService: {
    backgroundColor: '#fff',
    borderBottomColor: color.primaryBackground,
    borderBottomWidth: 1,
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 20
  },
  imageService: {
    borderRadius: 4,
    height: 80,
    width: 80
  },
  textContent: {
    color: '#9d9d9d',
    fontSize: 12,
    fontWeight: '400',
    // width: 290
  },
  textPriceMinMax: {
    color: '#f3373a',
    fontSize: 14,
    fontWeight: '600',
    lineHeight: 16
  },
  textTitleService: {
    color: '#333',
    fontSize: 14,
    fontWeight: '400',
    width: '80%'
  },
  viewIcon: {
    bottom: 0,
    position: 'absolute',
    right: 0
  },
  viewImageService: {
    alignItems: 'center',
    borderRadius: 4,
    // height: tab1ItemSize,
    justifyContent: 'center',
    marginLeft: 0,
    padding: 2,
    backgroundColor: '#fff',
    borderColor: color.primaryBackground,
    borderWidth: 1
  },
  viewPrice: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  viewService: {
    flexDirection: 'column',
    flex: 1,
    justifyContent: 'space-between',
    marginLeft: 10
  },
})
export default styles
