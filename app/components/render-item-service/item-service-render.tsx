import React, { useEffect, useState } from 'react'
import { TouchableOpacity, View, Text } from 'react-native'
import Icon from 'react-native-vector-icons/Ionicons'
import styles from './styles'
import { LazyImage } from '@app/components'
import { formatMoney } from '@app/utils'
import { useStores } from '@app/models'
import { color } from '@app/theme'
import { BookingType } from '@app/constants/bookingType'

export interface ItemServiceProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: any
  item: any
  setChooseData: any
  bookingType: any
}

export const ItemService = (props: ItemServiceProps) => {
  const [priceMin, setPriceMin] = useState('')
  const [priceMax, setPriceMax] = useState('')
  const regex = new RegExp('VND', 'g')
  const { item } = props.item
  const { bookingStore } = useStores()
  const [itemPrices, setItemPrices] = useState([])
  const [itemPriceView, setItemPriceView] = useState([])

  const getChooseData = (item) => {
    return item?.classify?.length > 0
  }

  useEffect(() => {
    __DEV__ && console.log('useEffect  selectedClassifyIndex  chooseData')
    if (item) {
      // bookingStore.clearFields()
      setItemPriceView(null)
      // setItemPrices([])
      // setWeight(null)
      // setStrTypePet(null)
      setItemPrices(
        getChooseData(item) ? item.classify[0]?.data.map(item => {
          return { value: item.price, label: item.name, _id: item._id }
        }) : [{ value: formatMoney(item.price), label: 'Giá chung', _id: 'giachung' }]
      )
      setPriceMin(getChooseData(item) ? item.classify[0]?.data[0].price : formatMoney(item.price))
      setPriceMax(getChooseData(item) ? item.classify[0]?.data[item.classify[0]?.data.length - 1].price : '')
      setItemPriceView(getChooseData(item) ? item.classify[0]?.data : [{
        price: formatMoney(item.price),
        name: 'Giá chung'
      }])
    }
  }, [])

  const getNumberPrice = (value, replaceWith = '') => {
    const trimValue = value.replace(regex, replaceWith)
    // console.log('trimValue', trimValue)
    return trimValue
  }

  return (
    <TouchableOpacity onPress={() => {
      props.setChooseData(item)
    }}>
      <View style={styles.containerService}>
        <View
          style={styles.viewImageService}>
          <LazyImage style={styles.imageService} source={{ uri: item.image }}
            resizeMode='cover'/>
        </View>
        <View style={styles.viewService}>
          <Text numberOfLines={1} style={styles.textTitleService}>{item.name}</Text>
          <Text numberOfLines={3} style={styles.textContent}>{item.shortDes}</Text>
          <View style={styles.viewPrice}>
            { priceMin?.length > 1 ? <View>
              <Text
                style={styles.textPriceMinMax}>{priceMin ? getNumberPrice(priceMin, 'đ') : ''} {priceMax ? '-' : 'đ'} {priceMax ? getNumberPrice(priceMax, 'đ') : ''}</Text>
            </View> : <View></View>}
            { props.bookingType != BookingType.GAS && <TouchableOpacity onPress={() => {
              props.setChooseData(item)
            }}>
              <Icon name={'add-circle-outline'} size={26} color={color.primary}/>
            </TouchableOpacity>}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  )
}
