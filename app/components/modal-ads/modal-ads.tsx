import * as React from 'react'
import {
  ImageBackground,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
  TouchableNativeFeedback
} from 'react-native'
import { observer } from 'mobx-react-lite'
import { typography } from '../../theme'
import Icon from 'react-native-vector-icons/Ionicons'
import Modal from 'react-native-modal'
import { palette } from '@app/theme/palette'
import { responsiveWidth } from 'react-native-responsive-dimensions'
import { adsPopUpImage } from '@app/assets/images'

export interface ModalAdsProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: ViewStyle,
  isVisible: boolean,
  goBack:any
  callBackVisible?:any
  title:string,
  uriImage?: string
}

/**
 * Describe your component here
 */
export const ModalAds = observer(function ModalAds(props: ModalAdsProps) {
  return (
    <Modal isVisible={props.isVisible} style={styles.containerView}
      animationIn="zoomIn"
      animationOut="zoomOut"
      onBackdropPress={() => {
        props.callBackVisible()
      }}
    >
      <View style={styles.viewContainer}>
        <ImageBackground
          source={props.uriImage ? { uri: props.uriImage } : adsPopUpImage}
          style={styles.viewHeader}>
          {/* <Text style={styles.textTitle}>{props.title}</Text> */}
          { Platform.OS === 'ios' ? <TouchableOpacity onPress={() => { props.callBackVisible() }}>
            <Icon name={'close-circle'} size={30} color={'#fff'} style={styles.icon}/>
          </TouchableOpacity> : <TouchableNativeFeedback onPress={() => { props.callBackVisible() }}>
            <Icon name={'close-circle'} size={30} color={'#fff'} style={styles.icon}/>
          </TouchableNativeFeedback>}

        </ImageBackground>
      </View>
    </Modal>
  )
}
)
const styles = StyleSheet.create({
  containerDs: {
    alignItems: 'center',
    borderBottomColor: '#F4F4F4',
    borderBottomWidth: 1,
    borderStyle: 'solid',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingLeft: 15,
    paddingRight: 15,
    paddingVertical: 10,
  },
  containerView: {
    margin: 0
  },
  dsViewText: {
    color: 'rgba(0, 0, 0, 0.5)',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginTop: 9
  },
  icon: {
    position: 'absolute',
    right: 0,
    top: 0
  },
  selectPiker: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold'
  },
  textTitle: {
    color: palette.black,
    flex: 1,
    fontSize: 14,
    textAlign: 'center',
  },
  viewContainer: {
    backgroundColor: palette.white,
    marginHorizontal: responsiveWidth(100) - (responsiveWidth(100) - 60),
    padding: 2
  },
  viewHeader: {
    height: 370,
    width: '100%'
  },
  viewLabel: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  }
})
