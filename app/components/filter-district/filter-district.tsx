import * as React from 'react'
import { View, ViewStyle, Text, FlatList, TouchableOpacity } from 'react-native'
import { observer } from 'mobx-react-lite'
import styles from '@app/components/search-tab-rate-screen/style'
import { useState } from 'react'

export interface FilterDistrictProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: ViewStyle,
  isVisible?: boolean,
  data?: any
  onSelect?: any
  goBack?:any
  callBackVisible?:any
  title?:string,
  defaultValue?: string,
}

/**
 * Describe your component here
 */
export const FilterDistrict = observer(function FilterDistrict(props: FilterDistrictProps) {
  const [id, setId] = useState('')

  const listDistrict = ({ item, index }) => {
    return (
      <TouchableOpacity
        style={styles.itemDistrict}
        onPress={() => {
          setId(item._id)
          props.onSelect(item, index)
        }}
      >
        <Text style={styles.textName}>{item.label}</Text>
      </TouchableOpacity>
    )
  }

  return (
    <View>
      <View style={styles.viewListDistrict}>
        <FlatList
          showsVerticalScrollIndicator={false}
          // horizontal={true}
          numColumns={2}
          keyExtractor={(item, index) => item._id}
          data={props.data}
          renderItem={listDistrict}
          showsHorizontalScrollIndicator={false}
        />
      </View>
    </View>
  )
})
