import * as React from 'react'
import {
  FlatList,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native'
import { Text } from '../'
import Icon from 'react-native-vector-icons/Ionicons'

import { useEffect, useState } from 'react'
import Modal from 'react-native-modal'
import { typography, color } from '@app/theme'
import { responsiveWidth } from 'react-native-responsive-dimensions'
import { palette } from '@app/theme/palette'

export interface PickerSelectProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: ViewStyle,
  isVisible: boolean,
  data: any
  onSelect: any
  goBack:any
  callBackVisible?:any
  title:string,
  defaultValue?: string,
}

/**
 * Describe your component here
 */

export function PickerSelect(props: PickerSelectProps) {
  // const { t } : any = useTranslation()
  // const deviceWidth = responsiveWidth(20)
  // const deviceHeight = Platform.OS === 'ios'
  //   ? responsiveHeight(25)
  //   : require('react-native-extra-dimensions-android').get('REAL_WINDOW_HEIGHT')

  const [id, setId] = useState('')

  useEffect(() => {
    if (props.data) {
      const itemFind = props.data.find(x => x.label === props.defaultValue)
      if (itemFind) setId(itemFind._id)
    }
  }, [props.defaultValue])

  const rdItem = ({ item, index }) => {
    return (<TouchableOpacity key={index} onPress={() => {
      setId(item._id)
      props.onSelect(item, index)
    }} style={styles.containerDs}>
      <View style={{ width: '100%', flexDirection: 'column' }}>
        <View style={styles.viewLabel}>
          <Text style={styles.selectPiker}>{item?.label}</Text>
          {id === item._id ? <Icon name={'checkmark-outline'} size={24} color={color.primary} style={styles.dsImage1}/>
            : null }
        </View>
        {item?.value ? <Text style={styles.dsViewText}>{item?.value || ''}</Text> : null}
      </View>
    </TouchableOpacity>)
  }
  return (
    <Modal isVisible={props.isVisible} style={styles.containerView}
      animationIn="zoomIn"
      animationOut="zoomOut"
      // animationOutTiming={300}
      // animationInTiming={300}
      onBackdropPress={() => {
        props.callBackVisible()
      }}
    >
      <View style={styles.viewContainer}>
        <View style={styles.viewHeader}>
          <Text style={styles.textTitle}>{props.title}</Text>
          <TouchableOpacity onPress={() => { props.callBackVisible() }}>
            <Icon name={'close-circle'} size={24} color={'#d2d2d2'} style={styles.icon}/>
          </TouchableOpacity>
        </View>
        <FlatList
          data={props.data}
          extraData={props.data}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          keyExtractor={(item, index) => item._id + index.toString()}
          renderItem={rdItem} />
      </View>
    </Modal>
  )
}
const styles = StyleSheet.create({
  containerDs: {
    alignItems: 'center',
    borderBottomColor: '#F4F4F4',
    // borderBottomLeftRadius: 8,
    // borderBottomRightRadius: 10,
    borderBottomWidth: 1,
    borderStyle: 'solid',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingLeft: 15,
    paddingRight: 15,
    paddingVertical: 10,
  },
  containerView: {
    // alignItems: 'center',
    // flex: 1,
    // justifyContent: 'center'
    margin: 0,
    paddingVertical: 100
  },
  dsImage1: {
    // position: 'absolute',
    // right: 0,
    // top: 0
  },
  dsViewText: {
    color: 'rgba(0, 0, 0, 0.5)',
    fontFamily: typography.normal,
    fontSize: 14,
    letterSpacing: 0,
    marginTop: 9
  },
  icon: {
    paddingRight: 5
  },
  selectPiker: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: 'bold'
  },
  textTitle: {
    color: palette.black,
    flex: 1,
    fontSize: 14,
    textAlign: 'center',
  },
  viewContainer: {
    backgroundColor: palette.white,
    borderRadius: 4,
    marginHorizontal: responsiveWidth(100) - (responsiveWidth(100) - 60),
    paddingBottom: 20,
    padding: 10
  },
  viewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  viewLabel: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  }

}
)
