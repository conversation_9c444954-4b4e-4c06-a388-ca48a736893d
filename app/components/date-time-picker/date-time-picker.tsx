import * as React from 'react'
import { View, Text, TouchableOpacity, StyleSheet, Appearance } from 'react-native'
import { useEffect, useState } from 'react'
import moment from 'moment-timezone'
import { useTranslation } from 'react-i18next'
import Icon from 'react-native-vector-icons/Ionicons'
import SimpleToast from 'react-native-simple-toast/index'
import { color, typography } from '@app/theme'
import DatePicker from 'react-native-date-picker'
import AwesomeAlert from 'react-native-awesome-alerts'

const colorScheme = Appearance.getColorScheme()
const isDarkModeEnabled = colorScheme === 'dark'

export interface DateTimePickerProps {
  /**
   * An optional style override useful for padding & margin.
   */
  setStrDate:any
  setStrTime:any
  time:any
  isClear?:boolean
}

/**
 * Describe your component here
 */
export default function DateTimePickerCustom(props: DateTimePickerProps) {
  // const [showDatePicker, setShowDatePicker] = useState(false)
  // const [showTimePicker, setShowTimePicker] = useState(false)
  // const [strPiker, setStrPicker] = useState('')
  const [strDate, setStrDate] = useState(props.setStrDate)
  const [strTime, setStrTime] = useState(props.setStrTime)
  const [date, setDate] = useState(new Date())
  const [time, setTime] = useState(0)
  const { t } : any = useTranslation()
  const [show, setShow] = useState(false)

  useEffect(() => {
    __DEV__ && console.log('props.isClearrrrrrrrrrrrrr=>>>>>', props.isClear)
    if (props.isClear) {
      setStrDate(props.setStrDate)
      setStrTime(props.setStrTime)
      setDate(new Date())
      // setTime(null)
    }
  }, [props.isClear])

  // const hidePicker = () => {
  //   setShowDatePicker(false)
  //   setShowTimePicker(false)
  // }
  /**
   * select date
   * @param date
   */
  const handleConfirm = (date) => {
    __DEV__ && console.log('date=>>>>> ', date)
    const chooseDate = moment(new Date(date)) // choose date yyyy-mm-dd only
    const dateNow = moment(new Date())
    if (chooseDate.isSameOrBefore(dateNow)) {
      setTimeout(() => SimpleToast.show(t('Err_time_has_passed')), 500)
      setStrDate(t('Choose_time'))
      props.time(0)
      setDate(new Date())
      // setDate(null)
    } else {
      setDate(date)
      setStrDate(moment(date).format(' HH:mm   DD/MM/YYYY'))
      props.time(date)
    }
    // hidePicker()
    // const partDate = moment(date).format('DD/MM/YYYY')
    // // const isoString = date.toISOString().slice(0, 10)
    // const chooseDate = moment(new Date(date)) // choose date yyyy-mm-dd only
    // const dateNow = moment(new Date()) // date now yyyy-mm-dd only
    // if (chooseDate.isBefore(dateNow, 'day')) {
    //   setStrDate(t('CHONNGAYDEN'))
    //   setDate(null) // 2020-10-0
    //   setTime(0)
    //   setStrTime(t('SELECT_TIME'))
    //   props.time(0)
    //   setTimeout(() => SimpleToast.show(t('Err_time_has_passed')), 500)
    // } else {
    //   setStrDate(partDate)
    //   setDate(chooseDate) // 2020-10-06T
    //   if (time > 0) {
    //     const partTime = moment(time).format('HH:mm')
    //     const stringDateTime = `${chooseDate.format('YYYY-MM-DD')}T${partTime}`
    //     const dateChoose = moment(new Date(stringDateTime), 'h:mm:ss a')
    //     props.time(dateChoose)
    //   }
    //   // else {
    //   //   props.time(a)
    //   // }
    //   hidePicker()
    // }
  }
  /**
   * select time
   * @param time
   */
  // const handleConfirmTime = (time) => {
  //   hidePicker()
  //   validateDateTime(time)
  // }

  // const validateDateTime = (time) => {
  //   const partTime = moment(time).tz('Asia/Ho_Chi_Minh').format('HH:mm')
  //   if (validate.isEmpty(date)) {
  //     setStrTime(t('SELECT_TIME'))
  //     setTimeout(() => SimpleToast.show(t('SELECT_DATE')), 500)
  //   } else if (date) {
  //     const stringDateTime = `${date.format('YYYY-MM-DD')}T${partTime}:00+07:00`
  //     const dateChoose = moment(stringDateTime, 'h:mm:ss a').tz('Asia/Ho_Chi_Minh')
  //     const dateNow = moment(new Date(), 'h:mm:ss a').tz('Asia/Ho_Chi_Minh')
  //     // console.log('1', dateChooses)
  //     // console.log('2', dateNows)
  //     // const dateChoose = Date.parse(stringDateTime)
  //     // const dateNow = (new Date().getTime())
  //     console.log(')()()3', dateChoose.format())
  //     console.log(')()()4', stringDateTime)
  //     console.log(')()()5', time)
  //     console.log(')()()6', dateNow.format())
  //     // Alert.alert(`${dateChoose}`)
  //     if (dateChoose.isSameOrBefore(dateNow)) {
  //       setTime(0)
  //       setStrTime(t('SELECT_TIME'))
  //       props.time(0)
  //       setTimeout(() => SimpleToast.show(t('Err_time_has_passed')), 500)
  //       // SimpleToast.show(t('ERROR_SELECT_TIME'))
  //     } else {
  //       setTime(time.getTime())
  //       setStrTime(partTime)
  //       props.time(dateChoose)
  //       hidePicker()
  //     }
  //   }
  // }
  // const onShowDatePicker = () => {
  //   // setShowDatePicker(!showDatePicker)
  //   // setStrPicker(t('SELECT_DATE'))
  //   setShow(true)
  // }

  // const onShowTimePicker = () => {
  //   setShowTimePicker(!showTimePicker)
  //   setStrPicker(t('SELECT_TIME'))
  // }

  return (
    <View style={styles.viewContainerDatetime}>
      <TouchableOpacity style={styles.viewBtnDatetime} onPress={() => setShow(true)}>
        <View style={styles.viewIconLeft}>
          <Icon name={'time-outline'} size={20} color={color.primary} style={[styles.iconPiker, { marginRight: 5, marginLeft: 2 }]}/>
          <Text style={date ? styles.selectPiker : styles.strPiker}>{strDate}</Text>
        </View>
        <Icon name={'chevron-down-outline'} size={20} color={'#333'} style={styles.iconPiker}/>
      </TouchableOpacity>
      {/* <TouchableOpacity style={styles.viewBtntime} onPress={onShowTimePicker}> */}
      {/*  <View style={styles.viewIconLeft}> */}
      {/*    <Icon name={'time-outline'} size={24} color={color.primary} style={styles.iconPiker}/> */}
      {/*    <Text style={time ? styles.selectPiker : styles.strPiker}>{strTime}</Text> */}
      {/*  </View> */}
      {/*  <Icon name={'chevron-down-outline'} size={20} color={'#333'} style={styles.iconPiker}/> */}
      {/* </TouchableOpacity> */}

      {/* <DateTimePickerModal */}
      {/*  isVisible={showDatePicker} */}
      {/*  mode={'date'} */}
      {/*  locale="vi_VN" */}
      {/*  cancelTextIOS={t('CANCEL')} */}
      {/*  confirmTextIOS={t('XACNHAN')} */}
      {/*  headerTextIOS={strPiker} */}
      {/*  onConfirm={handleConfirm} */}
      {/*  onCancel={hidePicker} */}
      {/*  date={date ? new Date(date) : new Date()} */}
      {/*  isDarkModeEnabled={isDarkModeEnabled} */}
      {/* /> */}
      {/* <DateTimePickerModal */}
      {/*  isVisible={showTimePicker} */}
      {/*  mode={'time'} */}
      {/*  locale="vi_VN" */}
      {/*  cancelTextIOS={t('CANCEL')} */}
      {/*  confirmTextIOS={t('XACNHAN')} */}
      {/*  headerTextIOS={strPiker} */}
      {/*  onConfirm={handleConfirmTime} */}
      {/*  onCancel={hidePicker} */}
      {/*  date={time ? new Date(time) : new Date(new Date().getTime() + (30 * 60000))} */}
      {/*  isDarkModeEnabled={isDarkModeEnabled} */}
      {/* /> */}

      <AwesomeAlert
        show={show}
        showProgress={false}
        title={t('Choose_time')}
        closeOnTouchOutside={false}
        closeOnHardwareBackPress={false}
        showCancelButton={true}
        showConfirmButton={true}
        cancelText={t('CANCEL')}
        confirmText={t('XACNHAN')}
        confirmButtonColor={color.primary}
        cancelButtonTextStyle={[styles.textBtnStyles, { color: '#333' }]}
        cancelButtonStyle={[styles.btnStyles, { backgroundColor: '#f3f3f3' }]}
        confirmButtonStyle={styles.btnStyles}
        confirmButtonTextStyle={[styles.textBtnStyles]}
        customView={<DatePicker locale={'vi_VN'} date={date} onDateChange={setDate} mode="datetime" />}
        onCancelPressed={() => {
          setShow(false)
        }}
        onConfirmPressed={() => {
          setShow(false)
          setTimeout(() => { handleConfirm(date) }, 200)
        }}
      />
    </View>
  )
}
const styles = StyleSheet.create({
  btnStyles: {
    alignItems: 'center',
    borderRadius: 3,
    height: 40,
    justifyContent: 'center',
    minWidth: 100
  },
  iconPiker: {
    // marginLeft: 10,
  },
  selectPiker: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    // marginLeft: 10
  },
  strPiker: {
    color: '#A0A0A0',
    fontFamily: typography.normal,
    fontSize: 14,
    marginLeft: 10
  },
  textBtnStyles: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center'
  },
  viewBtnDatetime: {
    flexDirection: 'row',
    flex: 1,
    height: 44,
    borderRadius: 4,
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#edf1f7',
    alignItems: 'center',
    backgroundColor: color.primaryBackground,
    // marginBottom: 8,
    justifyContent: 'space-between',
    paddingHorizontal: 5
  },
  viewBtntime: {
    flexDirection: 'row',
    flex: 1,
    height: 44,
    borderRadius: 4,
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#edf1f7',
    alignItems: 'center',
    // marginLeft: 10,
    backgroundColor: color.primaryBackground,
    justifyContent: 'space-between',
    paddingHorizontal: 15
  },
  viewContainerDatetime: {
    // flexDirection: 'row',
    flex: 1,
  },
  viewIconLeft: {
    alignItems: 'center',
    flexDirection: 'row',
  },
})
