import React, { useEffect, useImperativeHandle, useState } from 'react'
import { observer } from 'mobx-react-lite'
import {
  View, FlatList, Text
} from 'react-native'

import styles from '../search-tab-rate-screen/style'
import { useTranslation } from 'react-i18next'

import { useNavigation } from '@react-navigation/native'
import { useStores } from '@app/models'
import { SCREENS } from '@app/navigation'
import { color } from '@app/theme'
import { EmptyData, PlaceHolder, StoreListItem } from '@app/components'
import { useAbortableEffect } from '@app/use-hooks'
import { DEFAULT_DISTANCE } from '@app/constants/configs'

/**
 * Describe your component here
 */
export const SearchTabRateScreen = observer((props: any, ref) => {
  const navigation = useNavigation()
  const { navigate } = useNavigation()
  const goBack = () => navigation.goBack()
  const { t } : any = useTranslation()
  const { searchStore } = useStores()
  const [refreshing, setRefreshing] = useState(false)
  const [loadMore, setLoadMore] = useState(false) // mark scrollEnd to load more
  const [isFetched, setIsFetched] = useState(true) // event view placeholder
  const [page, setPage] = useState(1)
  const [currentDistance, setCurrentDistance] = useState(DEFAULT_DISTANCE)
  const [typeShip, setTypeShip] = useState(props.typeShip || 1)

  // first load
  useAbortableEffect(() => {
    loadData({ page, type: props.type, distance: currentDistance, valueShipping: '', valuePrice: '', valueRate: '', typeShip: props.typeShip }).then(r => {})
  }, [page])

  useImperativeHandle(ref, () => {
    return {
      loadData: loadData
    }
  })

  useEffect(() => {
    if (props.filter) {
      setPage(1)
    }
  }, [props.filter])

  /**
   * call Store
   */

  const loadData = async ({ page, type, distance, valueShipping, valuePrice, valueRate, typeShip }) => {
    // const t = data.find(x => x.id === props.type)
    // setTitleFilter(t.name)
    setCurrentDistance(distance)
    if (page === 1) {
      setPage(1)
    }
    const isLoadMore = page > 1
    if (!isLoadMore) {
      setIsFetched(true)
      searchStore.clearFields()
    }
    await searchStore.searchRate(page, searchStore.keyword, isLoadMore, type, distance || DEFAULT_DISTANCE, '&sort=rating', valueShipping || '', valuePrice || '', valueRate || '', typeShip)
    setLoadMore(false)
    setRefreshing(false)
    setIsFetched(false)
  }

  const refreshData = async () => {
    searchStore.clearFields()
    const rating = '&sort=rating'
    setIsFetched(true)
    await searchStore.searchRate(1, props.keyword || '', false, props.type || '', currentDistance || DEFAULT_DISTANCE, rating, props.valueShipping || '', props.valuePrice || '', props.valuePrice || '', typeShip)
    setLoadMore(false)
    setRefreshing(false)
    setIsFetched(false)
  }

  /**
   * onRefresh
   */
  const onRefresh = () => {
    __DEV__ && console.log('onRefresh ', page)
    setRefreshing(true)
    if (page > 1) {
      setPage(1)
    } else {
      refreshData().then(r => {
      })
    }
  }
  /**
   * onLoadMore Data
   */
  const handleLoadMore = () => {
    // if (!loadMore) return
    // khi scroll dừng lại sẽ gọi vào hàm này
    __DEV__ && console.log('onMomentumScrollEnd')
    const totalPage = searchStore.totalPage
    if (page < totalPage) {
      setPage(page + 1)
    }
    if (page === totalPage) {
      __DEV__ && console.log('No more data...')
      setLoadMore(false)
    }
  }

  function gotoScreen(e, item) {
    if (props.type === 0) {
      navigate(SCREENS.productDetails, { id: e.id })
    } else {
      navigate(SCREENS.serviceDetail, { id: e, type: props.type })
    }
  }

  /**
   * render Footer UI
   */

  const renderFlatList = ({ item }) => (
    <StoreListItem type={props.type} item={item} onPress={(e) => {
      gotoScreen(e, item)
    }
    }/>
  )
  const renderFooter = () => {
    const Spinner = require('react-native-spinkit')
    return loadMore === true ? (
      <View
        style={{
          marginTop: 10,
          alignItems: 'center',
        }}
      >
        <Spinner isVisible={true} size={40} type='ThreeBounce' color={color.primary}/>
      </View>
    ) : <Text style={{ padding: 16, color: color.primary, textAlign: 'center' }}>{t('No_more_data')}</Text>
  }
  return (<View style={styles.flatListContainer}>
    {isFetched ? <PlaceHolder/> : <View>
      {!searchStore.dataSearchRate || !searchStore.dataSearchRate.length ? <EmptyData title={t('Không có dữ liệu')} message={t('Không có dữ liệu ở tỉnh thành bạn vừa chọn')}/> : <FlatList
        data={searchStore.dataSearchRate}
        initialNumToRender={10}
        refreshing={refreshing}
        numColumns={2}
        onRefresh={onRefresh}
        // keyExtractor={item => item.id + '2'}
        keyExtractor={(item, index) => index.toString()}
        renderItem={renderFlatList}
        extraData={searchStore.dataSearchRate}
        showsVerticalScrollIndicator={false}
        onScrollBeginDrag={e => {
          __DEV__ && console.log('onScrollBeginDrag')
          setLoadMore(true)
        }}
        onMomentumScrollEnd={handleLoadMore}
        ListFooterComponent={renderFooter}
      />}
    </View>
    }
  </View>
  )
}, { forwardRef: true })
