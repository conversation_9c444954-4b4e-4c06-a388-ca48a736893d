import * as React from 'react'
import { StyleSheet, View, ViewStyle } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Placeholder, PlaceholderLine, PlaceholderMedia, ShineOverlay } from 'rn-placeholder'
import { responsiveHeight, responsiveWidth } from 'react-native-responsive-dimensions'

export interface PlaceHolderHomeProps {
  /**
   * An optional style override useful for padding & margin.
   */
  style?: ViewStyle
}

/**
 * Describe your component here
 */
export function PlaceHolderHome(props: PlaceHolderHomeProps) {
  const PlaceholderComponent = (props) => {
    return <Placeholder
      {...props}
      Animation={ShineOverlay}
      style={styles.placeHolder}
      Left={props => (
        <PlaceholderMedia
          style={[
            props.style,
            {
              width: responsiveWidth(13),
              height: responsiveHeight(6),
            },
          ]}
        />
      )}
    >
      <PlaceholderLine style={{ marginTop: responsiveHeight(0) }} width={60}/>
      <PlaceholderLine style={{ marginTop: responsiveHeight(0) }} width={85}/>
      <PlaceholderLine width={40}/>
    </Placeholder>
  }
  const PlaceholderComponentTop = (props) => {
    return <Placeholder
      {...props}
      Animation={ShineOverlay}
      style={styles.plcTop}
      Left={props => (
        <PlaceholderMedia
          style={[
            props.style,
            {
              width: responsiveWidth(13),
              height: responsiveHeight(6),
            },
          ]}
        />
      )}
    >
    </Placeholder>
  }
  const RenderPlaceholders = () => {
    const placeholders = []
    const topPL = []
    for (let i = 0; i < 8; i++) {
      placeholders.push(PlaceholderComponent({ key: i + '_normal' }))
    }
    for (let i = 0; i < 5; i++) {
      topPL.push(PlaceholderComponentTop({ key: i + '_top' }))
    }
    return (<View>
      <Placeholder
        Animation={ShineOverlay}
        style={styles.plcHeader}
      >
        <PlaceholderLine style={{ marginTop: responsiveHeight(0) }} width={60}/>
        <PlaceholderLine style={{ marginTop: responsiveHeight(0) }} width={85}/>
        <View style={styles.top}>
          <PlaceholderMedia
            style={[
              props.style,
              {
                width: responsiveWidth(18),
                height: responsiveHeight(8),
              },
            ]}
          />
          <View>
            <PlaceholderLine width={responsiveWidth(450)} style={{ marginLeft: 15 }}/>
            <PlaceholderLine width={responsiveWidth(450)} style={{ marginLeft: 15 }}/>
          </View>
        </View>
      </Placeholder>
      <View style={{ marginTop: 42 }}>
        <View style={styles.center}>{topPL}</View>
        <View style={styles.center}>{topPL}</View>
      </View>
      <View style={styles.list}>{placeholders}</View>
    </View>)
    // return this.state.posts.map((e, i) => <PlaceholderComponent key={i}/>);
  }
  return (
    <SafeAreaView>
      <View style={styles.container}>
        <RenderPlaceholders/>
      </View>
    </SafeAreaView>

  )
}

const styles = StyleSheet.create({
  center: {
    flexDirection: 'row',
    marginLeft: 15,
    marginRight: 15
  },
  container: {
    backgroundColor: '#fff',
  },
  list: {
    marginTop: 53
  },
  placeHolder: {
    borderRadius: 4,
    marginHorizontal: 15,
    marginVertical: 6,
  },
  plcHeader: {
    borderRadius: 4,
    marginHorizontal: 15,
    marginVertical: 6,
  },
  plcTop: {
    borderRadius: 4,
    marginHorizontal: 6,
    marginVertical: 6,
  },
  top: {
    flexDirection: 'row',
  },
})
