import React, { useEffect, useImperativeHandle, useState } from 'react'
import { observer } from 'mobx-react-lite'
import {
  View, FlatList, Text
} from 'react-native'

import styles from '../search-tab-rate-screen/style'

import { useNavigation } from '@react-navigation/native'
import { useStores } from '@app/models'
import { SCREENS } from '@app/navigation'
import { color } from '@app/theme'
import { EmptyData, PlaceHolder, StoreListItem } from '@app/components'
import { useAbortableEffect } from '@app/use-hooks'
import { useTranslation } from 'react-i18next'
import { DEFAULT_DISTANCE } from '@app/constants/configs'

/**
 * Describe your component here
 */
export const SearchTabNearScreen = observer((props: any, ref) => {
  const { navigate } = useNavigation()
  const { searchStore } = useStores()
  const [refreshing, setRefreshing] = useState(false)
  const [loadMore, setLoadMore] = useState(false) // mark scrollEnd to load more
  const [isFetched, setIsFetched] = useState(true) // event view placeholder
  const [page, setPage] = useState(1)
  const [options, setOptions] = useState({ type: props.type, distance: DEFAULT_DISTANCE, valueShipping: '', valuePrice: '', valueRate: '' })
  const [currentDistance, setCurrentDistance] = useState(DEFAULT_DISTANCE)
  const [typeShip, setTypeShip] = useState(props.typeShip || 1)

  // const [distance, setDistance] = useState(20)
  // const [filter, setFilter] = useState(null)
  const { t } : any = useTranslation()

  useImperativeHandle(ref, () => {
    return {
      loadData: loadData
    }
  })

  // first load
  useAbortableEffect(() => {
    loadData({ page, type: props.type, distance: currentDistance, valueShipping: '', valuePrice: '', valueRate: '', typeShip: props.typeShip }).then(r => {})
  }, [page])

  useEffect(() => {
    if (props.filter) {
      setPage(1)
    }
  }, [props.filter])

  /**
   * call Store
   */

  const loadData = async ({ page, type, distance, valueShipping, valuePrice, valueRate, typeShip }) => {
    const isLoadMore = page > 1
    setCurrentDistance(distance)
    if (page === 1) {
      setPage(1)
    }
    if (!isLoadMore) {
      setIsFetched(true)
      searchStore.clearFields()
    }
    await searchStore.searchNear(page, searchStore.keyword, isLoadMore, type, distance || DEFAULT_DISTANCE, '', valueShipping || '', valuePrice || '', valueRate || '', typeShip)
    setLoadMore(false)
    setRefreshing(false)
    setIsFetched(false)
  }

  const refreshData = async () => {
    searchStore.clearFields()
    setIsFetched(true)
    await searchStore.searchNear(1, props.keyword || '', false, props.type || '', currentDistance || DEFAULT_DISTANCE, '', props.valueShipping || '', props.valuePrice || '', props.valueRate || '', typeShip)
    setLoadMore(false)
    setRefreshing(false)
    setIsFetched(false)
  }

  /**
   * onRefresh
   */

  const onRefresh = () => {
    __DEV__ && console.log('onRefresh ', page)
    setRefreshing(true)
    if (page > 1) {
      setPage(1)
    } else {
      refreshData().then(r => {
      })
    }
  }

  /**
   * onLoadMore Data
   */

  const handleLoadMore = () => {
    // if (!loadMore) return
    // khi scroll dừng lại sẽ gọi vào hàm này
    __DEV__ && console.log('onMomentumScrollEnd')
    const totalPage = searchStore.totalPage
    if (page < totalPage) {
      setPage(page + 1)
    }
    if (page === totalPage) {
      __DEV__ && console.log('No more data...')
      setLoadMore(false)
    }
  }

  function gotoScreen(e, item) {
    if (props.type === 0) {
      navigate(SCREENS.productDetails, { id: e.id })
    } else {
      navigate(SCREENS.serviceDetail, { id: e, type: props.type })
    }
  }

  /**
   * render Footer UI
   */

  const renderFlatList = ({ item }) => (
    <StoreListItem type={props.type} item={item} onPress={(e) => {
      gotoScreen(e, item)
    }
    }/>
  )

  const renderFooter = () => {
    const Spinner = require('react-native-spinkit')
    return loadMore === true ? (
      <View
        style={{
          marginTop: 10,
          alignItems: 'center',
        }}
      >
        <Spinner isVisible={true} size={40} type='ThreeBounce' color={color.primary}/>
      </View>
    ) : <Text style={{ padding: 16, color: color.primary, textAlign: 'center' }}>{t('No_more_data')}</Text>
  }

  return (
    <View style={styles.flatListContainer}>
      {isFetched ? <PlaceHolder/> : <View>
        {!searchStore.dataSearchNear || !searchStore.dataSearchNear.length ? <EmptyData title={t('Không có dữ liệu')} message={t('Không có dữ liệu ở tỉnh thành bạn vừa chọn')}/> : <FlatList
          data={searchStore.dataSearchNear}
          initialNumToRender={5}
          refreshing={refreshing}
          numColumns={2}
          onRefresh={onRefresh}
          // keyExtractor={item => item.id + '1'}
          keyExtractor={(item, index) => index.toString()}
          renderItem={renderFlatList}
          extraData={searchStore.dataSearchNear}
          showsVerticalScrollIndicator={false}
          onScrollBeginDrag={e => {
            __DEV__ && console.log('onScrollBeginDrag')
            setLoadMore(true)
          }}
          onMomentumScrollEnd={handleLoadMore}
          ListFooterComponent={renderFooter}
        />}
      </View>
      }
    </View>
  )
}, { forwardRef: true })
