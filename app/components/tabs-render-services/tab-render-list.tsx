import React, { useEffect } from 'react'
import { observer } from 'mobx-react-lite'
import {
  View, FlatList
} from 'react-native'
import { ItemService } from '@app/components/render-item-service/item-service-render'

interface TabRenderSpaProps {
  data: any,
  bookingType: any,
  header: any,
  index: string,
  onSelect: any
}
/**
 * Describe your component here
 */
export const TabRenderListScreen = observer((props: any) => {
  useEffect(() => {
    __DEV__ && console.log('props.tabRenderListScreen', props.data)
  }, [])
  return (
    <View key={props.index} style={{ flex: 1, backgroundColor: '#fff' }}>
      <FlatList
        data={props.data}
        renderItem={(item) => <ItemService item={item} setChooseData={(item) => {
          props.onSelect({ item, type: props.bookingType })
        }} bookingType={props.bookingType}/>}
        ListHeaderComponent={props.header}
        keyExtractor={(item, index) => item._id + '_' + props.bookingType}
      />
    </View>
  )
})
