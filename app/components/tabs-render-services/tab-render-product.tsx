import React from 'react'
import { observer } from 'mobx-react-lite'
import {
  View, FlatList
} from 'react-native'
import { StoreListItem } from '@app/components'
import { SCREENS } from '@app/navigation'
import { useNavigation } from '@react-navigation/native'

interface TabRenderSpaProps {
  index: string,
  header: any,
  bookingType: any,
  data: any
}
/**
 * Describe your component here
 */
export const TabRenderProductScreen = observer((props: TabRenderSpaProps) => {
  const { navigate } = useNavigation()

  function gotoScreen(e) {
    navigate(SCREENS.productDetails, { id: e._id })
  }

  const renderItemProduct = ({ item }) => (
    <StoreListItem type={props.bookingType} item={{ ...item, serviceName: item.name, id: item._id }} onPress={(e) => { gotoScreen(e) } }/>
  )

  return (
    <View key={props.index} style={{ flex: 1, paddingLeft: 8, backgroundColor: '#fff' }}>
      <FlatList
        style={{ backgroundColor: '#fff' }}
        numColumns={2}
        data={props.data}
        renderItem={renderItemProduct}
        ListHeaderComponent={props.header}
        keyExtractor={(item, index) => item._id + '_product'}
      />
    </View>
  )
})
