import React from 'react'
import { observer } from 'mobx-react-lite'
import {
  View, Text, TouchableOpacity
} from 'react-native'
import { useStores } from '@app/models'
import renderTabReview from '@app/screens/service-detail-screen/tabrender/tabreview/tabreview'
import RenderStar from '@app/screens/service-detail-screen/tabrender/RenderStar/renderStar'
import styles from '@app/screens/service-detail-screen/styles'
import { useTranslation } from 'react-i18next'
import { HFlatList, HScrollView } from 'react-native-head-tab-view'

interface TabRenderSpaProps {
  index: number,
  onPress?: any
}
/**
 * Describe your component here
 */
export const TabRenderReviewScreen = observer((props: TabRenderSpaProps) => {
  const { t } : any = useTranslation()
  const { serviceStore } = useStores()

  const renderHeaderTabReview = () => {
    return (<View>
      <RenderStar/>
      <View style={styles.viewtextService}>
        <Text style={styles.textComment}>{t('COMMENT')} ({serviceStore.totalComment})</Text>
        <TouchableOpacity style={styles.topShowall} onPress={props.onPress}>
          <Text style={styles.textTop}>{t('SEE_ALL')} {'>>'}</Text>
        </TouchableOpacity>
      </View>
    </View>)
  }

  return (
    serviceStore.topComment.length ? <HFlatList
      index={props.index}
      data={serviceStore.topComment}
      renderItem={renderTabReview}
      ListHeaderComponent={renderHeaderTabReview}
      keyExtractor={(item, index) => index.toString()}
    /> : <HScrollView scrollEnabled={false} index={props.index}>
      {renderHeaderTabReview()}
    </HScrollView>
  )
})
