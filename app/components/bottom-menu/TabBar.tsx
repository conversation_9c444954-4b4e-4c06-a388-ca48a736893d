import React, { useState } from 'react'
import {
  View,
  TouchableOpacity,
  Dimensions,
  Animated,
  StyleSheet,
} from 'react-native'
import { BottomTabBarProps } from '@react-navigation/bottom-tabs'
import { BottomMenuItem } from './BottomMenuItem'
import { color } from '@app/theme'
import { useSafeAreaInsets } from 'react-native-safe-area-context'

export const TabBar = ({
  state,
  descriptors,
  navigation,
}: BottomTabBarProps) => {
  const [translateValue] = useState(new Animated.Value(0))
  const totalWidth = Dimensions.get('window').width
  const tabWidth = totalWidth / state.routes.length

  // return null
  const focusedOptions = descriptors[state.routes[state.index].key].options
  __DEV__ && console.log('focusedOptions', focusedOptions)
  if (focusedOptions.tabBarVisible !== undefined && focusedOptions.tabBarVisible === false) {
    return null
  }

  return (
    <View style={[style.tabContainer, { width: totalWidth, height: 60 + useSafeAreaInsets().bottom, paddingBottom: useSafeAreaInsets().bottom }]}>
      <View style={{ flexDirection: 'row' }}>
        <Animated.View
          style={[
            style.slider,
            {
              transform: [{ translateX: translateValue }],
              width: tabWidth - 20,
            },
          ]}
        />

        {state.routes.map((route, index) => {
          const { options } = descriptors[route.key]
          const label =
            options.tabBarLabel !== undefined
              ? options.tabBarLabel
              : options.title !== undefined
                ? options.title
                : route.name

          const isFocused = state.index === index

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            })

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name)
            }
          }

          if (isFocused) {
            Animated.spring(translateValue, {
              toValue: index !== 2 ? index * tabWidth : -1000,
              velocity: 10,
              useNativeDriver: true,
            }).start()
          }

          const onLongPress = () => {
            navigation.emit({
              type: 'tabLongPress',
              target: route.key,
            })
          }

          return (
            <TouchableOpacity
              accessibilityRole="button"
              // accessibilityStates={isFocused ? ["selected"] : []}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              testID={options.tabBarTestID}
              onPress={onPress}
              onLongPress={onLongPress}
              style={{ flex: 1 }}
              key={index}
            >
              <BottomMenuItem
                tabIndex={index}
                tabBarLabel={label.toString()}
                isCurrent={isFocused}
              />
            </TouchableOpacity>
          )
        })}
      </View>
    </View>
  )
}

const style = StyleSheet.create({
  slider: {
    backgroundColor: color.primary,
    borderRadius: 8,
    height: 3,
    left: 10,
    position: 'absolute',
    top: 0,
  },
  tabContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    bottom: 0,
    elevation: 10,
    height: 60,
    position: 'absolute',
    shadowOffset: {
      width: 0,
      height: -1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4.0,
  }
})
