import React from 'react'
import { View, Text, StyleSheet, Image } from 'react-native'
import {
  iconNewsNormal4,
  iconNewsActive4,
  iconHomeActive4,
  iconHomeNormal4,
  iconUserNormal4,
  iconUserActive4, iconCartNormal4, iconCartActive4, iconMenuWallet
} from '@app/assets/images'
import { color } from '@app/theme'
import Icon from 'react-native-vector-icons/Ionicons'

const ACTIVE_COLOR = color.primary
const MENU_COLOR = '#bbbbbb'

type Props = {
  tabBarLabel: string;
  isCurrent?: boolean;
  tabIndex: number
};

const renderIcon = (tabIndex, isCurrent) => {
  let icon
  switch (tabIndex) {
    case 0:
      icon = <Image style={styles.iconImage} source={isCurrent ? iconHomeActive4 : iconHomeNormal4} />
      // iconName = 'home'
      break
    case 1:
      icon = <Image style={styles.iconImage} source={isCurrent ? iconNewsActive4 : iconNewsNormal4} />
      // iconName = 'shopping-bag'
      break
    case 2:
      icon = <Image style={styles.iconImageCenter} source={isCurrent ? iconMenuWallet : iconMenuWallet} />
      // iconName = 'book-open'
      break
    case 3:
      icon = <Image style={styles.iconImage} source={isCurrent ? iconCartActive4 : iconCartNormal4} />
      // iconName = 'bell'
      break
    case 4:
      icon = <Image style={styles.iconImage} source={isCurrent ? iconUserActive4 : iconUserNormal4} />
      // iconName = 'user'
      break
  }
  return icon
}

const renderIconName = (tabIndex, isCurrent) => {
  let iconName
  switch (tabIndex) {
    case 0:
      iconName = 'home'
      break
    case 1:
      iconName = 'receipt-outline'
      break
    case 2:
      iconName = 'calendar-outline'
      break
    case 3:
      iconName = 'settings-outline'
      break
    // case 4:
    //   iconName = 'user'
    //   break
  }
  return <Icon name={iconName} size={20} color={isCurrent ? color.primary : '#333'}/>
}

export const BottomMenuItem = ({ tabBarLabel, isCurrent, tabIndex }: Props) => {
  // const iconName = ''
  return (<View
    style={{
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
    }}
  >
    {renderIconName(tabIndex, isCurrent)}
    { tabIndex !== 10 && <Text style={[styles.label, { color: isCurrent ? ACTIVE_COLOR : MENU_COLOR }]}>{tabBarLabel}</Text>}
  </View>
  )
}

// export const BottomMenuItem = ({ tabBarLabel, isCurrent, tabIndex }: Props) => {
//   return (<View
//     style={{
//       height: '100%',
//       justifyContent: 'center',
//       alignItems: 'center',
//     }}
//   >
//     {renderIcon(tabIndex, isCurrent)}
//     <Text style={[styles.label, { color: isCurrent ? ACTIVE_COLOR : MENU_COLOR }]}>{tabBarLabel}</Text>
//   </View>
//   )
// }

const styles = StyleSheet.create({
  iconImage: {
    height: 24,
    resizeMode: 'contain',
    width: 24
  },
  iconImageCenter: {
    height: 40,
    marginBottom: 0,
    resizeMode: 'contain',
    width: 40
  },
  label: {
    fontSize: 12,
    marginTop: 4
  },
  // label2: {
  //   bottom: 10,
  //   fontSize: 12,
  //   position: 'absolute'
  // }
})
