
import { Dimensions, StyleSheet } from 'react-native'
import { color, spacing, typography } from '@app/theme'
import { palette } from '@app/theme/palette'

const freeShipIconRatio = 55 / 14
const saleIconRatio = 38 / 14

const styles = StyleSheet.create({
  btnBook: {
    // position: 'absolute',
    // right: 8,
    // bottom: 8,
    width: 90,
    height: 26,
    borderRadius: 3,
    backgroundColor: '#e5293e',
    alignItems: 'center',
    justifyContent: 'center'
  },
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 8,
    marginLeft: 8,
  },
  customStar: {
    marginRight: 2,
    marginTop: 3
  },
  discount: {
    backgroundColor: color.primary,
    borderRadius: 5,
    padding: 3,
    position: 'absolute',
    right: 3,
    top: 3
  },
  discountViewRow: {
    bottom: 2,
    flexDirection: 'row',
    left: 10,
    position: 'absolute'
  },
  distance: {
    color: color.primary,
    fontSize: 12
  },
  freeShipIc: {
    height: 15,
    marginRight: 4,
    width: 15 * freeShipIconRatio
  },

  image: {
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    height: 170,
    width: '100%'
  },
  pointStar: {
    alignSelf: 'center',
    color: '#333',
    fontSize: 12,
    marginBottom: 2,
    marginLeft: 5
  },
  priceSale: {
    color: '#9D9D9D',
    fontSize: 12,
    fontWeight: '400',
    marginBottom: 5,
    marginLeft: 8,
    textAlign: 'left',
    textDecorationLine: 'line-through'
  },
  productImg: {
    // borderRadius: 3,
    height: '100%',
    resizeMode: 'cover',
    width: '100%'
  },
  renderProduct: {
    borderRadius: 4,
    height: 360,
    marginBottom: 10,
    // marginLeft: 16,
    width: Dimensions.get('window').width / 2 - 20,
    backgroundColor: '#F7F7F7',
  },
  renderStar: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  rspTopViewText: {
    color: '#333',
    fontSize: 14,
    fontWeight: '400',
    height: 35,
    marginLeft: 8,
    marginVertical: 10,
    textAlign: 'left',
    width: '90%'
  },
  rspTopViewTextPrice: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 5,
    textAlign: 'left'
  },
  saleIcon: {
    height: 15,
    width: 15 * saleIconRatio
  },
  sectionRate: {
    // flexDirection: 'row',
    // justifyContent: 'space-between',
    marginHorizontal: 8
  },
  star: {
    color: '#ff8900',
    fontSize: 14,
  },
  textAddress: {
    color: '#555',
    fontFamily: typography.normal,
    fontSize: 12,
    marginTop: 8
  },
  textBook: {
    color: palette.white,
    fontFamily: typography.normal,
    fontSize: 12,
    letterSpacing: 0.94,
    textAlign: 'center',
    width: 87
  },
  textBtnAdd: {
    alignItems: 'center',
    borderColor: color.primary,
    borderRadius: 3,
    borderWidth: 1,
    color: color.primary,
    justifyContent: 'center',
    margin: 6,
    marginBottom: 8,
    padding: 8,
    textAlign: 'center'
  },
  textDiscount: {
    color: '#fff',
    fontSize: 12,
  },
  textName: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 14,
    fontWeight: '500'
  },
  textOldPrice: {
    color: '#9D9D9D',
    fontFamily: typography.normal,
    fontSize: 10,
    textDecorationLine: 'line-through'
  },
  textSaleOff: {
    color: color.primary,
    fontFamily: typography.normal,
    fontSize: 10,
    marginLeft: 8
  },
  viewDistance: {
    flexDirection: 'row',
    width: '100%'
  },
  viewImage: {
    borderRadius: 2,
    height: 170,
    padding: 4,
    resizeMode: 'cover',
    width: '100%'
  },
  viewItem: {
    backgroundColor: '#F7F7F7',
    borderRadius: 4,
    width: Dimensions.get('window').width / 2 - 20,
  },
  viewRate: {
    flexDirection: 'row',
    paddingVertical: 3
  },
  viewStar: {
    paddingRight: spacing.small,
    flexDirection: 'row',
    width: '100%',
    // flex: 1,
    height: 20,
    marginTop: 3
  },
})

export default styles
