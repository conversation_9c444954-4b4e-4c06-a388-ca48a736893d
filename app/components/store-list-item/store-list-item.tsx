import * as React from 'react'
import { useContext, useEffect, useState } from 'react'
import { Image, ImageStyle, Platform, Text, TextStyle, TouchableOpacity, View, ViewStyle } from 'react-native'
import { color, spacing, typography } from '../../theme'
import Icon from 'react-native-vector-icons/MaterialCommunityIcons'
import { palette } from '@app/theme/palette'
import { useTranslation } from 'react-i18next'
import { LazyImage, NearDistance } from '@app/components'
import { showLocation } from 'react-native-map-link'
import SimpleToast from 'react-native-simple-toast'
import styles from './styles'
import StarRating from 'react-native-star-rating'
import { freeShipIcon, saleIcon } from '@app/assets/images'
import { Api } from '@app/services/api'
import { ModalContext } from '@app/components/modal-success'
import { useStores } from '@app/models'
import { numberFormat } from '@app/utils/number'

const VIEWCONTEX: ViewStyle = {
  flexDirection: 'column',
  justifyContent: 'flex-start',
  alignItems: 'flex-start',
  flex: 1
}
const STARVIEW: ViewStyle = {
  paddingLeft: spacing.small,
  paddingRight: spacing.small,
  paddingBottom: 4,
  flexDirection: 'row',
  width: '100%'
}
const VIEWROW: ViewStyle = {
  flexDirection: 'row',
  alignItems: 'center',
  width: '100%'
}
const AVATARSTORE: any = {
  width: 100,
  height: 100,
  alignSelf: 'center',
  resizeMode: 'cover',
  borderTopLeftRadius: 8,
  borderBottomLeftRadius: 8
}
const ICMAP: ImageStyle = {
  marginVertical: 6,
  marginHorizontal: 5
  // resizeMode: 'contain',
}

const TEXT: TextStyle = {
  fontSize: 14,
  paddingTop: 5,
  paddingLeft: spacing.small,
  paddingRight: spacing.small,
  fontWeight: '600',
  fontFamily: typography.normal,
  color: '#333'
}
const TEXTADDRESS: TextStyle = {
  fontSize: 13,
  color: '#46474D',
  paddingRight: 40,
  fontFamily: typography.normal,
  marginTop: 5
}
const TEXTDISTANCE: TextStyle = {
  fontSize: 13,
  color: '#46474D',
  paddingLeft: 15,
  fontFamily: typography.normal,
  marginTop: 5
}
const STAR: TextStyle = {
  color: '#ff8900',
  fontSize: 14
}
const POINSTAR: TextStyle = {
  marginLeft: 5,
  fontSize: 12,
  color: '#333'
}
const TEXTBOOK: TextStyle = {
  width: 87,
  fontFamily: typography.normal,
  fontSize: 12,
  letterSpacing: 0.94,
  textAlign: 'center',
  color: palette.white
}
const BUTTONBOOK: ViewStyle = {
  position: 'absolute',
  right: 8,
  bottom: 8,
  width: 97,
  height: 25,
  borderRadius: 8,
  backgroundColor: '#e5293e',
  alignItems: 'center',
  justifyContent: 'center'
}
const CALCULATED: TextStyle = {
  fontSize: 13,
  color: '#46474D',
  paddingLeft: spacing.small,
  fontFamily: typography.normal,
  marginTop: 5
}

export interface StoreListItemProps {
  /**
   * An optional style override useful for padding & margin.
   */
  item: any
  onPress: any
  onPressFavorite?: any
  type?: any
  style?: any
}

/**
 * Describe your component here
 */

const api = new Api()

export const StoreListItem = React.memo((props: StoreListItemProps) => {
  const item = props.item
  const { t } : any = useTranslation()
  const [rateValue, setRateValue] = useState(0)
  const { showError, showSuccess, showCustomError, showCustomSuccess } = useContext(ModalContext)
  const distance = item?.calculated ? Number.parseFloat(String(item?.calculated / 1000)).toFixed(1) : parseFloat(item?.distance || 0.0)
  const { profileStore, searchStore } = useStores()
  const [isChecked, setIsChecked] = useState(false)

  async function onPressFavorite(item: any) {
    const rs = await api.addFavorite(item.serviceId)
    if (rs && rs.temporary) {
      showError(t('FAIL'), t('CANNOT_CONNECT'))
    } else if (rs && rs.data.error) {
      showError(t('FAIL'), t(`${rs.data.message}`))
    } else {
      setIsChecked(true)
      await searchStore.searchFavorite(1, false)
      // showSuccess(t('THANHCONG'), t('Đổi quà thành công'))
    }
  }

  async function onPressRemoveFavorite(item: any) {
    const rs = await api.removeFavorite(item.serviceId)
    if (rs && rs.temporary) {
      showError(t('FAIL'), t('CANNOT_CONNECT'))
    } else if (rs && rs.data.error) {
      showError(t('FAIL'), t(`${rs.data.message}`))
    } else {
      setIsChecked(false)
      await searchStore.searchFavorite(1, false)
      // showSuccess(t('THANHCONG'), t('Đổi quà thành công'))
    }
  }

  useEffect(() => {
    loadChecked(item?.usersFavorites || [])
  }, [])

  const loadChecked = (items = []) => {
    // __DEV__ && console.log('*******************', items)
    setIsChecked(items.includes(profileStore._id))
  }

  const RenderProduct = () => {
    const discount = (item.price - item.priceOld) / item.priceOld
    const percentDiscount = discount * 100
    return (
      <TouchableOpacity
        onPress={() => {
          props.onPress(item)
        }}
        style={styles.renderProduct}>
        <View style={styles.viewImage}>
          <LazyImage source={{ uri: item.thumbnail || item.image || item.picture }}
            style={{ width: '100%', height: 170 }} />
          {/* <LazyImage source={{ uri: item.image || item.thumbail || item.picture }} style={{ width: '100%', height: 170 }} ><View style={styles.discount}><Text style={styles.textDiscount}>-10%</Text></View></LazyImage> */}
          <View style={styles.discountViewRow}>
            {item.typeShip === 0 && <Image source={freeShipIcon} style={styles.freeShipIc}></Image>}
            {item.priceOld && <Image source={saleIcon} style={styles.saleIcon}></Image>}
          </View>
        </View>
        <View style={{ flex: 1 }}>
          <Text numberOfLines={2}
            ellipsizeMode='tail'
            style={styles.rspTopViewText}>{item?.serviceName}</Text>
          <View style={{ marginLeft: 8 }}>
            <Text style={styles.rspTopViewTextPrice}>{numberFormat(item.price)} đ</Text>
            {item.priceOld && item.priceOld > 0 && !isNaN(percentDiscount) && percentDiscount != 0 && <View style={{ flexDirection: 'row' }}>
              <Text numberOfLines={2} style={styles.textOldPrice}>{numberFormat(item.priceOld)} đ</Text>
              <Text numberOfLines={2} style={styles.textSaleOff}>{numberFormat(percentDiscount)}%</Text>
            </View>}
          </View>
          <View style={styles.sectionRate}>
            <Text style={styles.textAddress}>{item.storeName}</Text>
            {rateValue > 0 ? <View style={styles.renderStar}>
              <StarRating
                fullStarColor={'#FFC107'}
                disabled={true}
                maxStars={5}
                rating={rateValue}
                emptyStarColor={'#edf1f7'}
                emptyStar={'star'}
                fullStar={'star'}
                halfStar={'star-half-o'}
                iconSet={'FontAwesome'}
                starSize={10}
                // containerStyle={styles.startContainer}
                starStyle={styles.customStar}
                // selectedStar={(rating) => ratingCompleted(rating)}
              />
            </View> : <Text style={styles.textAddress}>Chưa có đánh giá</Text>}
          </View>
        </View>
        <View style={{ flexDirection: 'row' }}>
          <TouchableOpacity onPress={() => {
            props.onPress(item)
          }} style={{ justifyContent: 'center' }}>
            {/* <Text style={styles.textBtnAdd}>Thêm vào giỏ</Text> */}
            <Text style={styles.textBtnAdd}>Mua ngay</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    )
  }

  const RenderService = () => {
    return (
      <View style={styles.viewItem}>
        <TouchableOpacity
          onPress={() => props.onPress(item.id)}
        >
          <LazyImage style={styles.image} source={{ uri: item.thumbnail || item.image || item.picture }} />
        </TouchableOpacity>
        <View style={{ padding: 10 }}>
          <TouchableOpacity onPress={() => props.onPress(item.id)}>
            <Text numberOfLines={1} style={styles.textName}>{item?.serviceName}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              if (item?.dist?.location?.coordinates) {
                const options: any = {
                  latitude: item.dist.location.coordinates[1],
                  longitude: item.dist.location.coordinates[0]
                }
                if (Platform.OS === 'ios') {
                  options.title = item.name
                }
                showLocation(options).then((r) => {
                })
              }
            }}
          >
            <Text numberOfLines={2} style={styles.textAddress}>{item?.address}</Text>
            <View style={styles.viewDistance}>
              {/* <Image style={ICMAP} source={IcMap}/> */}
              {/* <Text numberOfLines={1} style={styles.textAddress}>0912 222 222</Text> */}
              <View style={{ flexDirection: 'row', marginTop: 7 }}>
                <Icon name={'map-marker'} size={14} color={color.primary} />
                {distance && distance !== 'NaN' && distance !== '0.0' ? <View style={{ flexDirection: 'row' }}><Text
                  style={styles.distance}> {distance} km</Text></View> : null}
              </View>
            </View>
          </TouchableOpacity>
          <View style={styles.viewRate}>
            <View style={styles.viewStar}>
              {item.totalRate ? <Icon style={styles.star} name='star' /> : null}
              {item.totalRate > 0 ? <Text style={styles.pointStar}>{item?.totalRate}</Text> : null}
            </View>
          </View>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
            <TouchableOpacity style={styles.btnBook} onPress={() => props.onPress(item.id)}>
              <Text style={styles.textBook}> {t('BOOKNOW')}</Text>
            </TouchableOpacity>
            {isChecked
              ? <TouchableOpacity style={{}} onPress={() => onPressRemoveFavorite(item)}><Icon name={'heart'} size={20}
                color={color.primary} /></TouchableOpacity>
              : <TouchableOpacity style={{}} onPress={() => onPressFavorite(item)}><Icon name={'heart-outline'} size={20}
                color={color.primary} /></TouchableOpacity>}
          </View>

        </View>
      </View>
    )
  }

  // const RenderNews = () => {
  //   return (
  //     <View style={{ flexDirection: 'row', flex: 1 }}>
  //       <TouchableOpacity
  //         onPress={() => props.onPress(item.id)}
  //       >
  //         <LazyImage
  //           style={AVATARSTORE}
  //           source={{ uri: item.image || item.thumbail || item.picture }} />
  //       </TouchableOpacity>
  //       <View style={VIEWCONTEX}>
  //         <TouchableOpacity
  //           onPress={() => props.onPress(item.id)}
  //         >
  //           <Text numberOfLines={1} style={TEXT}>{item?.title}</Text>
  //         </TouchableOpacity>
  //         <TouchableOpacity
  //         >
  //           <View style={VIEWROW}>
  //             <Text numberOfLines={2} style={[TEXTADDRESS, { marginLeft: 8 }]}>{item?.description}</Text>
  //           </View>
  //         </TouchableOpacity>
  //         <TouchableOpacity style={BUTTONBOOK} onPress={() => props.onPress(item.id)}>
  //           <Text style={TEXTBOOK}> {t('Xem chi tiết')}</Text>
  //         </TouchableOpacity>
  //       </View>
  //     </View>
  //   )
  // }

  // List màn hình xem tất cả

  const RenderTopBranch = () => {
    return (
      <View style={{ flexDirection: 'row' }}>
        <TouchableOpacity
          onPress={() => props.onPress(item.id)}
        >
          <LazyImage
            style={AVATARSTORE}
            source={{ uri: item.image || item.thumbail || item.picture }} />
        </TouchableOpacity>
        <View style={VIEWCONTEX}>
          <TouchableOpacity
            onPress={() => props.onPress(item.id)}
          >
            <Text numberOfLines={1} style={TEXT}>{item?.serviceName}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              if (item?.storeInfo?.branches?.length > 0 && item.storeInfo.branches[0].lat && item.storeInfo.branches[0].lng) {
                const options: any = {
                  latitude: item.storeInfo.branches[0].lat,
                  longitude: item.storeInfo.branches[0].lng
                }
                if (Platform.OS === 'ios') {
                  options.title = item.name
                }
                showLocation(options).then((r) => {
                })
              } else {
                SimpleToast.show('Không có chỉ dẫn')
              }
            }}
          >
            {item?.screen === 'SERVICE_DETAIL' ? <View style={VIEWROW}>
              <Icon style={ICMAP} name={'map-marker'} size={16} color={'red'} />
              <Text numberOfLines={2} style={TEXTADDRESS}>{item?.storeInfo.address}</Text>
            </View> : null}
          </TouchableOpacity>
          <View style={VIEWROW}>
            <View style={{ flex: 1 }}>
              {distance !== 'NaN' ? <Text style={CALCULATED}>{distance} km</Text> : item?.branches &&
                <NearDistance item={item.branches} />}
              {item.totalRate ? <View style={STARVIEW}>
                <View style={{ flex: 1, flexDirection: 'row' }}>
                  <Icon style={STAR} name='star' />
                  <Text style={POINSTAR}>{item?.totalRate}</Text>
                </View>
              </View> : null}
            </View>
          </View>
          {item?.screen === 'SERVICE_DETAIL' && item?.storeInfo
            ? <TouchableOpacity style={BUTTONBOOK} onPress={() => props.onPress(item.id)}>
              <Text style={TEXTBOOK}> {t('BOOKNOW')}</Text>
            </TouchableOpacity> : item?.screen === 'BLOG_SCREEN'
              ? <TouchableOpacity style={BUTTONBOOK} onPress={() => props.onPress(item.id)}>
                <Text style={TEXTBOOK}> {t('Xem chi tiết')}</Text>
              </TouchableOpacity> : item?.screen === 'PRODUCT_DETAILS'
                ? <TouchableOpacity style={BUTTONBOOK} onPress={() => props.onPress(item.id)}>
                  <Text style={TEXTBOOK}> {t('Mua ngay')}</Text>
                </TouchableOpacity> : null}
        </View>
      </View>
    )
  }

  const switchScreen = () => {
    if (props.type === 'top-branch' && item?.screen === 'SERVICE_DETAIL' && item?.storeInfo) {
      return RenderTopBranch()
    }
    // if (props.type === 'news') {
    //   return RenderNews()
    // }
    if (props.type === 0) {
      return RenderProduct()
    }
    return RenderService()
  }

  return (
    <View style={
      // CONTAINER
      styles.container
    }>
      {switchScreen()}
      {/* {props.type === 'top-branch' && item?.screen === 'SERVICE_DETAIL' && item?.storeInfo ? RenderTopBranch() : props.type === 'news' ? RenderNews() : props.type === 0 ? RenderProduct() : RenderService()} */}
    </View>
  )
})
