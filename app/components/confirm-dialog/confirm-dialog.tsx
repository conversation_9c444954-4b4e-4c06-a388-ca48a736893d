import * as React from 'react'
import { StyleSheet, View, ViewStyle } from 'react-native'

import AwesomeAlert from 'react-native-awesome-alerts'
import { color } from '@app/theme'
import { useTranslation } from 'react-i18next'
export interface ConfirmDialogProps {
  /**
   * An optional style override useful for padding & margin.
   */
  isVisible: boolean
  style?: ViewStyle
  onClosed?: () => any
  onConfirm: () => any
  message: any
  title?: any
  cancelText?:string
  confirmText?:string
}

/**
 * Describe your component here
 */
export function ConfirmDialog(props: ConfirmDialogProps) {
  const { style } = props
  const { t } : any = useTranslation()
  return (
    <View>
      <AwesomeAlert
        useNativeDriver={true}
        show={props.isVisible}
        actionContainerStyle={styles.alertConfirm}
        showProgress={false}
        // title={props.title}
        message={props.message}
        closeOnTouchOutside={false}
        closeOnHardwareBackPress={false}
        showCancelButton={true}
        showConfirmButton={true}
        cancelText={props.cancelText}
        confirmText={props.confirmText}
        confirmButtonColor={color.primary}
        cancelButtonStyle={[styles.alertBtn, { backgroundColor: '#f3f3f3' }]}
        confirmButtonStyle={styles.alertBtn}
        cancelButtonTextStyle={[styles.btnTextStyles, { color: '#333' }]}
        confirmButtonTextStyle={styles.btnTextStyles}
        // titleStyle={{}}
        messageStyle={styles.textMes}
        onCancelPressed={() => {
          props.onClosed && props.onClosed()
        }}
        onConfirmPressed={() => {
          props.onConfirm && props.onConfirm()
        }}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  alertBtn: {
    borderRadius: 4,
    width: 100
  },
  alertConfirm: {
    // height: 80,
  },
  btnTextStyles: {
    fontSize: 14,
    fontWeight: '500',
    paddingVertical: 5,
    textAlign: 'center'
  },
  textMes: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
    padding: 10
  }
})
