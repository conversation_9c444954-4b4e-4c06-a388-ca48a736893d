import { load, remove, save } from '@app/utils/storage'
const CACHE_VERSION = 199

export const saveToken = async (token) => {
  await save(`token-${CACHE_VERSION}`, token)
}

export const getToken = async () => {
  return await load(`token-${CACHE_VERSION}`)
}

export const removeToken = async () => {
  await remove(`token-${CACHE_VERSION}`)
}

export const saveRocketChatData = async (data) => {
  // https://prnt.sc/wgmuse
  await save('rocketChatData', data)
}

export const removeRocketChatData = async () => {
  // https://prnt.sc/wgmuse
  await remove('rocketChatData')
}

export const getRocketChatLogged = async () => {
  return await load('rocketChatData')
}
