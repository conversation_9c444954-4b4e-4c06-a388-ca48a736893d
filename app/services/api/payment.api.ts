/* eslint-disable */
import { ApiResponse, ApisauceInstance, create } from "apisauce"
import { getGeneralApiProblem } from "./api-problem"
import { ApiConfig, DEFAULT_API_CONFIG } from './api-config'
import { getToken } from '@app/services'

/**
 * Manages all requests to the API.
 */
export class PaymentApi {
  /**
   * The underlying apisauce instance which performs the requests.
   */
  apisauce: ApisauceInstance

  /**
   * Configurable options.
   */
  config: ApiConfig

  token: string

  /**
   * Creates the api.
   *
   * @param config The configuration to use.
   */
  constructor(config: ApiConfig = DEFAULT_API_CONFIG) {
    this.config = config
  }

  /**
   * Sets up the API.  This will be called during the bootup
   * sequence and will happen before the first React component
   * is mounted.
   *
   * Be as quick as possible in here.
   */

  async setup(isAuth = true) {
    let headers = {
      Accept: "application/json"
    }

    // construct the apisauce instance
    this.token = await getToken()
    console.log('token global:', this.token)

    if (this.token !== null && isAuth) {
      headers["access-token"] = this.token
    }

    let options = {
      baseURL: this.config.url,
      timeout: this.config.timeout,
      headers: headers,
    }
    this.apisauce = create(options)
  }

  /**
   * xoá đơn hàng khi thanh toán lỗi sản phẩm
   * @param orderId
   */
  async deleteOrderById(paymentId: string): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/xoa-order.html/${paymentId}?output=json`,{})
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  /**
   * xoá đơn hàng khi thanh toán lỗi sản phẩm
   * @param orderId
   */
  async deleteOrderBookServiceById(orderId: string, typeBooking ): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/xoa-order-of-spa.html/${orderId}?output=json&type=${typeBooking}`,{})
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

}

