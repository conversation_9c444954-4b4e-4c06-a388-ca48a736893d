// Use this import if you want to use "env.js" file
const {
  API_URL,
  API_URL2,
  URL_PVCOMBANK,
} = require('../../config/env')
// Or just specify it directly like this:

/**
 * The options used to configure the API.
 */
export interface ApiConfig {
  url: string,
  url2: string,
  pviCpid: string, // PVI
  pviKey: string, // PVI
  apiBhPvi: string // PVI,
  pvcbAppId?: string // pvcb appId,
  pvcbAppCode?: string // pvcb appCode,
  pvcbKeyTracking?: string // pvcb keyTracking,
  pvcbIsProd?: boolean // pvcb is prod
  url_pvcombank: string,
  apiBhBic: string,
  bicDeviceId: string,
  bicUserName: string,
  bicPassword: string,
  timeout: number
}

/**
 * The default configuration for the app.
 */
export const DEFAULT_API_CONFIG: ApiConfig = {
  url: API_URL || 'https://jsonplaceholder.typicode.com',
  url2: API_URL2 || '',
  url_pvcombank: URL_PVCOMBANK,
  apiBhPvi: '',
  pviCpid: '',
  pviKey: '',
  apiBhBic: '',
  bicDeviceId: '',
  bicUserName: '',
  bicPassword: '',
  timeout: 10000,
}
