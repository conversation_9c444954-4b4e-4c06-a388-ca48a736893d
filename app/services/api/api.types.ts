import { GeneralApiProblem } from './api-problem'
import { QuestionSnapshot } from '../../models/question'

export interface User {
  id: number
  name: string
}

export type GetUsersResult = { kind: 'ok'; users: User[] } | GeneralApiProblem
export type GetUserResult = { kind: 'ok'; user: User } | GeneralApiProblem
export type GetQuestionsResult = { kind: 'ok'; questions: QuestionSnapshot[] } | GeneralApiProblem
export type GetBannersResult = { kind: 'ok'; banners: any } | GeneralApiProblem
