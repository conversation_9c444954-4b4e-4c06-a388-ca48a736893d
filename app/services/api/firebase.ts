
import auth, { FirebaseAuthTypes } from '@react-native-firebase/auth'
import messaging from '@react-native-firebase/messaging'

type User = FirebaseAuthTypes.User | null;

export class fireBase {
  constructor() {

  }

  /**
     * lay user dang dang nhap
     */
  async getCurrentUser(): Promise<User> {
    return await auth().currentUser
  }

  /**
     * sua ten hien thi
     * @param user
     */
  async updateProfile(user: FirebaseAuthTypes.UpdateProfile) {
    return await auth().currentUser.updateProfile(user)
  }

  async getFCMToken() {
    messaging().requestPermission().then(r => {
      messaging().getToken().then(token => {
        __DEV__ && console.log('TOKEN FCM', token)
        // return saveTokenToDatabase(token)
      })
    })
  }
}
