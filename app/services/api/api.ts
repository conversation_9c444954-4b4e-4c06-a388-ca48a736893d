/* eslint-disable */
import { ApiResponse, ApisauceInstance, create } from "apisauce"
import { getGeneralApiProblem } from "./api-problem"
import { ApiConfig, DEFAULT_API_CONFIG } from './api-config'

import { getToken } from '@app/services'
import { getRemoveConfig, load, loadString } from '@app/utils/storage'
import _ from 'lodash'
import { removeUtf8 } from '@app/utils/string'
import { stringMd5 } from 'react-native-quick-md5'

const handlerResponse = (response: any) => {
  if (!response.ok) {
    const problem = getGeneralApiProblem(response)
    if (problem) return problem
  }
  try {
    return { kind: "ok", data: response.data }
  } catch (e) {
    __DEV__ && console.log(e.message)
    return { kind: "bad-data" }
  }
}

/**
 * Manages all requests to the API.
 */
export class Api {
  /**
   * The underlying apisauce instance which performs the requests.
   */
  apisauce: ApisauceInstance

  /**
   * Configurable options.
   */
  config: ApiConfig

  token: string

  /**
   * Creates the api.
   *
   * @param config The configuration to use.
   */
  constructor(config: ApiConfig = DEFAULT_API_CONFIG) {
    this.config = config
    this.loadConfigFromLocal()
  }

  async loadConfigFromLocal() {
    // TODO: xóa vì đang dùng object
    this.config = await getRemoveConfig()
    __DEV__ && console.log('this.config is already', this.config)
  }

  /**
   * Sets up the API.  This will be called during the bootup
   * sequence and will happen before the first React component
   * is mounted.
   *
   * Be as quick as possible in here.
   */

  async setup(isAuth = true) {
    let headers = {
      Accept: "application/json"
    }

    // construct the apisauce instance
    this.token = await getToken()
    // const location = await load('locationSelected')

    if (this.token !== null && isAuth) {
      headers["access-token"] = this.token
      headers["Authorization"] = `Bearer ${this.token}`
    }

    let location = await load('location')
    if (!_.isEmpty(location)) {
      location = { lat: location.latitude, lng: location.longitude }
      headers["location"] = JSON.stringify(location)
    }

    const provinceSelected = await loadString('provinceSelected')
    if (provinceSelected) {
      headers["province"] = removeUtf8(provinceSelected)
    }

    const districtSelected = await loadString('districtSelected')
    if (districtSelected) {
      headers["district"] = removeUtf8(districtSelected)
    }

    let options: any = {
      baseURL: this.config.url,
      timeout: this.config.timeout,
      headers: headers,
    }
    this.apisauce = create(options)
  }


  async getBanners(): Promise<any> {
    // make the api call
    await this.setup(false)
    const response: ApiResponse<any> = await this.apisauce.get(
      "user/api/get-banner.html?output=json",
      {}
    )
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getStoreById(storeId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/get-spa-id.html/${storeId}`,
      {},
    )
    return handlerResponse(response)
  }

  async getListCarById(userId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `api/cars?populate=*&filters[user_id][$eq]=${userId}`,
      {},
    )
    return handlerResponse(response)
  }

  async getCommentStoreById(storeId, page, userId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/get-comment.html?page=${page}&storeId=${storeId}&output=json`,
      { userId },
    )
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getCommentProduct(id, page): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/product-rate/${id}?output=json`)
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getAllCommentOfStore(storeId, page): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/get-all-comment-of-store.html?page=${page}&storeId=${storeId}&output=json`)
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async postComment(commentModel): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      "user/api/comment.html",
      commentModel,
    )
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async postCommentProduct(commentCreate): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      "user/api/product-rate?output=json",
      commentCreate,
    )
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async checkCoupon(code, subTotal, feeShip, storeId): Promise<any> {
    // make the api call
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      "user/api/calculate-order?output=json",
      {code, subTotal, feeShip, storeId},
    )
    return handlerResponse(response)
  }

  async checkCouponService(code, subTotal, feeShip, storeId): Promise<any> {
    // make the api call
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      "user/api/calculate-order-service?output=json",
      {code, subTotal, feeShip, storeId},
    )
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async editComment(id, dataCommentEdit): Promise<any> {
    // make the api call
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/edit-comment.html/${id}?output=json`,
      dataCommentEdit,
    )
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }
  async deleteComment(id): Promise<any> {
    // make the api call
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/delete-comment.html/${id}?output=json`,
    )
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }
  async getServiceSpaByStoreId(storeId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/get-service-spa-by-storeid.html/${storeId}`,
      {},
    )
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }
  async getRoomHotelByStoreId(storeId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/get-room-hotel-by-storeid.html/${storeId}`,
      {},
    )
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }
  //API lấy danh sách dịch vụ khám của 1 Garage
  async getServiceOfClinicByStoreId(storeId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/get-clinic-by-storeid.html/${storeId}`, //TODO đang đợi api để hoàn thiện nốt
      {},
    )
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getProductSpaByStoreId(storeId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/get-products-by-storeid.html/${storeId}`,
      {},
    )
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async bookingRoomHotel(storeId, bookingType): Promise<any> {
    // make the api call
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/booking-v2/${storeId}?output=json`,
      {bookingType},
    )
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data}
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async postBookingSpa(dataBookingSpa): Promise<any> {
    // make the api call
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      "user/api/booking-spa.html?output=json",
      dataBookingSpa,
    )
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async postBooking(dataBooking): Promise<any> {
    // make the api call
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      "user/api/booking-v2?output=json",
      dataBooking,
    )
    return handlerResponse(response)
  }

  async bookingShowroom(dataBooking): Promise<any> {
    // make the api call
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      "user/api/booking-show-room?output=json",
      dataBooking,
    )
    return handlerResponse(response)
  }

  async getService(type): Promise<any> {
    await this.setup(false)
    // make the api call
    const params = {
      typeServices: type,
      searchKey: "",
      page: 1,
      limit: 20,
      status:1
    }
    const response: ApiResponse<any> = await this.apisauce.post("rest/v1/services/filter", params)
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  /**
   * đăng nhập qua id mạng xã hội, hoặc đăng nhập qua email + pass, phone + password
   * tham số truyền lên cần check api
   * @param data
   */
  async loginAccount(data): Promise<any> {
    await this.setup(false)
    const response: ApiResponse<any> = await this.apisauce.post("user/api/login.html", data)
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  /**
   * yêu cầu khoá tài khoản bằng mật khẩu
   * @param data
   */
  async requestLockAccount(data): Promise<any> {
    await this.setup(false)
    const response: ApiResponse<any> = await this.apisauce.post("user/api/request-lock-account", data)
    // the typical ways to die when calling an api
    return handlerResponse(response)
  }

  async registerAccount(data): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post("user/api/register-by-phone.html", data)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async recoverPassword(phoneNumber, password, confirmPassword): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post("user/api/recover-password.html", {
      phone: phoneNumber,
      password: password,
      confirmPassword: confirmPassword

    })
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async changePassword(data): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post("user/api/thay-doi-mat-khau.html?output=json", data)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async forgotYourPassword(phoneNumber): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post("user/api/forgot-password.html", {
      phone: phoneNumber,
    })
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getTokenHeader() {
    let token = await getToken()
    let headers = {
    }
    if (token != null) {
      headers["access-token"] = token
    }
    return headers;
  }

  async getProfileInfo(): Promise<any> {
    await this.setup()
    let token = await getToken()
    const response: ApiResponse<any> = await this.apisauce.post("user/api/check-token.html?output=json", {
      token: token,
    })
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }

    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data?.error ? response.data : response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  /**
   * search screen
   * @param page
   * @param search
   * @param type
   * @param distance
   * @param rating
   * @param shipping
   * @param price
   * @param rate
   * @param typeShip
   */
  async searchSpa(page, search, type,distance,rating, shipping, price, rate, typeShip = 1): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/search-v2?page=${page}&search=${search}&type=${type}&distance=${distance}${rating}&shipping=${shipping}&price=${price}&rate=${rate}&typeShip=${typeShip}`,
    )
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }


  async loadFavorite(page): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/favorites?page=${page}&output=json`,
    )
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  /**
   * home screen
   * @param type
   * @param page
   * @param search
   */
  async searchServices(type ,page ,search): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/search-v2?type=${type}&page=${page}&search=${search}&limit=10`,
    )
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }
  async getHistorySearchSpa(user, type): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post("user/api/lich-su-tim-kiem.html?output=json", {
      user,
      type,
    })
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }
  async updateProfile(body): Promise<any> {
    await this.setup()
    __DEV__ && console.log(body)
    let dataUpdate = {...body};
    delete dataUpdate.photoUrl;
    __DEV__ && console.log('after', dataUpdate)
    const response: ApiResponse<any> = await this.apisauce.post("user/api/update-profile.html?output=json", dataUpdate)
    console.log('', response)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async uploadImages(url, files): Promise<any> {
    // Create the form data object
    const data = new FormData()
    if (files != null && files.length > 0) {
      files.forEach((fileData) => {
        data.append(fileData.name, {
          uri: fileData.uri,
          name: fileData.fileName,
          type: fileData.type
        });

      })
    }
    return new Promise(async (resolve) => {
      let headers = {
        Accept: "*/*",
        "content-type": "multipart/form-data",
      }

      let token = await getToken()
      if (token != null) {
        headers["access-token"] = token
        headers["Authorization"] = token
      }
      // Create the config object for the POST
      // You typically have an OAuth2 token that you use for authentication
      const config = {
        method: 'POST',
        headers: headers,
        body: data
      };

      fetch(`${this.config.url}${url}?output=json`, config)
        .then((response) => response.json())
        .then(responseData => {
        return resolve(responseData)
      }).catch(err => {
        return resolve({ error: true, message: "Xảy ra lỗi: " + err});
    })})
  }

  async uploadImagesV2(url, files): Promise<any> {
    // Create the form data object
    const urlUpload = `${this.config.url2}${url}`
    console.log('URL Upload IMAGE', urlUpload)
    const data = new FormData()
    if (files != null && files.length > 0) {
      files.forEach((fileData) => {
        data.append(fileData.name, {
          uri: fileData.uri,
          name: fileData.fileName,
          type: fileData.type
        });

      })
    }
    return new Promise(async (resolve) => {
      let headers = {
        Accept: "*/*",
        "content-type": "multipart/form-data",
      }

      let token = await getToken()
      if (token != null) {
        headers["access-token"] = token
        headers["Authorization"] = `Bearer ${token}`
      }
      // Create the config object for the POST
      // You typically have an OAuth2 token that you use for authentication
      const config = {
        method: 'POST',
        headers: headers,
        body: data
      };

      try {
        fetch(urlUpload, config)
          .then((response) => response.json())
          .then(responseData => {
            return resolve(responseData)
          }).catch(err => {
          return resolve({ error: true, message: "Xảy ra lỗi: " + err});
        })
      } catch (err) {
        return resolve({ error: true, message: "Xảy ra lỗi upload: " + err});
      }
    }
    )
  }

  async updateAvatarById(body): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post("user/api/update-avatar.html" ,body)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getDataSupportCenter(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/ho-tro.html?output=json`, {}
    )
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }
  //TODO: fix paging booking hitory
  async getBookingHistory(page, limit = 9999999): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/lay-lich-su-spa.html/1?page=${page}&limit=${limit}&output=json`, {}
    )
    console.log('response lịch sử',response)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getBookingHotel(page, limit = 9999999): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/lay-lich-su-khach-san/1?page=${page}&limit=${limit}&output=json`, {}
    )
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }
  async getBookingClinic(page, limit = 9999999): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/lay-lich-su-kham-benh/1?page=${page}&limit=${limit}&output=json`, {}
    )
    __DEV__ && console.log('lichsukhambenh', response)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getBookingClassification(page, limit = 9999999): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/get-list-booking-show-room/1?page=${page}&limit=${limit}&output=json`, {}
    )
   return handlerResponse(response)
  }

  async getBookingProduct(page, limit = 9999999): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/lay-lich-su-mua-hang/?page=${page}&limit=${limit}&output=json`, {}
    )
    __DEV__ && console.log('lich su mua hang', response)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  /**
   * GET BOOKING SPA DETAIL
   */
  async getBookingSpaDetail(id): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/order-booking-spa-detail/${id}?output=json`)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }
  /**
   * GET BOOKING HOTEL DETAIL
   */
  async getBookingHotelDetail(id): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/order-booking-hotel-detail/${id}?output=json`)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }
  /**
   * GET BOOKING CLINIC DETAIL
   */
  async getBookingClinicDetail(id): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/order-booking-clinic-detail/${id}?output=json`)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getBookingProductDetail(orderId): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/order-product-detail/${orderId}?output=json`)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getBookingShowRoomDetail(id): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/order-booking-show-room-detail/${id}?output=json`)
    return handlerResponse(response)
  }

  /**
   * CANCEL BOOKING SERVICE
   */
  async cancelBooking(id, type, reasonCancel, noteCancel): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/huy-dich-vu.html/${id}/${type}?output=json`, { reasonCancel, noteCancel })
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  /**
   * CANCEL BOOKING Product
   */
  async cancelBookingProduct(id,reasonCancel, noteCancel): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/huy-don-hang.html/${id}?output=json`, { reasonCancel, noteCancel })
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }


  /**
   * API notification
   */

  async resetCountNotification(): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      "user/api/reset-count-notification.html?output=json")
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }
  async getNotification(page, typeNotify = -1): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      "/lay-du-lieu-thong-bao.html?output=json",{ page, typeNotify })
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }
  async watchedNotification(id): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/da-xem-thong-bao/${id}.html?output=json`)
    return handlerResponse(response)
  }
  async getFeatured(storeId): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/get-service-spa-featured.html`, { storeId })
    return handlerResponse(response)
  }

  /**
   * lấy dịch vụ nổi bật của spa
   */
  async getServiceFeature(categoryId, storeId): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/get-service-spa-featured.html`, {categoryId, storeId})
    return handlerResponse(response)
  }

  async saveTokenFirebase(body): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `update-user-fcm-token.html?output=json`, body
    )
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }


  /**
   * lấy danh sách bài viết
   * @param categoriesID
   * @param page
   */
  async getPostsByCategories(categoriesID,page): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `wp/v2/posts?categories=${categoriesID}&per_page=6&_embed&page=${page}`,{})
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getPosts(catName = ''): Promise<any> {
    let url = `${this.config.url2}/api/list-news?populate=*&sort[0]=id:desc`
    if (catName !== '')
    {
      url += `&filters[$and][0][categories][name][$contains]=${catName}`
    }
    const response: ApiResponse<any> = await this.apisauce.get(url)
    return handlerResponse(response)
  }
  async getPostDetail(id): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/list-news/${id}?populate=*`)
    return handlerResponse(response)
  }
  async getPostCategories(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/categories-news?pagination%5BwithCount%5D=true`)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }
  async getCarLine(catName = ''): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/car-categories?pagination%5BwithCount%5D=false&populate=%2A&filters[$and][0][car_brands][name][$contains]=${catName}`)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  /**
   * lấy danh sách loại xe
   */
  async getCarType(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/loai-xes?pagination[withCount]=true&pagination[pageSize]=999&populate=%2A`)
    return handlerResponse(response)
  }
  async getCarTypeByType(type = 'other'): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/car-types?pagination[withCount]=true&pagination[pageSize]=999&populate=%2A&filters[type][$eq]=${type}`)
    return handlerResponse(response)
  }

  /**
   * Lấy danh sách danh mục
   */
  async getCategoriesPosts(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `/wp/v2/list-cat`,{})
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  /**
   * api tinh tien booking hotel
   * @param body
   */
  async handleCalculate(body): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/booking-hotel-calculator.html?output=json`, body)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async downloadImage(path): Promise<any> {
    return new Promise(async (resolve) => {
      const response: ApiResponse<any> = await this.apisauce.get(
        `/user/api/download-image?path=${path}`,
        {},
      )

      if (!response.ok) {
        const problem = getGeneralApiProblem(response)
        if (problem) resolve(problem)
      }
      try {
        resolve({ kind: "ok", data: response.data })
      } catch (e) {
        __DEV__ && console.log(e.message)
        resolve(e)
      }
    })
  }

  async getPromotion(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-promotion-for-home`)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }
  async getPromotions(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get("/user/api/get-promotions")
    return handlerResponse(response)
  }
  async getProvinces(): Promise<any> {
    await this.setup(false)
    const response: ApiResponse<any> = await this.apisauce.get(
      "/user/api/get-list-province")
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getPromotionById(promotionId): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-promotion-by-id?id=${promotionId}`)
    return handlerResponse(response)
  }

  async getProducts(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/shopping.html`, )
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getProductDetails(productId): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/chi-tiet-san-pham/${productId}.html?&output=json`, )
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getProductRate(productId): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/product-rate/${productId}?output=json`, )
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getShoppingCartInformation(shopProducts): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/tai-thong-tin-gio-hang.html?output=json`, {shopProducts})
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async createOrder(dataBooking): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/create-order-product?output=json`, dataBooking)
    return handlerResponse(response)
  }

  async getCategoryById(categoryId, page, order, shipping, price, rate): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/search-v2?search=&type=0&limit=10&page=${page}&categoryId=${categoryId}&order=${order}&shipping=${shipping}&price=${price}&rate=${rate}`, )
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getProductForHome(): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/search-v2?page=1&search=&type=0&distance=10&sort=rating&shipping=&price=&rate=&limit=6`, )
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async calculateFeeShip(body): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/calculator-shipping?output=json`, body )
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async updatePetProfile(id, body): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/edit-pet?output=json&id=${id}`, body)
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getListPet(): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/get-list-car?output=json`,
      {},
    )
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async deletePet(id): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/delete-pet?output=json&id=${id}`,
      {},
    )
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async addPetProfile(body): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/add-car?output=json`, body)
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async createDiary(body): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `${this.config.url2}/api/car-histories`, body)
    // the typical ways to die when calling an api
    return handlerResponse(response)
  }

  async getDiary(carId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/car-histories?populate=*&filters[car][id][$eq]=${carId}&sort[0]=id:desc`)
    // the typical ways to die when calling an api
    return handlerResponse(response)
  }

  async getPetProfile(petId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/get-pet?output=json&id=${petId}`,
      {},
    )
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getTopBranch(): Promise<any> {
    await this.setup(false)
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-banner-store.html?tag=store_hot`)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getBannerHome2(): Promise<any> {
    await this.setup(false)
    const response: ApiResponse<any> = await this.apisauce.get(
        `/user/api/get-banner-store.html?tag=banner_home_2`)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getShippingInfo(orderId): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-shipping-info/${orderId}`)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getNewsForHome(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get("/user/api/get-news")
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getNewsDetail(id): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(`/user/api/get-news-detail/${id}`)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getBannerShopping(): Promise<any> {
    await this.setup(false)
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-banner-store.html?tag=banner_shopping`)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getBannerShoppingTop(): Promise<any> {
    await this.setup(false)
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-banner-store.html?tag=banner_shopping_top`)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getProductByCategory(type, page): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-products-for-pet?pet=${type}&page=${page}&limit=10`)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getViewedProducts(): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-user-viewed?output=json&field=productIds&page=1&limit=10`)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async sendProductViewed(send): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/update-user-viewed?output=json`, send)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }


  async getDistrict(): Promise<any> {
    // await this.setup()
    const provinceSelected = await loadString('provinceSelected')
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-district-by-province?province=${removeUtf8(provinceSelected)}`)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async loadProductFilter(): Promise<any> {
    // await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/load-product-filter`)
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

  async getListFanPage(): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-banner-store.html?tag=community`)
    return handlerResponse(response)
  }

  async getAlbums(id): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/pet-gallery-by-pet-id/${id}?output=json`)
    return handlerResponse(response)
  }

  async updateAlbums(body): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/pet-gallery/update-photo?output=json`, body)
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }
  async getHomeCategories(tag = 'menu'): Promise<any> {
    await this.setup(false)

    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/grid-menu-homes?&populate=*&sort[0]=order&filters[tag][$eq]=${tag}`,{})
    return handlerResponse(response)
  }
  async getCarByUserId(userId: any): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/cars?populate=*&filters[user_id][$eq]=${userId}`,{})
    return handlerResponse(response)
  }

  async addCar(body: any): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `${this.config.url2}/api/cars`, body)
    return handlerResponse(response)
  }
  async editCar(body, id): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.put(
      `${this.config.url2}/api/cars/${id}`, body)
    // the typical ways to die when calling an api
    return handlerResponse(response)
  }

  async getCarById(id): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/cars/${id}?populate=*`)
    // the typical ways to die when calling an api
    return handlerResponse(response)
  }

  async deleteCar(id): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.delete(
      `${this.config.url2}/api/cars/${id}`)
    // the typical ways to die when calling an api
    return handlerResponse(response)
  }

  /**
   * xoá ảnh theo ID
   * @param id
   */
  async deleteImageV2ById(id): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.delete(
      `${this.config.url2}/api/upload/files/${id}`)
    // the typical ways to die when calling an api
    return handlerResponse(response)
  }

  // lấy danh sách dịch vụ mở rộng theo type
  async getClassificationDataByType(storeId, type): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/services-of-brand/${storeId}/${type}`,
      {},
    )
    return handlerResponse(response)
  }

  /**
   * lịch sử đổi điểm của user
   */
  async getUserPointHistory(): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/user-point-history?output=json`,
      {},
    )
    return handlerResponse(response)
  }

  /**
   * lấy thông tin cấu hình app
   */
  async getAppConfig(): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/app-config?populate=*`,
      {},
    )
    return handlerResponse(response)
  }
  async getRewardPointProducts(): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/shops?populate=*&filters[gift_shop_danh_muc][$eq]=2`,
      {},
    )
    return handlerResponse(response)
  }

  async getListItemLuckyWheel(): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/shops?populate=*&filters[gift_shop_danh_muc][$eq]=1`,
      {},
    )
    return handlerResponse(response)
  }

  /**
   * đổi qua bằng điểm
   * @param body
   */
  async buyProductWithPoint(body): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/pay-product-with-point?output=json`, body)
    // the typical ways to die when calling an api
    return handlerResponse(response)
  }

  async addFavorite(serviceId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/add-brand-favorite/${serviceId}?output=json`, {})
    // the typical ways to die when calling an api
    return handlerResponse(response)
  }

  async removeFavorite(serviceId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/remove-brand-favorite/${serviceId}?output=json`, {})
    // the typical ways to die when calling an api
    return handlerResponse(response)
  }

  /**
   * gửi thông báo có tin nhắn mới
   * @param toUserId
   * @param fromUserId
   * @param message
   */
  async pushFcmNotification(toUserId, fromUserId, message): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/push-fcm-notification?output=json`, { toUserId, fromUserId, message})
    // the typical ways to die when calling an api
    return handlerResponse(response)
  }

  async logoutUser(): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/logout?output=json`,
      {},
    )
    return handlerResponse(response)
  }

  async getInsurancePosts(catName = ''): Promise<any> {
    let url = `${this.config.url2}/api/bao-hiems?populate=*&sort[0]=id:desc`
    if (catName !== '')
    {
      url += `&filters[$and][0][loai_bao_hiems][name][$contains]=${catName}`
    }
    const response: ApiResponse<any> = await this.apisauce.get(url)
    return handlerResponse(response)
  }
  async getInsurancePostDetail(id): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/bao-hiems/${id}?populate=*`)
    return handlerResponse(response)
  }
  async getInsuranceCategories(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/loai-bao-hiems?pagination%5BwithCount%5D=true&populate=*`)
    return handlerResponse(response)
  }

  async getCostCategories(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/loai-chi-phis?pagination%5BwithCount%5D=true&populate=*`)
    return handlerResponse(response)
  }

  async getLineChartData(id, m, y): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/report-car-history?id=${id}&m=${m}&y=${y}`)
    return handlerResponse(response)
  }

  /**
   * PVI - Get loại xe cho bảo hiểm TNDS
   * @param id
   * @param m
   * @param y
   */
  async getLoaiXe(body = {}): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/get-loai-xe-bh`, body)
    return handlerResponse(response?.data)
  }

  async getMaLoaiXe(body = {}): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/get-ma-loai-xe-bh`, body)
    return handlerResponse(response?.data)
  }
  /**
   * tính trước chi phí bảo hiểm TNDS Auto
   * @param body
   */
  async tinhPhiTNDSOto(body = {}): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/tinh-phi-tnds-oto`, body)
    return handlerResponse(response?.data)
  }


  async taoDonOtoV2(body = {}): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/tao-don-oto`, body)
    return handlerResponse(response?.data)
  }


  async tinhPhiTNDSXMV2(body = {}): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/tinh-phi-tnds-xm`, body)
    return handlerResponse(response?.data)
  }

  async tinhPhiVCXOtoV2(body = {}): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/tinh-phi-vcx-oto`, body)
    return handlerResponse(response?.data)
  }

  async taoDonTNDSXMV2(body = {}): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/tao-don-tnds-xm`, body)
    return handlerResponse(response?.data)
  }


  /**
   * lưu lại bảo hiểm đã mua
   * @param body
   */
  async createOrderInsuranceHistory(body): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `${this.config.url2}/api/insurance-histories`, body)
    // the typical ways to die when calling an api
    return handlerResponse(response)
  }

  /**
   * tạo url payment bảo hiểm
   * @param dataBooking
   */
  async createUrlPayment(body): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/create-url-payment?output=json`, body)
    return handlerResponse(response)
  }

  /**
   * lấy lịch sử mua bao hiểm
   * @param userId
   */
  async getLichSuMuaBHByUserId(userId, bksoat): Promise<any> {
    let filters = `&filters[user_id][$eq]=${userId}&filters[PaymentId][$ne]=CANCEL&filters[active][$eq]=true`
    if (bksoat)
    {
      filters += `&filters[BienKiemSoat][$eq]=${bksoat}`
    }
    const url = `${this.config.url2}/api/insurance-histories?populate[0]=loai_bao_hiem&populate[1]=loai_bao_hiem.image${filters}&sort[0]=id:desc`
    __DEV__ && console.warn('url', url)
    const response: ApiResponse<any> = await this.apisauce.get(url,{})
    return handlerResponse(response)
  }

  async xoaLichSuMuaBH(id): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.put(
      `${this.config.url2}/api/insurance-histories/${id}`, {data: { active: false }})
    // the typical ways to die when calling an api
    return handlerResponse(response)
  }

  async bicLogin(): Promise<any> {
    await this.setup(false)
    const headers: any  = {
      'content-type': 'application/json',
      'deviceId': this.config.bicDeviceId
    }
    const body = {
      "username":"maxq_app_user",
      "password":"U2gNtQIlu2",
      "checksum": stringMd5(this.config.bicPassword + '|' + this.config.bicDeviceId + '|' + this.config.bicUserName)
    }

    const response: ApiResponse<any> = await this.apisauce.post("https://mybic.htecom.vn/resapi/login", body, headers)
    return handlerResponse(response)
  }

  async getUrlPvi(body = {}): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/get-url-bh`, body)
    return handlerResponse(response?.data)
  }

  /**
   * lấy danh mục sản phẩm or dịch vụ
   * 0,1,2 = sản phẩm
   * 3 = dịch vụ spa
   * @param type
   */
  async getCategoriesByType(type = '0,1,2'): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/categories-by-type?output=json&cat=${type}`, )
    return handlerResponse(response)
  }

  async addLogPvcombank(body): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `${this.config.url2}/api/pvcombank-logs`, body)
    return handlerResponse(response)
  }

  async getLuckyWheels(): Promise<any> {
    await this.setup(false)

    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/luck-ky-wheel?populate=*&sort=id:desc`,{})
    return handlerResponse(response)
  }

  /**
   * lưu kết quả trúng thưởng
   * @param body
   */
  async postResultLuckyWheel(body: any): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `${this.config.url2}/api/lich-su-quays`, body)
    return handlerResponse(response)
  }

  async checkMaxSpin(user_id: string, idProgram: string): Promise<any> {
    await this.setup()

    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/lich-su-quays/check-max-spin`,
      { user_id, idProgram: idProgram }
    )
    return handlerResponse(response)
  }

  /**
   * update kết quả trúng thưởng
   * @param body
   * @param id
   */
  async putUpdateResultLuckyWheel(body: any, id: string): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.put(
      `${this.config.url2}/api/lich-su-quays/${id}`, body)
    return handlerResponse(response)
  }

  /**
   * get danh sách trúng thưởng
   */
  async getWinners(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/lich-su-quays?populate=*&filters[result][$eq]=true&filters[confirmed][$eq]=true&sort[0]=createdAt:desc`,{})
    return handlerResponse(response)
  }

}


