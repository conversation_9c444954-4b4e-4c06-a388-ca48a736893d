/* eslint-disable */
import { ApiResponse, ApisauceInstance, create } from "apisauce"
import { getGeneralApiProblem } from "./api-problem"
import { ApiConfig } from './api-config'
const { ROCKETCHAT_API_URL } = require('../../config/env')


export const DEFAULT_ROCKETCHAT_API_URL: ApiConfig = {
  url: ROCKETCHAT_API_URL,
  timeout: 10000,
}

/**
 * Manages all requests to the API.
 */
export class RocketChatApi {
  /**
   * The underlying apisauce instance which performs the requests.
   */
  apisauce: ApisauceInstance

  /**
   * Configurable options.
   */
  config: ApiConfig


  /**
   * Creates the api.
   *
   * @param config The configuration to use.
   */
  constructor(config: ApiConfig = DEFAULT_ROCKETCHAT_API_URL) {
    this.config = config
  }

  /**
   * Sets up the API.  This will be called during the bootup
   * sequence and will happen before the first React component
   * is mounted.
   *
   * Be as quick as possible in here.
   */

  setup({authToken, userId}) {
    let headers = {
      Accept: "application/json"
    }

    if (authToken && userId) {
      headers["X-Auth-Token"] = authToken
      headers["X-User-Id"] = userId
    } else {
      throw new Error('need setup X-Auth-Token & X-User-Id rocketchat api')
    }

    let options = {
      baseURL: this.config.url,
      timeout: this.config.timeout,
      headers: headers,
    }
    this.apisauce = create(options)
  }

  /**
   * to userName
   * @param toUserName
   */
  async createDirectMessage(toUserName: string): Promise<any> {
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      "/api/v1/im.create",
      { username: toUserName }
    )
    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }
    // transform the data into the format we are expecting
    try {
      return { kind: "ok", data: response.data }
    } catch (e) {
      __DEV__ && console.log(e.message)
      return { kind: "bad-data" }
    }
  }

}

export const RocketChatApiService = new RocketChatApi()
