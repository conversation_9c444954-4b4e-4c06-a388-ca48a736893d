import messaging from '@react-native-firebase/messaging'

export const registerAppWithFCM = async () => {
  await messaging().registerDeviceForRemoteMessages()
}

export const requestUserPermission = async () => {
  const authStatus = await messaging().requestPermission()
  const enabled =
    authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
    authStatus === messaging.AuthorizationStatus.PROVISIONAL
  if (enabled) {
    console.log('Authorization status:', authStatus)
  }
}
