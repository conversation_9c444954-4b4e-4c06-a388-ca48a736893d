import AsyncStorage from '@react-native-async-storage/async-storage'
const CACHE_VERSION = 199

export const saveUserLogin = async user => {
  delete user.services
  await AsyncStorage.setItem(`user-${CACHE_VERSION}`, JSON.stringify(user))
}

export const getUserLogin = async () => {
  const str = await AsyncStorage.getItem(`user-${CACHE_VERSION}`)
  if (str == null || str == '') {
    return null
  }
  return JSON.parse(str)
}

export const removeUserLogin = async () => {
  await AsyncStorage.removeItem(`user-${CACHE_VERSION}`)
}
