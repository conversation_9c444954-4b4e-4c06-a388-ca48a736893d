import io from 'socket.io-client'
import { getToken } from './tokenService'
const { API_URL } = require('../config/env')

export const ws = async () => {
  const token = await getToken()
  const connectionConfig = {
    transports: ['websocket'],
    jsonp: false,
    forceNew: true,
    reconnection: true,
    reconnectionDelay: 1000,
    reconnectionDelayMax: 5000,
    pingTimeout: 10000,
    reconnectionAttempts: 99999,
    query: `token=${token}`,
  }
  const socket = io(API_URL, connectionConfig)
  return socket
}
