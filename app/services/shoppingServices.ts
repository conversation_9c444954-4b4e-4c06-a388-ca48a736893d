import AsyncStorage from '@react-native-async-storage/async-storage'
import _ from 'lodash'
const CACHE_VERSION = '1234'
const keyGioHangCache = `giohang4-${CACHE_VERSION}`

export const addProduct = async (productId, count, shopId, classifyActive, context, noteProduct) => {
  __DEV__ && console.log('**************', productId)
  let giohang = await getGioHang()
  /**
   * Kiểm tra xem có san phầm này trong gio hàn chưa
   */
  let checkProductId = false
  let indexProductMatch = -1
  giohang.forEach((item, index) => {
    if (productId == item.productId) {
      checkProductId = true
      indexProductMatch = index
      giohang[index].count = giohang[index].count + Number(count)
      giohang[index].noteProduct = noteProduct
    }
  })

  if (!checkProductId) {
    giohang.unshift({
      productId, count, shopId, classifyActive, noteProduct
    })
  } else {
    const newObject = giohang[indexProductMatch]
    delete giohang[indexProductMatch]
    giohang.unshift(newObject)
  }

  const newArr = []
  giohang.forEach(e => {
    if (e && e != null && e) {
      newArr.push(e)
    }
  })
  giohang = newArr

  await AsyncStorage.setItem(keyGioHangCache, JSON.stringify(giohang))
  await getCountProductGioHang(context)
}

export const editProduct = async (productId, count, context, noteProduct = '') => {
  let giohang = await getGioHang()

  /**
   * Kiểm tra xem có san phầm này trong gio hàn chưa
   */
  let checkProductId = false
  let indexProductMatch = -1
  giohang.forEach((item, index) => {
    if (productId == item.productId) {
      checkProductId = true
      indexProductMatch = index
      giohang[index].count = Number(count)
      if (noteProduct.length > 0) {
        giohang[index].noteProduct = noteProduct
      }
    }
  })

  if (checkProductId) {
    const newObject = giohang[indexProductMatch]
    delete giohang[indexProductMatch]
    giohang.unshift(newObject)

    const newArr = []
    giohang.forEach(e => {
      if (e && e != null && e) {
        newArr.push(e)
      }
    })
    giohang = newArr

    await AsyncStorage.setItem(keyGioHangCache, JSON.stringify(giohang))
  }
  await getCountProductGioHang(context)
}

export const removeProduct = async (productId, context) => {
  const giohang = await getGioHang()

  /**
   * Kiểm tra xem có san phầm này trong gio hàn chưa
   */
  giohang.forEach((item, index) => {
    if (productId == item.productId) {
      delete giohang[index]
      reCreateGioHang(giohang, context)
    }
  })
}

/**
 * Xóa tất cả những mặt hàng trong shop đã mua, sau khi thanh toán thành công
 * @param products
 * @param context
 */
export const removeItemBuyedAllShop = async (shops, context) => {
  const carts = await getGioHang()
  shops.forEach((s, shopIndex) => {
    for (const p of s.products) {
      carts.forEach((item, index) => {
        if (p.productId == item.productId) {
          delete carts[index]
        }
      })
    }
  })
  await reCreateGioHang(carts, context)
}

export const getGioHang = async () => {
  const str = await AsyncStorage.getItem(keyGioHangCache)
  if (str == null || str == '') {
    return []
  }
  let giohang = JSON.parse(str)
  const newArr = []
  giohang.forEach(e => {
    if (e && e != null) {
      newArr.push(e)
    }
  })
  giohang = newArr
  return giohang
}

// @ts-ignore
export const getCountProductGioHang = async (updateShoppingCount) : number => {
  const itemsCart = await getGioHang()
  const count = _.sumBy(itemsCart, (o: any) => (Number(o.count)))
  if (updateShoppingCount) {
    await updateShoppingCount(count)
  }
  return count || 0
}

export const getGioHangGroupByShopId = async () => {
  const gioHang = await getGioHang()
  const groupByShop = {}
  gioHang.forEach(e => {
    if (groupByShop[e.shopId]) {
      // nếu shop có sản phẩm rồi thì thêm mới vào mảng
      groupByShop[e.shopId].products.push(e)
    } else {
      // shop chưa có sản phẩm nào thì tạo mới
      groupByShop[e.shopId] = {
        products: [e]
      }
    }
  })

  const rsl = []
  for (const shopId in groupByShop) {
    rsl.push({ [shopId]: groupByShop[shopId] })
  }
  return rsl
}

export const reCreateGioHang = async (array, context) => {
  await AsyncStorage.setItem(keyGioHangCache, JSON.stringify(array.filter(x => x)))
  await getCountProductGioHang(context)
}

export const removeAllInCart = async (context) => {
  await AsyncStorage.setItem(keyGioHangCache, JSON.stringify([]))
  await getCountProductGioHang(context)
}
