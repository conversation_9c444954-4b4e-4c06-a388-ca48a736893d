import React, { useState } from 'react'
import {
  View,
  Text,
  TouchableOpacity,
  TextInput, StyleSheet
} from 'react-native'
import { observer } from 'mobx-react-lite'
import { color } from '@app/theme'

export const InputCount = observer((props: any) => {
  const [state] = useState({
    max: props.max || 500,
    min: props.min || 1
  })
  const onChangeText = props.onChangeText

  const add = () => {
    const newVal = Number(props.value || 0) + 1
    if (newVal <= state.max) {
      onChangeText(newVal)
    }
  }

  const sub = () => {
    const newVal = Number(props.value || 0) - 1
    if (newVal >= state.min) {
      onChangeText(newVal)
    }
  }

  const checkVal = (newVal) => {
    newVal = Number(newVal || 0)
    if (newVal >= state.min && newVal <= state.max) {
      props.onChangeText(newVal)
    }
  }
  return (
    <View style={{ ...props.style, flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
      <TouchableOpacity disabled={props.disabled || false}
        onPress={sub} style={styles.btnAdd}>
        <Text
          style={{ fontSize: 14, color: '#000' }}>-</Text>
      </TouchableOpacity>
      <TextInput
        editable={(props.disabled != null) ? !props.disabled : true}
        keyboardType="numeric"
        placeholderTextColor='#333333'
        underlineColorAndroid="transparent"
        value={props.value + '' || '0'}
        onChangeText={checkVal}
        style={{
          flex: 1,
          borderColor: '#EFEFEF',
          borderWidth: 1,
          height: '100%',
          textAlign: 'center',
          color: '#333',
          backgroundColor: '#fff',
          marginHorizontal: 5,
          borderRadius: 4
        }}
      />
      <TouchableOpacity disabled={props.disabled || false}
        onPress={add} style={styles.btnAdd}>
        <Text
          style={{ fontSize: 14, color: '#000' }}>+</Text>
      </TouchableOpacity>
    </View>
  )
})

const styles = StyleSheet.create({
  btnAdd: {
    alignItems: 'center',
    backgroundColor: color.primaryBackground,
    borderColor: '#EFEFEF',
    borderRadius: 4,
    borderWidth: 1,
    height: '100%',
    justifyContent: 'center',
    width: 40
  }
})
