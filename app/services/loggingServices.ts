import crashlytics from '@react-native-firebase/crashlytics'
import analytics from '@react-native-firebase/analytics'

export const LogEvent = (action: string, value: any) => {
  analytics().logEvent(action, { value })
}

export const LogInfo = (action: string, value: any) => {
  crashlytics().log(`INFO : ${action} : ${JSON.stringify(value)}`)
}

export const LogError = (error) => {
  crashlytics().recordError(error)
}
