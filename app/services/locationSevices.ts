import Geocoder from 'react-native-geocoding'
import * as geolib from 'geolib'
import { load, save } from '../utils/storage'
import _ from 'lodash'
import { removeUtf8 } from '@app/utils'

export const getLatLngFromAdd = async (address) => {
  if (!address) {
    return null
  }

  // check location lat lng cache
  const addressUTF = removeUtf8(address)
  const location = await load(addressUTF)

  if (!_.isEmpty(location)) {
    return location
  }

  // Initialize the module (needs to be done only once)
  Geocoder.init('AIzaSyCr8NK8i0Jh95LBGTRdmMeTzJYxk2rm0cY') // use a valid API key

  Geocoder.from(address)
    .then(json => {
      const location = json.results[0].geometry.location
      save(addressUTF, location)
      return location
    })
    .catch(error => {
      console.warn('error', error.code)
      return null
    })
}

export const calculateDistance = (to) : Promise<any> => {
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve) => {
    const fromLocation = await load('location')
    if (fromLocation && fromLocation.latitude && fromLocation.longitude) {
      const distance = geolib.getDistance(fromLocation, {
        latitude: to.lat,
        longitude: to.lng
      })
      __DEV__ && console.log(`calculateDistance : ${distance} km`)
      resolve(parseFloat((distance / 1000).toFixed(1)))
    }
    resolve(0)
  })
}
