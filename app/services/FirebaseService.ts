import auth from '@react-native-firebase/auth'
import database from '@react-native-firebase/database'

class Firebase {
  constructor() {
    // this.init();
    // this.observeAuth();
  }

  // init = () =>
  //   firebase.initializeApp({
  //     apiKey: 'AIzaSyDLgW8QG1qO8O5WZLC1U8WaqCr5-CvEVmo',
  //     authDomain: 'chatter-b85d7.firebaseapp.com',
  //     databaseURL: 'https://chatter-b85d7.firebaseio.com',
  //     projectId: 'chatter-b85d7',
  //     storageBucket: '',
  //     messagingSenderId: '************',
  //   });

  // observeAuth = () =>
  //   firebase.auth().onAuthStateChanged(this.onAuthStateChanged);

  onAuthStateChanged = user => {
    if (!user) {
      try {
        auth().signInAnonymously()
      } catch ({ message }) {
        // alert(message)
      }
    }
  };

  get uid() {
    // @ts-ignore
    return (auth().currentUser || {}).uid
  }

  get ref() {
    return database().ref('messages')
  }

  parse = snapshot => {
    const { timestamp: numberStamp, text, user } = snapshot.val()
    const { key: _id } = snapshot
    const timestamp = new Date(numberStamp)
    const message = {
      _id,
      timestamp,
      text,
      user,
    }
    return message
  };

  on = callback =>
    this.ref
      .limitToLast(20)
      .on('child_added', snapshot => callback(this.parse(snapshot)));

  get timestamp() {
    return database.ServerValue.TIMESTAMP
  }

  // send the message to the Backend
  send = messages => {
    for (let i = 0; i < messages.length; i++) {
      const { text, user } = messages[i]
      const message = {
        text,
        user,
        timestamp: this.timestamp,
      }
      this.append(message)
    }
  };

  append = message => this.ref.push(message);

  // close the connection to the Backend
  off() {
    this.ref.off()
  }
}

export const FirebaseService = new Firebase()
