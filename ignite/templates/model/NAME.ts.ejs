---
patches:
  - path: "app/models/root-store/root-store.ts"
    after: "from \"mobx-state-tree\"\n"
    insert: "import { <%= props.pascalCaseName %>Model } from \"./<%= props.pascalCaseName %>\"\n"
    skip: <%= !props.pascalCaseName.endsWith('Store') %>
  - path: "app/models/root-store/root-store.ts"
    after: "types.model(\"RootStore\").props({\n"
    insert: "  <%= props.camelCaseName %>: types.optional(<%= props.pascalCaseName %>Model, {} as any),\n"
    skip: <%= !props.pascalCaseName.endsWith('Store') %>
  - path: "app/models/index.ts"
    append: "export * from \"./<%= props.pascalCaseName %>\"\n" 
    skip: <%= props.skipIndexFile %>
---
import { Instance, SnapshotIn, SnapshotOut, types } from "mobx-state-tree"

/**
 * Model description here for TypeScript hints.
 */
export const <%= props.pascalCaseName %>Model = types
  .model("<%= props.pascalCaseName %>")
  .props({})
  .views((self) => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars
  .actions((self) => ({})) // eslint-disable-line @typescript-eslint/no-unused-vars

export interface <%= props.pascalCaseName %> extends Instance<typeof <%= props.pascalCaseName %>Model> {}
export interface <%= props.pascalCaseName %>SnapshotOut extends SnapshotOut<typeof <%= props.pascalCaseName %>Model> {}
export interface <%= props.pascalCaseName %>SnapshotIn extends SnapshotIn<typeof <%= props.pascalCaseName %>Model> {}
export const create<%= props.pascalCaseName %>DefaultModel = () => types.optional(<%= props.pascalCaseName %>Model, {})
