<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="IP 8 Plus" type="ReactNative" factoryName="React Native" activateToolWindowBeforeRun="false">
    <node-interpreter value="project" />
    <react-native value="$PROJECT_DIR$/node_modules/react-native" />
    <platform value="IOS" />
    <run-arguments value="npx react-native run-ios --simulator=&quot;iPhone 8 Plus&quot; " />
    <envs />
    <only-packager />
    <browser value="98ca6316-2f89-46d9-a9e5-fa9e2b0625b3" />
    <method v="2">
      <option name="ReactNativePackager" enabled="true" />
    </method>
  </configuration>
</component>