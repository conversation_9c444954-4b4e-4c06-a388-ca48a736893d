{
    // other settings
    // formatting using eslint
    // let editor format using prettier for all other files
    "editor.formatOnSave": true,
    // disable editor formatting, so eslint can handle it
    "[javascript]": {
      "editor.formatOnSave": false,
    },
    // available through eslint plugin in vscode
    "eslint.autoFixOnSave": true,
    "eslint.alwaysShowStatus": true,
    "[typescript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit",
        "source.fixAll": "explicit",
        "source.fixAll.stylelint": "explicit"
    },
    "editor.formatOnType": true,
    "nuxt.isNuxtApp": false,
}