diff --git a/node_modules/react-native-gifted-chat/lib/MessageContainer.js b/node_modules/react-native-gifted-chat/lib/MessageContainer.js
index 193772a..3a1156e 100644
--- a/node_modules/react-native-gifted-chat/lib/MessageContainer.js
+++ b/node_modules/react-native-gifted-chat/lib/MessageContainer.js
@@ -55,18 +55,18 @@ export default class MessageContainer extends React.PureComponent {
         this.attachKeyboardListeners = () => {
             const { invertibleScrollViewProps: invertibleProps } = this.props;
             if (invertibleProps) {
-                Keyboard.addListener('keyboardWillShow', invertibleProps.onKeyboardWillShow);
-                Keyboard.addListener('keyboardDidShow', invertibleProps.onKeyboardDidShow);
-                Keyboard.addListener('keyboardWillHide', invertibleProps.onKeyboardWillHide);
-                Keyboard.addListener('keyboardDidHide', invertibleProps.onKeyboardDidHide);
+                this.willShowSub = Keyboard.addListener('keyboardWillShow', invertibleProps.onKeyboardWillShow);
+                this.didShowSub = Keyboard.addListener('keyboardDidShow', invertibleProps.onKeyboardDidShow);
+                this.willHideSub = Keyboard.addListener('keyboardWillHide', invertibleProps.onKeyboardWillHide);
+                this.didHideSub = Keyboard.addListener('keyboardDidHide', invertibleProps.onKeyboardDidHide);
             }
         };
         this.detachKeyboardListeners = () => {
             const { invertibleScrollViewProps: invertibleProps } = this.props;
-            Keyboard.removeListener('keyboardWillShow', invertibleProps.onKeyboardWillShow);
-            Keyboard.removeListener('keyboardDidShow', invertibleProps.onKeyboardDidShow);
-            Keyboard.removeListener('keyboardWillHide', invertibleProps.onKeyboardWillHide);
-            Keyboard.removeListener('keyboardDidHide', invertibleProps.onKeyboardDidHide);
+            this.willShowSub?.remove();
+            this.didShowSub?.remove();
+            this.willHideSub?.remove();
+            this.didHideSub?.remove();
         };
         this.renderTypingIndicator = () => {
             if (Platform.OS === 'web') {
@@ -114,7 +114,7 @@ export default class MessageContainer extends React.PureComponent {
             }
             else {
                 if (contentOffsetY < scrollToBottomOffset &&
-                    contentSizeHeight - layoutMeasurementHeight > scrollToBottomOffset) {
+                  contentSizeHeight - layoutMeasurementHeight > scrollToBottomOffset) {
                     this.setState({ showScrollBottom: true });
                 }
                 else {
@@ -156,28 +156,28 @@ export default class MessageContainer extends React.PureComponent {
         this.renderChatEmpty = () => {
             if (this.props.renderChatEmpty) {
                 return this.props.inverted ? (this.props.renderChatEmpty()) : (<View style={styles.emptyChatContainer}>
-          {this.props.renderChatEmpty()}
-        </View>);
+                    {this.props.renderChatEmpty()}
+                </View>);
             }
             return <View style={styles.container}/>;
         };
         this.renderHeaderWrapper = () => (<View style={styles.headerWrapper}>{this.renderLoadEarlier()}</View>);
         this.onLayoutList = () => {
             if (!this.props.inverted &&
-                !!this.props.messages &&
-                this.props.messages.length) {
+              !!this.props.messages &&
+              this.props.messages.length) {
                 setTimeout(() => this.scrollToBottom && this.scrollToBottom(false), 15 * this.props.messages.length);
             }
         };
         this.onEndReached = ({ distanceFromEnd }) => {
             const { loadEarlier, onLoadEarlier, infiniteScroll, isLoadingEarlier, } = this.props;
             if (infiniteScroll &&
-                distanceFromEnd > 0 &&
-                distanceFromEnd <= 100 &&
-                loadEarlier &&
-                onLoadEarlier &&
-                !isLoadingEarlier &&
-                Platform.OS !== 'web') {
+              distanceFromEnd > 0 &&
+              distanceFromEnd <= 100 &&
+              loadEarlier &&
+              onLoadEarlier &&
+              !isLoadingEarlier &&
+              Platform.OS !== 'web') {
                 onLoadEarlier();
             }
         };
@@ -193,15 +193,15 @@ export default class MessageContainer extends React.PureComponent {
     }
     componentDidUpdate(prevProps) {
         if (prevProps.messages &&
-            prevProps.messages.length === 0 &&
-            this.props.messages &&
-            this.props.messages.length > 0) {
+          prevProps.messages.length === 0 &&
+          this.props.messages &&
+          this.props.messages.length > 0) {
             this.detachKeyboardListeners();
         }
         else if (prevProps.messages &&
-            this.props.messages &&
-            prevProps.messages.length > 0 &&
-            this.props.messages.length === 0) {
+          this.props.messages &&
+          prevProps.messages.length > 0 &&
+          this.props.messages.length === 0) {
             this.attachKeyboardListeners();
         }
     }
@@ -220,19 +220,19 @@ export default class MessageContainer extends React.PureComponent {
     renderScrollToBottomWrapper() {
         const propsStyle = this.props.scrollToBottomStyle || {};
         return (<View style={[styles.scrollToBottomStyle, propsStyle]}>
-        <TouchableOpacity onPress={() => this.scrollToBottom()} hitSlop={{ top: 5, left: 5, right: 5, bottom: 5 }}>
-          {this.renderScrollBottomComponent()}
-        </TouchableOpacity>
-      </View>);
+            <TouchableOpacity onPress={() => this.scrollToBottom()} hitSlop={{ top: 5, left: 5, right: 5, bottom: 5 }}>
+                {this.renderScrollBottomComponent()}
+            </TouchableOpacity>
+        </View>);
     }
     render() {
         const { inverted } = this.props;
         return (<View style={this.props.alignTop ? styles.containerAlignTop : styles.container}>
-        {this.state.showScrollBottom && this.props.scrollToBottom
-            ? this.renderScrollToBottomWrapper()
-            : null}
-        <FlatList ref={this.props.forwardRef} extraData={[this.props.extraData, this.props.isTyping]} keyExtractor={this.keyExtractor} enableEmptySections automaticallyAdjustContentInsets={false} inverted={inverted} data={this.props.messages} style={styles.listStyle} contentContainerStyle={styles.contentContainerStyle} renderItem={this.renderRow} {...this.props.invertibleScrollViewProps} ListEmptyComponent={this.renderChatEmpty} ListFooterComponent={inverted ? this.renderHeaderWrapper : this.renderFooter} ListHeaderComponent={inverted ? this.renderFooter : this.renderHeaderWrapper} onScroll={this.handleOnScroll} scrollEventThrottle={100} onLayout={this.onLayoutList} onEndReached={this.onEndReached} onEndReachedThreshold={0.1} {...this.props.listViewProps}/>
-      </View>);
+            {this.state.showScrollBottom && this.props.scrollToBottom
+              ? this.renderScrollToBottomWrapper()
+              : null}
+            <FlatList ref={this.props.forwardRef} extraData={[this.props.extraData, this.props.isTyping]} keyExtractor={this.keyExtractor} enableEmptySections automaticallyAdjustContentInsets={false} inverted={inverted} data={this.props.messages} style={styles.listStyle} contentContainerStyle={styles.contentContainerStyle} renderItem={this.renderRow} {...this.props.invertibleScrollViewProps} ListEmptyComponent={this.renderChatEmpty} ListFooterComponent={inverted ? this.renderHeaderWrapper : this.renderFooter} ListHeaderComponent={inverted ? this.renderFooter : this.renderHeaderWrapper} onScroll={this.handleOnScroll} scrollEventThrottle={100} onLayout={this.onLayoutList} onEndReached={this.onEndReached} onEndReachedThreshold={0.1} {...this.props.listViewProps}/>
+        </View>);
     }
 }
 MessageContainer.defaultProps = {
