diff --git a/node_modules/react-native-button/Button.js b/node_modules/react-native-button/Button.js
index fb7cf46..f776b52 100644
--- a/node_modules/react-native-button/Button.js
+++ b/node_modules/react-native-button/Button.js
@@ -6,6 +6,7 @@ import {
   TouchableOpacity,
   View,
   ViewPropTypes,
+  TextStyle
 } from 'react-native';
 
 import coalesceNonElementChildren from './coalesceNonElementChildren';
@@ -16,12 +17,12 @@ export default class But<PERSON> extends Component {
   static propTypes = {
     ...TouchableOpacity.propTypes,
     accessibilityLabel: PropTypes.string,
-    allowFontScaling: Text.propTypes.allowFontScaling,
+    allowFontScaling: PropTypes.bool,
     containerStyle: ViewPropTypes.style,
     disabledContainerStyle: ViewPropTypes.style,
     disabled: PropTypes.bool,
-    style: Text.propTypes.style,
-    styleDisabled: Text.propTypes.style,
+    style: PropTypes.shape(TextStyle),
+    styleDisabled: PropTypes.shape(TextStyle),
     childGroupStyle: ViewPropTypes.style,
   };
 
