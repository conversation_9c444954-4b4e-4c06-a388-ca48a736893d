<div class="register-container">
  <div class="register-card">
    <div class="register-header">
      <h2><PERSON><PERSON><PERSON>ý <PERSON>à<PERSON></h2>
      <p>Tạo tài khoản để truy cập và<PERSON> hệ thống</p>
    </div>
    
    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
      <app-text-input
        formControlName="fullName"
        label="Họ và tên"
        placeholder="Nhập họ và tên của bạn"
        [required]="true"
        [errorMessages]="{
          required: 'Họ và tên là bắt buộc'
        }"
      ></app-text-input>

      <app-text-input
        formControlName="phone"
        label="Số điện thoại"
        placeholder="Nhập số điện thoại"
        [required]="true"
        [errorMessages]="{
          required: 'Số điện thoại là bắt buộc',
          pattern: 'Số điện thoạ<PERSON> không đúng định dạng'
        }"
      ></app-text-input>



      <app-text-input
        formControlName="password"
        label="Mật khẩu"
        type="password"
        placeholder="Nhập mật khẩu"
        [required]="true"
        [errorMessages]="{
          required: 'Mật khẩu là bắt buộc',
          minlength: 'Mật khẩu phải có ít nhất 6 ký tự'
        }"
      ></app-text-input>

      <app-text-input
        formControlName="confirmPassword"
        label="Xác nhận mật khẩu"
        type="password"
        placeholder="Nhập lại mật khẩu"
        [required]="true"
        [errorMessages]="{
          required: 'Xác nhận mật khẩu là bắt buộc',
          passwordMismatch: 'Mật khẩu không khớp'
        }"
      ></app-text-input>

      <div class="terms-check">
        <app-checkbox-input
          formControlName="agreeTerms"
          label="Tôi đồng ý với quy định và điều khoản của dịch vụ"
          [required]="true"
          color="primary"
          [errorMessages]="{
            required: 'Bạn cần đồng ý với điều khoản',
            requiredTrue: 'Bạn cần đồng ý với điều khoản'
          }"
        ></app-checkbox-input>
        <a href="/dieu-khoan" class="terms-link">Xem điều khoản</a>
      </div>

      <button mat-raised-button color="primary" type="submit" class="full-width register-button" [disabled]="registerForm.invalid">Đăng Ký</button>
      
      <div class="login-link">
        <span>Đã có tài khoản? </span>
        <a routerLink="/dang-nhap">Đăng nhập ngay</a>
      </div>
    </form>
  </div>
</div>
