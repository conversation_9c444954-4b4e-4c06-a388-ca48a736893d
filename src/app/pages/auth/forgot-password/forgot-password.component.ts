import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { TextInputComponent } from '../../../shared/components/form-controls/text-input/text-input.component';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'app-forgot-password',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    TextInputComponent
  ],
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss']
})
export class ForgotPasswordComponent {
  forgotPasswordForm: FormGroup;
  isSubmitting = false;
  errorMessage = '';
  successMessage = '';

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router
  ) {
    this.forgotPasswordForm = this.formBuilder.group({
      phone: ['', [
        Validators.required,
        Validators.pattern(/^[0-9]{10}$/) // Validate 10-digit phone number
      ]]
    });
  }

  onSubmit() {
    if (this.forgotPasswordForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';
    this.successMessage = '';

    const phoneNumber = this.forgotPasswordForm.value.phone;

    // TODO: Implement forgot password functionality when the API is available
    // For now, just show a success message
    setTimeout(() => {
      this.isSubmitting = false;
      this.successMessage = 'Yêu cầu đặt lại mật khẩu đã được gửi đến số điện thoại của bạn.';
    }, 1000);

    // Actual implementation would be something like:
    /*
    this.authService.forgotPassword(phoneNumber).subscribe({
      next: (response) => {
        this.isSubmitting = false;
        this.successMessage = 'Yêu cầu đặt lại mật khẩu đã được gửi đến số điện thoại của bạn.';
      },
      error: (error) => {
        this.isSubmitting = false;
        this.errorMessage = error.error?.message || 'Đã xảy ra lỗi, vui lòng thử lại sau.';
      }
    });
    */
  }
}
