<div class="forgot-password-container">
  <div class="forgot-password-card">
    <h2 class="forgot-password-title"><PERSON>u<PERSON><PERSON> mật khẩu</h2>
    <p class="forgot-password-subtitle"><PERSON><PERSON> lòng nhập số điện thoại của bạn để đặt lại mật khẩu.</p>

    <form [formGroup]="forgotPasswordForm" (ngSubmit)="onSubmit()">
      <app-text-input
        formControlName="phone"
        label="Số điện thoại"
        [control]="forgotPasswordForm.controls['phone']"
        type="tel"
        placeholder="Nhập số điện thoại"
      ></app-text-input>

      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <div *ngIf="successMessage" class="success-message">
        {{ successMessage }}
      </div>

      <div class="actions-container">
        <button
          mat-raised-button
          color="primary"
          type="submit"
          [disabled]="forgotPasswordForm.invalid || isSubmitting"
          class="submit-button"
        >
          <span *ngIf="!isSubmitting"><PERSON><PERSON><PERSON> y<PERSON><PERSON> cầu</span>
          <span *ngIf="isSubmitting">Đang xử lý...</span>
        </button>
      </div>

      <div class="alternative-actions">
        <a routerLink="/dang-nhap">Đăng nhập</a> |
        <a routerLink="/dang-ky">Tạo tài khoản mới</a>
      </div>
    </form>
  </div>
</div>
