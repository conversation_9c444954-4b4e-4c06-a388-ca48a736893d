import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatSnackBar } from '@angular/material/snack-bar';
import { finalize } from 'rxjs/operators';

import { AuthService } from '../../../core/services/auth.service';
import { LoginRequest } from '../../../core/models/auth.model';
import { TextInputComponent } from '../../../shared/components/form-controls/text-input/text-input.component';
import { CheckboxInputComponent } from '../../../shared/components/form-controls/checkbox-input/checkbox-input.component';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    RouterLink,
    TextInputComponent,
    CheckboxInputComponent
  ]
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup;
  hidePassword = true;
  isLoading = false;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loginForm = this.fb.group({
      phone: ['', [Validators.required, Validators.pattern('^[0-9]{10}$')]],
      password: ['', Validators.required],
      rememberMe: [false]
    });
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      this.isLoading = true;
      const loginData: LoginRequest = {
        phone: this.loginForm.get('phone')?.value,
        password: this.loginForm.get('password')?.value
      };

      this.authService.login(loginData)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: (response) => {
            this.snackBar.open('Đăng nhập thành công!', 'Đóng', {
              duration: 3000,
              horizontalPosition: 'center',
              verticalPosition: 'bottom',
              panelClass: ['snackbar-success']
            });
            
            // Lưu trạng thái "Ghi nhớ đăng nhập" nếu được chọn
            if (this.loginForm.get('rememberMe')?.value) {
              localStorage.setItem('rememberMe', 'true');
            }
            
            // Chuyển hướng sau khi đăng nhập thành công
            this.router.navigate(['/']);
          },
          error: (error) => {
            console.error('Đăng nhập thất bại:', error);
            this.snackBar.open(
              error.error?.message || 'Đăng nhập thất bại. Vui lòng kiểm tra lại thông tin.',
              'Đóng',
              {
                duration: 5000,
                horizontalPosition: 'center',
                verticalPosition: 'bottom',
                panelClass: ['snackbar-error']
              }
            );
          }
        });
    } else {
      // Đánh dấu tất cả các trường là đã tương tác để hiển thị lỗi
      Object.keys(this.loginForm.controls).forEach(key => {
        const control = this.loginForm.get(key);
        control?.markAsTouched();
      });
    }
  }
}
