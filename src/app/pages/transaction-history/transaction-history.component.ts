import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatIconModule } from '@angular/material/icon';

interface Transaction {
  id: string;
  date: Date;
  amount: number;
  type: string;
  status: string;
  description: string;
}

@Component({
  selector: 'app-transaction-history',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatIconModule
  ],
  templateUrl: './transaction-history.component.html',
  styleUrls: ['./transaction-history.component.scss']
})
export class TransactionHistoryComponent implements OnInit {
  transactions: Transaction[] = [];
  displayedColumns: string[] = ['date', 'amount', 'type', 'status', 'description'];
  
  constructor() { }

  ngOnInit() {
    // Mock data - would be replaced with API call
    this.transactions = [
      {
        id: '1',
        date: new Date(2025, 5, 10),
        amount: 200000,
        type: 'Nạp tiền',
        status: 'Thành công',
        description: 'Nạp tiền vào tài khoản'
      },
      {
        id: '2',
        date: new Date(2025, 5, 9),
        amount: 150000,
        type: 'Thanh toán',
        status: 'Thành công',
        description: 'Thanh toán dịch vụ'
      },
      {
        id: '3',
        date: new Date(2025, 5, 8),
        amount: 500000,
        type: 'Nạp tiền',
        status: 'Thành công',
        description: 'Nạp tiền vào tài khoản'
      }
    ];
  }

  getStatusClass(status: string): string {
    switch(status.toLowerCase()) {
      case 'thành công':
        return 'status-success';
      case 'đang xử lý':
        return 'status-processing';
      case 'thất bại':
        return 'status-failed';
      default:
        return '';
    }
  }

  getTypeIcon(type: string): string {
    switch(type.toLowerCase()) {
      case 'nạp tiền':
        return 'ti ti-wallet';
      case 'thanh toán':
        return 'ti ti-shopping-cart';
      case 'rút tiền':
        return 'ti ti-cash';
      case 'hoàn tiền':
        return 'ti ti-arrow-back-up';
      default:
        return 'ti ti-credit-card';
    }
  }
}
