import { Component, Input, OnInit, Optional, Self } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgControl, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { BaseFormControlComponent } from '../base-form-control.component';
import { MatIconButton } from '@angular/material/button';

@Component({
  selector: 'app-text-input',
  templateUrl: './text-input.component.html',
  styleUrls: ['./text-input.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatIconButton
  ]
})
export class TextInputComponent extends BaseFormControlComponent implements OnInit {
  @Input() type = 'text'; // text, email, password, etc.
  @Input() prefixIcon = '';
  @Input() suffixIcon = '';
  @Input() maxLength?: number;
  @Input() minLength?: number;
  @Input() autocomplete = 'off';
  
  hidePassword = true;

  constructor(@Optional() @Self() public override ngControl: NgControl) {
    super(ngControl);
  }

  override ngOnInit(): void {
    super.ngOnInit();
    
    // Set default error messages if not provided
    if (Object.keys(this.errorMessages).length === 0) {
      this.errorMessages = {
        required: 'Trường này là bắt buộc',
        email: 'Vui lòng nhập đúng định dạng email',
        minlength: `Vui lòng nhập tối thiểu ${this.minLength} ký tự`,
        maxlength: `Vui lòng nhập tối đa ${this.maxLength} ký tự`,
        pattern: 'Giá trị không đúng định dạng'
      };
    }
  }

  togglePasswordVisibility(): void {
    if (this.type === 'password') {
      this.hidePassword = !this.hidePassword;
    }
  }

  get passwordVisibilityIcon(): string {
    return this.hidePassword ? 'visibility_off' : 'visibility';
  }
}
