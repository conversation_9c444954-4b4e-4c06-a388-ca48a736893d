:host {
  display: block;
  width: 100%;

  // Make input height 40px
  ::ng-deep .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      height: 40px;
    }
    .mat-mdc-form-field-flex {
      height: 40px;
    }
    .mat-mdc-form-field-infix {
      padding-top: 8px;
      padding-bottom: 8px;
      min-height: unset;
    }
  }
}

.w-100 {
  width: 100%;
}

.prefix-icon, .suffix-icon {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.54);
  margin-right: 8px;
}

.suffix-icon {
  margin-right: 0;
  margin-left: 8px;
}
