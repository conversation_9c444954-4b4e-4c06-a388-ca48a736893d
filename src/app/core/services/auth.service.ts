import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, BehaviorSubject, of, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';

import { ApiService } from './api.service';
import { environment } from '../../../environments/environment';
import { 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse, 
  UserModel,
  RefreshTokenRequest
} from '../models/auth.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject: BehaviorSubject<UserModel | null>;
  public currentUser: Observable<UserModel | null>;
  private isAuthenticatedSubject: BehaviorSubject<boolean>;
  public isAuthenticated: Observable<boolean>;

  constructor(
    private apiService: ApiService,
    private router: Router
  ) {
    // Khởi tạo user từ localStorage nếu đã đăng nhập
    const userJson = localStorage.getItem('user');
    let user: UserModel | null = null;
    
    try {
      if (userJson) {
        user = JSON.parse(userJson);
      }
    } catch (error) {
      console.error('Error parsing user from localStorage', error);
    }

    this.currentUserSubject = new BehaviorSubject<UserModel | null>(user);
    this.currentUser = this.currentUserSubject.asObservable();
    
    this.isAuthenticatedSubject = new BehaviorSubject<boolean>(!!user);
    this.isAuthenticated = this.isAuthenticatedSubject.asObservable();
  }

  /**
   * Lấy giá trị user hiện tại
   */
  public get currentUserValue(): UserModel | null {
    return this.currentUserSubject.value;
  }

  /**
   * Đăng nhập
   * @param credentials Thông tin đăng nhập (email, password)
   */
  public login(credentials: LoginRequest): Observable<AuthResponse> {
    return this.apiService.post<AuthResponse>('user/api/login.html', credentials)
      .pipe(
        map(response => {
          if (response.data) {
            // Lưu token vào localStorage
            this.saveToken(response.data.accessToken, response.data.refreshToken);
            
            // Lưu thông tin user vào localStorage
            this.saveUser(response.data.user);
            
            // Cập nhật BehaviorSubject
            this.currentUserSubject.next(response.data.user);
            this.isAuthenticatedSubject.next(true);
            
            return response.data;
          }
          return {} as AuthResponse;
        }),
        catchError(error => {
          console.error('Login error', error);
          return throwError(() => error);
        })
      );
  }

  /**
   * Đăng ký
   * @param userData Thông tin đăng ký
   */
  public register(userData: RegisterRequest): Observable<AuthResponse> {
    return this.apiService.post<AuthResponse>('user/api/register-by-phone.html', userData)
      .pipe(
        map(response => {
          if (response.data) {
            // Lưu token vào localStorage
            this.saveToken(response.data.accessToken, response.data.refreshToken);
            
            // Lưu thông tin user vào localStorage
            this.saveUser(response.data.user);
            
            // Cập nhật BehaviorSubject
            this.currentUserSubject.next(response.data.user);
            this.isAuthenticatedSubject.next(true);
            
            return response.data;
          }
          return {} as AuthResponse;
        }),
        catchError(error => {
          console.error('Register error', error);
          return throwError(() => error);
        })
      );
  }

  /**
   * Đăng xuất
   */
  public logout(): void {
    // Xóa token và user khỏi localStorage
    localStorage.removeItem(environment.tokenKey);
    localStorage.removeItem(environment.refreshTokenKey);
    localStorage.removeItem('user');
    
    // Reset BehaviorSubject
    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);
    
    // Điều hướng về trang đăng nhập
    this.router.navigate(['/dang-nhap']);
  }

  /**
   * Refresh token
   */
  public refreshToken(): Observable<AuthResponse> {
    const refreshToken = localStorage.getItem(environment.refreshTokenKey);
    
    if (!refreshToken) {
      return throwError(() => new Error('No refresh token available'));
    }
    
    const refreshTokenRequest: RefreshTokenRequest = {
      refreshToken: refreshToken
    };
    
    return this.apiService.post<AuthResponse>('auth/refresh-token', refreshTokenRequest)
      .pipe(
        map(response => {
          if (response.data) {
            // Lưu token mới vào localStorage
            this.saveToken(response.data.accessToken, response.data.refreshToken);
            return response.data;
          }
          return {} as AuthResponse;
        }),
        catchError(error => {
          console.error('Refresh token error', error);
          // Nếu refresh token thất bại, đăng xuất người dùng
          this.logout();
          return throwError(() => error);
        })
      );
  }

  /**
   * Lấy thông tin user từ server
   */
  public getUserProfile(): Observable<UserModel> {
    return this.apiService.get<UserModel>('users/profile')
      .pipe(
        map(response => {
          if (response.data) {
            // Cập nhật thông tin user trong localStorage
            this.saveUser(response.data);
            
            // Cập nhật BehaviorSubject
            this.currentUserSubject.next(response.data);
            
            return response.data;
          }
          return {} as UserModel;
        }),
        catchError(error => {
          console.error('Get user profile error', error);
          return throwError(() => error);
        })
      );
  }

  /**
   * Lưu token vào localStorage
   */
  private saveToken(token: string, refreshToken: string): void {
    localStorage.setItem(environment.tokenKey, token);
    localStorage.setItem(environment.refreshTokenKey, refreshToken);
  }

  /**
   * Lưu thông tin user vào localStorage
   */
  private saveUser(user: UserModel): void {
    localStorage.setItem('user', JSON.stringify(user));
  }
}
