module.exports = {
  presets: ["module:metro-react-native-babel-preset",['@babel/preset-env', {targets: {node: 'current'}}]],
  env: {
    production: {},
  },
  plugins: [
    [
      "@babel/plugin-proposal-decorators",
      {
        legacy: true,
      },
    ],
    ["@babel/plugin-proposal-optional-catch-binding"],
    ["react-native-reanimated/plugin", { globals: ['__decode'] }],
    ["nativewind/babel"],
    [
      'module-resolver',
      {
        root: ['./app'],
        extensions: [
          '.ios.ts',
          '.android.ts',
          '.ts',
          '.ios.tsx',
          '.android.tsx',
          '.tsx',
          '.jsx',
          '.js',
          '.json',
          '.svg',
        ],
        alias: {
          '@app/*': './app/*',
          '@app/icons': './app/assets/icons',
          '@app/images': './app/assets/images',
          '@app/theme': './app/theme',
          '@app/components': './app/components',
          '@app/components/*': './app/components/*',
          '@app/models': './app/models',
          '@app/navigation': './app/navigation',
          '@app/config': './app/config',
          '@app/context': './app/context',
          '@app/graphql': './app/graphql',
          '@app/constants': './app/constants',
          '@app/layout': './app/layout',
          '@app/screens': './app/screens',
          '@app/services': './app/services',
          '@app/types': './app/types',
          '@app/utils': './app/utils',
          '@app/use-hooks': './app/use-hooks',
          '@app/use-hooks/*': './app/use-hooks/*',
          '@app/assets': './app/assets',
          "test": "./test",
          "underscore": "lodash",
          '@app/lib': './app/lib',
          '@app/lib/*': './app/lib/*',
        }
      }],
  ],
}
