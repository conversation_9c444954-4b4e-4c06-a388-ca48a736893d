import type {Config} from '@jest/types';

// Sync object
const config: Config.InitialOptions = {
  verbose: true,
  preset: "react-native",
  setupFiles: [
    "<rootDir>/node_modules/react-native/jest/setup.js",
    "<rootDir>/test/setup.ts"
  ],
  testPathIgnorePatterns: [
    "/node_modules/",
    "/e2e"
  ],
  transformIgnorePatterns: [
    "node_modules/(?!(jest-)?react-native|react-native|react-navigation|@react-navigation|@react-native-community|@react-native-firebase|rn-tourguide|react-native-offline)"
  ]
};
export default config;
