// For more info on how to write Detox tests, see the official docs:
// https://github.com/wix/Detox/blob/master/docs/README.md

describe("StartApp", () => {
  beforeEach(async () => {
    await device.reloadReactNative()
  })

  it("WelcomeScreen", async () => {
    await expect(element(by.id("WelcomeScreen"))).toBeVisible()
    await element(by.id("btnstart")).tap()
  })
  //
  // it("HomeScreen", async () => {
  //   await expect(element(by.id("HomeScreen"))).toBeVisible()
  // })
})
