module.exports = {
  'parser': '@typescript-eslint/parser',
  'extends': [
    'prettier',
    'plugin:@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-native/all',
    'standard'
  ],
  'parserOptions': {
    'ecmaFeatures': {
      'jsx': false
    },
    'project': './tsconfig.json'
  },
  'plugins': [
    '@typescript-eslint',
    'react',
    'react-native',
    'unused-imports'
  ],
  'settings': {
    'react': {
      'pragma': 'React',
      'version': 'detect'
    },
    "import/parsers": {
      "@typescript-eslint/parser": [".ts", ".tsx"]
    },
    "import/resolver": {
      "typescript": {
        "alwaysTryTypes": true // always try to resolve types under `<root>@types` directory even it doesn't contain any source code, like `@types/unist`
      }
    }
  },
  'globals': {
    '__DEV__': false,
    'jasmine': false,
    'beforeAll': false,
    'afterAll': false,
    'beforeEach': false,
    'afterEach': false,
    'test': false,
    'expect': false,
    'describe': false,
    'jest': false,
    'it': false
  },
  'rules': {
    'no-useless-constructor': 0,
    'comma-dangle': 0,
    'no-unused-vars': 0,
    'no-undef': 0,
    'quotes': 'error',
    'react/no-unescaped-entities': 0,
    'react/prop-types': 'off',
    'react/display-name': 0,
    'react-native/no-raw-text': 0,
    'space-before-function-paren': 0,
    'react-native/no-color-literals': 0,
    '@typescript-eslint/ban-ts-ignore': 0,
    '@typescript-eslint/explicit-member-accessibility': 0,
    '@typescript-eslint/explicit-function-return-type': 0,
    '@typescript-eslint/member-delimiter-style': 0,
    '@typescript-eslint/no-explicit-any': 0,
    '@typescript-eslint/no-object-literal-type-assertion': 0,
    '@typescript-eslint/no-empty-interface': 0,
    '@typescript-eslint/no-var-requires': 0,
    '@typescript-eslint/no-empty-function': 0,
    "@typescript-eslint/naming-convention": [
      "error",
      {
        "selector": "memberLike",
        "modifiers": ["private"],
        "format": ["camelCase"],
        "leadingUnderscore": "require"
      }
    ],
    'no-unreachable': 0,
    'eqeqeq': 0,
    '@typescript-eslint/ban-ts-comment': 0,
    '@typescript-eslint/explicit-module-boundary-types': 0,
    "@typescript-eslint/no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars-experimental": "off",
    'import/no-unresolved': 'warn',
    'dot-notation': 0,
    'react-native/no-inline-styles': 1,
    "no-use-before-define": "off",
    "react-native/sort-styles": "off",
    "react-native/no-unused-styles": 'warn',
    "react-native/split-platform-components": 1,
    "react-native/no-single-element-style-arrays": 1,
    "import/named": "error",
    "camelcase": "warn",
    "no-restricted-imports": [
      "error",
      {
        "paths": [
          {
            "name": "react-native-gesture-handler",
            "importNames": [
              "TouchableOpacity",
              "TouchableNativeFeedback",
              "TouchableHighlight",
              "TouchableWithoutFeedback"
            ],
            "message": "Please import it from 'react-native' instead."
          },
          {
            "name": "react-native",
            "importNames": [
              "SafeAreaView",
            ],
            "message": "Please import it from 'react-native-safe-area-context' instead."
          }
        ]
      }
    ],
    "no-mixed-spaces-and-tabs": ["error", "smart-tabs"],
    "no-tabs": 0,
    "comma-spacing": 1,
    "unused-imports/no-unused-imports": "warn",
    "unused-imports/no-unused-vars": [
      "warn",
      { "vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_" }
    ]
  }
}
