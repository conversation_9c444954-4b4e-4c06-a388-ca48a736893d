fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

### inc_ver_ios

```sh
[bundle exec] fastlane inc_ver_ios
```

iOS: Increment build number and set the version to package.json version.

### bump

```sh
[bundle exec] fastlane bump
```

Bump build numbers, and set the version to match the package.json version.

### bump_android

```sh
[bundle exec] fastlane bump_android
```

Bump build numbers, and set the version to match the package.json version.

### build_ios

```sh
[bundle exec] fastlane build_ios
```



### build_i

```sh
[bundle exec] fastlane build_i
```



### release_beta

```sh
[bundle exec] fastlane release_beta
```



### submit_review

```sh
[bundle exec] fastlane submit_review
```



### build_android

```sh
[bundle exec] fastlane build_android
```



### release_android

```sh
[bundle exec] fastlane release_android
```



----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
