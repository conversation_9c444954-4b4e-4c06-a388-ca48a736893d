# ENV["FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD"] = "cucx-ipdw-bdpy-ljpt" thanhnx.1911
ENV["FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD"] = "wlkp-pkqp-npuk-srqz"
# ENV["FASTLANE_ITUNES_TRANSPORTER_PATH"]="/Applications/Transporter.app/Contents/itms"

desc 'Android: Increment versionCode and set versionName to package.json version.'
  package = load_json(json_path: "./package.json")

  private_lane :inc_ver_and do
    increment_version_code(
      gradle_file_path: "./android/app/build.gradle",
    )

    increment_version_name(
      gradle_file_path: "./android/app/build.gradle",
#       version_name: package['version-android'],
      bump_type: "patch"
    )
  end


  desc 'iOS: Increment build number and set the version to package.json version.'
  lane :inc_ver_ios do
  package = load_json(json_path: "./package.json")

    increment_build_number(
      xcodeproj: './ios/' + package['name'] + '.xcodeproj'
    )
#     Available: patch, minor, major
    increment_version_number(
      xcodeproj: './ios/' + package['name'] + '.xcodeproj',
      bump_type: "patch",
#       version_number: package['version-ios']
    )
  end


  desc 'Bump build numbers, and set the version to match the package.json version.'
  lane :bump do
    inc_ver_ios
#     inc_ver_and
  end

    desc 'Bump build numbers, and set the version to match the package.json version.'
    lane :bump_android do
#       inc_ver_ios
      inc_ver_and
    end




lane :build_ios do
  build_app(scheme: "maxQ",
            workspace: './ios/' + package['name'] + '.xcworkspace',
            include_bitcode: true,
            export_options: {
                manageAppVersionAndBuildNumber: true,
                stripSwiftSymbols: false
            })
end

lane :build_i do
  build_app(scheme: "maxQ",
            workspace: './ios/' + package['name'] + '.xcworkspace',
            include_bitcode: true,
            skip_archive: true,
            export_options: { manageAppVersionAndBuildNumber: true })
end

lane :release_beta do
  upload_to_testflight(
    username: "<EMAIL>",
    team_name: "TG Technology Solution Corporation"
  )
end

lane :submit_review do
  deliver(
   username: "<EMAIL>",
   team_name: "TG Technology Solution Corporation",
    ipa: 'maxQ.ipa',
    app_version: '1.4',
    copyright: '2022',
    submit_for_review: true,
    automatic_release: true,
    force: true, # Skip HTMl report verification
    skip_metadata: true,
    skip_screenshots: true,
    skip_binary_upload: true,
    run_precheck_before_submit: false,
    submission_information: {
          add_id_info_limits_tracking: true,
          add_id_info_serves_ads: false,
          add_id_info_tracks_action: true,
          add_id_info_tracks_install: true,
          add_id_info_uses_idfa: false,
          content_rights_has_rights: true,
          content_rights_contains_third_party_content: true,
          export_compliance_platform: 'ios',
          export_compliance_compliance_required: false,
          export_compliance_encryption_updated: false,
          export_compliance_app_type: nil,
          export_compliance_uses_encryption: false,
          export_compliance_is_exempt: false,
          export_compliance_contains_third_party_cryptography: false,
          export_compliance_contains_proprietary_cryptography: false,
          export_compliance_available_on_french_store: false
    }
  )
end

lane :build_android do
  gradle(
    project_dir: 'android',
    task: 'bundle',
    build_type: 'Release',
    print_command: true,
#       properties: {
#         "android.injected.signing.store.file" => "keystore.jks",
#         "android.injected.signing.store.password" => "store_password",
#         "android.injected.signing.key.alias" => "key_alias",
#         "android.injected.signing.key.password" => "key_password",
#       }
  )
end

lane :release_android do
    upload_to_play_store(
      aab: 'android/app/build/outputs/bundle/release/app-release.aab',
      json_key_data: {

      },
      package_name: 'com.tgcorp.carcity.vn'
    )
end
