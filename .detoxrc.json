{"testRunner": "jest", "configurations": {"ios.sim.debug": {"binaryPath": "ios/build/Build/Products/Debug-iphonesimulator/maxQ.app", "build": "xcodebuild -workspace ios/mypet.xcworkspace -scheme mypet -configuration Debug -sdk iphonesimulator -derivedDataPath ios/build -UseModernBuildSystem=NO", "type": "ios.simulator", "name": "iPhone 8"}, "ios.sim.release": {"binaryPath": "ios/build/Build/Products/Release-iphonesimulator/maxQ.app", "build": "xcodebuild -workspace ios/mypet.xcworkspace -scheme mypet -configuration Release -sdk iphonesimulator -derivedDataPath ios/build -UseModernBuildSystem=NO", "type": "ios.simulator", "name": "iPhone 8"}, "android.emu.debug": {"binaryPath": "android/app/build/outputs/apk/debug/app-debug.apk", "build": "cd android && ./gradlew assembleDebug assembleAndroidTest -DtestBuildType=debug && cd ..", "type": "android.emulator", "name": "Pixel_3_XL_API_29"}, "android.emu.release": {"binaryPath": "android/app/build/outputs/apk/release/app-release.apk", "build": "cd android && ./gradlew assembleRelease assembleAndroidTest -DtestBuildType=release && cd ..", "type": "android.emulator", "name": "Pixel_3_XL_API_29"}}}