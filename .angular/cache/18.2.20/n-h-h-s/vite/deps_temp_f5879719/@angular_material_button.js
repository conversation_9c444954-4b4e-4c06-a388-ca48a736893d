import {
  MAT_BUTTO<PERSON>_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  MatAnchor,
  MatButton,
  MatButtonModule,
  MatFabAnchor,
  MatF<PERSON>Button,
  <PERSON><PERSON>conAnch<PERSON>,
  <PERSON><PERSON><PERSON>Button,
  Mat<PERSON>iniFabAnchor,
  MatMiniFabButton
} from "./chunk-FFCIPI4O.js";
import "./chunk-VOLFWMAK.js";
import "./chunk-UND7GNPC.js";
import "./chunk-IGF3QYBZ.js";
import "./chunk-6Q4RANH6.js";
import "./chunk-FFZIAYYX.js";
import "./chunk-CXCX2JKZ.js";
export {
  MAT_BUTTON_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  MatButtonModule,
  Mat<PERSON><PERSON><PERSON>nch<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>on,
  <PERSON><PERSON><PERSON>Anch<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>iniF<PERSON>Anch<PERSON>,
  <PERSON><PERSON>iniFabButton
};
//# sourceMappingURL=@angular_material_button.js.map
