import {
  MAT_BUTTON_CONFIG,
  MAT_<PERSON>B_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  Mat<PERSON>nchor,
  MatButton,
  MatButtonModule,
  MatFabAnchor,
  MatFabButton,
  <PERSON><PERSON>conAnchor,
  <PERSON><PERSON><PERSON>Button,
  Mat<PERSON>iniFabAnchor,
  MatMiniFabButton
} from "./chunk-V2PPR5ZU.js";
import "./chunk-OFGQ4V5I.js";
import "./chunk-UND7GNPC.js";
import "./chunk-IGF3QYBZ.js";
import "./chunk-FFZIAYYX.js";
import "./chunk-6Q4RANH6.js";
import "./chunk-CXCX2JKZ.js";
export {
  MAT_BUTTON_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  <PERSON><PERSON>nch<PERSON>,
  <PERSON><PERSON>utt<PERSON>,
  MatButtonModule,
  Mat<PERSON><PERSON><PERSON>nch<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>on,
  <PERSON><PERSON><PERSON><PERSON>nch<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>iniF<PERSON>Anch<PERSON>,
  Mat<PERSON>iniFabButton
};
//# sourceMappingURL=@angular_material_button.js.map
