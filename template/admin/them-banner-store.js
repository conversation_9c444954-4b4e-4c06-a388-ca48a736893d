$(() => {

    $('.upload-new-picture').on('change', function () {
        var that = this
        if ($(that).prop('files')[0]) {
            var reader = new FileReader();
            reader.onload = function (e) {
                $(that).parent().find('img').attr('src', e.target.result)
            }
            reader.readAsDataURL($(this).prop('files')[0]);
        }
    })

    $('#form-add-news').on('submit', (e) => {
        e.preventDefault();
        let data = $('#form-add-news').serializeArray();
        var formData = new FormData();
        data.forEach(item => {
            formData.append(item.name, item.value);
        })
        formData.append(`thumbail`, $(`#upload-news-0`).prop('files')[0])
        ajaxFile('/admin/them-banner-store.html', formData, (res) => {
            if (!res.error) {
                displaySuccess(res.message);
                setTimeout(() => {
                    location.href = '/admin/quan-ly-banner-store.html'
                }, 500)
            } else {
                displayError(res.message);
            }
        })
    })
})
