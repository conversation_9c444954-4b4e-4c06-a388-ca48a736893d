$(() => {
    $('.upload-new-picture').on('change', function () {
        var that = this
        if ($(that).prop('files')[0]) {
            var reader = new FileReader();
            reader.onload = function (e) {
                $(that).parent().find('img').attr('src', e.target.result)
            }
            reader.readAsDataURL($(this).prop('files')[0]);
        }
    })

    function renderListCity() {
        let html = '<option value="toan-quoc">Toàn Quốc</option>'
        for (let key in countrys) {
            html += `
        <option value="${removeUtf8ReplaceAll(key)}">${key}</option>
    `
        }
        $('#city-state').html(html);

        let valDefault = $('#city-state').attr('value');
        if (valDefault) $('#city-state').val(valDefault).change();
    }

    renderListCity()

    $('#form-edit-banner-app').on('submit', (e) => {
        e.preventDefault();
        let data = $('#form-edit-banner-app').serializeArray();
        var formData = new FormData();
        data.forEach(item => {
            formData.append(item.name, item.value);
        })
        formData.append(`thumbail`, $(`#upload-news-0`).prop('files')[0])
        ajaxFile(`/admin/chinh-sua-banner-store/${news._id}.html`, formData, (res) => {
            if (!res.error) {
                displaySuccess(res.message);
                setTimeout(() => {
                    location.href = '/admin/quan-ly-banner-store.html'
                }, 500)
            } else {
                displayError(res.message);
            }
        })
    })
})
