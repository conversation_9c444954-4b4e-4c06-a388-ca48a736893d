$(function () {
    $('.dateTimeHandle').datetimepicker({
        i18n: {
            de: {
                months: [
                    'Tha.1', 'Tha.2', 'Tha.3', 'Tha.4',
                    'Tha.5', 'Tha.6', 'Tha.7', 'Tha.8',
                    'Tha.9', 'Tha.10', 'Tha.11', 'Tha.12',
                ],
                dayOfWeek: [
                    "CN", "Thu.2", "Thu.3", "Thu.4",
                    "Thu.5", "Thu.6", "Thu.7",
                ]
            }
        },
        format: 'H:i, d/m/Y',
        scrollMonth: false,
        scrollInput: false,
        scrollTime: false

    });
})
var dataShows = {
    1: bookExams,
    2: bookRooms,
    3: bookSpas,
}
for (let key in dataShows) {
    let timeStart = dataShows[key][0] ? dataShows[key][0].timeCheckIn : new Date().getTime() - 7 * 24 * 60 * 60 * 1000
    let timeEnd = dataShows[key][0] ? dataShows[key][0].timeCheckIn : new Date().getTime() + 30 * 24 * 60 * 60 * 1000
    dataShows[key].forEach(item => {
        if (timeStart > item.timeCheckIn) {
            timeStart = item.timeCheckIn
        }
        if (timeEnd < item.timeCheckIn) {
            timeEnd = item.timeCheckIn
        }
    })
    $(`#time-start-${key}`).val(moment(Number(timeStart)).format('HH:mm, DD/MM/YYYY'))
    $(`#time-end-${key}`).val(moment(Number(timeEnd)).format('HH:mm, DD/MM/YYYY'))
}
let itemsPerPage = 10;

let currentPage = {
    1: 1,
    2: 1,
    3: 1
}
let statusTable = {
    1: 'all',
    2: 'all',
    3: 'all'
}
let totalPages = {
    1: Math.ceil(dataShows[1].length / itemsPerPage),
    2: Math.ceil(dataShows[2].length / itemsPerPage),
    3: Math.ceil(dataShows[3].length / itemsPerPage),
}

function renderTable(index) {
    let html = '';
    let start = (currentPage[index] - 1) * itemsPerPage;
    let end = currentPage[index] * itemsPerPage
    if (end > dataShows[index].length - 1) {
        end = dataShows[index].length
    }
    for (let i = start; i <= end; i++) {
        if (dataShows[index][i]) {
            let schedule = dataShows[index][i]

            html += `
            <tr id="${schedule._id}">
                    <td>${i + 1}</td>
                    <td>${schedule.orderId}</td>
                    <td>${schedule.storeName}</td>
                    <td>${schedule.userName}</td>
                    <td>${schedule.phone}</td>
                    <td>${schedule.price ? numberFormat(schedule.price) + 'VNĐ' : schedule.totalPrice ? numberFormat(schedule.totalPrice) + 'VNĐ' : ''}</td>
                    <td>${schedule.coupon ? schedule.coupon : '--'}</td>
                    <td>${moment(Number(schedule.createAt)).format('HH:mm DD/MM/YYYY')}</td>
                    <td>${schedule.paymentMethod == 0 ? '<span class="badge badge-info">Tiền mặt</span>' : schedule.paymentMethod == 1 ? '<span class="badge badge-info">Online</span>' : '--'}</td>
                    <td>${schedule.isPayOnline == 0 ? '<span class="badge badge-danger">Chưa TT</span>' : schedule.isPayOnline == 1 ? '<span class="badge badge-success">Đã TT</span>' : '<span class="badge badge-warning">Chưa rõ</span>'}</td>
                    <td class="status">
                        <span class="${schedule.status == 0 ? 'wait' : schedule.status == 1 ? 'delivery_product' : schedule.status == 3 ? 'deliveried_product' : 'cancle'}">
                        ${schedule.status == 0 ? 'Chờ duyệt' : schedule.status == 1 ? 'Chờ làm dịch vụ' : schedule.status == 2 ? 'Thất bại' : 'Hoàn thành'}
                        </span>
                    </td>
                    <td>
                        <a href="/admin/chi-tiet-lich-hen/${schedule._id}.html?index=${index}" title="Chỉnh sửa">
                            <img src="/template/ui/img/edit.svg" alt="">
                        </a>
                    </td>
            </tr>
            `
        }
    }
    $(`.pagination-${index} p`).text(`Hiển thị từ ${end < 0 ? 0 : start + 1} đến ${end} trong tổng số ${dataShows[index].length} kết quả`)
    $(`.table-${index} .tbody-table`).html(html);
    renderPagination(index)
}

function renderPagination(index) {
    let min = 1;
    let max = 1
    let html = '';
    $(`.pagination-${index} ul`).html('');
    html += `<li class="first-page"  index="${index}"><a href="javascript:;"><img src="/template/ui/img/arrrow-2-left.svg" alt=""></a></li>`
    html += `<li class="prev-page"  index="${index}"><a href="javascript:;"><img src="/template/ui/img/arrow-1-left.svg" alt=""></a></li>`
    if (5 >= totalPages[index]) {
        min = 1;
        max = totalPages[index]
    } else if (Number(currentPage[index]) + 2 > Number(totalPages[index])) {
        max = totalPages[index];
        min = totalPages[index] - 4
    } else if (Number(currentPage[index]) - 2 < 1) {
        min = 1;
        max = 5
    } else {
        min = Number(currentPage[index]) - 2;
        max = Number(currentPage[index]) + 2
    }
    if (min == 2) {
        html += `<li class="page" index="${index}" value="1"> <a href="javascript:;">1</a> </li>`
    }
    for (let i = min; i <= max; i++) {
        html += `<li class="page ${i == Number(currentPage[index]) ? 'active' : ''}" index="${index}" value="${i}"> <a href="javascript:;">${i}</a> </li>`
    }
    if (max == totalPages[index] - 2) {
        html += `<li class="page ${totalPages[index] == Number(currentPage[index]) ? 'active' : ''}" index="${index}"  value="${totalPages[index]}"><a href="javascript:;" >${totalPages[index]}</a></li>`
    }
    html += `<li class="next-page" index="${index}"><a href="javascript:;"><img src="/template/ui/img/arrow-1-right.svg" alt=""></a></li>`
    html += `<li class="last-page" index="${index}"><a href="javascript:;"><img src="/template/ui/img/arrow-2-right.svg" alt=""></a></li>`
    $(`.pagination-${index} .pagination-list`).html(html);
}

function filterList(index, render = true) {
    index = Number(index)
    currentPage[index] = 1;
    dataShows[index] = []
    statusTable[index] = $(`#status-${index}`).val()
    let data0 = []
    if (index == 1) {
        data0 = bookExams
    } else if (index == 2) {
        data0 = bookRooms
    } else {
        data0 = bookSpas
    }
    let data1 = []
    if (statusTable[index] == 'all') {
        data1 = data0
    } else {
        data0.forEach(schedule => {
            if (schedule.status == statusTable[index]) {
                data1.push(schedule)
            }
        })
    }
    let key = removeUtf8($(`#search-goods-${index}`).val().trim())
    let data2 = []
    data1.forEach(item => {
        if (removeUtf8(item.userName).includes(key)
            || removeUtf8(item.email.toString()).includes(key)
            || removeUtf8((item.phone).toString()).includes(key)
            || removeUtf8(item.storeName.toString()).includes(key)) {
            data2.push(item)
        }
    })

    let data3 = []

    function formatTime(time) {
        time = time.split(',')
        let timeDate = time[1].trim().split('/')
        time[1] = timeDate[1] + '/' + timeDate[0] + '/' + timeDate[2]
        return new Date(time.join(' ')).getTime()
    }

    let timeStart = formatTime($(`#time-start-${index}`).val())
    let timeEnd = formatTime($(`#time-end-${index}`).val())
    data2.forEach(item => {
        if (item.timeCheckIn >= timeStart && item.timeCheckIn <= timeEnd) {
            data3.push(item)
        }
    })
    dataShows[index] = data3
    totalPages[index] = Math.ceil(dataShows[index].length / itemsPerPage)
    if (render) {
        renderTable(index)
    } else {
        return data3
    }
}

$(() => {
    $(document).on('click', '.prev-page', function (e) {
        e.preventDefault()
        let index = $(this).attr('index')
        if (currentPage[index] > 1) {
            currentPage[index] = currentPage[index] - 1;
            renderTable(index);
        }
    });
    $(document).on('click', '.first-page', function (e) {
        e.preventDefault()
        let index = $(this).attr('index')
        currentPage[index] = 1;
        renderTable(index);
    });
    $(document).on('click', '.last-page', function (e) {
        e.preventDefault()
        let index = $(this).attr('index')
        currentPage[index] = totalPages[index];
        renderTable(index);
    });
    $(document).on('click', '.next-page', function (e) {
        e.preventDefault()
        let index = $(this).attr('index')
        if (Number(currentPage[index]) < Number(totalPages[index])) {
            currentPage[index] = currentPage[index] + 1;
            renderTable(index);
        }
    });
    $(document).on('click', '.page', function (e) {
        e.preventDefault();
        let index = $(this).attr('index')
        let page = e.currentTarget.value;
        currentPage[index] = page;
        renderTable(index);
    });

    $(document).on('click', '.export_excel', function (e) {
        e.preventDefault();
        let index = $('.menu_tab li.active').attr('index')
        post(`/parse-file-lich-hen.html`, {
            // lichHen: filterList(index,false),
            indexService: Number(index),
        }, res => {
            if (res.error) {
                alert('Đã có lỗi');
            } else {
                var element = document.createElement('a');
                element.setAttribute('href', res.data.path);
                element.setAttribute('target', '_blank');
                element.style.display = 'none';
                document.body.appendChild(element);
                element.click();
                document.body.removeChild(element);
            }
        })
    });

    renderTable(1)
    renderTable(2)
    renderTable(3)
});

