require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'

platform :ios, '12.4'
install! 'cocoapods', :deterministic_uuids => false

target 'maxQ' do
 config = use_native_modules!

    # Flags change depending on the env values.
#     flags = get_default_flags()

  use_react_native!(
    :path => config[:reactNativePath],
    # Her<PERSON> is now enabled by default. Disable by setting this flag to false.
    # Upcoming versions of React Native may rely on get_default_flags(), but
    # we make it explicit here to aid in the React Native upgrade process.
    :hermes_enabled => true,
    :fabric_enabled => true,
    # Enables Flipper.
    #
    # Note that if you have use_frameworks! enabled, <PERSON>lipper will not work and
    # you should disable the next line.
#     :flipper_configuration => FlipperConfiguration.enabled(["Debug"], { 'Flipper' => '0.159.0' }),
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  permissions_path = '../node_modules/react-native-permissions/ios'

#   pod 'Permission-AppTrackingTransparency', :path => "#{permissions_path}/AppTrackingTransparency"
#   pod 'Permission-BluetoothPeripheral', :path => "#{permissions_path}/BluetoothPeripheral"
#   pod 'Permission-Calendars', :path => "#{permissions_path}/Calendars"
  pod 'Permission-Camera', :path => "#{permissions_path}/Camera"
  pod 'Permission-Contacts', :path => "#{permissions_path}/Contacts"
  pod 'Permission-FaceID', :path => "#{permissions_path}/FaceID"
  pod 'Permission-LocationAccuracy', :path => "#{permissions_path}/LocationAccuracy"
  pod 'Permission-LocationAlways', :path => "#{permissions_path}/LocationAlways"
  pod 'Permission-LocationWhenInUse', :path => "#{permissions_path}/LocationWhenInUse"
  pod 'Permission-MediaLibrary', :path => "#{permissions_path}/MediaLibrary"
  pod 'Permission-Microphone', :path => "#{permissions_path}/Microphone"
  pod 'Permission-Motion', :path => "#{permissions_path}/Motion"
  pod 'Permission-Notifications', :path => "#{permissions_path}/Notifications"
  pod 'Permission-PhotoLibrary', :path => "#{permissions_path}/PhotoLibrary"
  pod 'Permission-PhotoLibraryAddOnly', :path => "#{permissions_path}/PhotoLibraryAddOnly"
  pod 'Permission-Reminders', :path => "#{permissions_path}/Reminders"
  pod 'Permission-Siri', :path => "#{permissions_path}/Siri"
  pod 'Permission-SpeechRecognition', :path => "#{permissions_path}/SpeechRecognition"
  pod 'Permission-StoreKit', :path => "#{permissions_path}/StoreKit"

  pod 'Firebase', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true
  pod 'FirebaseCoreInternal', :modular_headers => true
#   pod 'FirebaseStorageInternal', :modular_headers => true
  pod 'FirebaseCoreExtension', :modular_headers => true
  pod 'FirebaseAppCheckInterop', :modular_headers => true
  pod 'FirebaseAuthInterop', :modular_headers => true
  pod 'FirebaseMessagingInterop', :modular_headers => true
  pod 'GTMSessionFetcher', :modular_headers => true
  pod 'FirebaseAppCheckInterop', :modular_headers => true
  pod 'FirebaseAuthInterop', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  pod 'MLKitVision' , :podspec => 'MLKitVision.podspec.json' , :modular_headers => true
  pod 'MLKitCommon' , :podspec => 'MLKitCommon.podspec.json' , :modular_headers => true
  pod 'CryptoSwift', '~> 1.8.2'
  pod 'Alamofire'

  # Yêu cầu bổ sung đoạn mã sau vào Podfile
  dynamic_frameworks = ['CryptoSwift', 'SnapKit', 'lottie-ios', 'KeychainSwift', 'ObjectMapper']
  pre_install do |installer|
    installer.pod_targets.each do |pod|
      if dynamic_frameworks.include?(pod.name)
        puts "Overriding the build_type to dynamic_framework for #{pod.name}"
        def pod.build_type;
          Pod::BuildType.dynamic_framework
        end
      end
    end
  end

  # Enables Flipper.
  #
  # Note that if you have use_frameworks! enabled, Flipper will not work and
  # you should disable these next few lines.
  post_install do |installer|

      # Apple Silicon builds require a library path tweak for Swift library discovery or "symbol not found" for swift things
      installer.aggregate_targets.each do |aggregate_target|
        aggregate_target.user_project.native_targets.each do |target|
          target.build_configurations.each do |config|
            config.build_settings['LIBRARY_SEARCH_PATHS'] = ['$(SDKROOT)/usr/lib/swift', '$(inherited)']
          end
        end
        aggregate_target.user_project.save
       end

       # Flipper requires a crude patch to bump up iOS deployment target, or "error: thread-local storage is not supported for the current target"
      # I'm not aware of any other way to fix this one other than bumping iOS deployment target to match react-native (iOS 11 now)
      installer.pods_project.targets.each do |target|
        if target.respond_to?(:product_type) and target.product_type == "com.apple.product-type.bundle"
              target.build_configurations.each do |config|
                  config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
              end
            end
        target.build_configurations.each do |config|
          config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
         end
       end

      # Build Active Architecture Only "NO" (react-native-maps)
       installer.pods_project.targets.each do |target|
          target.build_configurations.each do |config|
            config.build_settings["ONLY_ACTIVE_ARCH"] = "NO"
          end
        end

       installer.pods_project.build_configurations.each do |config|
         config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
       end
       react_native_post_install(installer)
       __apply_Xcode_12_5_M1_post_install_workaround(installer)

       installer.pods_project.targets.each do |target|
         if ['CryptoSwift', 'SnapKit', 'lottie-ios', 'KeychainSwift', 'ObjectMapper'].include? "#{target}"
           target.build_configurations.each do |config|
             config.build_settings['BUILD_LIBRARY_FOR_DISTRIBUTION'] = 'YES'
             # Xcode 14 supports only iOS >= 11.0.
             deployment_target = config.build_settings['IPHONEOS_DEPLOYMENT_TARGET']
             if !deployment_target.nil? && !deployment_target.empty? && deployment_target.to_f < 12.0
               config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
             end
           end
         end
       end

    end
end

