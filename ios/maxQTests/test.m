

(BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
  
  
  
  [[FBSDKApplicationDelegate sharedInstance]
   application:application didFinishLaunchingWithOptions:launchOptions]; return YES; } -

(BOOL)application:(UIApplication *)application openURL:(NSURL *)url options:(nonnull NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options { [[FBSDKApplicationDelegate sharedInstance] application:application openURL:url options:options]; return YES; }
    
