#!/bin/sh
#!/bin/sh

# Installing Node.js
echo "🥰 ==> Installing Node.js"
brew install node@18

# Adding Node.js to PATH
echo "🥰 ==> Adding Node.js to PATH"
export PATH="/usr/local/opt/node@18/bin:$PATH"

# Moving into parent directory
echo "🥰 --> Moving into parent directory"
cd ../..

# Installing Node modules and redirecting to /ios
echo "🥰 ==> Installing Node modules"
npm install

# Navigate to iOS folder
echo "🥰 --> Moving into /ios directory"
cd ios

# Install CocoaPods using Homebrew (if not already installed)
echo "🥰 ==> Installing CocoaPods using Homebrew."
brew install cocoapods

# Install dependencies managed with CocoaPods again
echo "🥰 ==> Installing dependencies with CocoaPods"
pod install

