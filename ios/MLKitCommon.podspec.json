{"authors": "Google, Inc.", "dependencies": {"GTMSessionFetcher/Core": ["~> 2.1.0"], "GoogleDataTransport": ["~> 9.0"], "GoogleToolboxForMac/Logger": ["~> 2.1"], "GoogleToolboxForMac/NSData+zlib": ["~> 2.1"], "GoogleToolboxForMac/NSDictionary+URLArguments": ["~> 2.1"], "GoogleUtilities/UserDefaults": ["~> 7.0"], "GoogleUtilitiesComponents": ["~> 1.0"], "Protobuf": ["~> 3.12"]}, "description": "This is a top level pod containing common elements for all the ML Kit pods, including APIs for managing models.", "frameworks": ["Foundation", "LocalAuthentication"], "homepage": "https://developers.google.com/ml-kit/guides", "libraries": ["c++", "sqlite3", "z"], "license": {"text": "Copyright 2022 Google", "type": "Copyright"}, "module_name": "MLKitCommon", "name": "MLKitCommon", "platforms": {"ios": "10.0"}, "pod_target_xcconfig": {"EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64"}, "preserve_paths": ["CHANGELOG.md", "README.md", "NOTICES"], "source": {"http": "https://dl.google.com/dl/cpdc/127d68bf72709c34/MLKitCommon-8.0.0.tar.gz"}, "summary": "ML Kit Common SDK for iOS", "user_target_xcconfig": {"EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64"}, "vendored_frameworks": ["Frameworks/MLKitCommon.framework"], "version": "8.0.0"}