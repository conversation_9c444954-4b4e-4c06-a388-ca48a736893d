<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Giai Phap Ung Luong</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb297103988139929</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.139958195942-dkohtmuq33dh5nl22r7qsc6e8cbqq1eg</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FacebookAppID</key>
	<string>297103988139929</string>
	<key>FacebookDisplayName</key>
	<string>maxQ</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fbapi</string>
		<string>fbapi20130214</string>
		<string>fbapi20130410</string>
		<string>fbapi20130702</string>
		<string>fbapi20131010</string>
		<string>fbapi20131219</string>
		<string>fbapi20140410</string>
		<string>fbapi20140116</string>
		<string>fbapi20150313</string>
		<string>fbapi20150629</string>
		<string>fbapi20160328</string>
		<string>fbauth</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NFCReaderUsageDescription</key>
	<string>SCAN ID CHIP</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>$(PRODUCT_NAME) cần truy cập vào thư viện đa phương tiện</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>$(PRODUCT_NAME) yêu cầu truy cập vào Bluetooh để gửi dữ liệu giữa các thiết bị</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>$(PRODUCT_NAME) cần truy cập vào Bluetooth để gửi dữ liệu giữa các thiết bị</string>
	<key>NSCalendarsUsageDescription</key>
	<string>$(PRODUCT_NAME) cần truy cập vào lịch cài đặt nhắc nhở</string>
	<key>NSCameraUsageDescription</key>
	<string>$(PRODUCT_NAME) cần quyền camera để chụp ảnh để cập nhật ảnh đại diện, ảnh bìa tài khoản, hoặc gửi tin nhắn ảnh</string>
	<key>NSContactsUsageDescription</key>
	<string>$(PRODUCT_NAME) cần truy cập vào danh bạ</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>$(PRODUCT_NAME) yêu cầu cho phép sử dụng vị trí ở chế độ nền</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>$(PRODUCT_NAME) yêu cầu cho phép sử dụng vị trí khi sử dụng ứng dụng</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>$(PRODUCT_NAME) collect the user&apos;s location to locate the user to the nearest store. Users can agree and refuse to grant location collection permission when used.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>$(PRODUCT_NAME) yêu cầu cho phép sử dụng thu âm giọng nói</string>
	<key>NSMotionUsageDescription</key>
	<string>$(PRODUCT_NAME) cần truy cập vào cảm biến</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>$(PRODUCT_NAME) cần tuy cập Photo Library để cập nhật ảnh đại diện, ảnh bìa tài khoản, hoặc gửi tin nhắn ảnh</string>
	<key>NSSiriUsageDescription</key>
	<string>$(PRODUCT_NAME) yêu cầu cho phép sử dụng Siri</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>$(PRODUCT_NAME) yêu cầu cho phép sử dụng nhận diện giọng nói</string>
	<key>UIAppFonts</key>
	<array>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Fontisto.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Zocial.ttf</string>
		<string>SF-Pro-Display-Black.otf</string>
		<string>SF-Pro-Display-BlackItalic.otf</string>
		<string>SF-Pro-Display-Bold.otf</string>
		<string>SF-Pro-Display-BoldItalic.otf</string>
		<string>SF-Pro-Display-Heavy.otf</string>
		<string>SF-Pro-Display-HeavyItalic.otf</string>
		<string>SF-Pro-Display-Light.otf</string>
		<string>SF-Pro-Display-LightItalic.otf</string>
		<string>SF-Pro-Display-Medium.otf</string>
		<string>SF-Pro-Display-MediumItalic.otf</string>
		<string>SF-Pro-Display-Regular.otf</string>
		<string>SF-Pro-Display-RegularItalic.otf</string>
		<string>SF-Pro-Display-Semibold.otf</string>
		<string>SF-Pro-Display-SemiboldItalic.otf</string>
		<string>SF-Pro-Display-Thin.otf</string>
		<string>SF-Pro-Display-ThinItalic.otf</string>
		<string>SF-Pro-Display-Ultralight.otf</string>
		<string>SF-Pro-Display-UltralightItalic.otf</string>
		<string>SFProDisplay-Black.otf</string>
		<string>SFProDisplay-BlackItalic.otf</string>
		<string>SFProDisplay-Bold.otf</string>
		<string>SFProDisplay-BoldItalic.otf</string>
		<string>SFProDisplay-Heavy.otf</string>
		<string>SFProDisplay-HeavyItalic.otf</string>
		<string>SFProDisplay-Light.otf</string>
		<string>SFProDisplay-LightItalic.otf</string>
		<string>SFProDisplay-Medium.otf</string>
		<string>SFProDisplay-MediumItalic.otf</string>
		<string>SFProDisplay-Regular.otf</string>
		<string>SFProDisplay-RegularItalic.otf</string>
		<string>SFProDisplay-Semibold.otf</string>
		<string>SFProDisplay-SemiboldItalic.otf</string>
		<string>SFProDisplay-Thin.otf</string>
		<string>SFProDisplay-ThinItalic.otf</string>
		<string>SFProDisplay-Ultralight.otf</string>
		<string>SFProDisplay-UltralightItalic.otf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>splashscreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>com.apple.developer.nfc.readersession.felica.systemcodes</key>
	<array>
		<string>12FC</string>
	</array>
	<key>com.apple.developer.nfc.readersession.iso7816.select-identifiers</key>
	<array>
		<string>00000000000000</string>
		<string>A0000002471001</string>
		<string>A0000002472001</string>
	</array>
</dict>
</plist>
