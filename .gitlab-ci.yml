image: reactnativecommunity/react-native-android

before_script:
  - npx envinfo

stages:
  - build
  - deploy

build:
  stage: build
  script:
    - npm install --legacy-peer-deps
    - cd android && sh android-version-and-tag-automation.sh && ./gradlew bundleRelease
  artifacts:
    paths:
      - android/app/build/outputs/

deploy:
  stage: deploy
  script:
    - curl -F "token=ceae3a6ca79098d4f308fe249d4c6fa4f07a17bd" \
            -F "file=@android/app/build/outputs/apk/release/app-release.apk" \
            -F "message=New build from GitLab CI/CD" \
            https://deploygate.com/api/users/thanhdevapp/apps
