# React Native Project Base IgniteProject

[![CircleCI](https://circleci.com/gh/infinitered/ignite-bowser.svg?style=svg)](https://circleci.com/gh/infinitered/ignite-bowser)

## The latest and greatest boilerplate for Infinite Red opinions

This is the boilerplate that [Infinite Red](https://infinite.red) uses as a way to test bleeding-edge changes to our React Native stack.

Currently includes:

- React Native
- React Navigation
- MobX State Tree
- TypeScript
- And more!

## Quick Start

The Ignite Bowser boilerplate project's structure will look similar to this:

```
ignite-project
├── app
│   ├── components
│   ├── i18n
│   ├── utils
│   ├── models
│   ├── navigation
│   ├── screens
│   ├── services
│   ├── theme
│   ├── app.tsx
├── test
│   ├── __snapshots__
│   ├── mock-i18n.ts
│   ├── mock-reactotron.ts
│   ├── setup.ts
├── README.md
├── android
│   ├── app
│   ├── build.gradle
│   ├── gradle
│   ├── gradle.properties
│   ├── gradlew
│   ├── gradlew.bat
│   ├── keystores
│   └── settings.gradle
├── ignite
│   ├── ignite.json
│   └── plugins
├── index.ts
├── ios
│   ├── IgniteProject
│   ├── IgniteProject-tvOS
│   ├── IgniteProject-tvOSTests
│   ├── IgniteProject.xcodeproj
│   └── IgniteProjectTests
├── .env
└── package.json

```

### ./app directory

Included in an Ignite boilerplate project is the `app` directory. This is a directory you would normally have to create when using vanilla React Native.

The inside of the src directory looks similar to the following:

```
app
│── components
│── i18n
├── models
├── navigation
├── screens
├── services
├── theme
├── utils
└── app.tsx
```

**components**
This is where your React components will live. Each component will have a directory containing the `.tsx` file, along with a story file, and optionally `.presets`, and `.props` files for larger components. The app will come with some commonly used components like Button.

**i18n**
This is where your translations will live if you are using `react-native-i18n`.

**models**
This is where your app's models will live. Each model has a directory which will contain the `mobx-state-tree` model file, test file, and any other supporting files like actions, types, etc.

**navigation**
This is where your `react-navigation` navigators will live.

**screens**
This is where your screen components will live. A screen is a React component which will take up the entire screen and be part of the navigation hierarchy. Each screen will have a directory containing the `.tsx` file, along with any assets or other helper files.

**services**
Any services that interface with the outside world will live here (think REST APIs, Push Notifications, etc.).

**theme**
Here lives the theme for your application, including spacing, colors, and typography.

**utils**
This is a great place to put miscellaneous helpers and utilities. Things like date helpers, formatters, etc. are often found here. However, it should only be used for things that are truely shared across your application. If a helper or utility is only used by a specific component or model, consider co-locating your helper with that component or model.

**app.tsx** This is the entry point to your app. This is where you will find the main App component which renders the rest of the application. This is also where you will specify whether you want to run the app in storybook mode.

### ./ignite directory

The `ignite` directory stores all things Ignite, including CLI and boilerplate items. Here you will find generators, plugins and examples to help you get started with React Native.

### ./storybook directory

This is where your stories will be registered and where the Storybook configs will live

### ./test directory

This directory will hold your Jest configs and mocks, as well as your [storyshots](https://github.com/storybooks/storybook/tree/master/addons/storyshots) test file. This is a file that contains the snapshots of all your component storybooks.

## Running Storybook

From the command line in your generated app's root directory, enter `yarn run storybook`
This starts up the storybook server.

In `index.ts`, change `SHOW_STORYBOOK` to `true` and reload the app.

For Visual Studio Code users, there is a handy extension that makes it easy to load Storybook use cases into a running emulator via tapping on items in the editor sidebar. Install the `React Native Storybook` extension by `Orta`, hit `cmd + shift + P` and select "Reconnect Storybook to VSCode". Expand the STORYBOOK section in the sidebar to see all use cases for components that have `.story.tsx` files in their directories.

## Previous Boilerplates

- [2017 aka Andross](https://github.com/infinitered/ignite-andross)
- [2016 aka Ignite 1.0](https://github.com/infinitered/ignite-ir-boilerplate-2016)

## Premium Support

[Ignite CLI](https://infinite.red/ignite), [Ignite Andross](https://github.com/infinitered/ignite-andross), and [Ignite Bowser](https://github.com/infinitered/ignite-bowser), as open source projects, are free to use and always will be. [Infinite Red](https://infinite.red/) offers premium Ignite support and general mobile app design/development services. Email us at [<EMAIL>](mailto:<EMAIL>) to get in touch with us for more details.

export ANDROID_HOME=/Users/<USER>/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/tools/bin
export PATH=$PATH:$ANDROID_HOME/platform-tools


yarn upgrade --latest && npx pod-install so you are up to date, then npx react-native-clean-project

#CODE-PUSH

appcenter codepush release-react -a mypet/mypet -d Production -t "2.3" --mandatory true
code-push deployment ls mypet  -k
A2EeOuDvAoY0appkhEX3KEzclshxRyEJ-vlaq

https://viblo.asia/p/hoc-react-native-tu-co-ban-den-nang-cao-phan-6-gioi-thieu-appcenter-codepush-va-1-so-chia-se-ca-nhan-gAm5yWJOZdb

https://ospfolio.com/two-way-to-change-default-font-family-in-react-native/

mypet-1 name android
mypet name ios

appcenter codepush deployment list -a mypet/mypet-1 --displayKeys
appcenter codepush deployment history -a mypet/mypet-1 Production
appcenter codepush deployment history -a mypet/mypet-1 Staging
appcenter codepush release-react -a mypet/mypet-1 -m -d Staging -t "*" 
appcenter codepush rollback -a mypet/mypet-1 Production --target-release v11

#userModal Context

`const { showError, showSuccess } = useContext(ModalContext)`

call function `showError('title' , 'message')` or `showSucess('title' , 'message')`

props
`title, message`


# How to run detox android

1. run command `build-android:e2e`
2. run command `test-android:e2e`

3 (Optional). Problems: replace android name if not exist msg error 
` Cannot boot Android Emulator with the name`
FIX: get list android name with command 

    `emulator -list-avds`

    config name android in file `.detoxrc.json` at root folder project
 
4. re-run command above


# IOS 
https://mymai91.github.io/reactnative/2019/04/01/react-native-interact-with-end-to-end-test.html

#fastlane add_plugin increment_version_name

MK Apple fastlane release mthn-aiup-bpbw-qzpw


// Deprecated Prop Types
get ColorPropType(): $FlowFixMe {
return require('deprecated-react-native-prop-types').ColorPropType;
},
get EdgeInsetsPropType(): $FlowFixMe {
return require('deprecated-react-native-prop-types').EdgeInsetsPropType;
},
get PointPropType(): $FlowFixMe {
return require('deprecated-react-native-prop-types').PointPropType;
},
get ViewPropTypes(): $FlowFixMe {
return require('deprecated-react-native-prop-types').ViewPropTypes;
},

# Huong dẫn tạo store

`npx ignite-cli g model  insuranceStore`

https://github.com/pvcb-NHS/PVCBeKYC-RN/tree/v0.4.x-stable
# PVCB Đăng ký yarn config set registry https://gitlab.com/api/v4/projects/55757852/packages/npm/
`yarn add @pvcb-nhs/pvcb-ekyc-rn`
