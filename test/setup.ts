// we always make sure 'react-native' gets included first
import 'react-native'

// libraries to mock
import './mock-async-storage'
import './mock-i18n'
import './mock-react-native-localize'
import './mock-reactotron'
import './mock-firebase'

import mockRNCNetInfo from '@react-native-community/netinfo/jest/netinfo-mock.js'

jest.mock('react-native-push-notification', () => ({
  configure: jest.fn(),
  onRegister: jest.fn(),
  onNotification: jest.fn(),
  addEventListener: jest.fn(),
  requestPermissions: jest.fn(),
}))

jest.mock('react-native-gesture-handler', () => ({
  Direction: jest.fn(),
}))

jest.mock('react-native-appearance', () => ({
  Appearance: {
    getColorScheme: jest.fn()
  }
}))

jest.mock('react-native-simple-toast', () => ({
  SimpleToast: jest.fn()
}))

jest.mock('react-native-share', () => ({
  default: jest.fn(),
}))

jest.mock('react-native-device-info', () => ({
  default: jest.fn(),
}))

jest.mock('react-native-reanimated', () =>
  jest.requireActual('../node_modules/react-native-reanimated/mock'),
)

jest.mock('@react-native-community/geolocation', () => ({
  addListener: jest.fn(),
  getCurrentPosition: jest.fn(),
  removeListeners: jest.fn(),
  requestAuthorization: jest.fn(),
  setConfiguration: jest.fn(),
  startObserving: jest.fn(),
  stopObserving: jest.fn()
}))
jest.mock('@react-native-community/netinfo', () => mockRNCNetInfo)

declare global {
  // @ts-ignore
  let __TEST__
}

jest.mock('react-native-reanimated', () => require('react-native-reanimated/mock'))
