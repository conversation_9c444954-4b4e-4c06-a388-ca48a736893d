#!/bin/bash

# Đường dẫn đến file build.gradle
FILE="app/build.gradle"

# Lấy giá trị hiện tại của versionCode và versionName
CURRENT_VERSION_CODE=$(awk '/versionCode/ {print $2; exit}' $FILE)
CURRENT_VERSION_NAME=$(awk '/versionName/ {print $2; exit}' $FILE)

# Tăng giá trị versionCode lên 1
NEW_VERSION_CODE=$((CURRENT_VERSION_CODE + 1))

# Tạo giá trị mới cho versionName dựa trên versionCode
NEW_VERSION_NAME="1.2.$NEW_VERSION_CODE"

# Cập nhật versionCode và versionName trong file
perl -i -pe "s/versionCode \d+/versionCode $NEW_VERSION_CODE/" $FILE
perl -i -pe "s/versionName \"[^\"]*\"/versionName \"$NEW_VERSION_NAME\"/" $FILE

# In giá trị sau khi cập nhật
echo "Updated content of versionCode:"
awk '/versionCode/ {print $0; exit}' $FILE
echo "Updated content of versionName:"
awk '/versionName/ {print $0; exit}' $FILE

echo "Updated versionCode to $NEW_VERSION_CODE and versionName to \"$NEW_VERSION_NAME\""
