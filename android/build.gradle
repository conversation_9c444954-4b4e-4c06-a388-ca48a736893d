// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "31.0.0"
        minSdkVersion = 24
        compileSdkVersion = 33
        targetSdkVersion = 33
        supportLibVersion = "28.0.0"
        googlePlayServicesVersion = "16.1.0" // default: "+"
        firebaseVersion = "17.3.4" // default: "+"
        mediaCompatVersion = '1.0.1'
        supportV4Version = '1.0.0'
        firebaseIidVersion = "19.0.1"
        agp_version = '7.2.2'

       //https://github.com/joltup/rn-fetch-blob/issues/826
        okhttp = "4.10.0" // <----- add this line

        if (System.properties['os.arch'] == "aarch64") {
            // For M1 Users we need to use the NDK 24 which added support for aarch64
            ndkVersion = "24.0.8215888"
        } else {
            // Otherwise we default to the side-by-side NDK version from AGP.
            ndkVersion = "21.4.7075529"
        }

    }
    ext.kotlin_version = '1.7.0'

    repositories {
        google()
        jcenter()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:$agp_version")
        classpath 'com.google.gms:google-services:4.3.13'
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("de.undercouch:gradle-download-task:5.0.1")
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

def REACT_NATIVE_VERSION = new File(['node', '--print', "JSON.parse(require('fs').readFileSync(require.resolve('react-native/package.json'), 'utf-8')).version"].execute(null, rootDir).text.trim())

allprojects {
    repositories {
        google()
        jcenter()
        mavenCentral {
            // We don't want to fetch react-native from Maven Central as there are
            // older versions over there.
            content {
                excludeGroup "com.facebook.react"
            }
        }

        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url("$rootDir/../node_modules/react-native/android")
        }
        maven {
            // Android JSC is installed from npm
            url("$rootDir/../node_modules/jsc-android/dist")
        }

        // ADD THIS
        maven { url 'https://maven.google.com' }
        maven { url 'https://www.jitpack.io' }
        maven {
            url = uri("https://maven.pkg.github.com/pvcbdigibank/SDK-KYC-v2-ANDROID")
            credentials {
                username = ""
                password = "****************************************"
            }
        }

        maven {
            // All of Detox' artifacts are provided via the npm module
            url "$rootDir/../node_modules/detox/Detox-android"
        }
    }

    configurations.all {
        resolutionStrategy {
            force "com.facebook.react:react-native:" + REACT_NATIVE_VERSION
        }
    }
}
