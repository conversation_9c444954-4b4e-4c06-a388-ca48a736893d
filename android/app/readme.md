Sử dụng Export and upload a key from Java keystore trên CHPLAY

Download the Play Encrypt Private Key (PEPK) tool. Download source code
Run the tool using the command below to export and encrypt your private key. Replace the arguments, and enter your keystore and key passwords when prompted.
$ java -jar pepk.jar --keystore=foo.keystore --alias=foo --output=output.zip --include-cert --encryptionkey=eb10fe8f7c7c9df715022017b00c6471f8ba8170b13049a11e6c09ffe3056a104a3bbe4ac5a955f4ba4fe93fc8cef27558a3eb9d2a529a2092761fb833b656cd48b9de6a
private_key.pepk
For increased security, create a new upload key (optional).
Show instructions

keytool -list -v -keystore android/app/debug.keystore -alias androiddebugkey -storepass android -keypass android
keytool -list -v -keystore android/app/release.keystore -alias maxq -storepass Thanhnx@123 -keypass Thanhnx@123

#Method 2
# APK file
keytool -printcert -jarfile app.apk

# AAB file
keytool -printcert -jarfile app.aab
