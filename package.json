{"name": "maxQ", "version": "1.3.1", "version-ios": "1.8.4", "version-android": "1.1", "private": true, "scripts": {"android": "react-native run-android", "android-release": "react-native run-android --variant release", "ios": "npx react-native run-ios --simulator=\"iPhone 15 Pro\"", "ios-device": "npx react-native run-ios --device", "ios-release": "react-native run-ios --configuration Release --simulator=\"iPhone 14 Pro Max\"", "start": "react-native start", "dev-tools": "react-devtools", "test": "jest", "lint": "eslint index.js app storybook test --fix --ext .js,.ts,.tsx", "lint-err": "eslint index.js app storybook test --fix --ext .js,.ts,.tsx --quiet", "test-lint": "eslint index.js app storybook test --ext .js,.ts,.tsx", "compile": "tsc --noEmit -p . --pretty", "format": "npm-run-all format:*", "format:js": "prettier --write '**/*.js'", "format:json": "prettier --write '**/*.json'", "format:md": "prettier --write '**/*.md'", "format:ts": "prettier --write '**/*.ts{,x}'", "patch": "jetify && patch-package", "patch-rn": "patch-package react-native", "postinstall": "patch-package", "storybook": "start-storybook -p 9001 -c ./storybook", "adb": "adb reverse tcp:9090 tcp:9090 && adb reverse tcp:3000 tcp:3000 && adb reverse tcp:9001 tcp:9001 && adb reverse tcp:8081 tcp:8081 && adb reverse tcp:5000 tcp:5000 && adb reverse tcp:8097 tcp:8097", "fixenfile": "echo kern.maxfiles=65536 | sudo tee -a /etc/sysctl.conf && echo kern.maxfilesperproc=65536 | sudo tee -a /etc/sysctl.conf && sudo sysctl -w kern.maxfiles=65536 && sudo sysctl -w kern.maxfilesperproc=65536", "clean-ios": "rm -rf ~/Library/Developer/Xcode/DerivedData/* && rm -rf ios/build/*", "clean-android": "cd android && ./gradlew clean && cd ..", "reinstall": "rm -rf node_modules && rm -rf package.lock && rm -rf yarn.lock && yarn && cd ios && rm -rf Podfile.lock && rm -rf Pods && npx pod-install", "clean-i-pod": "cd ios && rm -rf Podfile.lock && rm -rf Pods && npx pod-install", "kill-port": "npx kill-port 8081", "ios-xcode": "cd ios && open *.xcworkspace && cd ..", "build-android": "cd android && sh android-version-and-tag-automation.sh && ./gradlew bundleRelease && sh upload-deploygate.sh && open app/build/outputs/bundle/release", "build-android-dg": "cd android && sh android-version-and-tag-automation.sh && ./gradlew bundleRelease && sh upload-deploygate.sh && open app/build/outputs/bundle/release", "build-android-apk": "cd android && ./gradlew assembleRelease", "up-version": "bundle exec fastlane bump", "bump-patch": "npm version patch --no-git-tag-version && bundle exec fastlane bump", "bump-minor": "npm version minor --no-git-tag-version && bundle exec fastlane bump", "bump-major": "npm version major --no-git-tag-version && bundle exec fastlane bump", "dg-android": "yarn build-android && dg deploy android/app/build/outputs/bundle/release/app-release.aab", "dg-android-apk": "fastlane bump_android && yarn build-android-apk && dg deploy android/app/build/outputs/apk/release/app-release.apk", "upload-chplay": "fastlane supply --aab android/app/build/outputs/bundle/release/app-release.aab", "dg-ios": "dg deploy maxQ.ipa", "clean": "react-native-clean-project", "pod-update": "cd ios && pod repo update && pod install && pod update", "build:ios-index": "react-native bundle --entry-file='index.js' --bundle-output='./ios/main.jsbundle' --dev=false --platform='ios'", "build:tf": "fastlane inc_ver_ios && fastlane build_ios && fastlane release_beta", "submit-ios": "yarn tf-ios && fastlane submit_review", "build-ipa": "fastlane build_ios", "beta-android": "fastlane build_android && fastlane release_android", "install-fastlane": "brew install fastlane && sudo gem install bundler && rbenv rehash && cd fastlane && sudo bundle install && cd ..", "ad-device": "npx react-native run-android --deviceId=HZQL1838LAKC2200995"}, "dependencies": {"@gorhom/bottom-sheet": "^4.3.2", "@react-native-async-storage/async-storage": "^1.17.10", "@react-native-community/art": "^1.2.0", "@react-native-community/datetimepicker": "^3.0.1", "@react-native-community/geolocation": "^2.0.2", "@react-native-community/hooks": "^2.6.0", "@react-native-community/netinfo": "^5.9.9", "@react-native-community/push-notification-ios": "1.7.1", "@react-native-firebase/analytics": "^16.0.0", "@react-native-firebase/app": "^16.0.0", "@react-native-firebase/auth": "^16.0.0", "@react-native-firebase/crashlytics": "^16.0.0", "@react-native-firebase/database": "^16.0.0", "@react-native-firebase/firestore": "^16.0.0", "@react-native-firebase/messaging": "^16.0.0", "@react-native-firebase/remote-config": "^16.0.0", "@react-native/virtualized-lists": "^0.75.0-main", "@react-navigation/bottom-tabs": "^5.8.0", "@react-navigation/native": "^5.7.3", "@react-navigation/stack": "^5.9.0", "@rodw95/use-cancelable-promise": "^0.0.2", "@types/html-to-text": "^9.0.4", "@types/lodash": "^4.14.161", "apisauce": "1.1.2", "eslint-plugin-unused-imports": "^2.0.0", "geolib": "^3.3.1", "html-to-text": "^9.0.5", "i18n-js": "^3.0.11", "i18next": "^19.7.0", "jest-circus": "^26.6.1", "libphonenumber-js": "^1.7.27", "lodash.shuffle": "^4.2.0", "lodash.throttle": "4.1.1", "mobx": "^5.15.6", "mobx-react": "^6.2.5", "mobx-react-lite": "^2.0.7", "mobx-state-tree": "^3.14.1", "moment": "^2.27.0", "moment-timezone": "^0.5.34", "nativewind": "^2.0.11", "node-html-parser": "^1.2.21", "numeral": "^2.0.6", "prop-types": "15.7.2", "ramda": "0.27.1", "react": "18.1.0", "react-hook-form": "^4.4.8", "react-i18next": "^11.7.0", "react-is-mounted-hook": "^1.0.3", "react-native": "0.70.6", "react-native-action-button": "^2.8.5", "react-native-animatable": "^1.3.3", "react-native-appstate-hook": "^1.0.4", "react-native-awesome-alerts": "^1.4.2", "react-native-calendars": "^1.1307.0", "react-native-camera": "^4.2.1", "react-native-chart-kit": "^6.6.1", "react-native-confirmation-code-field": "^6.5.0", "react-native-currency-input": "^0.1.3", "react-native-date-picker": "^3.3.0", "react-native-device-info": "^10.3.0", "react-native-dialog-input": "^1.0.8", "react-native-document-picker": "3.5.3", "react-native-dropdown-picker": "^5.4.2", "react-native-elements": "^2.3.2", "react-native-emoji-selector": "^0.2.0", "react-native-event-listeners": "^1.0.7", "react-native-exit-app": "^1.1.0", "react-native-extra-dimensions-android": "^1.2.5", "react-native-fab": "^1.0.17", "react-native-fast-image": "^8.3.2", "react-native-gallery-swiper": "^1.23.1", "react-native-geocoding": "^0.4.0", "react-native-gesture-handler": "^1.8.0", "react-native-gifted-chat": "^0.16.3", "react-native-gogo-spin": "^0.0.5", "react-native-head-tab-view": "^4.0.0-rc.13", "react-native-image-crop-picker": "^0.26.1", "react-native-image-gallery": "^2.1.5", "react-native-image-slider-box": "^1.0.12", "react-native-inappbrowser-reborn": "^3.7.0", "react-native-iphone-x-helper": "^1.2.1", "react-native-keyboard-aware-scroll-view": "^0.9.1", "react-native-linear-gradient": "^2.5.6", "react-native-localize": "^1.3.1", "react-native-map-link": "^2.7.19", "react-native-masked-text": "^1.13.0", "react-native-modal": "^13.0.1", "react-native-modal-datetime-picker": "^14.0.0", "react-native-modalize": "^2.1.1", "react-native-month-year-picker": "^1.9.0", "react-native-offline": "^5.7.0", "react-native-pager-view": "5.4.25", "react-native-permissions": "3.6.1", "react-native-picker-select": "^8.0.0", "react-native-push-notification": "^8.1.1", "react-native-qrcode-scanner": "^1.5.5", "react-native-quick-md5": "^3.0.4", "react-native-radio-buttons": "latest", "react-native-reanimated": "3.8.1", "react-native-render-html": "^5.1.1", "react-native-responsive-dimensions": "^3.1.1", "react-native-safe-area-context": "4.4.1", "react-native-screens": "3.18.0", "react-native-scroll-menu": "^1.1.1", "react-native-share": "^3.7.1", "react-native-simple-toast": "1.1.4", "react-native-snap-carousel": "^3.9.1", "react-native-spinkit": "^1.5.0", "react-native-splash-screen": "3.2.0", "react-native-star-rating": "^1.1.0", "react-native-status-bar-height": "^2.4.0", "react-native-store-version": "^1.3.1", "react-native-super-grid": "^4.0.3", "react-native-svg": "^12.1.0", "react-native-svg-uri": "^1.2.3", "react-native-swiper": "^1.6.0", "react-native-tab-view": "^3.3.2", "react-native-tab-view-collapsible-header": "^2.0.1", "react-native-timeago": "^0.5.0", "react-native-use-websocket": "^0.2.0", "react-native-uuid": "^1.4.9", "react-native-vector-icons": "^9.2.0", "react-native-video": "^5.1.0-alpha8", "react-native-webview": "^11.22.7", "react-navigation-collapsible": "^5.8.1", "react-navigation-stack": "^2.8.2", "reactotron-mst": "^3.1.1", "reactotron-react-native": "^5.0.0", "rn-fetch-blob": "0.13.0-beta.2", "rn-placeholder": "^3.0.1", "rn-range-slider": "^2.0.4", "rn-tourguide": "^2.7.1", "ua-parser-js": "^0.7.21", "url": "^0.11.0", "url-parse": "^1.4.7", "use-deep-compare-effect": "^1.8.1", "uuid-random": "^1.3.2", "validate.js": "0.13.1"}, "devDependencies": {"@babel/core": "^7.12.9", "@babel/plugin-proposal-decorators": "^7.0.0", "@babel/plugin-proposal-optional-catch-binding": "^7.0.0", "@babel/preset-env": "^7.19.1", "@babel/runtime": "^7.12.5", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@react-native-community/eslint-config": "^2.0.0", "@types/jest": "^26.0.10", "@types/ramda": "0.27.14", "@types/react": "^18.0.21", "@types/react-native": "^0.70.4", "@types/react-test-renderer": "^16.9.2", "@typescript-eslint/eslint-plugin": "^3.9.1", "@typescript-eslint/parser": "^3.9.1", "babel-jest": "^26.6.3", "babel-plugin-module-resolver": "^4.0.0", "deprecated-react-native-prop-types": "^2.3.0", "eslint": "7.32.0", "eslint-config-prettier": "^6.0.0", "eslint-config-standard": "^14.1.0", "eslint-import-resolver-typescript": "^2.3.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.0.0", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-react": "^7.12.4", "eslint-plugin-react-native": "^3.6.0", "eslint-plugin-standard": "^4.0.0", "husky": "^4.3.0", "ignite-bowser": "^5.3.0", "jest": "^26.6.3", "jetifier": "^1.6.2", "metro-react-native-babel-preset": "0.72.3", "npm-run-all": "4.1.5", "patch-package": "^8.0.0", "postinstall-prepare": "1.0.1", "prettier": "^2.0.4", "react-devtools": "^4.10.0", "react-devtools-core": "^4.24.4", "react-native-codegen": "^0.0.7", "react-native-svg-transformer": "^0.14.3", "react-powerplug": "1.0.0", "react-test-renderer": "18.1.0", "rimraf": "3.0.2", "solidarity": "3.0.0", "tailwindcss": "3.3.2", "ts-node": "^9.1.1", "typescript": "3.9.7"}, "jest": {"preset": "react-native", "setupFiles": ["<rootDir>/node_modules/react-native/jest/setup.js", "<rootDir>/test/setup.ts"], "testPathIgnorePatterns": ["/node_modules/", "/e2e"], "transformIgnorePatterns": ["node_modules/(?!(jest-)?react-native|react-native|react-navigation|@react-navigation|@react-native-community|@react-native-firebase)"]}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "packageManager": "yarn@1.22.22+sha1.ac34549e6aa8e7ead463a7407e1c7390f61a6610"}