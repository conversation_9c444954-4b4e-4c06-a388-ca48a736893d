{"info": {"name": "Proxyman Code Generator: PostmanCollection2", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "GET /store/api/get-store-revenue/623a9950be781e0ba814f22a", "request": {"method": "GET", "url": {"raw": "http://api.maxq.vn:4000/store/api/get-store-revenue/623a9950be781e0ba814f22a?startTime=05-20-2022&endTime=08-18-2022&output=json", "protocol": "http", "host": "api.maxq.vn", "path": "/store/api/get-store-revenue/623a9950be781e0ba814f22a", "port": "4000", "query": [{"value": "05-20-2022", "key": "startTime"}, {"value": "08-18-2022", "key": "endTime"}, {"value": "json", "key": "output"}]}, "header": [{"key": "Location", "value": "{\"lat\":21.028511,\"lng\":105.804817}"}, {"key": "access-token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MjNhOTdjZGJlNzgxZTBiYTgxNGYyMjciLCJ0eXBlIjoxLCJjb2RlIjoxLCJiaXJ0aGRheSI6MTY0Nzk5MzYwMDAwMCwibW9kaWZ5QXQiOjE2NjAyMjY3NDMwMDAsImNyZWF0ZUF0IjoxNjQ4MDA3MTE3MDAwLCJwb2ludCI6MCwiY29uZmlybVBob25lIjowLCJjb25maXJtRW1haWwiOjEsIndhbGxldCI6MCwiZnVuZHMiOjAsImZlZSI6MCwic2VydmljZXNTcGEiOjAsInNlcnZpY2VzSG90ZWwiOjAsInNlcnZpY2VzRXhhbWluYXRpb24iOjAsInNlcnZpY2VzU3RvcmUiOjAsIm5vdGlmaWNhdGlvbnNCaWxsUmV0dXJuIjowLCJjb3VudE1lc3NhZ2UiOjAsImNvdW50T25saW5lU2Vzc2lvbiI6NTA3LCJub3RpZmljYXRpb25zIjowLCJzdGF0dXNTdG9yZSI6MSwic3RhdHVzIjoxLCJwaG9uZSI6IjA5ODI4NzYxODAiLCJhZGRyZXNzTGlzdCI6W3sic3RyZWV0IjoiMSIsInBob25lIjoiMDkzMTExNTI4MCIsImRlZmF1bHQiOmZhbHNlLCJhZGRyZXNzIjoiMSwgUGjGsOG7nW5nIENoxrDGoW5nIETGsMahbmcgxJDhu5ksIFF14bqtbiBIb8OgbiBLaeG6v20sIFRow6BuaCBwaOG7kSBIw6AgTuG7mWkiLCJuYW1lIjoiTmd1eeG7hW4gQSIsIndhcmQiOiJQaMaw4budbmcgQ2jGsMahbmcgRMawxqFuZyDEkOG7mSIsInByb3ZpbmNlIjoiVGjDoG5oIHBo4buRIEjDoCBO4buZaSIsImRpc3RyaWN0IjoiUXXhuq1uIEhvw6BuIEtp4bq_bSIsIl9pZCI6bnVsbH0seyJzdHJlZXQiOiIxIMSQw6BvIER1eSBBbmgiLCJwaG9uZSI6IjA5ODI4NzYxODAiLCJkZWZhdWx0IjpmYWxzZSwiYWRkcmVzcyI6IjEgxJDDoG8gRHV5IEFuaCwgUGjGsOG7nW5nIFBoxrDGoW5nIE1haSwgUXXhuq1uIMSQ4buRbmcgxJBhLCBUaMOgbmggcGjhu5EgSMOgIE7hu5lpIiwibmFtZSI6IkNhciBzaG9wIiwid2FyZCI6IlBoxrDhu51uZyBQaMawxqFuZyBNYWkiLCJwcm92aW5jZSI6IlRow6BuaCBwaOG7kSBIw6AgTuG7mWkiLCJkaXN0cmljdCI6IlF14bqtbiDEkOG7kW5nIMSQYSIsIl9pZCI6bnVsbH1dLCJhZGRyZXNzIjoiU-G7kSAxIMSQw6BvIER1eSBBbmgsIMSQ4buRbmcgxJBhLCBITiIsImxvY2F0aW9uIjoiIiwic3RyZWV0IjoiIiwiZW1haWwiOiJjYWZlYXV0b0B0Z2NvcnAuY29tLnZuIiwidXNlck5hbWUiOiIiLCJmdWxsTmFtZSI6IkNhciBQcm8iLCJfX3YiOjAsImRlc2NyaXB0aW9uIjpudWxsLCJwaWN0dXJlIjoiZmlsZXMvaW1hZ2VzL3BpY3R1cmUtMTY0ODYxODA5NjExMi1Hcm91cCUyMDI1MDAuanBnIiwic2V4IjowLCJpYXQiOjE2NjAyMjY3OTcsImV4cCI6MjQ0ODYyNjc5N30.3Ojrf-S80WBq4zGn_dzcqSEyWFi-H-LaSNXpSUYt5JY"}, {"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "en-US,en;q=0.9"}, {"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MjNhOTdjZGJlNzgxZTBiYTgxNGYyMjciLCJ0eXBlIjoxLCJjb2RlIjoxLCJiaXJ0aGRheSI6MTY0Nzk5MzYwMDAwMCwibW9kaWZ5QXQiOjE2NjAyMjY3NDMwMDAsImNyZWF0ZUF0IjoxNjQ4MDA3MTE3MDAwLCJwb2ludCI6MCwiY29uZmlybVBob25lIjowLCJjb25maXJtRW1haWwiOjEsIndhbGxldCI6MCwiZnVuZHMiOjAsImZlZSI6MCwic2VydmljZXNTcGEiOjAsInNlcnZpY2VzSG90ZWwiOjAsInNlcnZpY2VzRXhhbWluYXRpb24iOjAsInNlcnZpY2VzU3RvcmUiOjAsIm5vdGlmaWNhdGlvbnNCaWxsUmV0dXJuIjowLCJjb3VudE1lc3NhZ2UiOjAsImNvdW50T25saW5lU2Vzc2lvbiI6NTA3LCJub3RpZmljYXRpb25zIjowLCJzdGF0dXNTdG9yZSI6MSwic3RhdHVzIjoxLCJwaG9uZSI6IjA5ODI4NzYxODAiLCJhZGRyZXNzTGlzdCI6W3sic3RyZWV0IjoiMSIsInBob25lIjoiMDkzMTExNTI4MCIsImRlZmF1bHQiOmZhbHNlLCJhZGRyZXNzIjoiMSwgUGjGsOG7nW5nIENoxrDGoW5nIETGsMahbmcgxJDhu5ksIFF14bqtbiBIb8OgbiBLaeG6v20sIFRow6BuaCBwaOG7kSBIw6AgTuG7mWkiLCJuYW1lIjoiTmd1eeG7hW4gQSIsIndhcmQiOiJQaMaw4budbmcgQ2jGsMahbmcgRMawxqFuZyDEkOG7mSIsInByb3ZpbmNlIjoiVGjDoG5oIHBo4buRIEjDoCBO4buZaSIsImRpc3RyaWN0IjoiUXXhuq1uIEhvw6BuIEtp4bq_bSIsIl9pZCI6bnVsbH0seyJzdHJlZXQiOiIxIMSQw6BvIER1eSBBbmgiLCJwaG9uZSI6IjA5ODI4NzYxODAiLCJkZWZhdWx0IjpmYWxzZSwiYWRkcmVzcyI6IjEgxJDDoG8gRHV5IEFuaCwgUGjGsOG7nW5nIFBoxrDGoW5nIE1haSwgUXXhuq1uIMSQ4buRbmcgxJBhLCBUaMOgbmggcGjhu5EgSMOgIE7hu5lpIiwibmFtZSI6IkNhciBzaG9wIiwid2FyZCI6IlBoxrDhu51uZyBQaMawxqFuZyBNYWkiLCJwcm92aW5jZSI6IlRow6BuaCBwaOG7kSBIw6AgTuG7mWkiLCJkaXN0cmljdCI6IlF14bqtbiDEkOG7kW5nIMSQYSIsIl9pZCI6bnVsbH1dLCJhZGRyZXNzIjoiU-G7kSAxIMSQw6BvIER1eSBBbmgsIMSQ4buRbmcgxJBhLCBITiIsImxvY2F0aW9uIjoiIiwic3RyZWV0IjoiIiwiZW1haWwiOiJjYWZlYXV0b0B0Z2NvcnAuY29tLnZuIiwidXNlck5hbWUiOiIiLCJmdWxsTmFtZSI6IkNhciBQcm8iLCJfX3YiOjAsImRlc2NyaXB0aW9uIjpudWxsLCJwaWN0dXJlIjoiZmlsZXMvaW1hZ2VzL3BpY3R1cmUtMTY0ODYxODA5NjExMi1Hcm91cCUyMDI1MDAuanBnIiwic2V4IjowLCJpYXQiOjE2NjAyMjY3OTcsImV4cCI6MjQ0ODYyNjc5N30.3Ojrf-S80WBq4zGn_dzcqSEyWFi-H-LaSNXpSUYt5JY"}, {"key": "Accept-Encoding", "value": "gzip, deflate"}, {"key": "User-Agent", "value": "maxQ%20Partners/34 CFNetwork/1333.0.4 Darwin/21.6.0"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Cache-Control", "value": "no-cache"}]}}]}