{
  "attributes": {
    "AnBKS": false,
    "AnPhi": false,
    "BienKiemSoat": "30A-262.11",
    "DiaChiChuXe": "1 Ngách 43/2 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>",
    "DiaChiCongTy": "",
    "DiaChiKH": "1 Ngách 43/2 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>",
    "DienThoai": "0982876180",
    "EmailKH": "<EMAIL>",
    "GioCuoi": "05:33",
    "GioDau": "05:33",
    "MTNLaiPhu": "50000000",
    "MaGioiThieu": "",
    "MaSoThue": "",
    "MayKeo": false,
    "MucDichSD": "1",
    "NamSD": "",
    "NamSX": "",
    "NgayCuoi": "15/01/2024",
    "NgayDau": "24/01/2023",
    "PaymentId": "1673778995765",
    "PhiBHLaiPhu": "0",
    "PhiBHTNDSBB": "469000",
    "Pr_key": null,
    "ProductId": "2",
    "SoKhung": "",
    "SoMay": "",
    "SoNguoiToiDa": "0",
    "TenChuXe": "Đinh Mạnh Trung",
    "Ten<PERSON>ongTy": "",
    "TenKH": "Đinh Mạnh Trung",
    "ThamGiaLaiPhu": false,
    "TrongTai": "",
    "XeBus": false,
    "XeChoTien": false,
    "XeChuyenDung": false,
    "XeCuuThuong": false,
    "XeDauKeo": false,
    "XePickUp": false,
    "XeTaiVan": false,
    "XeTapLai": false,
    "Xetaxi": false,
    "active": true,
    "createdAt": "2023-01-15T10:37:53.998Z",
    "dataRenew": {
      "AnBKS": false,
      "AnPhi": false,
      "BienKiemSoat": "30A26211",
      "ChoNgoi": "5",
      "DiaChiChuXe": "1 Ngách 43/2 Vạn Kiếp, Chương Dương, Hoàn Kiếm Hà Nội",
      "DiaChiKH": "1 Ngách 43/2 Vạn Kiếp, Chương Dương, Hoàn Kiếm Hà Nội",
      "DienThoai": "0982876180",
      "DongXe": "",
      "EmailKH": "<EMAIL>",
      "GioCuoi": "05:33",
      "GioDau": "05:33",
      "HieuXe": "",
      "LoaiXe": "3001",
      "MTNLaiPhu": "50000000",
      "MaMucDichSD": "1",
      "MayKeo": false,
      "MucDichSuDung": "Xe chở người không kinh doanh vận tải",
      "NamSD": "",
      "NamSX": "",
      "NgayCuoi": "15/01/2024",
      "NgayDau": "24/01/2023",
      "PhiBHLaiPhu": "0",
      "PhiBHTNDSBB": "469000",
      "Pr_key": "757872",
      "SoKhung": "",
      "SoMay": "",
      "SoNguoiToiDa": "0",
      "TenChuXe": "Đinh Mạnh Trung",
      "TenKH": "Đinh Mạnh Trung",
      "ThamGiaLaiPhu": false,
      "TongPhi": "469000",
      "TotalFeeNoVAT": "undefined",
      "TrongTai": "",
      "XeBus": false,
      "XeChoTien": false,
      "XeChuyenDung": false,
      "XeCuuThuong": false,
      "XeDauKeo": false,
      "XePickUp": false,
      "XeTaiVan": false,
      "XeTapLai": false,
      "Xetaxi": false,
      "ma_giaodich": "1673778995765",
      "ma_loaixe": "3001",
      "ma_loaixe_txt": "Xe không KDVT dưới 6 chỗ ngồi",
      "ma_mdsd": "1",
      "ma_trongtai": "",
      "mtn_laiphu": "50000000",
      "philpx_nhap": "0",
      "so_cho": "5",
      "so_nguoi": "5",
      "thamgia_laiphu": false,
      "thamgia_tndsbb": true,
      "type": "tnds"
    },
    "dongXe": "3001",
    "expire_date": "2024-01-15T05:33:00.000Z",
    "hangXe": "3001",
    "hieuXe": "",
    "loai_bao_hiem": {
      "data": [
        Object
      ]
    },
    "name": "Bảo hiểm TNDS Ô tô bắt buộc đối với chủ xe",
    "publishedAt": "2023-01-15T10:37:53.968Z",
    "type": "BẢO HIỂM TNDS",
    "updatedAt": "2023-10-16T11:35:56.202Z",
    "user_id": "623df63a51282810737ff2b4",
    "xuatVAT": false
  },
  "id": 173
}
