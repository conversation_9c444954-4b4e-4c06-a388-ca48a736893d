{"compilerOptions": {"noUnusedLocals": false, "noUnusedParameters": false, "allowJs": false, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "jsx": "react-native", "module": "es2015", "moduleResolution": "node", "noImplicitAny": false, "noImplicitReturns": true, "noImplicitThis": true, "sourceMap": true, "target": "esnext", "lib": ["esnext"], "skipLibCheck": true, "baseUrl": ".", "paths": {"@app/*": ["./app/*"], "@app/config/*": ["./app/config/*"], "@app/constants/*": ["./app/constants/*"], "@app/layout/*": ["./app/layout/*"], "@app/models/*": ["./app/models/*"], "@app/navigation/*": ["./app/navigation/*"], "@app/screens/*": ["./app/screens/*"], "@app/theme": ["./app/theme"], "@app/types/*": ["./app/types/*"], "@app/utils": ["./app/utils"], "@app/utils/*": ["./app/utils/*"], "@app/assets/*": ["./app/assets/*"], "@app/services/*": ["./app/services/*"], "@app/services": ["./app/services"], "@app/components/*": ["./app/components/*"], "@app/context": ["./app/context"], "@app/use-hooks": ["./app/use-hooks"]}}, "exclude": ["node_modules"], "include": ["index.js", "app", "test"]}